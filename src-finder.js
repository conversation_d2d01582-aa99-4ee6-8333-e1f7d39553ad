const fs = require('fs');
const path = require('path');
const https = require('https');
// 扫描所有带了域名的静态资源，下载到本地，并进行替换

const dirPath = path.join(__dirname, './');
const includeFileType = ['.php', '.scss', '.css', '.html'];
const excludeDir = ['node_modules', 'vender', 'dist', 'static', 'font'];
const downloadPath = path.join(__dirname, './public/ali-fonts/');

function scanDir(dir) {
    if (excludeDir.find(excludeStr => new RegExp(excludeStr).test(dir))) {
        return;
    }

    fs.readdir(dir, function (err, files) {
        if (err) {
            console.log(err);
        } else {
            files.forEach(name => {
                const filePath = path.join(dir, name);
                fs.stat(filePath, function (err, stats) {
                    if (stats.isDirectory()) {
                        // 文件夹 继续遍历
                        scanDir(filePath)
                    } else {
                        // 文件 读文件内容
                        readFile(filePath);
                    }
                });
            });
        }
    });
}

function readFile(filePath) {

    if (!includeFileType.includes(path.extname(filePath))) {
        return;
    }

    fs.readFile(filePath, 'utf8', (err, data) => {
        if (err) throw err;
        // https://at.alicdn.com/t/font_1101300_nd6iyfv2fh.js
        const reg = /(https\:)?\/\/at\.alicdn\.com.+\.(css|js|eot|woff2|woff|truetype|ttf|svg)/g;
        const regResult = data.match(reg);
        if (regResult && regResult.length) {
            regResult.forEach(file => {
                downloadFile(file, function(success) {
                    if (success) {
                        data = data.split(file).join('/ali-fonts/' + path.basename(file));
                        fs.writeFileSync(filePath, data);
                    }
                })
            })
        }
    });
}

function downloadFile(uri, cb) {
    const fileName = path.basename(uri);
    const dest = path.join(downloadPath, fileName);
    if (fs.existsSync(dest)) {
        cb(dest);
        return;
    }
    // 确保dest路径存在
    const file = fs.createWriteStream(dest);
    if(uri.indexOf('https') < 0) {
        uri = 'https:' + uri;
    }
    console.log('download start', uri);
    https.get(uri, (res) => {
        if (res.statusCode !== 200) {
            cb();
            return;
        }

        res.on('end', () => {
            console.log('download end');
        });

        file.on('finish', () => {
            console.log('finish write file')
            file.close(() => cb(true));
        }).on('error', (err) => {
            fs.unlink(dest);
            if (cb) cb();
        })

        res.pipe(file);
    });
}

scanDir(dirPath);
