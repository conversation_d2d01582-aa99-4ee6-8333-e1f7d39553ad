<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class UpdateClientDevices extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('client_devices', function (Blueprint $table) {
            $table->integer('auth_token_id', false, true)->default(0)->nullable()->after('user_id');
            $table->string('session_id', 64)->default('')->after('auth_token_id')->nullable();
            $table->integer('real_id', false, true)->default(0)->after('session_id')->nullable();

            $table->integer('credited_time', false, true)->default(0)->change();
            $table->integer('logout_time', false, true)->default(0)->change();
            $table->integer('deleted', false, true)->default(0)->change();
            $table->integer('settings', false, true)->default(0)->change();

            $table->dropIndex('idx_device_token_user_id');
            $table->dropIndex('user_id');
            $table->index(['user_id', 'device_token'], 'idx_user_id_device_token');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('client_devices', function (Blueprint $table) {

            $table->dropIndex('idx_user_id_device_token');
            $table->index('user_id', 'user_id');
            $table->index(['device_token', 'user_id'], 'idx_device_token_user_id');

            $table->integer('settings', false, true)->default(NULL)->change();
            $table->integer('deleted', false, true)->default(NULL)->change();
            $table->integer('logout_time', false, true)->default(NULL)->change();
            $table->integer('credited_time', false, true)->default(NULL)->change();

            $table->dropColumn('real_id');
            $table->dropColumn('session_id');
            $table->dropColumn('auth_token_id');
        });
    }
}
