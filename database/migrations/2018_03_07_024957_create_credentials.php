<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateCredentials extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('credentials', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('login_user_id', false, true);
            $table->string('identifier', 128);
            $table->string('ext_identifier', 128)->default('');
            $table->tinyInteger('type', false, true);
            $table->tinyInteger('status', false, true);

            $table->string('activation_code', 40)->default('');
            $table->integer('activation_expires_at', false, true)->default(0);
            $table->integer('settings', false, true)->default(0);
            $table->string('value_box', 512)->default('');
            $table->integer('created', false, true);
            $table->integer('updated', false, true);

            $table->unique(['type', 'identifier', 'ext_identifier'], 'login');
            $table->unique(['login_user_id', 'type'], 'user_type');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('credentials');
    }
}
