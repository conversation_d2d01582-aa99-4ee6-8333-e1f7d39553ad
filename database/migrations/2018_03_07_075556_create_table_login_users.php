<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateTableLoginUsers extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('login_users', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('user_id', false, true);
            $table->string('password', 80);
            $table->integer('activated', false, true)->default(0);
            $table->string('remember_token', 60)->default('');
            $table->integer('settings', false, true)->default(0);
            $table->string('value_box', 512)->default('');
            $table->integer('created', false, true);
            $table->integer('updated', false, true);

            $table->index('user_id', 'user_id');
//            $table->index('remember_token', 'remember_token');
        });

        \Illuminate\Support\Facades\DB::statement('ALTER TABLE `login_users` ADD INDEX `remember_token` ( `remember_token`(10) )');
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('login_users');
    }
}
