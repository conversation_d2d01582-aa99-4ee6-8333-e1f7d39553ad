<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateTablePasswordResets extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('password_resets', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('login_user_id', false, true);
            $table->string('unique_code', 32);
            $table->integer('expires_at', false, true);
            $table->integer('created', false, true);
            $table->integer('updated', false, true);

            $table->unique(['login_user_id'], 'login_user_id');
            $table->unique(['unique_code'], 'unique_code');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('password_resets');
    }
}
