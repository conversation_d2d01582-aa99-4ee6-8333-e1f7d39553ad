<?php

use Illuminate\Http\Request;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::post('/security/check_device', 'Api\SecurityController@checkDevice');
// 刷新auth token
Route::post('/security/create_auth_token', 'Api\SecurityController@createAuthToken');
Route::post('/security/refresh_auth_token', 'Api\SecurityController@refreshAuthToken');
Route::post('/security/revoke_auth_token', 'Api\SecurityController@revokeAuthToken');
Route::post('/security/verify_identity', 'Api\SecurityController@verifyIdentity');
Route::post('/security/login', 'Api\SecurityController@login');
Route::post('/security/quick_register', 'Api\RegisterController@quickRegister');
Route::post('/security/check_two_step_login', 'Api\SecurityController@checkTwoStepLogin');
Route::post('/security/get_available_two_step_method', 'Api\SecurityController@getAvailableTwoStepMethod');
Route::post('/security/set_two_step', 'Api\SecurityController@setTwoStep');
Route::post('/security/get_google_qrcode', 'Api\SecurityController@getGoogleQRcode');
Route::post('/security/get_google_secret', 'Api\SecurityController@getGoogleSecret');
Route::post('/security/send_two_step_code', 'Api\SecurityController@sendCode');
Route::get('/security/get_wechat_qrcode', 'Api\SecurityController@getWechatQRcode');
Route::post('/security/change_password', 'Api\SecurityController@changePassword');
Route::get('/security/forgot_password', 'Api\SecurityController@showForgotPasswordForm')->name('password.forgot');
Route::post('/security/captcha_sms_send', 'Api\SecurityController@sendCaptchaSms');
