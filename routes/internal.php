<?php

use Illuminate\Http\Request;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::post('/account/check_credential_existence', 'Internal\AccountController@checkCredentialExistence');
Route::post('/account/check_third_credential_existence', 'Internal\AccountController@checkThirdCredentialExistence');
Route::post('/account/update_third_users_credential', 'Internal\AccountController@updateThirdUsersCredential');
Route::post('/account/create_users', 'Internal\AccountController@createUsers');
Route::post('/account/create_user', 'Internal\AccountController@createUser');
Route::post('/account/create_personal_user', 'Internal\AccountController@createPersonalUser');
Route::post('/account/change_password', 'Internal\AccountController@changePassword');
Route::post('/account/delete_user', 'Internal\AccountController@deleteUser');
Route::post('/account/renew_users_credentials', 'Internal\AccountController@renewUsersCredentials');
Route::post('/account/login', 'Internal\AccountController@login');
Route::post('/account/logout', 'Internal\AccountController@logout');
Route::post('/account/get_active_status', 'Internal\AccountController@getUserActiveStatus');
Route::post('/account/get_two_step_status', 'Internal\AccountController@getUserTwoStepStatus');
Route::post('/account/close_two_step', 'Internal\AccountController@closeTwoStep');
Route::post('/account/get_login_info', 'Internal\AccountController@getLoginInfo');
Route::post('/account/delete_client_device', 'Internal\AccountController@deleteClientDevice');

Route::post('/security/get_code', 'Internal\SecurityController@getCode');

Route::post('/account/create_credential', 'Internal\AccountController@createCredential');
