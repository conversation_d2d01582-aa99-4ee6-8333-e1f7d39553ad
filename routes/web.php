<?php

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::get('/', 'HomeController@index');
Route::get('/home', 'HomeController@index')->name('home');

Route::get('/login_settings', 'UserSettingController@loginSettings')->name('login_settings');
Route::get('/security_settings', 'UserSettingController@securitySettings')->name('security_settings');

Route::get('/client/auth/sync_login', 'Auth\LoginController@showLoginForm')->name('sync_login');
Route::get('/auth/login', 'Auth\LoginController@showLoginForm')->name('login');
Route::get('/login', 'Auth\LoginController@showLoginForm')->name('login');
Route::get('/login_v3', 'Auth\LoginController@showLoginFormV3')->name('login');
Route::get('/two_step_login', 'Auth\LoginController@showLoginVerifyForm')->name('login_verify');
Route::post('/two_step_login','Auth\LoginController@twoStepLogin')->name('do_login_verify');
Route::get('/force_set_two_step', 'Auth\LoginController@showForceStepForm')->name('force_set_two_step');
Route::get('/sso_entry', 'Auth\SsoEntryController@showSsoEntryForm')->name('sso_entry');

Route::get('/other_enterprise', 'Auth\LoginController@showOtherLoginForm')->name('other_enterprise');
Route::get('/hybrid_enterprise', 'Auth\LoginController@showHybridLoginForm')->name('hybrid_enterprise');
Route::get('/international_login', 'Auth\LoginController@showInternationalLoginForm')->name('international_login');
Route::post('/login', 'Auth\LoginController@login');
Route::post('/app_login', 'Auth\LoginController@appLogin');
Route::post('/sync_login', 'Auth\LoginController@syncLogin');
Route::any('/logout', 'Auth\LoginController@logout')->name('logout');
Route::any('/quick_login/v1/logout', 'Auth\LoginController@logoutV2')->name('quick_login.v1.logout');
Route::get('/check_login', 'Auth\LoginController@checkPicCaptchaNeeded');

Route::get('/activate_by_phone', 'Auth\ActivationController@showActivationForm')->name('activate_by_phone');
Route::post('/activation/activate', 'Auth\ActivationController@activate');
Route::post('/activation/confirm_account', 'Auth\ActivationController@confirmAccount');
Route::post('/activation/verify_phone', 'Auth\ActivationController@verifyPhone');
Route::get('/activation/{code}', 'Auth\ActivationController@activation')->where(['code' => '[0-9a-z]{32,40}'])->name('activation');

// 第三方账号绑定登录
Route::post('/bind_login', 'Auth\ThirdLoginController@bindLogin');
Route::get('/bind_login', 'Auth\ThirdLoginController@showBindLoginForm')->name('bind_login');

Route::get('/third/dingtalklogin', 'Auth\ThirdLoginController@dingtalkLogin')->name('dingtalklogin');
Route::get('/third/dingtalkcallback', 'Auth\ThirdLoginController@dingtalkCallback');
Route::get('/third/wechatlogin', 'Auth\ThirdLoginController@wechatLogin')->name('wechatlogin');
Route::get('/third/wechatcallback', 'Auth\ThirdLoginController@wechatCallback');
Route::get('/third/qihoo360login', 'Auth\ThirdLoginController@qihoo360Login')->name('qihoo360login');
Route::get('/third/qihoo360callback', 'Auth\ThirdLoginController@qihoo360Callback');

// 解绑第三方
Route::post('/security/unbind_third', 'SecurityController@unbindThird');
Route::get('/security/get_thirds', 'SecurityController@getThirdAccounts');

// Registration Routes...
Route::get('/personal_register', 'Auth\RegisterController@showRegistrationForm')->name('personal_register');
Route::get('/register', 'Auth\RegisterController@showRegistrationForm')->name('register');
Route::post('/bind_register', 'Auth\ThirdRegisterController@bindRegister');
Route::get('/register_success', 'HomeController@showRegisterSuccessForm')->name('register_success');
Route::get('/enterprise_register_success', 'HomeController@showEnterpriseRegisterSuccessForm')->name('enterprise_register_success');
Route::get('/validate_success', 'HomeController@showValidateSuccessForm')->name('validate_success');
Route::get('/validate_user_success/{name}/{space}', 'HomeController@showValidateUserSuccessForm')->name('validate_user_success');
Route::post('/register', 'Auth\RegisterController@register');
Route::post('/quick_login/v1/register', 'Auth\RegisterController@quickLogin');
Route::post('/become_referral', 'Auth\RegisterController@becomeReferral');
Route::post('/register_enterprise_edition', 'Auth\RegisterController@registerEnterpriseEdition');
Route::post('/personal_register', 'Auth\RegisterController@personalRegister');
// ali market registration Routes...
Route::get('/ali_market_register', 'Auth\AliMarketRegisterController@showRegistrationForm')->name('ali_market_register');
Route::post('/ali_market_register', 'Auth\AliMarketRegisterController@register');
Route::post('/check_channel_coupon', 'Auth\RegisterController@checkChannelCoupon');
Route::get('/check_channel_coupon', 'Auth\RegisterController@showCheckChannelCouponForm');

// Route::post('/new_register', 'Auth\RegisterController@checkChannelCoupon');
Route::get('/new_register', 'Auth\RegisterController@newEnterpriseRegister');
Route::get('/introduction_register', 'Auth\RegisterController@introductionRegister');

// Password Reset Routes...
Route::get('/password/forgot_for_international', 'Auth\ForgotPasswordController@showForgotPasswordForm')->name('password.forgot_for_international');
Route::get('/password/forgot', 'Auth\ForgotPasswordController@showForgotPasswordForm')->name('password.forgot');
Route::post('/password/confirm_account', 'Auth\ForgotPasswordController@confirmAccount')->name('password.confirm_account');
Route::post('/password/confirm_forgot', 'Auth\ForgotPasswordController@confirmForgot');
Route::get('/password/reset/{code}', 'Auth\ResetPasswordController@showResetForm')->where(['code' => '[0-9a-z]{32}'])->name('password.reset');
Route::post('/password/reset', 'Auth\ResetPasswordController@reset')->name('password.confirm_reset');
Route::post('/password/change', 'Auth\PasswordController@change');
Route::post('/password/set', 'Auth\PasswordController@setPasswordForSsoUser');
Route::get('/password/init', 'Auth\PasswordController@showInitForm')->name('init_password');
Route::post('/password/init', 'Auth\PasswordController@initPassword')->name('init_password');

Route::get('/captcha/get_pic/{type}', 'CaptchaController@getPicCaptcha');
Route::post('/captcha/sms_send', 'CaptchaController@sendSms');
Route::post('/quick_login/v1/captcha/sms_send', 'CaptchaController@sendSms');
Route::post('/captcha/email_send', 'CaptchaController@sendEmail');
Route::get('/captcha/check_phone/{type}', 'CaptchaController@checkPicCaptchaNeeded');
Route::post('/captcha/send_weiling_sms', 'CaptchaController@sendWeilingSms');
Route::post('/captcha/weiling_captcha_check', 'CaptchaController@weilingCaptchaCheck');

Route::post('/credential/update_email', 'Auth\CredentialController@updateEmail');
Route::post('/credential/update_phone', 'Auth\CredentialController@updatePhone');
Route::post('/credential/verify_email', 'Auth\CredentialController@verifyEmail');
Route::post('/credential/verify_phone', 'Auth\CredentialController@verifyPhone');
Route::post('/credential/pre_verify_phone', 'Auth\CredentialController@preVerifyPhone');
Route::post('/credential/pre_verify_email', 'Auth\CredentialController@preVerifyEmail');
Route::post('/credential/check_existence', 'Auth\CredentialController@checkExistence');
//Route::post('credential/unbind', 'Auth\CredentialController@unbind');
Route::get('/credential/get_login', 'Auth\CredentialController@getBasicLogin');

Route::get('/credential/app_update_email', 'Auth\CredentialController@appShowUpdateEmailForm');
Route::get('/credential/app_update_phone', 'Auth\CredentialController@appShowUpdatePhoneForm');
Route::post('/credential/app_update_email', 'Auth\CredentialController@updateEmail');
Route::post('/credential/app_update_phone', 'Auth\CredentialController@updatePhone');

Route::get('/validation/{code}', 'Auth\CredentialController@validation')->where(['code' => '[0-9a-z]{32,40}'])->name('credential.validation');

// 设备管理+二次验证相关
Route::name('security')->group(function () {
    Route::post('/security/check_device', 'SecurityController@checkDevice');
    Route::post('/security/get_devices', 'SecurityController@getDevices');
    Route::post('/security/delete_device', 'SecurityController@deleteDevice');
    Route::get('/security/get_wechat_qrcode', 'SecurityController@getWechatQRcode');
    Route::get('/security/get_google_qrcode', 'SecurityController@getGoogleQRcode');
    Route::get('/security/get_google_secret', 'SecurityController@getGoogleSecret');
    Route::post('/security/send_two_step_code', 'SecurityController@sendCode');
    Route::post('/security/set_two_step', 'SecurityController@setTwoStep');
    Route::get('/security/set_two_step', 'UserSettingController@showSetTwoStepForm');
    Route::post('/security/close_two_step', 'SecurityController@closeTwoStep');
    Route::post('/security/get_available_two_step_method', 'SecurityController@getAvailableTwoStepMethod');
    Route::get('/security/get_two_step_method', 'SecurityController@getTwoStepMethod');
    Route::post('/security/verify_identity', 'SecurityController@verifyIdentity');
    Route::post('/security/get_open_api_token_info', 'SecurityController@getOpenApiTokenInfo');
    Route::post('/security/refresh_open_api_token_info', 'SecurityController@refreshOpenApiTokenInfo');
});

// 扫码登录
Route::get('/qr_login/{token}', 'Auth\QrLoginController@showMobileConfirmForm')->where(['token' => '[0-9a-zA-Z]{32}'])->name('qr_login');
Route::post('qr_login/confirm_login', 'Auth\QrLoginController@confirmLogin');
Route::post('qr_login/check_token', 'Auth\QrLoginController@checkToken');
Route::get('/qr_login/image/{token}', 'Auth\QrLoginController@getImage')->where(['token' => '[0-9a-zA-Z]{32}']);
Route::get('/h5_download_notice', 'Auth\LoginController@h5DownloadNotice')->name('h5_download_notice');

// oauth 登录
Route::get('/oauth/login', 'Auth\OAuthController@showLoginForm');
Route::post('/oauth/login', 'Auth\OAuthController@oauthLogin');

Route::post('/oauth/qrlogin', 'Auth\OAuthController@oauthQrLogin');


Route::get('/dedicated_enterprise/get_sso_product_id', 'Auth\DedicatedEnterpriseController@getSsoProductId');

Route::get('/refresh', 'HomeController@showRefreshTokenPage');

// 阿里账号注册绑定
Route::get('/aliyun/bind', 'Auth\RegisterController@showAliyunBindForm')->name('aliyun_bind');

Route::post('/report_why_leave', 'Auth\RegisterController@reportWhyLeave');
