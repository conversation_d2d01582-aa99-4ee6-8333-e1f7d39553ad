{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#composer-lock-the-lock-file", "This file is @generated automatically"], "content-hash": "b09a58929a975cd2191e26b9f55a5ee3", "packages": [{"name": "bacon/bacon-qr-code", "version": "1.0.3", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/bacon/bacon-qr-code/1.0.3/bacon-bacon-qr-code-1.0.3.zip", "reference": "5a91b62b9d37cee635bbf8d553f4546057250bee", "shasum": ""}, "require": {"ext-iconv": "*", "php": "^5.4|^7.0"}, "require-dev": {"phpunit/phpunit": "^4.8"}, "suggest": {"ext-gd": "to generate QR code images"}, "type": "library", "autoload": {"psr-0": {"BaconQrCode": "src/"}}, "license": ["BSD-2-<PERSON><PERSON>"], "authors": [{"name": "<PERSON> 'DASPRiD'", "email": "<EMAIL>", "homepage": "http://www.dasprids.de", "role": "Developer"}], "description": "BaconQrCode is a QR code generator for PHP.", "homepage": "https://github.com/Bacon/BaconQrCode", "time": "2017-10-17T09:59:25+00:00"}, {"name": "beber<PERSON>i/assert", "version": "v3.3.1", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/beberlei/assert/v3.3.1/beberlei-assert-v3.3.1.zip", "reference": "5e721d7e937ca3ba2cdec1e1adf195f9e5188372", "shasum": ""}, "require": {"ext-ctype": "*", "ext-json": "*", "ext-mbstring": "*", "ext-simplexml": "*", "php": "^7.0 || ^8.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "*", "phpstan/phpstan": "*", "phpunit/phpunit": ">=6.0.0", "yoast/phpunit-polyfills": "^0.1.0"}, "suggest": {"ext-intl": "Needed to allow Assertion::count(), Assertion::isCountable(), Assertion::minCount(), and Assertion::maxCount() to operate on ResourceBundles"}, "type": "library", "autoload": {"psr-4": {"Assert\\": "lib/Assert"}, "files": ["lib/Assert/functions.php"]}, "license": ["BSD-2-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Lead Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Collaborator"}], "description": "Thin assertion library for input validation in business models.", "keywords": ["assert", "assertion", "validation"], "time": "2021-04-18T20:11:03+00:00"}, {"name": "composer/ca-bundle", "version": "1.2.9", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/composer/ca-bundle/1.2.9/composer-ca-bundle-1.2.9.zip", "reference": "78a0e288fdcebf92aa2318a8d3656168da6ac1a5", "shasum": ""}, "require": {"ext-openssl": "*", "ext-pcre": "*", "php": "^5.3.2 || ^7.0 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^0.12.55", "psr/log": "^1.0", "symfony/phpunit-bridge": "^4.2 || ^5", "symfony/process": "^2.5 || ^3.0 || ^4.0 || ^5.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.x-dev"}}, "autoload": {"psr-4": {"Composer\\CaBundle\\": "src"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "Lets you find a path to the system CA bundle, and includes a fallback to the Mozilla CA bundle.", "keywords": ["cabundle", "cacert", "certificate", "ssl", "tls"], "time": "2021-01-12T12:10:35+00:00"}, {"name": "dnoegel/php-xdg-base-dir", "version": "v0.1.1", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/dnoegel/php-xdg-base-dir/v0.1.1/dnoegel-php-xdg-base-dir-v0.1.1.zip", "reference": "8f8a6e48c5ecb0f991c2fdcf5f154a47d85f9ffd", "shasum": ""}, "require": {"php": ">=5.3.2"}, "require-dev": {"phpunit/phpunit": "~7.0|~6.0|~5.0|~4.8.35"}, "type": "library", "autoload": {"psr-4": {"XdgBaseDir\\": "src/"}}, "license": ["MIT"], "description": "implementation of xdg base directory specification for php", "time": "2019-12-04T15:06:13+00:00"}, {"name": "doctrine/inflector", "version": "1.4.4", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/doctrine/inflector/1.4.4/doctrine-inflector-1.4.4.zip", "reference": "4bd5c1cdfcd00e9e2d8c484f79150f67e5d355d9", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^8.0", "phpstan/phpstan": "^0.12", "phpstan/phpstan-phpunit": "^0.12", "phpstan/phpstan-strict-rules": "^0.12", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Common\\Inflector\\": "lib/Doctrine/Common/Inflector", "Doctrine\\Inflector\\": "lib/Doctrine/Inflector"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Inflector is a small library that can perform string manipulations with regard to upper/lowercase and singular/plural forms of words.", "homepage": "https://www.doctrine-project.org/projects/inflector.html", "keywords": ["inflection", "inflector", "lowercase", "manipulation", "php", "plural", "singular", "strings", "uppercase", "words"], "time": "2021-04-16T17:34:40+00:00"}, {"name": "doctrine/lexer", "version": "1.0.2", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/doctrine/lexer/1.0.2/doctrine-lexer-1.0.2.zip", "reference": "1febd6c3ef84253d7c815bed85fc622ad207a9f8", "shasum": ""}, "require": {"php": ">=5.3.2"}, "require-dev": {"phpunit/phpunit": "^4.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Common\\Lexer\\": "lib/Doctrine/Common/Lexer"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Lexer parser library that can be used in Top-Down, Recursive Descent Parsers.", "homepage": "https://www.doctrine-project.org/projects/lexer.html", "keywords": ["annotations", "doc<PERSON>", "lexer", "parser", "php"], "time": "2019-06-08T11:03:04+00:00"}, {"name": "egulias/email-validator", "version": "2.1.25", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/egulias/email-validator/2.1.25/egulias-email-validator-2.1.25.zip", "reference": "0dbf5d78455d4d6a41d186da50adc1122ec066f4", "shasum": ""}, "require": {"doctrine/lexer": "^1.0.1", "php": ">=5.5", "symfony/polyfill-intl-idn": "^1.10"}, "require-dev": {"dominicsayers/isemail": "^3.0.7", "phpunit/phpunit": "^4.8.36|^7.5.15", "satooshi/php-coveralls": "^1.0.1"}, "suggest": {"ext-intl": "PHP Internationalization Libraries are required to use the SpoofChecking validation"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.1.x-dev"}}, "autoload": {"psr-4": {"Egulias\\EmailValidator\\": "src"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "A library for validating emails against several RFCs", "homepage": "https://github.com/egulias/EmailValidator", "keywords": ["email", "emailvalidation", "emailvalidator", "validation", "validator"], "time": "2020-12-29T14:50:06+00:00"}, {"name": "erusev/parsedown", "version": "1.7.4", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/erusev/parsedown/1.7.4/erusev-parsedown-1.7.4.zip", "reference": "cb17b6477dfff935958ba01325f2e8a2bfa6dab3", "shasum": ""}, "require": {"ext-mbstring": "*", "php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35"}, "type": "library", "autoload": {"psr-0": {"Parsedown": ""}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://erusev.com"}], "description": "<PERSON><PERSON>r for <PERSON>down.", "homepage": "http://parsedown.org", "keywords": ["markdown", "parser"], "time": "2019-12-30T22:54:17+00:00"}, {"name": "fangcloud/content-detection", "version": "1.0.0", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/qiyi<PERSON>_composer/fangcloud/content-detection/1.0.0/fangcloud-content-detection-1.0.0.zip", "reference": "2e04ebed05a59f587a037546eabfe061d6a5fae7", "shasum": "2e04ebed05a59f587a037546eabfe061d6a5fae7"}, "type": "package", "autoload": {"classmap": ["src/Content_Detection", "src/Content_Detection/Client"], "files": ["src/Content_Detection/Constants.php"]}, "license": ["MIT"], "description": "Filter for text, pic, etc.", "time": "2020-07-29T06:44:00+00:00"}, {"name": "fangcloud/ip", "version": "1.1.0", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/qiyi<PERSON>_composer/fangcloud/ip/1.1.0/fangcloud-ip-1.1.0.zip", "reference": "80ef92628429e1099e07a1c92c02733713848b9e", "shasum": "80ef92628429e1099e07a1c92c02733713848b9e"}, "require": {"geoip2/geoip2": "~2.0"}, "type": "package", "autoload": {"psr-4": {"Fangcloud\\IP\\": "src/"}}, "time": "2020-07-29T06:41:12+00:00"}, {"name": "fideloper/proxy", "version": "3.3.4", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/fideloper/proxy/3.3.4/fideloper-proxy-3.3.4.zip", "reference": "9cdf6f118af58d89764249bbcc7bb260c132924f", "shasum": ""}, "require": {"illuminate/contracts": "~5.0", "php": ">=5.4.0"}, "require-dev": {"illuminate/http": "~5.0", "mockery/mockery": "~0.9.3", "phpunit/phpunit": "^5.7"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.3-dev"}, "laravel": {"providers": ["Fideloper\\Proxy\\TrustedProxyServiceProvider"]}}, "autoload": {"psr-4": {"Fideloper\\Proxy\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Set trusted proxies for <PERSON><PERSON>", "keywords": ["load balancing", "proxy", "trusted proxy"], "time": "2017-06-15T17:19:42+00:00"}, {"name": "geoip2/geoip2", "version": "v2.10.0", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/geoip2/geoip2/v2.10.0/geoip2-geoip2-v2.10.0.zip", "reference": "419557cd21d9fe039721a83490701a58c8ce784a", "shasum": ""}, "require": {"ext-json": "*", "maxmind-db/reader": "~1.5", "maxmind/web-service-common": "~0.6", "php": ">=5.6"}, "require-dev": {"friendsofphp/php-cs-fixer": "2.*", "phpunit/phpunit": "5.*", "squizlabs/php_codesniffer": "3.*"}, "type": "library", "autoload": {"psr-4": {"GeoIp2\\": "src"}}, "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.maxmind.com/"}], "description": "MaxMind GeoIP2 PHP API", "homepage": "https://github.com/maxmind/GeoIP2-php", "keywords": ["IP", "geoip", "geoip2", "geolocation", "maxmind"], "time": "2019-12-12T18:48:39+00:00"}, {"name": "guzzlehttp/guzzle", "version": "6.5.5", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/guzzlehttp/guzzle/6.5.5/guzzlehttp-guzzle-6.5.5.zip", "reference": "9d4290de1cfd701f38099ef7e183b64b4b7b0c5e", "shasum": ""}, "require": {"ext-json": "*", "guzzlehttp/promises": "^1.0", "guzzlehttp/psr7": "^1.6.1", "php": ">=5.5", "symfony/polyfill-intl-idn": "^1.17.0"}, "require-dev": {"ext-curl": "*", "phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.4 || ^7.0", "psr/log": "^1.1"}, "suggest": {"psr/log": "Required for using the Log middleware"}, "type": "library", "extra": {"branch-alias": {"dev-master": "6.5-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\": "src/"}, "files": ["src/functions_include.php"]}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Guzzle is a PHP HTTP client library", "homepage": "http://guzzlephp.org/", "keywords": ["client", "curl", "framework", "http", "http client", "rest", "web service"], "time": "2020-06-16T21:01:06+00:00"}, {"name": "guzzlehttp/promises", "version": "1.4.1", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/guzzlehttp/promises/1.4.1/guzzlehttp-promises-1.4.1.zip", "reference": "8e7d04f1f6450fef59366c399cfad4b9383aa30d", "shasum": ""}, "require": {"php": ">=5.5"}, "require-dev": {"symfony/phpunit-bridge": "^4.4 || ^5.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.4-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\Promise\\": "src/"}, "files": ["src/functions_include.php"]}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Guzzle promises library", "keywords": ["promise"], "time": "2021-03-07T09:25:29+00:00"}, {"name": "guzzlehttp/psr7", "version": "1.8.1", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/guzzlehttp/psr7/1.8.1/guzzlehttp-psr7-1.8.1.zip", "reference": "35ea11d335fd638b5882ff1725228b3d35496ab1", "shasum": ""}, "require": {"php": ">=5.4.0", "psr/http-message": "~1.0", "ralouphie/getallheaders": "^2.0.5 || ^3.0.0"}, "provide": {"psr/http-message-implementation": "1.0"}, "require-dev": {"ext-zlib": "*", "phpunit/phpunit": "~4.8.36 || ^5.7.27 || ^6.5.14 || ^7.5.20 || ^8.5.8 || ^9.3.10"}, "suggest": {"laminas/laminas-httphandlerrunner": "Emit PSR-7 responses"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.7-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\Psr7\\": "src/"}, "files": ["src/functions_include.php"]}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "homepage": "https://github.com/Tobion"}], "description": "PSR-7 message implementation that also provides common utility methods", "keywords": ["http", "message", "psr-7", "request", "response", "stream", "uri", "url"], "time": "2021-03-21T16:25:00+00:00"}, {"name": "illuminate/log", "version": "********", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/qiyiyun_composer/illuminate/log/********/illuminate-log-********.zip", "reference": "e38253672063e9fd52ec9d2847cebb3794d29e1d", "shasum": "e38253672063e9fd52ec9d2847cebb3794d29e1d"}, "require": {"illuminate/contracts": "5.5.*", "illuminate/support": "5.5.*", "monolog/monolog": "~1.11", "php": ">=7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.5-dev"}}, "autoload": {"psr-4": {"Illuminate\\Log\\": ""}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Log package.", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "time": "2020-07-29T06:42:26+00:00"}, {"name": "intervention/image", "version": "2.5.1", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/intervention/image/2.5.1/intervention-image-2.5.1.zip", "reference": "abbf18d5ab8367f96b3205ca3c89fb2fa598c69e", "shasum": ""}, "require": {"ext-fileinfo": "*", "guzzlehttp/psr7": "~1.1", "php": ">=5.4.0"}, "require-dev": {"mockery/mockery": "~0.9.2", "phpunit/phpunit": "^4.8 || ^5.7"}, "suggest": {"ext-gd": "to use GD library based image processing.", "ext-imagick": "to use Imagick based image processing.", "intervention/imagecache": "Caching extension for the Intervention Image library"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.4-dev"}, "laravel": {"providers": ["Intervention\\Image\\ImageServiceProvider"], "aliases": {"Image": "Intervention\\Image\\Facades\\Image"}}}, "autoload": {"psr-4": {"Intervention\\Image\\": "src/Intervention/Image"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://olivervogel.com/"}], "description": "Image handling and manipulation library with support for Laravel integration", "homepage": "http://image.intervention.io/", "keywords": ["gd", "image", "imagick", "laravel", "thumbnail", "watermark"], "time": "2019-11-02T09:15:47+00:00"}, {"name": "jakub-onderka/php-console-color", "version": "v0.2", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/jakub-onderka/php-console-color/v0.2/jakub-onderka-php-console-color-v0.2.zip", "reference": "d5deaecff52a0d61ccb613bb3804088da0307191", "shasum": ""}, "require": {"php": ">=5.4.0"}, "require-dev": {"jakub-onderka/php-code-style": "1.0", "jakub-onderka/php-parallel-lint": "1.0", "jakub-onderka/php-var-dump-check": "0.*", "phpunit/phpunit": "~4.3", "squizlabs/php_codesniffer": "1.*"}, "type": "library", "autoload": {"psr-4": {"JakubOnderka\\PhpConsoleColor\\": "src/"}}, "license": ["BSD-2-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "abandoned": "php-parallel-lint/php-console-color", "time": "2018-09-29T17:23:10+00:00"}, {"name": "jakub-onderka/php-console-highlighter", "version": "v0.4", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/jakub-onderka/php-console-highlighter/v0.4/jakub-onderka-php-console-highlighter-v0.4.zip", "reference": "9f7a229a69d52506914b4bc61bfdb199d90c5547", "shasum": ""}, "require": {"ext-tokenizer": "*", "jakub-onderka/php-console-color": "~0.2", "php": ">=5.4.0"}, "require-dev": {"jakub-onderka/php-code-style": "~1.0", "jakub-onderka/php-parallel-lint": "~1.0", "jakub-onderka/php-var-dump-check": "~0.1", "phpunit/phpunit": "~4.0", "squizlabs/php_codesniffer": "~1.5"}, "type": "library", "autoload": {"psr-4": {"JakubOnderka\\PhpConsoleHighlighter\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.acci.cz/"}], "description": "Highlight PHP code in terminal", "abandoned": "php-parallel-lint/php-console-highlighter", "time": "2018-09-29T18:48:56+00:00"}, {"name": "jaybizzle/crawler-detect", "version": "v1.2.105", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/jaybizzle/crawler-detect/v1.2.105/jaybizzle-crawler-detect-v1.2.105.zip", "reference": "719c1ed49224857800c3dc40838b6b761d046105", "shasum": ""}, "require": {"php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "^4.8|^5.5|^6.5|^9.4"}, "type": "library", "autoload": {"psr-4": {"Jaybizzle\\CrawlerDetect\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "CrawlerDetect is a PHP class for detecting bots/crawlers/spiders via the user agent", "homepage": "https://github.com/JayBizzle/Crawler-Detect/", "keywords": ["crawler", "crawler detect", "crawler detector", "crawlerdetect", "php crawler detect"], "time": "2021-03-03T20:55:48+00:00"}, {"name": "jenssegers/agent", "version": "v2.6.4", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/jenssegers/agent/v2.6.4/jenssegers-agent-v2.6.4.zip", "reference": "daa11c43729510b3700bc34d414664966b03bffe", "shasum": ""}, "require": {"jaybizzle/crawler-detect": "^1.2", "mobiledetect/mobiledetectlib": "^2.7.6", "php": ">=5.6"}, "require-dev": {"php-coveralls/php-coveralls": "^2.1", "phpunit/phpunit": "^5.0|^6.0|^7.0"}, "suggest": {"illuminate/support": "Required for laravel service providers"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}, "laravel": {"providers": ["Jenssegers\\Agent\\AgentServiceProvider"], "aliases": {"Agent": "Jenssegers\\Agent\\Facades\\Agent"}}}, "autoload": {"psr-4": {"Jenssegers\\Agent\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "homepage": "https://jenssegers.com"}], "description": "Desktop/mobile user agent parser with support for <PERSON><PERSON>, based on Mobiledetect", "homepage": "https://github.com/jenssegers/agent", "keywords": ["Agent", "browser", "desktop", "laravel", "mobile", "platform", "user agent", "useragent"], "time": "2020-06-13T08:05:20+00:00"}, {"name": "kylekatarnls/update-helper", "version": "1.2.1", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/kylekatarnls/update-helper/1.2.1/kylekatarnls-update-helper-1.2.1.zip", "reference": "429be50660ed8a196e0798e5939760f168ec8ce9", "shasum": ""}, "require": {"composer-plugin-api": "^1.1.0 || ^2.0.0", "php": ">=5.3.0"}, "require-dev": {"codeclimate/php-test-reporter": "dev-master", "composer/composer": "2.0.x-dev || ^2.0.0-dev", "phpunit/phpunit": ">=4.8.35 <6.0"}, "type": "composer-plugin", "extra": {"class": "UpdateHelper\\ComposerPlugin"}, "autoload": {"psr-0": {"UpdateHelper\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Update helper", "time": "2020-04-07T20:44:10+00:00"}, {"name": "laravel/framework", "version": "v5.5.21", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/laravel/framework/v5.5.21/laravel-framework-v5.5.21.zip", "reference": "6321069a75723d88103526903d3192f0b231544a", "shasum": ""}, "require": {"doctrine/inflector": "~1.1", "erusev/parsedown": "~1.6", "ext-mbstring": "*", "ext-openssl": "*", "league/flysystem": "~1.0", "monolog/monolog": "~1.12", "mtdowling/cron-expression": "~1.0", "nesbot/carbon": "~1.20", "php": ">=7.0", "psr/container": "~1.0", "psr/simple-cache": "^1.0", "ramsey/uuid": "~3.0", "swiftmailer/swiftmailer": "~6.0", "symfony/console": "~3.3", "symfony/debug": "~3.3", "symfony/finder": "~3.3", "symfony/http-foundation": "~3.3", "symfony/http-kernel": "~3.3", "symfony/process": "~3.3", "symfony/routing": "~3.3", "symfony/var-dumper": "~3.3", "tijsverkoyen/css-to-inline-styles": "~2.2", "vlucas/phpdotenv": "~2.2"}, "replace": {"illuminate/auth": "self.version", "illuminate/broadcasting": "self.version", "illuminate/bus": "self.version", "illuminate/cache": "self.version", "illuminate/config": "self.version", "illuminate/console": "self.version", "illuminate/container": "self.version", "illuminate/contracts": "self.version", "illuminate/cookie": "self.version", "illuminate/database": "self.version", "illuminate/encryption": "self.version", "illuminate/events": "self.version", "illuminate/filesystem": "self.version", "illuminate/hashing": "self.version", "illuminate/http": "self.version", "illuminate/log": "self.version", "illuminate/mail": "self.version", "illuminate/notifications": "self.version", "illuminate/pagination": "self.version", "illuminate/pipeline": "self.version", "illuminate/queue": "self.version", "illuminate/redis": "self.version", "illuminate/routing": "self.version", "illuminate/session": "self.version", "illuminate/support": "self.version", "illuminate/translation": "self.version", "illuminate/validation": "self.version", "illuminate/view": "self.version", "tightenco/collect": "self.version"}, "require-dev": {"aws/aws-sdk-php": "~3.0", "doctrine/dbal": "~2.5", "filp/whoops": "^2.1.4", "mockery/mockery": "~1.0", "orchestra/testbench-core": "3.5.*", "pda/pheanstalk": "~3.0", "phpunit/phpunit": "~6.0", "predis/predis": "^1.1.1", "symfony/css-selector": "~3.3", "symfony/dom-crawler": "~3.3"}, "suggest": {"aws/aws-sdk-php": "Required to use the SQS queue driver and SES mail driver (~3.0).", "doctrine/dbal": "Required to rename columns and drop SQLite columns (~2.5).", "ext-pcntl": "Required to use all features of the queue worker.", "ext-posix": "Required to use all features of the queue worker.", "fzaninotto/faker": "Required to use the eloquent factory builder (~1.4).", "guzzlehttp/guzzle": "Required to use the Mailgun and Mandrill mail drivers and the ping methods on schedules (~6.0).", "laravel/tinker": "Required to use the tinker console command (~1.0).", "league/flysystem-aws-s3-v3": "Required to use the Flysystem S3 driver (~1.0).", "league/flysystem-rackspace": "Required to use the Flysystem Rackspace driver (~1.0).", "nexmo/client": "Required to use the Nexmo transport (~1.0).", "pda/pheanstalk": "Required to use the beanstalk queue driver (~3.0).", "predis/predis": "Required to use the redis cache and queue drivers (~1.0).", "pusher/pusher-php-server": "Required to use the <PERSON><PERSON><PERSON> broadcast driver (~3.0).", "symfony/css-selector": "Required to use some of the crawler integration testing tools (~3.3).", "symfony/dom-crawler": "Required to use most of the crawler integration testing tools (~3.3).", "symfony/psr-http-message-bridge": "Required to psr7 bridging features (~1.0)."}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.5-dev"}}, "autoload": {"files": ["src/Illuminate/Foundation/helpers.php", "src/Illuminate/Support/helpers.php"], "psr-4": {"Illuminate\\": "src/Illuminate/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Laravel Framework.", "homepage": "https://laravel.com", "keywords": ["framework", "laravel"], "time": "2017-11-14T15:08:13+00:00"}, {"name": "laravel/tinker", "version": "v1.0.10", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/laravel/tinker/v1.0.10/laravel-tinker-v1.0.10.zip", "reference": "ad571aacbac1539c30d480908f9d0c9614eaf1a7", "shasum": ""}, "require": {"illuminate/console": "~5.1|^6.0", "illuminate/contracts": "~5.1|^6.0", "illuminate/support": "~5.1|^6.0", "php": ">=5.5.9", "psy/psysh": "0.7.*|0.8.*|0.9.*", "symfony/var-dumper": "~3.0|~4.0"}, "require-dev": {"phpunit/phpunit": "~4.0|~5.0"}, "suggest": {"illuminate/database": "The Illuminate Database package (~5.1)."}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}, "laravel": {"providers": ["Laravel\\Tinker\\TinkerServiceProvider"]}}, "autoload": {"psr-4": {"Laravel\\Tinker\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Powerful REPL for the Laravel framework.", "keywords": ["REPL", "Tinker", "laravel", "psysh"], "time": "2019-08-07T15:10:45+00:00"}, {"name": "league/flysystem", "version": "1.0.70", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/league/flysystem/1.0.70/league-flysystem-1.0.70.zip", "reference": "585824702f534f8d3cf7fab7225e8466cc4b7493", "shasum": ""}, "require": {"ext-fileinfo": "*", "php": ">=5.5.9"}, "conflict": {"league/flysystem-sftp": "<1.0.6"}, "require-dev": {"phpspec/phpspec": "^3.4 || ^4.0 || ^5.0 || ^6.0", "phpunit/phpunit": "^5.7.26"}, "suggest": {"ext-fileinfo": "Required for MimeType", "ext-ftp": "Allows you to use FTP server storage", "ext-openssl": "Allows you to use FTPS server storage", "league/flysystem-aws-s3-v2": "Allows you to use S3 storage with AWS SDK v2", "league/flysystem-aws-s3-v3": "Allows you to use S3 storage with AWS SDK v3", "league/flysystem-azure": "Allows you to use Windows Azure Blob storage", "league/flysystem-cached-adapter": "Flysystem adapter decorator for metadata caching", "league/flysystem-eventable-filesystem": "Allows you to use EventableFilesystem", "league/flysystem-rackspace": "Allows you to use Rackspace Cloud Files", "league/flysystem-sftp": "Allows you to use SFTP server storage via phpseclib", "league/flysystem-webdav": "Allows you to use WebDAV storage", "league/flysystem-ziparchive": "Allows you to use ZipArchive adapter", "spatie/flysystem-dropbox": "Allows you to use Dropbox storage", "srmklive/flysystem-dropbox-v2": "Allows you to use Dropbox storage for PHP 5 applications"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "autoload": {"psr-4": {"League\\Flysystem\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Filesystem abstraction: Many filesystems, one API.", "keywords": ["Cloud Files", "WebDAV", "abstraction", "aws", "cloud", "copy.com", "dropbox", "file systems", "files", "filesystem", "filesystems", "ftp", "rackspace", "remote", "s3", "sftp", "storage"], "time": "2020-07-26T07:20:36+00:00"}, {"name": "league/oauth2-client", "version": "2.6.0", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/league/oauth2-client/2.6.0/league-oauth2-client-2.6.0.zip", "reference": "badb01e62383430706433191b82506b6df24ad98", "shasum": ""}, "require": {"guzzlehttp/guzzle": "^6.0 || ^7.0", "paragonie/random_compat": "^1 || ^2 || ^9.99", "php": "^5.6 || ^7.0 || ^8.0"}, "require-dev": {"mockery/mockery": "^1.3", "php-parallel-lint/php-parallel-lint": "^1.2", "phpunit/phpunit": "^5.7 || ^6.0 || ^9.3", "squizlabs/php_codesniffer": "^2.3 || ^3.0"}, "type": "library", "extra": {"branch-alias": {"dev-2.x": "2.0.x-dev"}}, "autoload": {"psr-4": {"League\\OAuth2\\Client\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.alexbilbie.com", "role": "Developer"}, {"name": "<PERSON>", "homepage": "https://github.com/shadowhand", "role": "Contributor"}], "description": "OAuth 2.0 Client Library", "keywords": ["Authentication", "SSO", "authorization", "identity", "idp", "o<PERSON>h", "oauth2", "single sign on"], "time": "2020-10-28T02:03:40+00:00"}, {"name": "maxmind-db/reader", "version": "v1.6.0", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/maxmind-db/reader/v1.6.0/maxmind-db-reader-v1.6.0.zip", "reference": "febd4920bf17c1da84cef58e56a8227dfb37fbe4", "shasum": ""}, "require": {"php": ">=5.6"}, "conflict": {"ext-maxminddb": "<1.6.0,>=2.0.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "2.*", "php-coveralls/php-coveralls": "^2.1", "phpunit/phpcov": "^3.0", "phpunit/phpunit": "5.*", "squizlabs/php_codesniffer": "3.*"}, "suggest": {"ext-bcmath": "bcmath or gmp is required for decoding larger integers with the pure PHP decoder", "ext-gmp": "bcmath or gmp is required for decoding larger integers with the pure PHP decoder", "ext-maxminddb": "A C-based database decoder that provides significantly faster lookups"}, "type": "library", "autoload": {"psr-4": {"MaxMind\\Db\\": "src/MaxMind/Db"}}, "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.maxmind.com/"}], "description": "MaxMind DB Reader API", "homepage": "https://github.com/maxmind/MaxMind-DB-Reader-php", "keywords": ["database", "geoip", "geoip2", "geolocation", "maxmind"], "time": "2019-12-19T22:59:03+00:00"}, {"name": "maxmind/web-service-common", "version": "v0.7.0", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/maxmind/web-service-common/v0.7.0/maxmind-web-service-common-v0.7.0.zip", "reference": "74c996c218ada5c639c8c2f076756e059f5552fc", "shasum": ""}, "require": {"composer/ca-bundle": "^1.0.3", "ext-curl": "*", "ext-json": "*", "php": ">=5.6"}, "require-dev": {"friendsofphp/php-cs-fixer": "2.*", "phpunit/phpunit": "^4.8.36 || ^5.7 || ^6.5 || ^7.0", "squizlabs/php_codesniffer": "3.*"}, "type": "library", "autoload": {"psr-4": {"MaxMind\\Exception\\": "src/Exception", "MaxMind\\WebService\\": "src/WebService"}}, "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Internal MaxMind Web Service API", "homepage": "https://github.com/maxmind/web-service-common-php", "time": "2020-05-06T14:07:26+00:00"}, {"name": "mews/captcha", "version": "*******", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/qiyiyun_composer/mews/captcha/*******/mews-captcha-*******.zip", "reference": "49fc47fa816eab7b571540e0f8ce862370b5e4c0", "shasum": "49fc47fa816eab7b571540e0f8ce862370b5e4c0"}, "require": {"ext-gd": "*", "illuminate/config": "~5.0", "illuminate/filesystem": "~5.0", "illuminate/hashing": "~5.0", "illuminate/support": "~5.0", "intervention/image": "~2.2", "php": ">=5.4"}, "require-dev": {"mockery/mockery": "0.9.*", "phpunit/phpunit": "~4.1"}, "type": "package", "extra": {"laravel": {"providers": ["Mews\\Captcha\\PicCaptchaServiceProvider"], "aliases": {"PicCaptcha": "Mews\\Captcha\\Facades\\PicCaptcha"}}}, "autoload": {"psr-4": {"Mews\\Captcha\\": "src/"}}, "autoload-dev": {"classmap": ["tests"], "psr-4": {"Mews\\Test\\": "tests/"}}, "license": ["MIT"], "authors": [{"name": "Muharrem ERİN", "email": "<EMAIL>", "homepage": "https://github.com/mewebstudio", "role": "Developer"}], "description": "Laravel 5 Captcha Package", "homepage": "https://github.com/mewebstudio/captcha", "keywords": ["<PERSON><PERSON>", "laravel5 Captcha", "laravel5 Security"], "time": "2020-07-29T06:43:31+00:00"}, {"name": "mobiledetect/mobiledetectlib", "version": "2.8.37", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/mobiledetect/mobiledetectlib/2.8.37/mobiledetect-mobiledetectlib-2.8.37.zip", "reference": "9841e3c46f5bd0739b53aed8ac677fa712943df7", "shasum": ""}, "require": {"php": ">=5.0.0"}, "require-dev": {"phpunit/phpunit": "~4.8.35||~5.7"}, "type": "library", "autoload": {"classmap": ["Mobile_Detect.php"], "psr-0": {"Detection": "namespaced/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "serban<PERSON><PERSON>@gmail.com", "homepage": "http://mobiledetect.net", "role": "Developer"}], "description": "Mobile_Detect is a lightweight PHP class for detecting mobile devices. It uses the User-Agent string combined with specific HTTP headers to detect the mobile environment.", "homepage": "https://github.com/serbanghita/Mobile-Detect", "keywords": ["detect mobile devices", "mobile", "mobile detect", "mobile detector", "php mobile detect"], "time": "2021-02-19T21:22:57+00:00"}, {"name": "monolog/monolog", "version": "1.26.0", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/monolog/monolog/1.26.0/monolog-monolog-1.26.0.zip", "reference": "2209ddd84e7ef1256b7af205d0717fb62cfc9c33", "shasum": ""}, "require": {"php": ">=5.3.0", "psr/log": "~1.0"}, "provide": {"psr/log-implementation": "1.0.0"}, "require-dev": {"aws/aws-sdk-php": "^2.4.9 || ^3.0", "doctrine/couchdb": "~1.0@dev", "graylog2/gelf-php": "~1.0", "php-amqplib/php-amqplib": "~2.4", "php-console/php-console": "^3.1.3", "phpstan/phpstan": "^0.12.59", "phpunit/phpunit": "~4.5", "ruflin/elastica": ">=0.90 <3.0", "sentry/sentry": "^0.13", "swiftmailer/swiftmailer": "^5.3|^6.0"}, "suggest": {"aws/aws-sdk-php": "Allow sending log messages to AWS services like DynamoDB", "doctrine/couchdb": "Allow sending log messages to a CouchDB server", "ext-amqp": "Allow sending log messages to an AMQP server (1.0+ required)", "ext-mongo": "Allow sending log messages to a MongoDB server", "graylog2/gelf-php": "Allow sending log messages to a GrayLog2 server", "mongodb/mongodb": "Allow sending log messages to a MongoDB server via PHP Driver", "php-amqplib/php-amqplib": "Allow sending log messages to an AMQP server using php-amqplib", "php-console/php-console": "Allow sending log messages to Google Chrome", "rollbar/rollbar": "Allow sending log messages to Rollbar", "ruflin/elastica": "Allow sending log messages to an Elastic Search server", "sentry/sentry": "Allow sending log messages to a Sentry server"}, "type": "library", "autoload": {"psr-4": {"Monolog\\": "src/Monolog"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "Sends your logs to files, sockets, inboxes, databases and various web services", "homepage": "http://github.com/Seldaek/monolog", "keywords": ["log", "logging", "psr-3"], "time": "2020-12-14T12:56:38+00:00"}, {"name": "mtdowling/cron-expression", "version": "v1.2.3", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/mtdowling/cron-expression/v1.2.3/mtdowling-cron-expression-v1.2.3.zip", "reference": "9be552eebcc1ceec9776378f7dcc085246cacca6", "shasum": ""}, "require": {"php": ">=5.3.2"}, "require-dev": {"phpunit/phpunit": "~4.0|~5.0"}, "type": "library", "autoload": {"psr-4": {"Cron\\": "src/Cron/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "CRON for PHP: Calculate the next or previous run date and determine if a CRON expression is due", "keywords": ["cron", "schedule"], "abandoned": "dragonmantank/cron-expression", "time": "2019-12-28T04:23:06+00:00"}, {"name": "nesbot/carbon", "version": "1.39.1", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/nesbot/carbon/1.39.1/nesbot-carbon-1.39.1.zip", "reference": "4be0c005164249208ce1b5ca633cd57bdd42ff33", "shasum": ""}, "require": {"kylekatarnls/update-helper": "^1.1", "php": ">=5.3.9", "symfony/translation": "~2.6 || ~3.0 || ~4.0"}, "require-dev": {"composer/composer": "^1.2", "friendsofphp/php-cs-fixer": "~2", "phpunit/phpunit": "^4.8.35 || ^5.7"}, "bin": ["bin/upgrade-carbon"], "type": "library", "extra": {"update-helper": "Carbon\\Upgrade", "laravel": {"providers": ["Carbon\\Laravel\\ServiceProvider"]}}, "autoload": {"psr-4": {"": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://nesbot.com"}], "description": "A simple API extension for DateTime.", "homepage": "http://carbon.nesbot.com", "keywords": ["date", "datetime", "time"], "time": "2019-10-14T05:51:36+00:00"}, {"name": "nikic/php-parser", "version": "v4.10.4", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/nikic/php-parser/v4.10.4/nikic-php-parser-v4.10.4.zip", "reference": "c6d052fc58cb876152f89f532b95a8d7907e7f0e", "shasum": ""}, "require": {"ext-tokenizer": "*", "php": ">=7.0"}, "require-dev": {"ircmaxell/php-yacc": "^0.0.7", "phpunit/phpunit": "^6.5 || ^7.0 || ^8.0 || ^9.0"}, "bin": ["bin/php-parse"], "type": "library", "extra": {"branch-alias": {"dev-master": "4.9-dev"}}, "autoload": {"psr-4": {"PhpParser\\": "lib/Php<PERSON><PERSON>er"}}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>"}], "description": "A PHP parser written in PHP", "keywords": ["parser", "php"], "time": "2020-12-20T10:01:03+00:00"}, {"name": "paragonie/constant_time_encoding", "version": "v2.4.0", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/paragonie/constant_time_encoding/v2.4.0/paragonie-constant_time_encoding-v2.4.0.zip", "reference": "f34c2b11eb9d2c9318e13540a1dbc2a3afbd939c", "shasum": ""}, "require": {"php": "^7|^8"}, "require-dev": {"phpunit/phpunit": "^6|^7|^8|^9", "vimeo/psalm": "^1|^2|^3|^4"}, "type": "library", "autoload": {"psr-4": {"ParagonIE\\ConstantTime\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "Paragon Initiative Enterprises", "email": "<EMAIL>", "homepage": "https://paragonie.com", "role": "Maintainer"}, {"name": "<PERSON> 'Sc00bz' <PERSON>", "email": "<EMAIL>", "homepage": "https://www.tobtu.com", "role": "Original Developer"}], "description": "Constant-time Implementations of RFC 4648 Encoding (Base-64, Base-32, Base-16)", "keywords": ["base16", "base32", "base32_decode", "base32_encode", "base64", "base64_decode", "base64_encode", "bin2hex", "encoding", "hex", "hex2bin", "rfc4648"], "time": "2020-12-06T15:14:20+00:00"}, {"name": "paragonie/random_compat", "version": "v9.99.99", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/paragonie/random_compat/v9.99.99/paragonie-random_compat-v9.99.99.zip", "reference": "84b4dfb120c6f9b4ff7b3685f9b8f1aa365a0c95", "shasum": ""}, "require": {"php": "^7"}, "require-dev": {"phpunit/phpunit": "4.*|5.*", "vimeo/psalm": "^1"}, "suggest": {"ext-libsodium": "Provides a modern crypto API that can be used to generate random bytes."}, "type": "library", "license": ["MIT"], "authors": [{"name": "Paragon Initiative Enterprises", "email": "<EMAIL>", "homepage": "https://paragonie.com"}], "description": "PHP 5.x polyfill for random_bytes() and random_int() from PHP 7", "keywords": ["csprng", "polyfill", "pseudorandom", "random"], "time": "2018-07-02T15:55:56+00:00"}, {"name": "php-amqplib/php-amqplib", "version": "v2.6.3", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/php-amqplib/php-amqplib/v2.6.3/php-amqplib-php-amqplib-v2.6.3.zip", "reference": "fa2f0d4410a11008cb36b379177291be7ee9e4f6", "shasum": ""}, "require": {"ext-bcmath": "*", "ext-mbstring": "*", "php": ">=5.3.0"}, "replace": {"videlalvaro/php-amqplib": "self.version"}, "require-dev": {"phpunit/phpunit": "^4.8", "scrutinizer/ocular": "^1.1", "squizlabs/php_codesniffer": "^2.5"}, "suggest": {"ext-sockets": "Use AMQPSocketConnection"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.7-dev"}}, "autoload": {"psr-4": {"PhpAmqpLib\\": "PhpAmqpLib/"}}, "license": ["LGPL-2.1"], "authors": [{"name": "<PERSON><PERSON>", "role": "Original Maintainer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Maintainer"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Maintainer"}], "description": "Formerly videlalvaro/php-amqplib.  This library is a pure PHP implementation of the AMQP protocol. It's been tested against RabbitMQ.", "homepage": "https://github.com/php-amqplib/php-amqplib/", "keywords": ["message", "queue", "rabbitmq"], "time": "2016-04-11T14:30:01+00:00"}, {"name": "predis/predis", "version": "v1.1.7", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/predis/predis/v1.1.7/predis-predis-v1.1.7.zip", "reference": "b240daa106d4e02f0c5b7079b41e31ddf66fddf8", "shasum": ""}, "require": {"php": ">=5.3.9"}, "require-dev": {"phpunit/phpunit": "~4.8"}, "suggest": {"ext-curl": "Allows access to Webdis when paired with phpiredis", "ext-phpiredis": "Allows faster serialization and deserialization of the Redis protocol"}, "type": "library", "autoload": {"psr-4": {"Predis\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://clorophilla.net", "role": "Creator & Maintainer"}, {"name": "<PERSON>", "homepage": "https://till.im", "role": "Maintainer"}], "description": "Flexible and feature-complete Redis client for PHP and HHVM", "homepage": "http://github.com/predis/predis", "keywords": ["nosql", "predis", "redis"], "time": "2021-04-04T19:34:46+00:00"}, {"name": "psr/container", "version": "1.0.0", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/psr/container/1.0.0/psr-container-1.0.0.zip", "reference": "b7ce3b176482dbbc1245ebf52b181af44c2cf55f", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Container\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common Container Interface (PHP FIG PSR-11)", "homepage": "https://github.com/php-fig/container", "keywords": ["PSR-11", "container", "container-interface", "container-interop", "psr"], "time": "2017-02-14T16:28:37+00:00"}, {"name": "psr/http-message", "version": "1.0.1", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/psr/http-message/1.0.1/psr-http-message-1.0.1.zip", "reference": "f6561bf28d520154e4b0ec72be95418abe6d9363", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "time": "2016-08-06T14:39:51+00:00"}, {"name": "psr/log", "version": "1.1.3", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/psr/log/1.1.3/psr-log-1.1.3.zip", "reference": "0f73288fd15629204f9d42b7055f72dacbe811fc", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-4": {"Psr\\Log\\": "Psr/Log/"}}, "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "time": "2020-03-23T09:12:05+00:00"}, {"name": "psr/simple-cache", "version": "1.0.1", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/psr/simple-cache/1.0.1/psr-simple-cache-1.0.1.zip", "reference": "408d5eafb83c57f6365a3ca330ff23aa4a5fa39b", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\SimpleCache\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interfaces for simple caching", "keywords": ["cache", "caching", "psr", "psr-16", "simple-cache"], "time": "2017-10-23T01:57:42+00:00"}, {"name": "psy/psysh", "version": "v0.9.12", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/psy/psysh/v0.9.12/psy-psysh-v0.9.12.zip", "reference": "90da7f37568aee36b116a030c5f99c915267edd4", "shasum": ""}, "require": {"dnoegel/php-xdg-base-dir": "0.1.*", "ext-json": "*", "ext-tokenizer": "*", "jakub-onderka/php-console-highlighter": "0.3.*|0.4.*", "nikic/php-parser": "~1.3|~2.0|~3.0|~4.0", "php": ">=5.4.0", "symfony/console": "~2.3.10|^2.4.2|~3.0|~4.0|~5.0", "symfony/var-dumper": "~2.7|~3.0|~4.0|~5.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.2", "hoa/console": "~2.15|~3.16", "phpunit/phpunit": "~4.8.35|~5.0|~6.0|~7.0"}, "suggest": {"ext-pcntl": "Enabling the PCNTL extension makes PsySH a lot happier :)", "ext-pdo-sqlite": "The doc command requires SQLite to work.", "ext-posix": "If you have PCNTL, you'll want the POSIX extension as well.", "ext-readline": "Enables support for arrow-key history navigation, and showing and manipulating command history.", "hoa/console": "A pure PHP readline implementation. You'll want this if your PHP install doesn't already support readline or libedit."}, "bin": ["bin/psysh"], "type": "library", "extra": {"branch-alias": {"dev-develop": "0.9.x-dev"}}, "autoload": {"files": ["src/functions.php"], "psr-4": {"Psy\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://justinhileman.com"}], "description": "An interactive shell for modern PHP.", "homepage": "http://psysh.org", "keywords": ["REPL", "console", "interactive", "shell"], "time": "2019-12-06T14:19:43+00:00"}, {"name": "ralouphie/getallheaders", "version": "3.0.3", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/ralouphie/getallheaders/3.0.3/ralouphie-getallheaders-3.0.3.zip", "reference": "120b605dfeb996808c31b6477290a714d356e822", "shasum": ""}, "require": {"php": ">=5.6"}, "require-dev": {"php-coveralls/php-coveralls": "^2.1", "phpunit/phpunit": "^5 || ^6.5"}, "type": "library", "autoload": {"files": ["src/getallheaders.php"]}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A polyfill for getallheaders.", "time": "2019-03-08T08:55:37+00:00"}, {"name": "ramsey/uuid", "version": "3.9.3", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/ramsey/uuid/3.9.3/ramsey-uuid-3.9.3.zip", "reference": "7e1633a6964b48589b142d60542f9ed31bd37a92", "shasum": ""}, "require": {"ext-json": "*", "paragonie/random_compat": "^1 | ^2 | 9.99.99", "php": "^5.4 | ^7 | ^8", "symfony/polyfill-ctype": "^1.8"}, "replace": {"rhumsaa/uuid": "self.version"}, "require-dev": {"codeception/aspect-mock": "^1 | ^2", "doctrine/annotations": "^1.2", "goaop/framework": "1.0.0-alpha.2 | ^1 | ^2.1", "jakub-onderka/php-parallel-lint": "^1", "mockery/mockery": "^0.9.11 | ^1", "moontoast/math": "^1.1", "paragonie/random-lib": "^2", "php-mock/php-mock-phpunit": "^0.3 | ^1.1", "phpunit/phpunit": "^4.8 | ^5.4 | ^6.5", "squizlabs/php_codesniffer": "^3.5"}, "suggest": {"ext-ctype": "Provides support for PHP Ctype functions", "ext-libsodium": "Provides the PECL libsodium extension for use with the SodiumRandomGenerator", "ext-openssl": "Provides the OpenSSL extension for use with the OpenSslGenerator", "ext-uuid": "Provides the PECL UUID extension for use with the PeclUuidTimeGenerator and PeclUuidRandomGenerator", "moontoast/math": "Provides support for converting UUID to 128-bit integer (in string form).", "paragonie/random-lib": "Provides RandomLib for use with the RandomLibAdapter", "ramsey/uuid-console": "A console application for generating UUIDs with ramsey/uuid", "ramsey/uuid-doctrine": "Allows the use of Ramsey\\Uuid\\Uuid as Doctrine field type."}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "autoload": {"psr-4": {"Ramsey\\Uuid\\": "src/"}, "files": ["src/functions.php"]}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://benramsey.com"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Formerly rhumsaa/uuid. A PHP 5.4+ library for generating RFC 4122 version 1, 3, 4, and 5 universally unique identifiers (UUID).", "homepage": "https://github.com/ramsey/uuid", "keywords": ["guid", "identifier", "uuid"], "time": "2020-02-21T04:36:14+00:00"}, {"name": "simplesoftwareio/simple-qrcode", "version": "2.0.0", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/simplesoftwareio/simple-qrcode/2.0.0/simplesoftwareio-simple-qrcode-2.0.0.zip", "reference": "90b2282dd29be1e52565e9832dc23af41610ea07", "shasum": ""}, "require": {"bacon/bacon-qr-code": "1.0.*", "ext-gd": "*", "illuminate/support": ">=5.0.0", "php": ">=7.0"}, "require-dev": {"mockery/mockery": "0.9.*", "phpunit/phpunit": "~6"}, "type": "library", "extra": {"laravel": {"providers": ["SimpleSoftwareIO\\QrCode\\QrCodeServiceProvider"], "aliases": {"QrCode": "SimpleSoftwareIO\\QrCode\\Facades\\QrCode"}}}, "autoload": {"psr-0": {"SimpleSoftwareIO\\QrCode\\": "src"}}, "license": ["MIT"], "authors": [{"name": "Simple Software LLC", "email": "<EMAIL>"}], "description": "Simple QrCode is a QR code generator made for Laravel.", "homepage": "http://www.simplesoftware.io", "keywords": ["Simple", "generator", "laravel", "qrcode", "wrapper"], "time": "2017-11-26T15:27:12+00:00"}, {"name": "spomky-labs/otphp", "version": "v9.1.4", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/spomky-labs/otphp/v9.1.4/spomky-labs-otphp-v9.1.4.zip", "reference": "48d463cf909320399fe08eab2e1cd18d899d5068", "shasum": ""}, "require": {"beberlei/assert": "^2.4|^3.0", "paragonie/constant_time_encoding": "^2.0", "php": "^7.1"}, "require-dev": {"phpunit/phpunit": "^6.0", "satooshi/php-coveralls": "^1.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "9.0.x-dev"}}, "autoload": {"psr-4": {"OTPHP\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "Florent <PERSON>", "homepage": "https://github.com/Spomky"}, {"name": "All contributors", "homepage": "https://github.com/Spomky-Labs/otphp/contributors"}], "description": "A PHP library for generating one time passwords according to RFC 4226 (HOTP Algorithm) and the RFC 6238 (TOTP Algorithm) and compatible with Google Authenticator", "homepage": "https://github.com/Spomky-Labs/otphp", "keywords": ["FreeOTP", "RFC 4226", "RFC 6238", "google authenticator", "hotp", "otp", "totp"], "time": "2019-03-18T10:08:51+00:00"}, {"name": "swiftmailer/swiftmailer", "version": "v6.2.7", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/swiftmailer/swiftmailer/v6.2.7/swiftmailer-swiftmailer-v6.2.7.zip", "reference": "15f7faf8508e04471f666633addacf54c0ab5933", "shasum": ""}, "require": {"egulias/email-validator": "^2.0|^3.1", "php": ">=7.0.0", "symfony/polyfill-iconv": "^1.0", "symfony/polyfill-intl-idn": "^1.10", "symfony/polyfill-mbstring": "^1.0"}, "require-dev": {"mockery/mockery": "^1.0", "symfony/phpunit-bridge": "^4.4|^5.0"}, "suggest": {"ext-intl": "Needed to support internationalized email addresses"}, "type": "library", "extra": {"branch-alias": {"dev-master": "6.2-dev"}}, "autoload": {"files": ["lib/swift_required.php"]}, "license": ["MIT"], "authors": [{"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Swiftmailer, free feature-rich PHP mailer", "homepage": "https://swiftmailer.symfony.com", "keywords": ["email", "mail", "mailer"], "time": "2021-03-09T12:30:35+00:00"}, {"name": "symfony/console", "version": "v3.4.47", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/symfony/console/v3.4.47/symfony-console-v3.4.47.zip", "reference": "a10b1da6fc93080c180bba7219b5ff5b7518fe81", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "symfony/debug": "~2.8|~3.0|~4.0", "symfony/polyfill-mbstring": "~1.0"}, "conflict": {"symfony/dependency-injection": "<3.4", "symfony/process": "<3.3"}, "provide": {"psr/log-implementation": "1.0"}, "require-dev": {"psr/log": "~1.0", "symfony/config": "~3.3|~4.0", "symfony/dependency-injection": "~3.4|~4.0", "symfony/event-dispatcher": "~2.8|~3.0|~4.0", "symfony/lock": "~3.4|~4.0", "symfony/process": "~3.3|~4.0"}, "suggest": {"psr/log": "For using the console logger", "symfony/event-dispatcher": "", "symfony/lock": "", "symfony/process": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Console\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Console Component", "homepage": "https://symfony.com", "time": "2020-10-24T10:57:07+00:00"}, {"name": "symfony/css-selector", "version": "v4.4.20", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/symfony/css-selector/v4.4.20/symfony-css-selector-v4.4.20.zip", "reference": "f907d3e53ecb2a5fad8609eb2f30525287a734c8", "shasum": ""}, "require": {"php": ">=7.1.3"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\CssSelector\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Converts CSS selectors to XPath expressions", "homepage": "https://symfony.com", "time": "2021-01-27T09:09:26+00:00"}, {"name": "symfony/debug", "version": "v3.4.47", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/symfony/debug/v3.4.47/symfony-debug-v3.4.47.zip", "reference": "ab42889de57fdfcfcc0759ab102e2fd4ea72dcae", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "psr/log": "~1.0"}, "conflict": {"symfony/http-kernel": ">=2.3,<2.3.24|~2.4.0|>=2.5,<2.5.9|>=2.6,<2.6.2"}, "require-dev": {"symfony/http-kernel": "~2.8|~3.0|~4.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Debug\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Debug Component", "homepage": "https://symfony.com", "time": "2020-10-24T10:57:07+00:00"}, {"name": "symfony/event-dispatcher", "version": "v4.4.20", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/symfony/event-dispatcher/v4.4.20/symfony-event-dispatcher-v4.4.20.zip", "reference": "c352647244bd376bf7d31efbd5401f13f50dad0c", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/event-dispatcher-contracts": "^1.1"}, "conflict": {"symfony/dependency-injection": "<3.4"}, "provide": {"psr/event-dispatcher-implementation": "1.0", "symfony/event-dispatcher-implementation": "1.1"}, "require-dev": {"psr/log": "~1.0", "symfony/config": "^3.4|^4.0|^5.0", "symfony/dependency-injection": "^3.4|^4.0|^5.0", "symfony/error-handler": "~3.4|~4.4", "symfony/expression-language": "^3.4|^4.0|^5.0", "symfony/http-foundation": "^3.4|^4.0|^5.0", "symfony/service-contracts": "^1.1|^2", "symfony/stopwatch": "^3.4|^4.0|^5.0"}, "suggest": {"symfony/dependency-injection": "", "symfony/http-kernel": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\EventDispatcher\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools that allow your application components to communicate with each other by dispatching events and listening to them", "homepage": "https://symfony.com", "time": "2021-01-27T09:09:26+00:00"}, {"name": "symfony/event-dispatcher-contracts", "version": "v1.1.9", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/symfony/event-dispatcher-contracts/v1.1.9/symfony-event-dispatcher-contracts-v1.1.9.zip", "reference": "84e23fdcd2517bf37aecbd16967e83f0caee25a7", "shasum": ""}, "require": {"php": ">=7.1.3"}, "suggest": {"psr/event-dispatcher": "", "symfony/event-dispatcher-implementation": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\EventDispatcher\\": ""}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to dispatching event", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "time": "2020-07-06T13:19:58+00:00"}, {"name": "symfony/finder", "version": "v3.4.47", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/symfony/finder/v3.4.47/symfony-finder-v3.4.47.zip", "reference": "b6b6ad3db3edb1b4b1c1896b1975fb684994de6e", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Finder\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Finder Component", "homepage": "https://symfony.com", "time": "2020-11-16T17:02:08+00:00"}, {"name": "symfony/http-foundation", "version": "v3.4.47", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/symfony/http-foundation/v3.4.47/symfony-http-foundation-v3.4.47.zip", "reference": "b9885fcce6fe494201da4f70a9309770e9d13dc8", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "symfony/polyfill-mbstring": "~1.1", "symfony/polyfill-php70": "~1.6"}, "require-dev": {"symfony/expression-language": "~2.8|~3.0|~4.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\HttpFoundation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony HttpFoundation Component", "homepage": "https://symfony.com", "time": "2020-10-24T10:57:07+00:00"}, {"name": "symfony/http-kernel", "version": "v3.4.47", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/symfony/http-kernel/v3.4.47/symfony-http-kernel-v3.4.47.zip", "reference": "a98a4c30089e6a2d52a9fa236f718159b539f6f5", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "psr/log": "~1.0", "symfony/debug": "^3.3.3|~4.0", "symfony/event-dispatcher": "~2.8|~3.0|~4.0", "symfony/http-foundation": "~3.4.12|~4.0.12|^4.1.1", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-php56": "~1.8"}, "conflict": {"symfony/config": "<2.8", "symfony/dependency-injection": "<3.4.10|<4.0.10,>=4", "symfony/var-dumper": "<3.3", "twig/twig": "<1.34|<2.4,>=2"}, "provide": {"psr/log-implementation": "1.0"}, "require-dev": {"psr/cache": "~1.0", "symfony/browser-kit": "~2.8|~3.0|~4.0", "symfony/class-loader": "~2.8|~3.0", "symfony/config": "~2.8|~3.0|~4.0", "symfony/console": "~2.8|~3.0|~4.0", "symfony/css-selector": "~2.8|~3.0|~4.0", "symfony/dependency-injection": "^3.4.10|^4.0.10", "symfony/dom-crawler": "~2.8|~3.0|~4.0", "symfony/expression-language": "~2.8|~3.0|~4.0", "symfony/finder": "~2.8|~3.0|~4.0", "symfony/process": "~2.8|~3.0|~4.0", "symfony/routing": "~3.4|~4.0", "symfony/stopwatch": "~2.8|~3.0|~4.0", "symfony/templating": "~2.8|~3.0|~4.0", "symfony/translation": "~2.8|~3.0|~4.0", "symfony/var-dumper": "~3.3|~4.0"}, "suggest": {"symfony/browser-kit": "", "symfony/config": "", "symfony/console": "", "symfony/dependency-injection": "", "symfony/finder": "", "symfony/var-dumper": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\HttpKernel\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony HttpKernel Component", "homepage": "https://symfony.com", "time": "2020-11-27T08:42:42+00:00"}, {"name": "symfony/polyfill-ctype", "version": "v1.22.1", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/symfony/polyfill-ctype/v1.22.1/symfony-polyfill-ctype-v1.22.1.zip", "reference": "c6c942b1ac76c82448322025e084cadc56048b4e", "shasum": ""}, "require": {"php": ">=7.1"}, "suggest": {"ext-ctype": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.22-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Ctype\\": ""}, "files": ["bootstrap.php"]}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for ctype functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "ctype", "polyfill", "portable"], "time": "2021-01-07T16:49:33+00:00"}, {"name": "symfony/polyfill-iconv", "version": "v1.22.1", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/symfony/polyfill-iconv/v1.22.1/symfony-polyfill-iconv-v1.22.1.zip", "reference": "06fb361659649bcfd6a208a0f1fcaf4e827ad342", "shasum": ""}, "require": {"php": ">=7.1"}, "suggest": {"ext-iconv": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.22-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Iconv\\": ""}, "files": ["bootstrap.php"]}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Iconv extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "iconv", "polyfill", "portable", "shim"], "time": "2021-01-22T09:19:47+00:00"}, {"name": "symfony/polyfill-intl-idn", "version": "v1.22.1", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/symfony/polyfill-intl-idn/v1.22.1/symfony-polyfill-intl-idn-v1.22.1.zip", "reference": "2d63434d922daf7da8dd863e7907e67ee3031483", "shasum": ""}, "require": {"php": ">=7.1", "symfony/polyfill-intl-normalizer": "^1.10", "symfony/polyfill-php72": "^1.10"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.22-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Intl\\Idn\\": ""}, "files": ["bootstrap.php"]}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's idn_to_ascii and idn_to_utf8 functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "idn", "intl", "polyfill", "portable", "shim"], "time": "2021-01-22T09:19:47+00:00"}, {"name": "symfony/polyfill-intl-normalizer", "version": "v1.22.1", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/symfony/polyfill-intl-normalizer/v1.22.1/symfony-polyfill-intl-normalizer-v1.22.1.zip", "reference": "43a0283138253ed1d48d352ab6d0bdb3f809f248", "shasum": ""}, "require": {"php": ">=7.1"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.22-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Intl\\Normalizer\\": ""}, "files": ["bootstrap.php"], "classmap": ["Resources/stubs"]}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's Normalizer class and related functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "intl", "normalizer", "polyfill", "portable", "shim"], "time": "2021-01-22T09:19:47+00:00"}, {"name": "symfony/polyfill-mbstring", "version": "v1.22.1", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/symfony/polyfill-mbstring/v1.22.1/symfony-polyfill-mbstring-v1.22.1.zip", "reference": "5232de97ee3b75b0360528dae24e73db49566ab1", "shasum": ""}, "require": {"php": ">=7.1"}, "suggest": {"ext-mbstring": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.22-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}, "files": ["bootstrap.php"]}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "time": "2021-01-22T09:19:47+00:00"}, {"name": "symfony/polyfill-php56", "version": "v1.20.0", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/symfony/polyfill-php56/v1.20.0/symfony-polyfill-php56-v1.20.0.zip", "reference": "54b8cd7e6c1643d78d011f3be89f3ef1f9f4c675", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "metapackage", "extra": {"branch-alias": {"dev-main": "1.20-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 5.6+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "time": "2020-10-23T14:02:19+00:00"}, {"name": "symfony/polyfill-php70", "version": "v1.20.0", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/symfony/polyfill-php70/v1.20.0/symfony-polyfill-php70-v1.20.0.zip", "reference": "5f03a781d984aae42cebd18e7912fa80f02ee644", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "metapackage", "extra": {"branch-alias": {"dev-main": "1.20-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.0+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "time": "2020-10-23T14:02:19+00:00"}, {"name": "symfony/polyfill-php72", "version": "v1.22.1", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/symfony/polyfill-php72/v1.22.1/symfony-polyfill-php72-v1.22.1.zip", "reference": "cc6e6f9b39fe8075b3dabfbaf5b5f645ae1340c9", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.22-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Php72\\": ""}, "files": ["bootstrap.php"]}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.2+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "time": "2021-01-07T16:49:33+00:00"}, {"name": "symfony/process", "version": "v3.4.47", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/symfony/process/v3.4.47/symfony-process-v3.4.47.zip", "reference": "b8648cf1d5af12a44a51d07ef9bf980921f15fca", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Process\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Process Component", "homepage": "https://symfony.com", "time": "2020-10-24T10:57:07+00:00"}, {"name": "symfony/routing", "version": "v3.4.47", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/symfony/routing/v3.4.47/symfony-routing-v3.4.47.zip", "reference": "3e522ac69cadffd8131cc2b22157fa7662331a6c", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8"}, "conflict": {"symfony/config": "<3.3.1", "symfony/dependency-injection": "<3.3", "symfony/yaml": "<3.4"}, "require-dev": {"doctrine/annotations": "~1.0", "psr/log": "~1.0", "symfony/config": "^3.3.1|~4.0", "symfony/dependency-injection": "~3.3|~4.0", "symfony/expression-language": "~2.8|~3.0|~4.0", "symfony/http-foundation": "~2.8|~3.0|~4.0", "symfony/yaml": "~3.4|~4.0"}, "suggest": {"doctrine/annotations": "For using the annotation loader", "symfony/config": "For using the all-in-one router or any loader", "symfony/expression-language": "For using expression matching", "symfony/http-foundation": "For using a Symfony Request object", "symfony/yaml": "For using the YAML loader"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Routing\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Routing Component", "homepage": "https://symfony.com", "keywords": ["router", "routing", "uri", "url"], "time": "2020-10-24T10:57:07+00:00"}, {"name": "symfony/translation", "version": "v4.3.11", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/symfony/translation/v4.3.11/symfony-translation-v4.3.11.zip", "reference": "46e462be71935ae15eab531e4d491d801857f24c", "shasum": ""}, "require": {"php": "^7.1.3", "symfony/polyfill-mbstring": "~1.0", "symfony/translation-contracts": "^1.1.6"}, "conflict": {"symfony/config": "<3.4", "symfony/dependency-injection": "<3.4", "symfony/yaml": "<3.4"}, "provide": {"symfony/translation-implementation": "1.0"}, "require-dev": {"psr/log": "~1.0", "symfony/config": "~3.4|~4.0", "symfony/console": "~3.4|~4.0", "symfony/dependency-injection": "~3.4|~4.0", "symfony/finder": "~2.8|~3.0|~4.0", "symfony/http-kernel": "~3.4|~4.0", "symfony/intl": "~3.4|~4.0", "symfony/service-contracts": "^1.1.2", "symfony/var-dumper": "~3.4|~4.0", "symfony/yaml": "~3.4|~4.0"}, "suggest": {"psr/log-implementation": "To use logging capability in translator", "symfony/config": "", "symfony/yaml": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.3-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Translation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Translation Component", "homepage": "https://symfony.com", "time": "2020-01-04T12:24:57+00:00"}, {"name": "symfony/translation-contracts", "version": "v1.1.10", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/symfony/translation-contracts/v1.1.10/symfony-translation-contracts-v1.1.10.zip", "reference": "84180a25fad31e23bebd26ca09d89464f082cacc", "shasum": ""}, "require": {"php": ">=7.1.3"}, "suggest": {"symfony/translation-implementation": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Translation\\": ""}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to translation", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "time": "2020-09-02T16:08:58+00:00"}, {"name": "symfony/var-dumper", "version": "v3.4.47", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/symfony/var-dumper/v3.4.47/symfony-var-dumper-v3.4.47.zip", "reference": "0719f6cf4633a38b2c1585140998579ce23b4b7d", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "symfony/polyfill-mbstring": "~1.0"}, "conflict": {"phpunit/phpunit": "<4.8.35|<5.4.3,>=5.0"}, "require-dev": {"ext-iconv": "*", "twig/twig": "~1.34|~2.4"}, "suggest": {"ext-iconv": "To convert non-UTF-8 strings to UTF-8 (or symfony/polyfill-iconv in case ext-iconv cannot be used).", "ext-intl": "To show region name in time zone dump", "ext-symfony_debug": ""}, "type": "library", "autoload": {"files": ["Resources/functions/dump.php"], "psr-4": {"Symfony\\Component\\VarDumper\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony mechanism for exploring and dumping PHP variables", "homepage": "https://symfony.com", "keywords": ["debug", "dump"], "time": "2020-10-24T10:57:07+00:00"}, {"name": "tijsverkoyen/css-to-inline-styles", "version": "2.2.3", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/tijsverkoyen/css-to-inline-styles/2.2.3/tijsverkoyen-css-to-inline-styles-2.2.3.zip", "reference": "b43b05cf43c1b6d849478965062b6ef73e223bb5", "shasum": ""}, "require": {"ext-dom": "*", "ext-libxml": "*", "php": "^5.5 || ^7.0 || ^8.0", "symfony/css-selector": "^2.7 || ^3.0 || ^4.0 || ^5.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.0 || ^7.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.2.x-dev"}}, "autoload": {"psr-4": {"TijsVerkoyen\\CssToInlineStyles\\": "src"}}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "CssToInlineStyles is a class that enables you to convert HTML-pages/files into HTML-pages/files with inline styles. This is very useful when you're sending emails.", "homepage": "https://github.com/tijsverkoyen/CssToInlineStyles", "time": "2020-07-13T06:12:54+00:00"}, {"name": "v<PERSON><PERSON><PERSON>-y<PERSON><PERSON><PERSON>/laravel-queue-rabbitmq", "version": "5.5", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/vladimir-yuldashev/laravel-queue-rabbitmq/5.5/vladimir-yuldashev-laravel-queue-rabbitmq-5.5.zip", "reference": "3be5e3eed0a95e2d3c8149c220eb9ca47b9f0a47", "shasum": ""}, "require": {"illuminate/database": "5.5.*", "illuminate/queue": "5.5.*", "illuminate/support": "5.5.*", "php": ">=7.0", "php-amqplib/php-amqplib": "2.6.*"}, "require-dev": {"mockery/mockery": "^0.9.5", "phpunit/phpunit": "~6.0"}, "type": "library", "extra": {"laravel": {"providers": ["VladimirYuldashev\\LaravelQueueRabbitMQ\\LaravelQueueRabbitMQServiceProvider"]}}, "autoload": {"psr-4": {"VladimirYuldashev\\LaravelQueueRabbitMQ\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "RabbitMQ driver for <PERSON><PERSON>", "time": "2017-09-06T18:51:57+00:00"}, {"name": "vlucas/phpdotenv", "version": "v2.6.7", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/vlucas/phpdotenv/v2.6.7/vlucas-phpdotenv-v2.6.7.zip", "reference": "b786088918a884258c9e3e27405c6a4cf2ee246e", "shasum": ""}, "require": {"php": "^5.3.9 || ^7.0 || ^8.0", "symfony/polyfill-ctype": "^1.17"}, "require-dev": {"ext-filter": "*", "ext-pcre": "*", "phpunit/phpunit": "^4.8.36 || ^5.7.27 || ^6.5.14 || ^7.5.20"}, "suggest": {"ext-filter": "Required to use the boolean validator.", "ext-pcre": "Required to use most of the library."}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.6-dev"}}, "autoload": {"psr-4": {"Dotenv\\": "src/"}}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://gjcampbell.co.uk/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://vancelucas.com/"}], "description": "Loads environment variables from `.env` to `getenv()`, `$_ENV` and `$_SERVER` automagically.", "keywords": ["dotenv", "env", "environment"], "time": "2021-01-20T14:39:13+00:00"}], "packages-dev": [{"name": "barryvdh/laravel-ide-helper", "version": "v2.6.6", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/barryvdh/laravel-ide-helper/v2.6.6/barryvdh-laravel-ide-helper-v2.6.6.zip", "reference": "b91b959364d97af658f268c733c75dccdbff197e", "shasum": ""}, "require": {"barryvdh/reflection-docblock": "^2.0.6", "composer/composer": "^1.6", "doctrine/dbal": "~2.3", "illuminate/console": "^5.5|^6", "illuminate/filesystem": "^5.5|^6", "illuminate/support": "^5.5|^6", "php": ">=7"}, "require-dev": {"illuminate/config": "^5.5|^6", "illuminate/view": "^5.5|^6", "phpro/grumphp": "^0.14", "phpunit/phpunit": "4.*", "scrutinizer/ocular": "~1.1", "squizlabs/php_codesniffer": "^3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.6-dev"}, "laravel": {"providers": ["Barryvdh\\LaravelIdeHelper\\IdeHelperServiceProvider"]}}, "autoload": {"psr-4": {"Barryvdh\\LaravelIdeHelper\\": "src"}}, "license": ["MIT"], "authors": [{"name": "Barry vd. Heuvel", "email": "<EMAIL>"}], "description": "Laravel IDE Helper, generates correct PHPDocs for all Facade classes, to improve auto-completion.", "keywords": ["autocomplete", "codeintel", "helper", "ide", "laravel", "netbeans", "phpdoc", "phpstorm", "sublime"], "time": "2019-10-30T20:53:27+00:00"}, {"name": "barryvdh/reflection-docblock", "version": "v2.0.6", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/barryvdh/reflection-docblock/v2.0.6/barryvdh-reflection-docblock-v2.0.6.zip", "reference": "6b69015d83d3daf9004a71a89f26e27d27ef6a16", "shasum": ""}, "require": {"php": ">=5.3.3"}, "require-dev": {"phpunit/phpunit": "~4.0,<4.5"}, "suggest": {"dflydev/markdown": "~1.0", "erusev/parsedown": "~1.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-0": {"Barryvdh": ["src/"]}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "time": "2018-12-13T10:34:14+00:00"}, {"name": "composer/composer", "version": "1.10.21", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/composer/composer/1.10.21/composer-composer-1.10.21.zip", "reference": "04021432f4a9cbd9351dd166b8c193f42c36a39c", "shasum": ""}, "require": {"composer/ca-bundle": "^1.0", "composer/semver": "^1.0", "composer/spdx-licenses": "^1.2", "composer/xdebug-handler": "^1.1", "justinrainbow/json-schema": "^5.2.10", "php": "^5.3.2 || ^7.0 || ^8.0", "psr/log": "^1.0", "seld/jsonlint": "^1.4", "seld/phar-utils": "^1.0", "symfony/console": "^2.7 || ^3.0 || ^4.0 || ^5.0", "symfony/filesystem": "^2.7 || ^3.0 || ^4.0 || ^5.0", "symfony/finder": "^2.7 || ^3.0 || ^4.0 || ^5.0", "symfony/process": "^2.7 || ^3.0 || ^4.0 || ^5.0"}, "conflict": {"symfony/console": "2.8.38"}, "require-dev": {"phpspec/prophecy": "^1.10", "symfony/phpunit-bridge": "^4.2"}, "suggest": {"ext-openssl": "Enabling the openssl extension allows you to access https URLs for repositories and packages", "ext-zip": "Enabling the zip extension allows you to unzip archives", "ext-zlib": "Allow gzip compression of HTTP requests"}, "bin": ["bin/composer"], "type": "library", "extra": {"branch-alias": {"dev-master": "1.10-dev"}}, "autoload": {"psr-4": {"Composer\\": "src/Composer"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.naderman.de"}, {"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "Composer helps you declare, manage and install dependencies of PHP projects. It ensures you have the right stack everywhere.", "homepage": "https://getcomposer.org/", "keywords": ["autoload", "dependency", "package"], "time": "2021-04-01T07:16:35+00:00"}, {"name": "composer/semver", "version": "1.7.2", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/composer/semver/1.7.2/composer-semver-1.7.2.zip", "reference": "647490bbcaf7fc4891c58f47b825eb99d19c377a", "shasum": ""}, "require": {"php": "^5.3.2 || ^7.0 || ^8.0"}, "require-dev": {"phpunit/phpunit": "^4.5 || ^5.0.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Composer\\Semver\\": "src"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.naderman.de"}, {"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://robbast.nl"}], "description": "Semver library that offers utilities, version constraint parsing and validation.", "keywords": ["semantic", "semver", "validation", "versioning"], "time": "2020-12-03T15:47:16+00:00"}, {"name": "composer/spdx-licenses", "version": "1.5.5", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/composer/spdx-licenses/1.5.5/composer-spdx-licenses-1.5.5.zip", "reference": "de30328a7af8680efdc03e396aad24befd513200", "shasum": ""}, "require": {"php": "^5.3.2 || ^7.0 || ^8.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.7 || 6.5 - 7"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.x-dev"}}, "autoload": {"psr-4": {"Composer\\Spdx\\": "src"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.naderman.de"}, {"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://robbast.nl"}], "description": "SPDX licenses list and validation library.", "keywords": ["license", "spdx", "validator"], "time": "2020-12-03T16:04:16+00:00"}, {"name": "composer/xdebug-handler", "version": "1.4.6", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/composer/xdebug-handler/1.4.6/composer-xdebug-handler-1.4.6.zip", "reference": "f27e06cd9675801df441b3656569b328e04aa37c", "shasum": ""}, "require": {"php": "^5.3.2 || ^7.0 || ^8.0", "psr/log": "^1.0"}, "require-dev": {"phpstan/phpstan": "^0.12.55", "symfony/phpunit-bridge": "^4.2 || ^5"}, "type": "library", "autoload": {"psr-4": {"Composer\\XdebugHandler\\": "src"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "john-s<PERSON><PERSON><PERSON>@blueyonder.co.uk"}], "description": "Restarts a process without Xdebug.", "keywords": ["Xdebug", "performance"], "time": "2021-03-25T17:01:18+00:00"}, {"name": "doctrine/cache", "version": "1.11.0", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/doctrine/cache/1.11.0/doctrine-cache-1.11.0.zip", "reference": "a9c1b59eba5a08ca2770a76eddb88922f504e8e0", "shasum": ""}, "require": {"php": "~7.1 || ^8.0"}, "conflict": {"doctrine/common": ">2.2,<2.4", "psr/cache": ">=3"}, "require-dev": {"alcaeus/mongo-php-adapter": "^1.1", "cache/integration-tests": "dev-master", "doctrine/coding-standard": "^8.0", "mongodb/mongodb": "^1.1", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0", "predis/predis": "~1.0", "psr/cache": "^1.0 || ^2.0", "symfony/cache": "^4.4 || ^5.2"}, "suggest": {"alcaeus/mongo-php-adapter": "Required to use legacy MongoDB driver"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Cache\\": "lib/Doctrine/Common/Cache"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Cache library is a popular cache implementation that supports many different drivers such as redis, memcache, apc, mongodb and others.", "homepage": "https://www.doctrine-project.org/projects/cache.html", "keywords": ["abstraction", "apcu", "cache", "caching", "couchdb", "memcached", "php", "redis", "xcache"], "time": "2021-04-13T14:46:17+00:00"}, {"name": "doctrine/dbal", "version": "2.13.1", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/doctrine/dbal/2.13.1/doctrine-dbal-2.13.1.zip", "reference": "c800380457948e65bbd30ba92cc17cda108bf8c9", "shasum": ""}, "require": {"doctrine/cache": "^1.0", "doctrine/deprecations": "^0.5.3", "doctrine/event-manager": "^1.0", "ext-pdo": "*", "php": "^7.1 || ^8"}, "require-dev": {"doctrine/coding-standard": "8.2.0", "jetbrains/phpstorm-stubs": "2020.2", "phpstan/phpstan": "0.12.81", "phpunit/phpunit": "^7.5.20|^8.5|9.5.0", "squizlabs/php_codesniffer": "3.6.0", "symfony/console": "^2.0.5|^3.0|^4.0|^5.0", "vimeo/psalm": "4.6.4"}, "suggest": {"symfony/console": "For helpful console commands such as SQL execution and import of files."}, "bin": ["bin/doctrine-dbal"], "type": "library", "autoload": {"psr-4": {"Doctrine\\DBAL\\": "lib/Doctrine/DBAL"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Powerful PHP database abstraction layer (DBAL) with many features for database schema introspection and management.", "homepage": "https://www.doctrine-project.org/projects/dbal.html", "keywords": ["abstraction", "database", "db2", "dbal", "ma<PERSON>b", "mssql", "mysql", "oci8", "oracle", "pdo", "pgsql", "postgresql", "queryobject", "sasql", "sql", "sqlanywhere", "sqlite", "sqlserver", "sqlsrv"], "time": "2021-04-17T17:30:19+00:00"}, {"name": "doctrine/deprecations", "version": "v0.5.3", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/doctrine/deprecations/v0.5.3/doctrine-deprecations-v0.5.3.zip", "reference": "9504165960a1f83cc1480e2be1dd0a0478561314", "shasum": ""}, "require": {"php": "^7.1|^8.0"}, "require-dev": {"doctrine/coding-standard": "^6.0|^7.0|^8.0", "phpunit/phpunit": "^7.0|^8.0|^9.0", "psr/log": "^1.0"}, "suggest": {"psr/log": "Allows logging deprecations via PSR-3 logger implementation"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Deprecations\\": "lib/Doctrine/Deprecations"}}, "license": ["MIT"], "description": "A small layer on top of trigger_error(E_USER_DEPRECATED) or PSR-3 logging with options to disable all deprecations or selectively for packages.", "homepage": "https://www.doctrine-project.org/", "time": "2021-03-21T12:59:47+00:00"}, {"name": "doctrine/event-manager", "version": "1.1.1", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/doctrine/event-manager/1.1.1/doctrine-event-manager-1.1.1.zip", "reference": "41370af6a30faa9dc0368c4a6814d596e81aba7f", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "conflict": {"doctrine/common": "<2.9@dev"}, "require-dev": {"doctrine/coding-standard": "^6.0", "phpunit/phpunit": "^7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Common\\": "lib/Doctrine/Common"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Doctrine Event Manager is a simple PHP event system that was built to be used with the various Doctrine projects.", "homepage": "https://www.doctrine-project.org/projects/event-manager.html", "keywords": ["event", "event dispatcher", "event manager", "event system", "events"], "time": "2020-05-29T18:28:51+00:00"}, {"name": "doctrine/instantiator", "version": "1.4.0", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/doctrine/instantiator/1.4.0/doctrine-instantiator-1.4.0.zip", "reference": "d56bf6102915de5702778fe20f2de3b2fe570b5b", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^8.0", "ext-pdo": "*", "ext-phar": "*", "phpbench/phpbench": "^0.13 || 1.0.0-alpha2", "phpstan/phpstan": "^0.12", "phpstan/phpstan-phpunit": "^0.12", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Instantiator\\": "src/Doctrine/Instantiator/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://ocramius.github.io/"}], "description": "A small, lightweight utility to instantiate objects in PHP without invoking their constructors", "homepage": "https://www.doctrine-project.org/projects/instantiator.html", "keywords": ["constructor", "instantiate"], "time": "2020-11-10T18:47:58+00:00"}, {"name": "filp/whoops", "version": "2.12.0", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/filp/whoops/2.12.0/filp-whoops-2.12.0.zip", "reference": "d501fd2658d55491a2295ff600ae5978eaad7403", "shasum": ""}, "require": {"php": "^5.5.9 || ^7.0 || ^8.0", "psr/log": "^1.0.1"}, "require-dev": {"mockery/mockery": "^0.9 || ^1.0", "phpunit/phpunit": "^4.8.36 || ^5.7.27 || ^6.5.14 || ^7.5.20 || ^8.5.8 || ^9.3.3", "symfony/var-dumper": "^2.6 || ^3.0 || ^4.0 || ^5.0"}, "suggest": {"symfony/var-dumper": "Pretty print complex values better with var-dumper available", "whoops/soap": "Formats errors as SOAP responses"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.7-dev"}}, "autoload": {"psr-4": {"Whoops\\": "src/Whoops/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "https://github.com/filp", "role": "Developer"}], "description": "php error handling for cool kids", "homepage": "https://filp.github.io/whoops/", "keywords": ["error", "exception", "handling", "library", "throwable", "whoops"], "time": "2021-03-30T12:00:00+00:00"}, {"name": "fzaninotto/faker", "version": "v1.9.2", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/fzaninotto/faker/v1.9.2/fzaninotto-faker-v1.9.2.zip", "reference": "848d8125239d7dbf8ab25cb7f054f1a630e68c2e", "shasum": ""}, "require": {"php": "^5.3.3 || ^7.0"}, "require-dev": {"ext-intl": "*", "phpunit/phpunit": "^4.8.35 || ^5.7", "squizlabs/php_codesniffer": "^2.9.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.9-dev"}}, "autoload": {"psr-4": {"Faker\\": "src/Faker/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "Faker is a PHP library that generates fake data for you.", "keywords": ["data", "faker", "fixtures"], "abandoned": true, "time": "2020-12-11T09:56:16+00:00"}, {"name": "hamcrest/hamcrest-php", "version": "v2.0.1", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/hamcrest/hamcrest-php/v2.0.1/hamcrest-hamcrest-php-v2.0.1.zip", "reference": "8c3d0a3f6af734494ad8f6fbbee0ba92422859f3", "shasum": ""}, "require": {"php": "^5.3|^7.0|^8.0"}, "replace": {"cordoval/hamcrest-php": "*", "davedevelopment/hamcrest-php": "*", "kodova/hamcrest-php": "*"}, "require-dev": {"phpunit/php-file-iterator": "^1.4 || ^2.0", "phpunit/phpunit": "^4.8.36 || ^5.7 || ^6.5 || ^7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.1-dev"}}, "autoload": {"classmap": ["hamcrest"]}, "license": ["BSD-3-<PERSON><PERSON>"], "description": "This is the PHP port of Hamcrest Matchers", "keywords": ["test"], "time": "2020-07-09T08:09:16+00:00"}, {"name": "justin<PERSON><PERSON>/json-schema", "version": "5.2.10", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/justinrainbow/json-schema/5.2.10/justinrainbow-json-schema-5.2.10.zip", "reference": "2ba9c8c862ecd5510ed16c6340aa9f6eadb4f31b", "shasum": ""}, "require": {"php": ">=5.3.3"}, "require-dev": {"friendsofphp/php-cs-fixer": "~2.2.20||~2.15.1", "json-schema/json-schema-test-suite": "1.2.0", "phpunit/phpunit": "^4.8.35"}, "bin": ["bin/validate-json"], "type": "library", "extra": {"branch-alias": {"dev-master": "5.0.x-dev"}}, "autoload": {"psr-4": {"JsonSchema\\": "src/JsonSchema/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "i<PERSON>@wiedler.ch"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A library to validate a json schema.", "homepage": "https://github.com/justinrainbow/json-schema", "keywords": ["json", "schema"], "time": "2020-05-27T16:41:55+00:00"}, {"name": "mockery/mockery", "version": "1.3.4", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/mockery/mockery/1.3.4/mockery-mockery-1.3.4.zip", "reference": "31467aeb3ca3188158613322d66df81cedd86626", "shasum": ""}, "require": {"hamcrest/hamcrest-php": "^2.0.1", "lib-pcre": ">=7.0", "php": ">=5.6.0"}, "require-dev": {"phpunit/phpunit": "^5.7.10|^6.5|^7.5|^8.5|^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.3.x-dev"}}, "autoload": {"psr-0": {"Mockery": "library/"}}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://blog.astrumfutura.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://davedevelopment.co.uk"}], "description": "Mockery is a simple yet flexible PHP mock object framework", "homepage": "https://github.com/mockery/mockery", "keywords": ["BDD", "TDD", "library", "mock", "mock objects", "mockery", "stub", "test", "test double", "testing"], "time": "2021-02-24T09:51:00+00:00"}, {"name": "myclabs/deep-copy", "version": "1.10.2", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/myclabs/deep-copy/1.10.2/myclabs-deep-copy-1.10.2.zip", "reference": "776f831124e9c62e1a2c601ecc52e776d8bb7220", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "replace": {"myclabs/deep-copy": "self.version"}, "require-dev": {"doctrine/collections": "^1.0", "doctrine/common": "^2.6", "phpunit/phpunit": "^7.1"}, "type": "library", "autoload": {"psr-4": {"DeepCopy\\": "src/DeepCopy/"}, "files": ["src/DeepCopy/deep_copy.php"]}, "license": ["MIT"], "description": "Create deep copies (clones) of your objects", "keywords": ["clone", "copy", "duplicate", "object", "object graph"], "time": "2020-11-13T09:40:50+00:00"}, {"name": "phar-io/manifest", "version": "1.0.1", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/phar-io/manifest/1.0.1/phar-io-manifest-1.0.1.zip", "reference": "2df402786ab5368a0169091f61a7c1e0eb6852d0", "shasum": ""}, "require": {"ext-dom": "*", "ext-phar": "*", "phar-io/version": "^1.0.1", "php": "^5.6 || ^7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Component for reading phar.io manifest information from a PHP Archive (PHAR)", "time": "2017-03-05T18:14:27+00:00"}, {"name": "phar-io/version", "version": "1.0.1", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/phar-io/version/1.0.1/phar-io-version-1.0.1.zip", "reference": "a70c0ced4be299a63d32fa96d9281d03e94041df", "shasum": ""}, "require": {"php": "^5.6 || ^7.0"}, "type": "library", "autoload": {"classmap": ["src/"]}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Library for handling version information and constraints", "time": "2017-03-05T17:38:23+00:00"}, {"name": "phpdocumentor/reflection-common", "version": "2.1.0", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/phpdocumentor/reflection-common/2.1.0/phpdocumentor-reflection-common-2.1.0.zip", "reference": "6568f4687e5b41b054365f9ae03fcb1ed5f2069b", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Common reflection classes used by phpdocumentor to reflect the code structure", "homepage": "http://www.phpdoc.org", "keywords": ["FQSEN", "phpDocumentor", "phpdoc", "reflection", "static analysis"], "time": "2020-04-27T09:25:28+00:00"}, {"name": "phpdocumentor/reflection-docblock", "version": "4.3.4", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/phpdocumentor/reflection-docblock/4.3.4/phpdocumentor-reflection-docblock-4.3.4.zip", "reference": "da3fd972d6bafd628114f7e7e036f45944b62e9c", "shasum": ""}, "require": {"php": "^7.0", "phpdocumentor/reflection-common": "^1.0.0 || ^2.0.0", "phpdocumentor/type-resolver": "~0.4 || ^1.0.0", "webmozart/assert": "^1.0"}, "require-dev": {"doctrine/instantiator": "^1.0.5", "mockery/mockery": "^1.0", "phpdocumentor/type-resolver": "0.4.*", "phpunit/phpunit": "^6.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": ["src/"]}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "With this component, a library can provide support for annotations via DocBlocks or otherwise retrieve information that is embedded in a DocBlock.", "time": "2019-12-28T18:55:12+00:00"}, {"name": "phpdocumentor/type-resolver", "version": "1.0.1", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/phpdocumentor/type-resolver/1.0.1/phpdocumentor-type-resolver-1.0.1.zip", "reference": "2e32a6d48972b2c1976ed5d8967145b6cec4a4a9", "shasum": ""}, "require": {"php": "^7.1", "phpdocumentor/reflection-common": "^2.0"}, "require-dev": {"ext-tokenizer": "^7.1", "mockery/mockery": "~1", "phpunit/phpunit": "^7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A PSR-5 based resolver of Class names, Types and Structural Element Names", "time": "2019-08-22T18:11:29+00:00"}, {"name": "phpspec/prophecy", "version": "v1.10.3", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/phpspec/prophecy/v1.10.3/phpspec-prophecy-v1.10.3.zip", "reference": "451c3cd1418cf640de218914901e51b064abb093", "shasum": ""}, "require": {"doctrine/instantiator": "^1.0.2", "php": "^5.3|^7.0", "phpdocumentor/reflection-docblock": "^2.0|^3.0.2|^4.0|^5.0", "sebastian/comparator": "^1.2.3|^2.0|^3.0|^4.0", "sebastian/recursion-context": "^1.0|^2.0|^3.0|^4.0"}, "require-dev": {"phpspec/phpspec": "^2.5 || ^3.2", "phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.5 || ^7.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.10.x-dev"}}, "autoload": {"psr-4": {"Prophecy\\": "src/Prophecy"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://everzet.com"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Highly opinionated mocking framework for PHP 5.3+", "homepage": "https://github.com/phpspec/prophecy", "keywords": ["Double", "Dummy", "fake", "mock", "spy", "stub"], "time": "2020-03-05T15:02:03+00:00"}, {"name": "phpunit/php-code-coverage", "version": "5.3.2", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/phpunit/php-code-coverage/5.3.2/phpunit-php-code-coverage-5.3.2.zip", "reference": "c89677919c5dd6d3b3852f230a663118762218ac", "shasum": ""}, "require": {"ext-dom": "*", "ext-xmlwriter": "*", "php": "^7.0", "phpunit/php-file-iterator": "^1.4.2", "phpunit/php-text-template": "^1.2.1", "phpunit/php-token-stream": "^2.0.1", "sebastian/code-unit-reverse-lookup": "^1.0.1", "sebastian/environment": "^3.0", "sebastian/version": "^2.0.1", "theseer/tokenizer": "^1.1"}, "require-dev": {"phpunit/phpunit": "^6.0"}, "suggest": {"ext-xdebug": "^2.5.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.3.x-dev"}}, "autoload": {"classmap": ["src/"]}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that provides collection, processing, and rendering functionality for PHP code coverage information.", "homepage": "https://github.com/sebastian<PERSON>mann/php-code-coverage", "keywords": ["coverage", "testing", "xunit"], "time": "2018-04-06T15:36:58+00:00"}, {"name": "phpunit/php-file-iterator", "version": "1.4.5", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/phpunit/php-file-iterator/1.4.5/phpunit-php-file-iterator-1.4.5.zip", "reference": "730b01bc3e867237eaac355e06a36b85dd93a8b4", "shasum": ""}, "require": {"php": ">=5.3.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.4.x-dev"}}, "autoload": {"classmap": ["src/"]}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "FilterIterator implementation that filters files based on a list of suffixes.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/", "keywords": ["filesystem", "iterator"], "time": "2017-11-27T13:52:08+00:00"}, {"name": "phpunit/php-text-template", "version": "1.2.1", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/phpunit/php-text-template/1.2.1/phpunit-php-text-template-1.2.1.zip", "reference": "31f8b717e51d9a2afca6c9f046f5d69fc27c8686", "shasum": ""}, "require": {"php": ">=5.3.3"}, "type": "library", "autoload": {"classmap": ["src/"]}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Simple template engine.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/", "keywords": ["template"], "time": "2015-06-21T13:50:34+00:00"}, {"name": "phpunit/php-timer", "version": "1.0.9", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/phpunit/php-timer/1.0.9/phpunit-php-timer-1.0.9.zip", "reference": "3dcf38ca72b158baf0bc245e9184d3fdffa9c46f", "shasum": ""}, "require": {"php": "^5.3.3 || ^7.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"classmap": ["src/"]}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Utility class for timing", "homepage": "https://github.com/sebastian<PERSON>mann/php-timer/", "keywords": ["timer"], "time": "2017-02-26T11:10:40+00:00"}, {"name": "phpunit/php-token-stream", "version": "2.0.2", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/phpunit/php-token-stream/2.0.2/phpunit-php-token-stream-2.0.2.zip", "reference": "791198a2c6254db10131eecfe8c06670700904db", "shasum": ""}, "require": {"ext-tokenizer": "*", "php": "^7.0"}, "require-dev": {"phpunit/phpunit": "^6.2.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"classmap": ["src/"]}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Wrapper around PHP's tokenizer extension.", "homepage": "https://github.com/sebastian<PERSON>mann/php-token-stream/", "keywords": ["tokenizer"], "abandoned": true, "time": "2017-11-27T05:48:46+00:00"}, {"name": "phpunit/phpunit", "version": "6.5.14", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/phpunit/phpunit/6.5.14/phpunit-phpunit-6.5.14.zip", "reference": "bac23fe7ff13dbdb461481f706f0e9fe746334b7", "shasum": ""}, "require": {"ext-dom": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-xml": "*", "myclabs/deep-copy": "^1.6.1", "phar-io/manifest": "^1.0.1", "phar-io/version": "^1.0", "php": "^7.0", "phpspec/prophecy": "^1.7", "phpunit/php-code-coverage": "^5.3", "phpunit/php-file-iterator": "^1.4.3", "phpunit/php-text-template": "^1.2.1", "phpunit/php-timer": "^1.0.9", "phpunit/phpunit-mock-objects": "^5.0.9", "sebastian/comparator": "^2.1", "sebastian/diff": "^2.0", "sebastian/environment": "^3.1", "sebastian/exporter": "^3.1", "sebastian/global-state": "^2.0", "sebastian/object-enumerator": "^3.0.3", "sebastian/resource-operations": "^1.0", "sebastian/version": "^2.0.1"}, "conflict": {"phpdocumentor/reflection-docblock": "3.0.2", "phpunit/dbunit": "<3.0"}, "require-dev": {"ext-pdo": "*"}, "suggest": {"ext-xdebug": "*", "phpunit/php-invoker": "^1.1"}, "bin": ["phpunit"], "type": "library", "extra": {"branch-alias": {"dev-master": "6.5.x-dev"}}, "autoload": {"classmap": ["src/"]}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "The PHP Unit Testing framework.", "homepage": "https://phpunit.de/", "keywords": ["phpunit", "testing", "xunit"], "time": "2019-02-01T05:22:47+00:00"}, {"name": "phpunit/phpunit-mock-objects", "version": "5.0.10", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/phpunit/phpunit-mock-objects/5.0.10/phpunit-phpunit-mock-objects-5.0.10.zip", "reference": "cd1cf05c553ecfec36b170070573e540b67d3f1f", "shasum": ""}, "require": {"doctrine/instantiator": "^1.0.5", "php": "^7.0", "phpunit/php-text-template": "^1.2.1", "sebastian/exporter": "^3.1"}, "conflict": {"phpunit/phpunit": "<6.0"}, "require-dev": {"phpunit/phpunit": "^6.5.11"}, "suggest": {"ext-soap": "*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Mock Object library for PHPUnit", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/phpunit-mock-objects/", "keywords": ["mock", "xunit"], "abandoned": true, "time": "2018-08-09T05:50:03+00:00"}, {"name": "sebastian/code-unit-reverse-lookup", "version": "1.0.2", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/sebastian/code-unit-reverse-lookup/1.0.2/sebastian-code-unit-reverse-lookup-1.0.2.zip", "reference": "1de8cd5c010cb153fcd68b8d0f64606f523f7619", "shasum": ""}, "require": {"php": ">=5.6"}, "require-dev": {"phpunit/phpunit": "^8.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Looks up which function or method a line of code belongs to", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/", "time": "2020-11-30T08:15:22+00:00"}, {"name": "sebastian/comparator", "version": "2.1.3", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/sebastian/comparator/2.1.3/sebastian-comparator-2.1.3.zip", "reference": "****************************************", "shasum": ""}, "require": {"php": "^7.0", "sebastian/diff": "^2.0 || ^3.0", "sebastian/exporter": "^3.1"}, "require-dev": {"phpunit/phpunit": "^6.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.1.x-dev"}}, "autoload": {"classmap": ["src/"]}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides the functionality to compare PHP values for equality", "homepage": "https://github.com/sebas<PERSON><PERSON><PERSON>/comparator", "keywords": ["comparator", "compare", "equality"], "time": "2018-02-01T13:46:46+00:00"}, {"name": "sebastian/diff", "version": "2.0.1", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/sebastian/diff/2.0.1/sebastian-diff-2.0.1.zip", "reference": "347c1d8b49c5c3ee30c7040ea6fc446790e6bddd", "shasum": ""}, "require": {"php": "^7.0"}, "require-dev": {"phpunit/phpunit": "^6.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"classmap": ["src/"]}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Diff implementation", "homepage": "https://github.com/sebastian<PERSON>mann/diff", "keywords": ["diff"], "time": "2017-08-03T08:09:46+00:00"}, {"name": "sebastian/environment", "version": "3.1.0", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/sebastian/environment/3.1.0/sebastian-environment-3.1.0.zip", "reference": "cd0871b3975fb7fc44d11314fd1ee20925fce4f5", "shasum": ""}, "require": {"php": "^7.0"}, "require-dev": {"phpunit/phpunit": "^6.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1.x-dev"}}, "autoload": {"classmap": ["src/"]}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to handle HHVM/PHP environments", "homepage": "http://www.github.com/sebastianbergmann/environment", "keywords": ["Xdebug", "environment", "hhvm"], "time": "2017-07-01T08:51:00+00:00"}, {"name": "sebastian/exporter", "version": "3.1.3", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/sebastian/exporter/3.1.3/sebastian-exporter-3.1.3.zip", "reference": "6b853149eab67d4da22291d36f5b0631c0fd856e", "shasum": ""}, "require": {"php": ">=7.0", "sebastian/recursion-context": "^3.0"}, "require-dev": {"ext-mbstring": "*", "phpunit/phpunit": "^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1.x-dev"}}, "autoload": {"classmap": ["src/"]}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Provides the functionality to export PHP variables for visualization", "homepage": "http://www.github.com/sebastianbergmann/exporter", "keywords": ["export", "exporter"], "time": "2020-11-30T07:47:53+00:00"}, {"name": "sebastian/global-state", "version": "2.0.0", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/sebastian/global-state/2.0.0/sebastian-global-state-2.0.0.zip", "reference": "e8ba02eed7bbbb9e59e43dedd3dddeff4a56b0c4", "shasum": ""}, "require": {"php": "^7.0"}, "require-dev": {"phpunit/phpunit": "^6.0"}, "suggest": {"ext-uopz": "*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"classmap": ["src/"]}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Snapshotting of global state", "homepage": "http://www.github.com/sebastian<PERSON>mann/global-state", "keywords": ["global state"], "time": "2017-04-27T15:39:26+00:00"}, {"name": "sebastian/object-enumerator", "version": "3.0.4", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/sebastian/object-enumerator/3.0.4/sebastian-object-enumerator-3.0.4.zip", "reference": "e67f6d32ebd0c749cf9d1dbd9f226c727043cdf2", "shasum": ""}, "require": {"php": ">=7.0", "sebastian/object-reflector": "^1.1.1", "sebastian/recursion-context": "^3.0"}, "require-dev": {"phpunit/phpunit": "^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Traverses array structures and object graphs to enumerate all referenced objects", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/", "time": "2020-11-30T07:40:27+00:00"}, {"name": "sebastian/object-reflector", "version": "1.1.2", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/sebastian/object-reflector/1.1.2/sebastian-object-reflector-1.1.2.zip", "reference": "9b8772b9cbd456ab45d4a598d2dd1a1bced6363d", "shasum": ""}, "require": {"php": ">=7.0"}, "require-dev": {"phpunit/phpunit": "^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "autoload": {"classmap": ["src/"]}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Allows reflection of object attributes, including inherited and non-public ones", "homepage": "https://github.com/sebastian<PERSON>mann/object-reflector/", "time": "2020-11-30T07:37:18+00:00"}, {"name": "sebastian/recursion-context", "version": "3.0.1", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/sebastian/recursion-context/3.0.1/sebastian-recursion-context-3.0.1.zip", "reference": "367dcba38d6e1977be014dc4b22f47a484dac7fb", "shasum": ""}, "require": {"php": ">=7.0"}, "require-dev": {"phpunit/phpunit": "^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to recursively process PHP variables", "homepage": "http://www.github.com/sebastian<PERSON>mann/recursion-context", "time": "2020-11-30T07:34:24+00:00"}, {"name": "sebastian/resource-operations", "version": "1.0.0", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/sebastian/resource-operations/1.0.0/sebastian-resource-operations-1.0.0.zip", "reference": "ce990bb21759f94aeafd30209e8cfcdfa8bc3f52", "shasum": ""}, "require": {"php": ">=5.6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides a list of PHP built-in functions that operate on resources", "homepage": "https://www.github.com/sebastianbergmann/resource-operations", "time": "2015-07-28T20:34:47+00:00"}, {"name": "sebastian/version", "version": "2.0.1", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/sebastian/version/2.0.1/sebastian-version-2.0.1.zip", "reference": "99732be0ddb3361e16ad77b68ba41efc8e979019", "shasum": ""}, "require": {"php": ">=5.6"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that helps with managing the version number of Git-hosted PHP projects", "homepage": "https://github.com/sebastian<PERSON>mann/version", "time": "2016-10-03T07:35:21+00:00"}, {"name": "seld/jsonlint", "version": "1.8.3", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/seld/jsonlint/1.8.3/seld-jsonlint-1.8.3.zip", "reference": "9ad6ce79c342fbd44df10ea95511a1b24dee5b57", "shasum": ""}, "require": {"php": "^5.3 || ^7.0 || ^8.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.0"}, "bin": ["bin/jsonlint"], "type": "library", "autoload": {"psr-4": {"Seld\\JsonLint\\": "src/Seld/JsonLint/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "JSON Linter", "keywords": ["json", "linter", "parser", "validator"], "time": "2020-11-11T09:19:24+00:00"}, {"name": "seld/phar-utils", "version": "1.1.1", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/seld/phar-utils/1.1.1/seld-phar-utils-1.1.1.zip", "reference": "8674b1d84ffb47cc59a101f5d5a3b61e87d23796", "shasum": ""}, "require": {"php": ">=5.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Seld\\PharUtils\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be"}], "description": "PHAR file format utilities, for when PHP phars you up", "keywords": ["phar"], "time": "2020-07-07T18:42:57+00:00"}, {"name": "symfony/filesystem", "version": "v4.4.21", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/symfony/filesystem/v4.4.21/symfony-filesystem-v4.4.21.zip", "reference": "940826c465be2690c9fae91b2793481e5cbd6834", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/polyfill-ctype": "~1.8"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Filesystem\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides basic utilities for the filesystem", "homepage": "https://symfony.com", "time": "2021-03-28T09:59:32+00:00"}, {"name": "theseer/tokenizer", "version": "1.1.3", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/theseer/tokenizer/1.1.3/theseer-tokenizer-1.1.3.zip", "reference": "11336f6f84e16a720dae9d8e6ed5019efa85a0f9", "shasum": ""}, "require": {"ext-dom": "*", "ext-tokenizer": "*", "ext-xmlwriter": "*", "php": "^7.0"}, "type": "library", "autoload": {"classmap": ["src/"]}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A small library for converting tokenized PHP source code into XML and potentially other formats", "time": "2019-06-13T22:48:21+00:00"}, {"name": "webmozart/assert", "version": "1.9.1", "dist": {"type": "zip", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/webmozart/assert/1.9.1/webmozart-assert-1.9.1.zip", "reference": "bafc69caeb4d49c39fd0779086c03a3738cbb389", "shasum": ""}, "require": {"php": "^5.3.3 || ^7.0 || ^8.0", "symfony/polyfill-ctype": "^1.8"}, "conflict": {"phpstan/phpstan": "<0.12.20", "vimeo/psalm": "<3.9.1"}, "require-dev": {"phpunit/phpunit": "^4.8.36 || ^7.5.13"}, "type": "library", "autoload": {"psr-4": {"Webmozart\\Assert\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Assertions to validate method input/output with nice error messages.", "keywords": ["assert", "check", "validate"], "time": "2020-07-08T17:02:28+00:00"}], "aliases": [], "minimum-stability": "stable", "stability-flags": [], "prefer-stable": false, "prefer-lowest": false, "platform": {"php": ">=7.0.0"}, "platform-dev": []}