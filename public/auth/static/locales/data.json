{"zh-CN": {"activate.enterPhoneSmsValidate": "请输入手机{0}获取的短信验证码", "forgot.byPhone": "通过手机号{0}找回密码", "forgot.byEmail": "通过邮箱{0}找回密码", "forgot.validateMode": "验证方式：", "forgot.phoneValidate": "手机验证", "forgot.emailValidate": "邮箱验证", "forgot.resetSuccess": "重设成功！请使用新密码登录", "forgot.login": "立即登录", "forgot.resetEmailSended": "重设密码链接已发送至{0}<br />请登录你的邮箱查看并按照邮件内容操作", "forgot.resetEmailSendedTips": "邮件可能会被你的邮箱拦截，如在收件箱中未找到，可尝试在垃圾邮件中查看。", "forgot.returnLogin": "返回到登录", "loginVerify.helperDes1": "1.请确认你设置接收验证码的微信号是否关注了亿方云公众号，如未关注，请重新搜索并关注“亿方云官方服务平台”。关注后点击“重新发送验证码”完成验证即可。", "loginVerify.helperDes2": "2.你也可以联系企业管理员或客服帮你关闭二次验证", "validate.wrongPhone": "请输入有效的手机号", "auth.register.phoneError": "请输入正确的手机号", "auth.register.sms_captchaError": "请输入正确的验证码", "auth.register.passwordError": "请设置正确的密码，8~32位数字和字母的组合", "auth.register.emailError": "请输入正确的邮箱", "auth.register.phoneRegistered": "该手机号已被注册，请直接<a href=\"{0}\">登录</a>或联系客服", "auth.register.phoneRegisteredAsPersonal": "你已注册个人帐号，请直接<a href=\"{0}\">登录</a>或前往网页版、PC客户端登录后进行套餐升级", "auth.register.phoneAlreadyAccupied": "你已被邀请加入{0} <a href=\"{1}\">立即激活</a>", "auth.register.phoneAlreadyAccupiedAndExpired": "你在30天前被邀请加入{0}，请联系管理员重发邀请后去<a href=\"{1}\">激活账号</a>", "base.needAcceptProtocal": "请阅读并接受服务条款和服务等级协议", "base.emptyValue": "请输入正确的信息后再试", "settings.twoStepVerification.modifyVerifySuccessTitle": "二次验证已修改", "settings.twoStepVerification.setVerifySuccessTitle": "二次验证已开启", "settings.twoStepVerification.setVerifySuccessDes": "二次验证已开启，每次登录{0}时，除帐号密码之外，还需要输入安全验证码。", "settings.thirdPartyBindFailure": "绑定失败", "settings.editPassword": "修改密码", "settings.oldPassword": "旧密码", "settings.newPassword": "新密码", "settings.confirmPassword": "确认密码", "base.submit": "提交", "settings.editPasswordSuccess": "密码修改成功，请重新登录", "base.confirm": "确定", "settings.email": "邮箱：", "settings.phone": "手机：", "settings.emptyEmailInfo": "绑定且验证后可用邮箱登录", "settings.emptyPhoneInfo": "绑定后可用手机登录", "settings.unvalidate": "未验证", "settings.unvalidateCredentialUsage": "验证后可用于登录、找回密码", "settings.validateTime": "验证时间：{0}", "settings.validateCredentialUsage": "可用于登录、找回密码", "settings.validate": "立即验证", "settings.bindImmediately": "立即绑定", "settings.phoneNotAvaliable": "该手机号已被其它帐号占用，请修改。", "settings.emailNotAvaliable": "该邮箱已被其它帐号占用，请修改。", "base.cancelText": "下次再说", "settings.editNow": "立即修改", "settings.forbiddenEditEmail": "暂时无法修改邮箱。请在手机号绑定成功后再试。", "settings.editPhone": "修改手机", "settings.validatePhone": "验证手机", "settings.bindPhone": "绑定手机", "settings.editPhoneSuccess": "手机号已修改成功", "settings.validatePhoneSuccess": "手机号已验证成功", "settings.bindPhoneSuccess": "手机号已绑定成功", "settings.currentCredential": "当前绑定：{0}", "settings.mobilePlaceholder": "请输入你的手机号", "settings.editEmail": "修改邮箱", "settings.validateEmail": "验证邮箱", "settings.bindEmail": "绑定邮箱", "settings.enterNewEmail": "请输入新的邮箱", "settings.emailSended": "验证邮件已发送至{0}，请登录你的邮箱查看。验证后，该邮箱可以用于登录、找回密码。", "settings.emailSendedTips": "邮件可能会被拦截，如未在收件箱中找到，可尝试在垃圾邮件中查找", "settings.unbind": "解绑", "settings.qiyeweixinLabel": "企业微信：", "settings.dingdingLabel": "钉钉：", "settings.qihoo360Label": "360账号：", "settings.dingding": "钉钉", "settings.qiyeweixin": "企业微信", "settings.qihoo360": "360", "settings.unbindThirdConfirmMain": "你确定要解绑{0}帐号“{1}”吗？", "settings.unbindConfirmSub": "解绑后，将无法使用该帐号登录", "settings.device.fromAndroid": "（Android App）", "settings.device.fromIos": "（iOS App）", "settings.device.fromWindowsSync": "（Windows同步端）", "settings.device.fromMacSync": "（Mac同步端）", "settings.device.fromWindowsDesktop": "（Windows客户端）", "settings.device.fromMacDesktop": "（Mac客户端）", "settings.device.deviceName": "设备名称", "settings.device.deivceLastLogin": "最近访问", "settings.device.deivceLastLocation": "最近访问地", "settings.device.deivceOperation": "操作", "settings.device.creditable": "可信", "settings.device.current": "当前", "settings.device.ip": "IP地址：{0}", "base.delete": "删除", "settings.device.noresult": "暂无数据", "settings.device.showMore": "展开全部", "settings.device.deleteIsCreditable": "在该设备上将退出登录，你需要重新登录才能继续访问，再次登录需要进行二次验证。", "settings.device.deleteNotCreditable": "在该设备上将退出登录，你需要重新登录才能继续访问。", "settings.device.deleteDevice": "删除此设备", "settings.device.hideMore": "收起", "settings.twoStepVerification.typePhone": "手机短信验证", "settings.twoStepVerification.typePhoneNumber": "（尾号为 {0}）", "settings.twoStepVerification.typeWechat": "微信公众号验证", "settings.twoStepVerification.typeGoogle": "谷歌验证器验证", "settings.twoStepVerification.hasOpen": "二次验证已开启", "settings.twoStepVerification.validateTime": "（设置于 {0}）", "settings.twoStepVerification.toClose": "关闭二次验证", "settings.twoStepVerification.type": "验证方式：", "base.modify": "修改", "settings.twoStepVerification.info": "二次验证为你的帐户增加一层安全保护。启用二次验证后，每次登录亿方云时，除帐号密码之外，还需要输入安全验证码（验证码将发送到你的手机上）。", "settings.twoStepVerification.toSet": "开启二次验证", "settings.twoStepVerification.disableToClose": "管理员开启了“企业成员二次验证”功能，你无法单独关闭二次验证。", "base.yfy": "亿方云", "base.Iknow": "知道了", "base.confirmToContinue": "由于你长时间未进行操作，请刷新或点击确认后继续使用", "base.loginFail": "登录已失效，请重新登录", "base.alert.server500.title": "服务器开了一点小差，已通知程序猿小哥处理，请稍等片刻或刷新重试。", "base.alert.server500.content": "如有疑问，请联系客服", "base.alert.server502": "服务器错误，请重试 (502)", "base.alert.server404": "网络出错(404)，请检查后重试", "base.alert.server403": "你无权限访问", "base.pageExpired": "当前页面已失效，请点击{0}重试", "base.ok": "确定", "base.passwordStrengthMid": "密码必须为8-32位字母和数字的组合", "base.passwordStartOrEndWithSpace": "密码首尾不能为空格", "base.passwordWithForbiddenCharacter": "密码包含不允许的字符(<a href=\"http://help.fangcloud.com/posts/view/57412/\" target=\"_blank\">了解详情</a>)", "base.time.justNow": "刚刚", "base.time.beforeSeconds": "{0}秒前", "base.time.beforeMinutes": "{0}分钟前", "base.time.beforeHours": "{0}小时前", "base.time.yesterday": "昨天", "base.time.theDayBefore": "前天", "unit.second": "{0}秒", "unit.minute": "{0}分钟", "unit.hour": "{0}小时", "unit.day": "{0}天", "base.hidePassword": "隐藏密码", "base.showPassword": "显示密码", "login.ent.expired.sub": "若要查看云端文件，可以在网页端下载。", "base.openBrowser": "打开网页端", "encryption.versionLimit": "你所在的企业开启了文件防泄漏，需下载特定版本客户端方可使用", "encryption.downloadNow": "立即下载", "base.passwordWithCaptial": "密码长度为{0}-32个字符，必须包含大写字母、小写字母和数字", "base.passwordWithSpecial": "密码长度为{0}-32个字符，必须包含字母、数字和特殊字符(<a class=\"get-detail\" href=\"http://help.fangcloud.com/posts/view/57412/\" target=\"_blank\">了解详情</a>)", "base.passwordWithCaptialAndSpecial": "密码长度为{0}-32个字符，必须包含大写字母、小写字母、数字和特殊字符(<a class=\"get-detail\" href=\"http://help.fangcloud.com/posts/view/57412/\" target=\"_blank\">了解详情</a>)", "base.passwordNormal": "密码长度为{0}-32个字符，必须包含字母和数字", "base.countrySelectorPlaceholder": "搜索你的国家/地区、区号", "base.searchEmpty": "无符合条件的搜索结果", "base.cancel": "取消", "base.picCaptchaPlaceholder": "请输入图形验证码", "base.smsCaptchaPlaceholder": "请输入{0}位验证码", "base.getVerificationCode": "获取验证码", "base.retrieveAfter": "{0}秒后重新获取", "base.retrieve": "重新获取验证码", "base.getVoice": "接收语音验证码", "base.voiceSMSCaptcha_send": "系统将通过免费电话给您发送语音验证码，60s内未收到可重新获取", "base.voiceSMSCaptchaConfirm": "我们将以电话的方式发送语音验证码。请注意接听并记下{0}位验证码。", "base.receiveVoiceConfirm": "接收语音验证码", "settings.confirmLoginPassword": "验证身份", "settings.confirmLoginPasswordHint": "为保障你的帐号安全，请输入登录密码进行身份验证。", "base.inputLoginPassword": "请输入你的登录密码", "base.next": "下一步", "settings.setLoginPassword": "设置独立登录密码", "settings.setLoginPasswordHint1": "设置独立登录密码，可配合绑定的邮箱/手机登录。", "settings.pleaseSetPassword": "独立登录密码", "settings.twoStepVerification.selectVerifyTitle": "选择二次验证方式", "settings.twoStepVerification.wechat": "微信", "settings.twoStepVerification.wechatSetDes": "使用微信扫描并关注我们的微信公众号，我们会将验证码通过公众号发送给你", "settings.twoStepVerification.sms": "手机短信", "settings.twoStepVerification.smsSetDes": "设置一个手机号，我们会将验证码通过短信方式发送给你。", "settings.twoStepVerification.google": "谷歌身份验证器", "settings.twoStepVerification.googleSetDes": "你可以下载身份验证器应用来获取验证码，即使手机未连接网络也无妨。", "settings.twoStepVerification.recommend": "推荐", "settings.twoStepVerification.wechatDes1": "1. 使用微信扫描二维码并关注我们的微信公众号，你将会收到验证码。", "settings.twoStepVerification.wechatDes2": "如已关注公众号，可直接扫描", "settings.twoStepVerification.wechatDes3": "2. 输入微信公众号发给你的6位验证码。", "base.twoStepVerification.setSubmit": "完成设置", "base.previous": "上一步", "settings.twoStepVerification.setByWechatTitle": "设置微信", "settings.twoStepVerification.setBySMSTitle": "设置手机号", "settings.twoStepVerification.googleIos": "iPhone用户", "settings.twoStepVerification.googleIosDes1": "1.在iPhone上，打开App Store。", "settings.twoStepVerification.googleIosDes2": "2.搜索谷歌身份验证器(Google Authenticator)", "settings.twoStepVerification.googleIosDes3": "3.下载并安装应用。", "settings.twoStepVerification.googleAndroid": "Android用户", "twoStepVerification.googleAndroidDes1": "1.在手机上访问Google Play或其它商店。", "twoStepVerification.googleAndroidDes2": "2.搜索谷歌身份验证器(Google Authenticator)", "twoStepVerification.googleAndroidDes3": "3.下载并安装应用。", "settings.twoStepVerification.googleHelp": "如何安装google身份验证器？", "settings.twoStepVerification.downloadGoogleTitle": "下载谷歌身份验证器", "settings.twoStepVerification.googleVerifyQRcodeIosDes": "1. 打开谷歌身份验证器，点击+通过扫描二维码完成设置", "settings.twoStepVerification.googleVerifyQRcodeDes": "1.打开应用，点击菜单，选择“设置帐户”，再扫描条形码即可。", "settings.twoStepVerification.googleVerifyCantQRcode": "无法扫描？", "settings.twoStepVerification.googleVerifyToManually": "手动设置", "settings.twoStepVerification.googleVerifyManuallyIosDes1": "1.打开谷歌身份验证器，点击+", "settings.twoStepVerification.googleVerifyManuallyIosDes2": "建议你在“帐户”中输入产品和帐户名，如“{0}：<EMAIL>”", "settings.twoStepVerification.googleVerifyManuallyIosDes3": "在“密钥”中输入16位密钥：", "settings.twoStepVerification.googleVerifyManuallyAndroidDes1": "1.打开应用，点击菜单，选择“设置账户”", "settings.twoStepVerification.googleVerifyManuallyAndroidDes2": "建议你在“账户”中输入产品和帐户名，如“{0}：<EMAIL>”", "settings.twoStepVerification.googleVerifyManuallyAndroidDes3": "在“密钥”中输入16位密钥", "settings.twoStepVerification.googleVerifyToQRcode": "返回扫描设置", "settings.twoStepVerification.googleVerifyInputCaptcha": "2. 输入谷歌验证器的6位验证码", "settings.twoStepVerification.setByGoogleTitle": "设置谷歌身份验证器", "settings.twoStepVerification.closeVerifySuccess": "二次验证已关闭"}, "en": {"activate.enterPhoneSmsValidate": "Enter the SMS code sent to phone number {0}", "base.Iknow": "OK", "base.confirmToContinue": "Since you have not operated for a long time, please refresh or click to confirm and continue to use", "base.confirm": "Yes", "base.loginFail": "Your session has expired. Please log in again", "base.alert.server500.title": "The server is down, and the programmer has been informed, please wait a moment or refresh to try again.", "base.alert.server500.content": "If you have any question, please contact the customer service", "base.alert.server502": "Server error (502). Please try again", "base.alert.server404": "Network error (404), please check and try again.", "base.alert.server403": "403-Forbidden:Access is denied.", "base.pageExpired": "Current session has expired, please click {0} to retry", "base.ok": "Yes", "base.passwordStrengthMid": "Your password must be a combination of 8-32 letters and figures", "base.passwordStartOrEndWithSpace": "There must be no space at the beginning and end of your password", "base.passwordWithForbiddenCharacter": "Your password contains character(s) that aren’t allowed (<a class=\"get-detail\" href=\"http://help.fangcloud.com/posts/view/57412/\" target=\"_blank\">Learn more</a>)", "base.time.justNow": "Just now", "base.time.beforeSeconds": "{0} second(s) ago", "base.time.beforeMinutes": "{0} minute(s) ago", "base.time.beforeHours": "{0} hour(s) ago", "base.time.yesterday": "Yesterday", "base.time.theDayBefore": "The day before yesterday", "unit.second": "{0} second(s)", "unit.minute": "{0} minute(s)", "unit.hour": "{0} hour(s)", "unit.day": "{0} day(s)", "base.hidePassword": "Hide the password", "base.showPassword": "Show the password", "login.ent.expired.sub": "If you want the cloud files, you can download them by logging into the website.", "base.openBrowser": "Open the website", "encryption.versionLimit": "Your company has turned on File Leakage Prevention System and needs to download a specific version of PC client to continue using", "encryption.downloadNow": "Download now", "base.passwordWithCaptial": "Your password must be a combination of {0}-32 characters, including upper-case letter(s), lower-case letter(s) and figures", "base.passwordWithSpecial": "Your password must be a combination of {0}-32 characters, including letter(s), number(s) and special character(s) (<a class=\"get-detail\" href=\"http://help.fangcloud.com/posts/view/57412/\" target=\"_blank\">Learn more</a>)", "base.passwordWithCaptialAndSpecial": "Your password must be a combination of {0}-32 characters, including upper-case letter(s), lower-case letter(s), figures and special character(s) (<a class=\"get-detail\" href=\"http://help.fangcloud.com/posts/view/57412/\" target=\"_blank\">Learn more</a>)", "base.passwordNormal": "Your password must be a combination of {0}-32 characters, including letter(s) and number(s)", "forgot.byPhone": "Reset your password via mobile phone number {0}", "forgot.byEmail": "Reset your password via email address {0}", "forgot.validateMode": "Verificaiton method:", "forgot.phoneValidate": "Via verified phone number", "forgot.emailValidate": "Via verified email", "forgot.resetSuccess": "Reset successful! Please log in with the new password", "forgot.login": "Log in now", "forgot.resetEmailSended": "The link for password reset has been sent to {0}<br />Please check your email and follow the instructions.", "forgot.resetEmailSendedTips": "The email may be blocked by your email account. If you can’t find it in your inbox, you may need to check spam.", "forgot.returnLogin": "Back to log in", "loginVerify.helperDes1": "1. Please confirm whether your WeChat account for receiving verification code has followed the FangCloud WeChat Official Account. If not, please search again and follow “亿方云官方服务平台”. Follow and then click “Resend verification code” to finish verification.", "loginVerify.helperDes2": "2. You can also contact your enterprise admin or customer service team to help disable it", "validate.wrongPhone": "Enter a valid mobile number", "auth.register.phoneError": "Please enter the correct phone number", "auth.register.sms_captchaError": "Please enter the correct verification code", "auth.register.passwordError": "Please set the correct password, a combination of 8 ~ 32 digits and letters", "auth.register.emailError": "Please enter the correct email", "auth.register.freeTrial": "Free of charge, try it now", "auth.register.freeExperienceFor15Days": "Try free for 15 days", "auth.register.leaveMessageForContact": "Please provide your contact information and we will contact you as soon as possible", "auth.register.normalTip": "If your enterprise has finished registration, please contact the administrator for invitation. Individual registration isn’t necessary", "auth.register.enterpirseContactNumber": "You may also call <b>************</b> to contact us", "base.needAcceptProtocal": "Please read and accept Terms & Conditions of Services and Service Level Agreement", "base.emptyValue": "Please enter the correct information and try again", "auth.register.phoneRegistered": "This phone number has been used for registration. Please <a href=\"{0}\"> log in </a> or contact Customer Services.", "auth.register.phoneRegisteredAsPersonal": "You have already registered as a personal account. Please <a href=\"{0}\"> login </a> or go to the website or PC client to login and update your plan.", "auth.register.phoneAlreadyAccupied": "You have been invited to join {0} <a href=\"{1}\"> activate now</a>", "auth.register.phoneAlreadyAccupiedAndExpired": "You were invited to join {0} 30 days ago, please contact the administrator to re-send the invitation to <a href=\"{1}\">activate the account</a>", "settings.confirmLoginPassword": "Identity verification", "settings.confirmLoginPasswordHint": "To ensure security of your account, please enter your login password for identity verification", "base.inputLoginPassword": "Enter your login password", "base.next": "Next", "settings.setLoginPassword": "Set an individual login password", "settings.setLoginPasswordHint1": "Set your individual login password and you may log in with the login password and the linked email address/phone number.", "settings.pleaseSetPassword": "Individual login password", "settings.twoStepVerification.selectVerifyTitle": "Choose Two-step verification method", "settings.twoStepVerification.wechat": "WeChat", "settings.twoStepVerification.wechatSetDes": "Use your WeChat app to scan and follow our WeChat Official Account, and we'll send the verification code to you via WeChat.", "settings.twoStepVerification.sms": "Text message", "settings.twoStepVerification.smsSetDes": "Set a phone number, and we'll send you the verification code via text message.", "settings.twoStepVerification.google": "Google Authenticator", "settings.twoStepVerification.googleSetDes": "You can download a Google Authenticator app to get the verification code, even if your phone is not connected to the network.", "settings.twoStepVerification.recommend": "Recommend", "settings.twoStepVerification.wechatDes1": "1. Use your WeChat app to scan and follow our WeChat Official Account, and you will receive the verification code.", "settings.twoStepVerification.wechatDes2": "If you have followed our WeChat Official Account, you can scan directly.", "settings.twoStepVerification.wechatDes3": "2. Enter the 6-digit verification code sent by the FangCloud WeChat Official Account.", "base.twoStepVerification.setSubmit": "Complete", "base.previous": "Back", "settings.twoStepVerification.setByWechatTitle": "Set WeChat", "settings.twoStepVerification.setBySMSTitle": "Set phone number", "settings.twoStepVerification.googleIos": "iPhone user", "settings.twoStepVerification.googleIosDes1": "1. Open App Store on iPhone.", "settings.twoStepVerification.googleIosDes2": "2. Search Google Authenticator", "settings.twoStepVerification.googleIosDes3": "3. Download and install the app", "settings.twoStepVerification.googleAndroid": "Android user", "twoStepVerification.googleAndroidDes1": "1. Visit Google Play or other app stores on your phone.", "twoStepVerification.googleAndroidDes2": "2. Search Google Authenticator", "twoStepVerification.googleAndroidDes3": "3. Download and install the app", "settings.twoStepVerification.googleHelp": "How to install Google Authenticator app?", "settings.twoStepVerification.downloadGoogleTitle": "Download Google Authenticator app", "settings.twoStepVerification.googleVerifyQRcodeIosDes": "1. Open Google Authenticator app, click + and scan the QR code to complete the configuration", "settings.twoStepVerification.googleVerifyQRcodeDes": "1. Open Google Authenticator app, click the menu and choose \"Setting Account\", then scan the QR code.", "settings.twoStepVerification.googleVerifyCantQRcode": "Can't scan?", "settings.twoStepVerification.googleVerifyToManually": "Set manually", "settings.twoStepVerification.googleVerifyManuallyIosDes1": "1. Open Google Authenticator app and click +", "settings.twoStepVerification.googleVerifyManuallyIosDes2": "It is recommended that you enter the product and account name in \"Account\", such as \"{0}: <EMAIL>\"", "settings.twoStepVerification.googleVerifyManuallyIosDes3": "Enter a 16-digit secret key in “Secret Key”:", "settings.twoStepVerification.googleVerifyManuallyAndroidDes1": "1. Open Google Authenticator app, click the menu and choose \"Setting Account\"", "settings.twoStepVerification.googleVerifyManuallyAndroidDes2": "It is recommended that you enter the product and account name in \"Account\", such as \"{0}: <EMAIL>\"", "settings.twoStepVerification.googleVerifyManuallyAndroidDes3": "Enter a 16-digit secret key in “Secret Key”", "settings.twoStepVerification.googleVerifyToQRcode": "Or scan a QR code instead", "settings.twoStepVerification.googleVerifyInputCaptcha": "2. Enter 6-digit code generated by Google Authenticator app", "settings.twoStepVerification.setByGoogleTitle": "Set Google Authenticator app", "settings.twoStepVerification.modifyVerifySuccessTitle": "Two-step verification has been changed", "settings.twoStepVerification.setVerifySuccessTitle": "Two-step verification is enabled", "settings.twoStepVerification.setVerifySuccessDes": "Every time you sign in {0}, you need to enter the verification code in addition to your account and password.", "settings.twoStepVerification.closeVerifySuccess": "Two-step verification has been disabled", "settings.thirdPartyBindFailure": "Linking failed", "settings.editPassword": "Change password", "settings.oldPassword": "Old password", "settings.newPassword": "New password", "settings.confirmPassword": "Confirm password", "settings.editPasswordSuccess": "Password changed successfully. Please login again.", "settings.email": "Email:", "settings.phone": "Mobile phone number:", "settings.emptyEmailInfo": "The email address can be used for login after being linked and verified", "settings.emptyPhoneInfo": "The mobile phone number can be used for login after being linked and verified", "settings.unvalidate": "Unverified", "settings.unvalidateCredentialUsage": "Can be used for login and retrieving your password after verification", "settings.validateTime": "Time of verification: {0}", "settings.validateCredentialUsage": "Can be used for login and retrieving password", "settings.validate": "Verify now", "settings.bindImmediately": "Link now", "settings.phoneNotAvaliable": "This phone number has been used by another account number. Please change it.", "settings.emailNotAvaliable": "This email address has been linked to another account. Please change it.", "base.cancelText": "Later", "settings.editNow": "Modify now", "settings.forbiddenEditEmail": "You can’t change the email address for now. Please try again after successful linking to your phone number.", "settings.editPhone": "Change mobile phone number", "settings.validatePhone": "Verify mobile phone number", "settings.bindPhone": "Link mobile phone number", "settings.editPhoneSuccess": "Your mobile phone number is successfully changed", "settings.validatePhoneSuccess": "The mobile phone number is successfully verified", "settings.bindPhoneSuccess": "The mobile phone number is successfully linked", "settings.currentCredential": "The existing linked number: {0}", "settings.mobilePlaceholder": "Enter your mobile phone number", "base.submit": "Submit", "settings.editEmail": "Change email address", "settings.validateEmail": "Verify email", "settings.bindEmail": "Link email address", "settings.enterNewEmail": "Enter a new email address", "settings.emailSended": "A verification email has been sent to {0}. Please check your inbox. This email address can be used for login and retrieving your password after verification.", "settings.emailSendedTips": "The email may be blocked by your email account. If you can’t find it in your inbox, you may need to check spam.", "settings.unbind": "Unlink", "settings.bind": "Link", "settings.qiyeweixinLabel": "Enterprise WeChat:", "settings.dingdingLabel": "DingTalk:", "settings.dingding": "DingTalk", "settings.qiyeweixin": "Enterprise WeChat", "settings.qihoo360": "360", "settings.qihoo360Label": "360 Account:", "settings.unbindThirdConfirmMain": "Are you sure that you want to unlink {0} account “{1}”?", "settings.unbindConfirmSub": "After being unlinked, the account number can’t be used for login", "settings.device.fromAndroid": "(Android App)", "settings.device.fromIos": "(iOS App)", "settings.device.fromWindowsSync": "(Sync for Windows)", "settings.device.fromMacSync": "(Sync for <PERSON>)", "settings.device.fromMacDesktop": "(FangCloud for Mac)", "settings.device.fromWindowsDesktop": "(FangCloud for Windows)", "settings.device.deviceName": "Device name", "settings.device.deivceLastLogin": "Recent access time", "settings.device.deivceLastLocation": "Recent access location", "settings.device.deivceOperation": "Operation", "settings.device.creditable": "Trust", "settings.device.current": "Current", "settings.device.ip": "IP address: {0}", "base.delete": "Delete", "settings.device.noresult": "No data for now", "settings.device.showMore": "Expand all", "settings.device.deleteIsCreditable": "Your account will be signed out of this device, and you need to login again. Two-step verification is required for next login.", "settings.device.deleteNotCreditable": "Your account will be signed out of this device, and you need to login again.", "settings.device.deleteDevice": "Delete device", "settings.device.hideMore": "<PERSON>de", "settings.twoStepVerification.typePhone": "Text message", "settings.twoStepVerification.typePhoneNumber": "(ending with {0})", "settings.twoStepVerification.typeWechat": "WeChat Official Account", "settings.twoStepVerification.typeGoogle": "Google Authenticator", "settings.twoStepVerification.hasOpen": "Two-step verification is enabled", "settings.twoStepVerification.validateTime": "(Set on {0})", "settings.twoStepVerification.toClose": "Disable Two-step verification", "settings.twoStepVerification.type": "Verificaiton method:", "base.modify": "Edit", "settings.twoStepVerification.info": "The two-step verification adds a layer of security to your account. Once two-step verification is enabled, you will need to enter a verification code in addition to the account number and password when you log into your FangCloud account (the verification code will be sent to your mobile phone).", "settings.twoStepVerification.toSet": "Click to enable", "settings.twoStepVerification.disableToClose": "The administrator has enabled \"Two-step verification of enterprise members\", and you can't disable Two-step verification by yourself.", "base.yfy": "FangCloud", "base.countrySelectorPlaceholder": "Search your country/region and area code", "base.searchEmpty": "No eligible search results", "base.cancel": "Cancel", "base.picCaptchaPlaceholder": "Enter captcha code", "base.smsCaptchaPlaceholder": "{0}-digit verification code", "base.getVerificationCode": "Get a verification code", "base.retrieveAfter": "{0}s", "base.retrieve": "Resend", "base.getVoice": "Use voice verification code", "base.voiceSMSCaptcha_send": "System will send you verification code via free call. You can retry after 60s.", "base.voiceSMSCaptchaConfirm": "We will send you a voice verification code to your registered phone number. Please answer the call and remember the {0}-digit verification code.", "base.receiveVoiceConfirm": "Send"}, "zh-TW": {"activate.enterPhoneSmsValidate": "請輸入手機{0}獲取的短信驗證碼", "base.Iknow": "知道了", "base.confirmToContinue": "由於妳長時間未進行操作，請刷新或點擊確認後繼續使用", "base.confirm": "確定", "base.loginFail": "登入已失效，請重新登入", "base.alert.server500.title": "伺服器開了一點小差，已通知程式猿小哥處理，請稍等片刻或重新整理重試。", "base.alert.server500.content": "如有疑問，請聯絡客服", "base.alert.server502": "伺服器錯誤，請重試 (502)", "base.alert.server404": "網路出錯(404)，請檢查後重試", "base.alert.server403": "你無許可權訪問", "base.pageExpired": "當前頁面已失效，請點擊{0}重試", "base.ok": "確定", "base.passwordStrengthMid": "密碼必須為8-32位字母和數字的組合", "base.passwordStartOrEndWithSpace": "密碼首尾不能為空格", "base.passwordWithForbiddenCharacter": "密碼包含不允許的字符(<a class=\"get-detail\" href=\"http://help.fangcloud.com/posts/view/57412/\" target=\"_blank\">了解詳情</a>)", "base.time.justNow": "剛剛", "base.time.beforeSeconds": "{0}秒前", "base.time.beforeMinutes": "{0}分鐘前", "base.time.beforeHours": "{0}小時前", "base.time.yesterday": "昨天", "base.time.theDayBefore": "前天", "unit.second": "{0}秒", "unit.minute": "{0}分鐘", "unit.hour": "{0}小時", "unit.day": "{0}天", "base.hidePassword": "隱藏密碼", "base.showPassword": "顯示密碼", "login.ent.expired.sub": "若要查看雲端文件，可以在網頁端下載。", "base.openBrowser": "打開網頁端", "encryption.versionLimit": "你所在的企業開啟了檔案防洩漏，需下載特定版本客戶端方可使用", "encryption.downloadNow": "立即下載", "base.passwordWithCaptial": "密碼長度為{0}-32個字符，必須包含大寫字母、小寫字母和數字", "base.passwordWithSpecial": "密碼長度為{0}-32個字符，必須包含字母、數字和特殊字符(<a class=\"get-detail\" href=\"http://help.fangcloud.com/posts/view/57412/\" target=\"_blank\">了解詳情</a>)", "base.passwordWithCaptialAndSpecial": "密碼長度為{0}-32個字符，必須包含大寫字母、小寫字母、數字和特殊字符(<a class=\"get-detail\" href=\"http://help.fangcloud.com/posts/view/57412/\" target=\"_blank\">了解詳情</a>)", "base.passwordNormal": "密碼長度為{0}-32個字符，必須包含字母和數字", "forgot.byPhone": "通過手機號{0}找回密碼", "forgot.byEmail": "通過郵箱{0}找回密碼", "forgot.validateMode": "驗證方式：", "forgot.phoneValidate": "手機驗證", "forgot.emailValidate": "郵箱驗證", "forgot.resetSuccess": "重設成功！請使用新密碼登入", "forgot.login": "立即登入", "forgot.resetEmailSended": "重設密碼鏈接已發送至{0}<br />請登入妳的郵箱查看並按照郵件內容操作", "forgot.resetEmailSendedTips": "郵件可能會被妳的郵箱攔截，如在收件箱中未找到，可嘗試在垃圾郵件中查看。", "forgot.returnLogin": "返回到登入", "loginVerify.helperDes1": "1.請確認妳設置接收驗證碼的微信號是否關註了億方雲公眾號，如未關註，請重新搜索並關註“億方雲官方服務平臺”。關註後點擊“重新發送驗證碼”完成驗證即可。", "loginVerify.helperDes2": "2.妳也可以聯系企業管理員或客服幫妳關閉二次驗證", "validate.wrongPhone": "請輸入有效的手機號", "auth.register.phoneError": "請輸入正確的手機號", "auth.register.sms_captchaError": "請輸入正確的驗證碼", "auth.register.passwordError": "請設置正確的密碼，8~32位數字和字母的組合", "auth.register.emailError": "請輸入正確的郵箱", "auth.register.freeTrial": "免費使用，立即體驗", "auth.register.freeExperienceFor15Days": "免費體驗15天", "auth.register.leaveMessageForContact": "請留下聯系方式，我們將盡快聯系妳", "auth.register.normalTip": "若妳的企業已註冊，聯系管理員邀請即可，無需單獨註冊", "auth.register.enterpirseContactNumber": "妳也可以撥打 <b>************</b> 聯系我們", "base.needAcceptProtocal": "請閱讀並接受服務條款和服務等級協議", "base.emptyValue": "請輸入正確的信息後再試", "auth.register.phoneRegistered": "該手機號已被註冊，請直接<a href=\"{0}\">登入</a>或聯系客服", "auth.register.phoneRegisteredAsPersonal": "你已註冊個人帳號，請直接<a href=\"{0}\">登入</a>或前往網頁版、PC客戶端登入後進行套餐升級", "auth.register.phoneAlreadyAccupied": "你已被邀請加入{0} <a href=\"{1}\">立即激活</a>", "auth.register.phoneAlreadyAccupiedAndExpired": "你在30天前被邀請加入{0}，請聯繫管理員重發邀請後去<a href=\"{1}\">激活賬號</a>", "settings.confirmLoginPassword": "驗證身份", "settings.confirmLoginPasswordHint": "為保障妳的帳號安全，請輸入登入密碼進行身份驗證。", "base.inputLoginPassword": "請輸入妳的登入密碼", "base.next": "下壹步", "settings.setLoginPassword": "設置獨立登入密碼", "settings.setLoginPasswordHint1": "設置獨立登入密碼，可配合綁定的郵箱/手機登入。", "settings.pleaseSetPassword": "獨立登入密碼", "settings.twoStepVerification.selectVerifyTitle": "選擇二次驗證方式", "settings.twoStepVerification.wechat": "微信", "settings.twoStepVerification.wechatSetDes": "使用微信掃描並關註我們的微信公眾號，我們會將驗證碼通過公眾號發送給妳", "settings.twoStepVerification.sms": "手機短信", "settings.twoStepVerification.smsSetDes": "設置壹個手機號，我們會將驗證碼通過短信方式發送給妳。", "settings.twoStepVerification.google": "谷歌身份驗證器", "settings.twoStepVerification.googleSetDes": "妳可以下載身份驗證器應用來獲取驗證碼，即使手機未連接網絡也無妨。", "settings.twoStepVerification.recommend": "推薦", "settings.twoStepVerification.wechatDes1": "1. 使用微信掃描二維碼並關註我們的微信公眾號，妳將會收到驗證碼。", "settings.twoStepVerification.wechatDes2": "如已關註公眾號，可直接掃描", "settings.twoStepVerification.wechatDes3": "2. 輸入微信公眾號發給妳的6位驗證碼。", "base.twoStepVerification.setSubmit": "完成設置", "base.previous": "上壹步", "settings.twoStepVerification.setByWechatTitle": "設置微信", "settings.twoStepVerification.setBySMSTitle": "設置手機號", "settings.twoStepVerification.googleIos": "iPhone用護", "settings.twoStepVerification.googleIosDes1": "1.在iPhone上，打開App Store。", "settings.twoStepVerification.googleIosDes2": "2.搜索谷歌身份驗證器(Google Authenticator)", "settings.twoStepVerification.googleIosDes3": "3.下載並安裝應用。", "settings.twoStepVerification.googleAndroid": "Android用護", "twoStepVerification.googleAndroidDes1": "1.在手機上訪問Google Play或其它商店。", "twoStepVerification.googleAndroidDes2": "2.搜索谷歌身份驗證器(Google Authenticator)", "twoStepVerification.googleAndroidDes3": "3.下載並安裝應用。", "settings.twoStepVerification.googleHelp": "如何安裝google身份驗證器？", "settings.twoStepVerification.downloadGoogleTitle": "下載谷歌身份驗證器", "settings.twoStepVerification.googleVerifyQRcodeIosDes": "1. 打開谷歌身份驗證器，點擊+通過掃描二維碼完成設置", "settings.twoStepVerification.googleVerifyQRcodeDes": "1.打開應用，點擊菜單，選擇“設置帳戶”，再掃描條形碼即可。", "settings.twoStepVerification.googleVerifyCantQRcode": "無法掃描？", "settings.twoStepVerification.googleVerifyToManually": "手動設置", "settings.twoStepVerification.googleVerifyManuallyIosDes1": "1.打開谷歌身份驗證器，點擊+", "settings.twoStepVerification.googleVerifyManuallyIosDes2": "建議妳在“帳戶”中輸入產品和帳戶名，如“{0}：<EMAIL>”", "settings.twoStepVerification.googleVerifyManuallyIosDes3": "在“密鑰”中輸入16位密鑰：", "settings.twoStepVerification.googleVerifyManuallyAndroidDes1": "1.打開應用，點擊菜單，選擇“設置賬戶”", "settings.twoStepVerification.googleVerifyManuallyAndroidDes2": "建議妳在“賬戶”中輸入產品和帳戶名，如“{0}：<EMAIL>”", "settings.twoStepVerification.googleVerifyManuallyAndroidDes3": "在“密鑰”中輸入16位密鑰", "settings.twoStepVerification.googleVerifyToQRcode": "返回掃描設置", "settings.twoStepVerification.googleVerifyInputCaptcha": "2. 輸入谷歌驗證器的6位驗證碼", "settings.twoStepVerification.setByGoogleTitle": "設置谷歌身份驗證器", "settings.twoStepVerification.modifyVerifySuccessTitle": "二次驗證已修改", "settings.twoStepVerification.setVerifySuccessTitle": "二次驗證已開啟", "settings.twoStepVerification.setVerifySuccessDes": "二次驗證已開啟，每次登入{0}時，除帳號密碼之外，還需要輸入安全驗證碼。", "settings.twoStepVerification.closeVerifySuccess": "二次驗證已關閉", "settings.thirdPartyBindFailure": "綁定失敗", "settings.editPassword": "修改密碼", "settings.oldPassword": "舊密碼", "settings.newPassword": "新密碼", "settings.confirmPassword": "確認密碼", "settings.editPasswordSuccess": "密碼修改成功，請重新登入", "settings.email": "郵箱：", "settings.phone": "手機：", "settings.emptyEmailInfo": "綁定且驗證後可用郵箱登入", "settings.emptyPhoneInfo": "綁定後可用手機登入", "settings.unvalidate": "未驗證", "settings.unvalidateCredentialUsage": "驗證後可用於登入、找回密碼", "settings.validateTime": "驗證時間：{0}", "settings.validateCredentialUsage": "可用於登入、找回密碼", "settings.validate": "立即驗證", "settings.bindImmediately": "立即綁定", "settings.phoneNotAvaliable": "該手機號已被其它帳號占用，請修改。", "settings.emailNotAvaliable": "該郵箱已被其它帳號占用，請修改。", "base.cancelText": "下次再說", "settings.editNow": "立即修改", "settings.forbiddenEditEmail": "暫時無法修改郵箱。請在手機號綁定成功後再試。", "settings.editPhone": "修改手機", "settings.validatePhone": "驗證手機", "settings.bindPhone": "綁定手機", "settings.editPhoneSuccess": "手機號已修改成功", "settings.validatePhoneSuccess": "手機號已驗證成功", "settings.bindPhoneSuccess": "手機號已綁定成功", "settings.currentCredential": "當前綁定：{0}", "settings.mobilePlaceholder": "請輸入妳的手機號", "base.submit": "提交", "settings.editEmail": "修改郵箱", "settings.validateEmail": "驗證郵箱", "settings.bindEmail": "綁定郵箱", "settings.enterNewEmail": "請輸入新的郵箱", "settings.emailSended": "驗證郵件已發送至{0}，請登入妳的郵箱查看。驗證後，該郵箱可以用於登入、找回密碼。", "settings.emailSendedTips": "郵件可能會被攔截，如未在收件箱中找到，可嘗試在垃圾郵件中查找", "settings.unbind": "解綁", "settings.bind": "綁定", "settings.qiyeweixinLabel": "企業微信：", "settings.dingdingLabel": "釘釘：", "settings.dingding": "釘釘", "settings.qiyeweixin": "企業微信", "settings.qihoo360": "360", "settings.qihoo360Label": "360賬號:", "settings.unbindThirdConfirmMain": "妳確定要解綁{0}帳號“{1}”嗎？", "settings.unbindConfirmSub": "解綁後，將無法使用該帳號登入", "settings.device.fromAndroid": "（Android App）", "settings.device.fromIos": "（iOS App）", "settings.device.fromWindowsSync": "（Windows同步端）", "settings.device.fromMacSync": "（Mac同步端）", "settings.device.fromMacDesktop": "（Mac客護端）", "settings.device.fromWindowsDesktop": "（Windows客護端）", "settings.device.deviceName": "設備名稱", "settings.device.deivceLastLogin": "最近訪問", "settings.device.deivceLastLocation": "最近訪問地", "settings.device.deivceOperation": "操作", "settings.device.creditable": "可信", "settings.device.current": "當前", "settings.device.ip": "IP地址：{0}", "base.delete": "刪除", "settings.device.noresult": "暫無數據", "settings.device.showMore": "展開全部", "settings.device.deleteIsCreditable": "在該設備上將退出登入，妳需要重新登入才能繼續訪問，再次登入需要進行二次驗證。", "settings.device.deleteNotCreditable": "在該設備上將退出登入，妳需要重新登入才能繼續訪問。", "settings.device.deleteDevice": "刪除此設備", "settings.device.hideMore": "收起", "settings.twoStepVerification.typePhone": "手機短信驗證", "settings.twoStepVerification.typePhoneNumber": "（尾號為 {0}）", "settings.twoStepVerification.typeWechat": "微信公眾號驗證", "settings.twoStepVerification.typeGoogle": "谷歌驗證器驗證", "settings.twoStepVerification.hasOpen": "二次驗證已開啟", "settings.twoStepVerification.validateTime": "（設置於 {0}）", "settings.twoStepVerification.toClose": "關閉二次驗證", "settings.twoStepVerification.type": "驗證方式：", "base.modify": "修改", "settings.twoStepVerification.info": "二次驗證為妳的帳戶增加壹層安全保護。啟用二次驗證後，每次登入億方雲時，除帳號密碼之外，還需要輸入安全驗證碼（驗證碼將發送到妳的手機上）。", "settings.twoStepVerification.toSet": "開啟二次驗證", "settings.twoStepVerification.disableToClose": "管理員開啟了“企業成員二次驗證”功能，妳無法單獨關閉二次驗證。", "base.yfy": "億方雲", "base.countrySelectorPlaceholder": "搜索妳的國家/地區、區號", "base.searchEmpty": "無符合條件的搜索結果", "base.cancel": "取消", "base.picCaptchaPlaceholder": "請輸入圖形驗證碼", "base.smsCaptchaPlaceholder": "請輸入{0}位驗證碼", "base.getVerificationCode": "獲取驗證碼", "base.retrieveAfter": "{0}秒後重新獲取", "base.retrieve": "重新獲取驗證碼", "base.getVoice": "接收語音驗證碼", "base.voiceSMSCaptcha_send": "系統將通過免費電話給您發送語音驗證碼，60s內未收到可重新獲取", "base.voiceSMSCaptchaConfirm": "我們將以電話的方式發送語音驗證碼。請註意接聽並記下{0}位驗證碼。", "base.receiveVoiceConfirm": "接收語音驗證碼"}, "zh-CN-education": {"loginVerify.helperDes2": "2.你也可以联系学校管理员或客服帮你关闭二次验证", "auth.register.normalTip": "若你的学校已注册，联系管理员邀请即可，无需单独注册"}, "zh-TW-education": {"loginVerify.helperDes2": "2.妳也可以聯系學校管理員或客服幫妳關閉二次驗證", "auth.register.normalTip": "若妳的學校已註冊，聯系管理員邀請即可，無需單獨註冊"}, "en-education": {"loginVerify.helperDes2": "2. You can also contact your school admin or customer service team to help disable it", "auth.register.normalTip": "If your school has finished registration, please contact the administrator for invitation. Individual registration isn’t necessary"}}