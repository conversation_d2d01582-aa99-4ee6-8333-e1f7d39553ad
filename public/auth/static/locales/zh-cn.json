{"zh-CN": {"activate.enterPhoneSmsValidate": "请输入手机{0}获取的短信验证码", "forgot.byPhone": "通过手机号{0}找回密码", "forgot.byEmail": "通过邮箱{0}找回密码", "forgot.validateMode": "验证方式：", "forgot.phoneValidate": "手机验证", "forgot.emailValidate": "邮箱验证", "forgot.resetSuccess": "重设成功！请使用新密码登录", "forgot.login": "立即登录", "forgot.resetEmailSended": "重设密码链接已发送至{0}<br />请登录你的邮箱查看并按照邮件内容操作", "forgot.resetEmailSendedTips": "邮件可能会被你的邮箱拦截，如在收件箱中未找到，可尝试在垃圾邮件中查看。", "forgot.returnLogin": "返回到登录", "loginVerify.helperDes1": "1.请确认你设置接收验证码的微信号是否关注了亿方云公众号，如未关注，请重新搜索并关注“亿方云官方服务平台”。关注后点击“重新发送验证码”完成验证即可。", "loginVerify.helperDes2": "2.你也可以联系企业管理员或客服帮你关闭二次验证", "validate.wrongPhone": "请输入有效的手机号", "auth.register.phoneError": "请输入正确的手机号", "auth.register.sms_captchaError": "请输入正确的验证码", "auth.register.passwordError": "请设置正确的密码，8~32位数字和字母的组合", "auth.register.emailError": "请输入正确的邮箱", "auth.register.phoneRegistered": "该手机号已被注册，请直接<a href=\"{0}\">登录</a>或联系客服", "auth.register.phoneRegisteredAsPersonal": "你已注册个人帐号，请直接<a href=\"{0}\">登录</a>或前往网页版、PC客户端登录后进行套餐升级", "auth.register.phoneAlreadyAccupied": "你已被邀请加入{0} <a href=\"{1}\">立即激活</a>", "auth.register.phoneAlreadyAccupiedAndExpired": "你在30天前被邀请加入{0}，请联系管理员重发邀请后去<a href=\"{1}\">激活账号</a>", "base.needAcceptProtocal": "请阅读并接受服务条款和服务等级协议", "base.emptyValue": "请输入正确的信息后再试", "settings.twoStepVerification.modifyVerifySuccessTitle": "二次验证已修改", "settings.twoStepVerification.setVerifySuccessTitle": "二次验证已开启", "settings.twoStepVerification.setVerifySuccessDes": "二次验证已开启，每次登录{0}时，除帐号密码之外，还需要输入安全验证码。", "settings.thirdPartyBindFailure": "绑定失败", "settings.editPassword": "修改密码", "settings.oldPassword": "旧密码", "settings.newPassword": "新密码", "settings.confirmPassword": "确认密码", "base.submit": "提交", "settings.editPasswordSuccess": "密码修改成功，请重新登录", "base.confirm": "确定", "settings.email": "邮箱：", "settings.phone": "手机：", "settings.emptyEmailInfo": "绑定且验证后可用邮箱登录", "settings.emptyPhoneInfo": "绑定后可用手机登录", "settings.unvalidate": "未验证", "settings.unvalidateCredentialUsage": "验证后可用于登录、找回密码", "settings.validateTime": "验证时间：{0}", "settings.validateCredentialUsage": "可用于登录、找回密码", "settings.validate": "立即验证", "settings.bindImmediately": "立即绑定", "settings.phoneNotAvaliable": "该手机号已被其它帐号占用，请修改。", "settings.emailNotAvaliable": "该邮箱已被其它帐号占用，请修改。", "base.cancelText": "下次再说", "settings.editNow": "立即修改", "settings.forbiddenEditEmail": "暂时无法修改邮箱。请在手机号绑定成功后再试。", "settings.editPhone": "修改手机", "settings.validatePhone": "验证手机", "settings.bindPhone": "绑定手机", "settings.editPhoneSuccess": "手机号已修改成功", "settings.validatePhoneSuccess": "手机号已验证成功", "settings.bindPhoneSuccess": "手机号已绑定成功", "settings.currentCredential": "当前绑定：{0}", "settings.mobilePlaceholder": "请输入你的手机号", "settings.editEmail": "修改邮箱", "settings.validateEmail": "验证邮箱", "settings.bindEmail": "绑定邮箱", "settings.enterNewEmail": "请输入新的邮箱", "settings.emailSended": "验证邮件已发送至{0}，请登录你的邮箱查看。验证后，该邮箱可以用于登录、找回密码。", "settings.emailSendedTips": "邮件可能会被拦截，如未在收件箱中找到，可尝试在垃圾邮件中查找", "settings.unbind": "解绑", "settings.qiyeweixinLabel": "企业微信：", "settings.dingdingLabel": "钉钉：", "settings.qihoo360Label": "360账号：", "settings.dingding": "钉钉", "settings.qiyeweixin": "企业微信", "settings.qihoo360": "360", "settings.unbindThirdConfirmMain": "你确定要解绑{0}帐号“{1}”吗？", "settings.unbindConfirmSub": "解绑后，将无法使用该帐号登录", "settings.device.fromAndroid": "（Android App）", "settings.device.fromIos": "（iOS App）", "settings.device.fromWindowsSync": "（Windows同步端）", "settings.device.fromMacSync": "（Mac同步端）", "settings.device.fromWindowsDesktop": "（Windows客户端）", "settings.device.fromMacDesktop": "（Mac客户端）", "settings.device.deviceName": "设备名称", "settings.device.deivceLastLogin": "最近访问", "settings.device.deivceLastLocation": "最近访问地", "settings.device.deivceOperation": "操作", "settings.device.creditable": "可信", "settings.device.current": "当前", "settings.device.ip": "IP地址：{0}", "base.delete": "删除", "settings.device.noresult": "暂无数据", "settings.device.showMore": "展开全部", "settings.device.deleteIsCreditable": "在该设备上将退出登录，你需要重新登录才能继续访问，再次登录需要进行二次验证。", "settings.device.deleteNotCreditable": "在该设备上将退出登录，你需要重新登录才能继续访问。", "settings.device.deleteDevice": "删除此设备", "settings.device.hideMore": "收起", "settings.twoStepVerification.typePhone": "手机短信验证", "settings.twoStepVerification.typePhoneNumber": "（尾号为 {0}）", "settings.twoStepVerification.typeWechat": "微信公众号验证", "settings.twoStepVerification.typeGoogle": "谷歌验证器验证", "settings.twoStepVerification.hasOpen": "二次验证已开启", "settings.twoStepVerification.validateTime": "（设置于 {0}）", "settings.twoStepVerification.toClose": "关闭二次验证", "settings.twoStepVerification.type": "验证方式：", "base.modify": "修改", "settings.twoStepVerification.info": "二次验证为你的帐户增加一层安全保护。启用二次验证后，每次登录亿方云时，除帐号密码之外，还需要输入安全验证码（验证码将发送到你的手机上）。", "settings.twoStepVerification.toSet": "开启二次验证", "settings.twoStepVerification.disableToClose": "管理员开启了“企业成员二次验证”功能，你无法单独关闭二次验证。", "base.yfy": "亿方云", "base.Iknow": "知道了", "base.confirmToContinue": "由于你长时间未进行操作，请刷新或点击确认后继续使用", "base.loginFail": "登录已失效，请重新登录", "base.alert.server500.title": "服务器开了一点小差，已通知程序猿小哥处理，请稍等片刻或刷新重试。", "base.alert.server500.content": "如有疑问，请联系客服", "base.alert.server502": "服务器错误，请重试 (502)", "base.alert.server404": "网络出错(404)，请检查后重试", "base.alert.server403": "你无权限访问", "base.pageExpired": "当前页面已失效，请点击{0}重试", "base.ok": "确定", "base.passwordStrengthMid": "密码必须为8-32位字母和数字的组合", "base.passwordStartOrEndWithSpace": "密码首尾不能为空格", "base.passwordWithForbiddenCharacter": "密码包含不允许的字符(<a href=\"http://help.fangcloud.com/posts/view/57412/\" target=\"_blank\">了解详情</a>)", "base.time.justNow": "刚刚", "base.time.beforeSeconds": "{0}秒前", "base.time.beforeMinutes": "{0}分钟前", "base.time.beforeHours": "{0}小时前", "base.time.yesterday": "昨天", "base.time.theDayBefore": "前天", "unit.second": "{0}秒", "unit.minute": "{0}分钟", "unit.hour": "{0}小时", "unit.day": "{0}天", "base.hidePassword": "隐藏密码", "base.showPassword": "显示密码", "login.ent.expired.sub": "若要查看云端文件，可以在网页端下载。", "base.openBrowser": "打开网页端", "encryption.versionLimit": "你所在的企业开启了文件防泄漏，需下载特定版本客户端方可使用", "encryption.downloadNow": "立即下载", "base.passwordWithCaptial": "密码长度为{0}-32个字符，必须包含大写字母、小写字母和数字", "base.passwordWithSpecial": "密码长度为{0}-32个字符，必须包含字母、数字和特殊字符(<a class=\"get-detail\" href=\"http://help.fangcloud.com/posts/view/57412/\" target=\"_blank\">了解详情</a>)", "base.passwordWithCaptialAndSpecial": "密码长度为{0}-32个字符，必须包含大写字母、小写字母、数字和特殊字符(<a class=\"get-detail\" href=\"http://help.fangcloud.com/posts/view/57412/\" target=\"_blank\">了解详情</a>)", "base.passwordNormal": "密码长度为{0}-32个字符，必须包含字母和数字", "base.countrySelectorPlaceholder": "搜索你的国家/地区、区号", "base.searchEmpty": "无符合条件的搜索结果", "base.cancel": "取消", "base.picCaptchaPlaceholder": "请输入图形验证码", "base.smsCaptchaPlaceholder": "请输入{0}位验证码", "base.getVerificationCode": "获取验证码", "base.retrieveAfter": "{0}秒后重新获取", "base.retrieve": "重新获取验证码", "base.getVoice": "接收语音验证码", "base.voiceSMSCaptcha_send": "系统将通过免费电话给您发送语音验证码，60s内未收到可重新获取", "base.voiceSMSCaptchaConfirm": "我们将以电话的方式发送语音验证码。请注意接听并记下{0}位验证码。", "base.receiveVoiceConfirm": "接收语音验证码", "settings.confirmLoginPassword": "验证身份", "settings.confirmLoginPasswordHint": "为保障你的帐号安全，请输入登录密码进行身份验证。", "base.inputLoginPassword": "请输入你的登录密码", "base.next": "下一步", "settings.setLoginPassword": "设置独立登录密码", "settings.setLoginPasswordHint1": "设置独立登录密码，可配合绑定的邮箱/手机登录。", "settings.pleaseSetPassword": "独立登录密码", "settings.twoStepVerification.selectVerifyTitle": "选择二次验证方式", "settings.twoStepVerification.wechat": "微信", "settings.twoStepVerification.wechatSetDes": "使用微信扫描并关注我们的微信公众号，我们会将验证码通过公众号发送给你", "settings.twoStepVerification.sms": "手机短信", "settings.twoStepVerification.smsSetDes": "设置一个手机号，我们会将验证码通过短信方式发送给你。", "settings.twoStepVerification.google": "谷歌身份验证器", "settings.twoStepVerification.googleSetDes": "你可以下载身份验证器应用来获取验证码，即使手机未连接网络也无妨。", "settings.twoStepVerification.recommend": "推荐", "settings.twoStepVerification.wechatDes1": "1. 使用微信扫描二维码并关注我们的微信公众号，你将会收到验证码。", "settings.twoStepVerification.wechatDes2": "如已关注公众号，可直接扫描", "settings.twoStepVerification.wechatDes3": "2. 输入微信公众号发给你的6位验证码。", "base.twoStepVerification.setSubmit": "完成设置", "base.previous": "上一步", "settings.twoStepVerification.setByWechatTitle": "设置微信", "settings.twoStepVerification.setBySMSTitle": "设置手机号", "settings.twoStepVerification.googleIos": "iPhone用户", "settings.twoStepVerification.googleIosDes1": "1.在iPhone上，打开App Store。", "settings.twoStepVerification.googleIosDes2": "2.搜索谷歌身份验证器(Google Authenticator)", "settings.twoStepVerification.googleIosDes3": "3.下载并安装应用。", "settings.twoStepVerification.googleAndroid": "Android用户", "twoStepVerification.googleAndroidDes1": "1.在手机上访问Google Play或其它商店。", "twoStepVerification.googleAndroidDes2": "2.搜索谷歌身份验证器(Google Authenticator)", "twoStepVerification.googleAndroidDes3": "3.下载并安装应用。", "settings.twoStepVerification.googleHelp": "如何安装google身份验证器？", "settings.twoStepVerification.downloadGoogleTitle": "下载谷歌身份验证器", "settings.twoStepVerification.googleVerifyQRcodeIosDes": "1. 打开谷歌身份验证器，点击+通过扫描二维码完成设置", "settings.twoStepVerification.googleVerifyQRcodeDes": "1.打开应用，点击菜单，选择“设置帐户”，再扫描条形码即可。", "settings.twoStepVerification.googleVerifyCantQRcode": "无法扫描？", "settings.twoStepVerification.googleVerifyToManually": "手动设置", "settings.twoStepVerification.googleVerifyManuallyIosDes1": "1.打开谷歌身份验证器，点击+", "settings.twoStepVerification.googleVerifyManuallyIosDes2": "建议你在“帐户”中输入产品和帐户名，如“{0}：<EMAIL>”", "settings.twoStepVerification.googleVerifyManuallyIosDes3": "在“密钥”中输入16位密钥：", "settings.twoStepVerification.googleVerifyManuallyAndroidDes1": "1.打开应用，点击菜单，选择“设置账户”", "settings.twoStepVerification.googleVerifyManuallyAndroidDes2": "建议你在“账户”中输入产品和帐户名，如“{0}：<EMAIL>”", "settings.twoStepVerification.googleVerifyManuallyAndroidDes3": "在“密钥”中输入16位密钥", "settings.twoStepVerification.googleVerifyToQRcode": "返回扫描设置", "settings.twoStepVerification.googleVerifyInputCaptcha": "2. 输入谷歌验证器的6位验证码", "settings.twoStepVerification.setByGoogleTitle": "设置谷歌身份验证器", "settings.twoStepVerification.closeVerifySuccess": "二次验证已关闭"}}