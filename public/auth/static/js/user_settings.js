webpackJsonp([0],{

/***/ 13:
/***/ (function(module, exports) {

module.exports = {"zh-CN":{"activate.enterPhoneSmsValidate":"请输入手机{0}获取的短信验证码","forgot.byPhone":"通过手机号{0}找回密码","forgot.byEmail":"通过邮箱{0}找回密码","forgot.validateMode":"验证方式：","forgot.phoneValidate":"手机验证","forgot.emailValidate":"邮箱验证","forgot.resetSuccess":"重设成功！请使用新密码登录","forgot.login":"立即登录","forgot.resetEmailSended":"重设密码链接已发送至{0}<br />请登录你的邮箱查看并按照邮件内容操作","forgot.resetEmailSendedTips":"邮件可能会被你的邮箱拦截，如在收件箱中未找到，可尝试在垃圾邮件中查看。","forgot.returnLogin":"返回到登录","loginVerify.helperDes1":"1.请确认你设置接收验证码的微信号是否关注了亿方云公众号，如未关注，请重新搜索并关注“亿方云官方服务平台”。关注后点击“重新发送验证码”完成验证即可。","loginVerify.helperDes2":"2.你也可以联系企业管理员或客服帮你关闭二次验证","validate.wrongPhone":"请输入有效的手机号","auth.register.freeTrial":"免费使用，立即体验","auth.register.freeExperienceFor15Days":"免费体验15天","auth.register.leaveMessageForContact":"请留下联系方式，我们将尽快联系你","auth.register.normalTip":"若你的企业(团队)已注册，联系管理员邀请即可，无需单独注册","auth.register.enterpirseContactNumber":"你也可以拨打 <b>************</b> 联系我们","base.emptyValue":"请有效填写所有信息后再试","base.needAcceptProtocal":"请阅读并接受服务条款和服务等级协议","auth.register.phoneRegistered":"该手机号已被注册，请直接<a href=\"{0}\">登录</a>或联系客服","auth.register.phoneRegisteredAsPersonal":"你已注册个人帐号，请直接<a href=\"{0}\">登录</a>或前往网页版、PC客户端登录后进行套餐升级","settings.twoStepVerification.modifyVerifySuccessTitle":"二次验证已修改","settings.twoStepVerification.setVerifySuccessTitle":"二次验证已开启","settings.twoStepVerification.setVerifySuccessDes":"二次验证已开启，每次登录{0}时，除帐号密码之外，还需要输入安全验证码。","settings.thirdPartyBindFailure":"绑定失败","settings.editPassword":"修改密码","settings.oldPassword":"旧密码","settings.newPassword":"新密码","settings.confirmPassword":"确认密码","base.submit":"提交","settings.editPasswordSuccess":"密码修改成功，请重新登录","base.confirm":"确定","settings.email":"邮箱：","settings.phone":"手机：","settings.emptyEmailInfo":"绑定且验证后可用邮箱登录","settings.emptyPhoneInfo":"绑定后可用手机登录","settings.unvalidate":"未验证","settings.unvalidateCredentialUsage":"验证后可用于登录、找回密码","settings.validateTime":"验证时间：{0}","settings.validateCredentialUsage":"可用于登录、找回密码","settings.validate":"立即验证","settings.bindImmediately":"立即绑定","settings.phoneNotAvaliable":"该手机号已被其它帐号占用，请修改。","settings.emailNotAvaliable":"该邮箱已被其它帐号占用，请修改。","base.cancelText":"下次再说","settings.editNow":"立即修改","settings.forbiddenEditEmail":"暂时无法修改邮箱。请在手机号绑定成功后再试。","settings.editPhone":"修改手机","settings.validatePhone":"验证手机","settings.bindPhone":"绑定手机","settings.editPhoneSuccess":"手机号已修改成功","settings.validatePhoneSuccess":"手机号已验证成功","settings.bindPhoneSuccess":"手机号已绑定成功","settings.currentCredential":"当前绑定：{0}","settings.mobilePlaceholder":"请输入你的手机号","settings.editEmail":"修改邮箱","settings.validateEmail":"验证邮箱","settings.bindEmail":"绑定邮箱","settings.enterNewEmail":"请输入新的邮箱","settings.emailSended":"验证邮件已发送至{0}，请登录你的邮箱查看。验证后，该邮箱可以用于登录、找回密码。","settings.emailSendedTips":"邮件可能会被拦截，如未在收件箱中找到，可尝试在垃圾邮件中查找","settings.unbind":"解绑","settings.qiyeweixinLabel":"企业微信：","settings.dingdingLabel":"钉钉：","settings.dingding":"钉钉","settings.qiyeweixin":"企业微信","settings.unbindThirdConfirmMain":"你确定要解绑{0}帐号“{1}”吗？","settings.unbindConfirmSub":"解绑后，将无法使用该帐号登录","settings.device.fromAndroid":"（Android App）","settings.device.fromIos":"（iOS App）","settings.device.fromWindowsSync":"（Windows同步端）","settings.device.fromMacSync":"（Mac同步端）","settings.device.fromWindowsDesktop":"（Windows客户端）","settings.device.fromMacDesktop":"（Mac客户端）","settings.device.deviceName":"设备名称","settings.device.deivceLastLogin":"最近访问","settings.device.deivceLastLocation":"最近访问地","settings.device.deivceOperation":"操作","settings.device.creditable":"可信","settings.device.current":"当前","settings.device.ip":"IP地址：{0}","base.delete":"删除","settings.device.noresult":"暂无数据","settings.device.showMore":"展开全部","settings.device.deleteIsCreditable":"在该设备上将退出登录，你需要重新登录才能继续访问，再次登录需要进行二次验证。","settings.device.deleteNotCreditable":"在该设备上将退出登录，你需要重新登录才能继续访问。","settings.device.deleteDevice":"删除此设备","settings.device.hideMore":"收起","settings.twoStepVerification.typePhone":"手机短信验证","settings.twoStepVerification.typePhoneNumber":"（尾号为 {0}）","settings.twoStepVerification.typeWechat":"微信公众号验证","settings.twoStepVerification.typeGoogle":"谷歌验证器验证","settings.twoStepVerification.hasOpen":"二次验证已开启","settings.twoStepVerification.validateTime":"（设置于 {0}）","settings.twoStepVerification.toClose":"关闭二次验证","settings.twoStepVerification.type":"验证方式：","base.modify":"修改","settings.twoStepVerification.info":"二次验证为你的帐户增加一层安全保护。启用二次验证后，每次登录亿方云时，除帐号密码之外，还需要输入安全验证码（验证码将发送到你的手机上）。","settings.twoStepVerification.toSet":"开启二次验证","settings.twoStepVerification.disableToClose":"管理员开启了“企业成员二次验证”功能，你无法单独关闭二次验证。","base.yfy":"亿方云","base.Iknow":"知道了","base.confirmToContinue":"由于你长时间未进行操作，请刷新或点击确认后继续使用","base.loginFail":"登录已失效，请重新登录","base.alert.server500.title":"服务器开了一点小差，已通知程序猿小哥处理，请稍等片刻或刷新重试。","base.alert.server500.content":"如有疑问，请联系客服","base.alert.server502":"服务器错误，请重试 (502)","base.alert.server404":"网络出错(404)，请检查后重试","base.alert.server403":"你无权限访问","base.pageExpired":"当前页面已失效，请点击{0}重试","base.ok":"确定","base.passwordStrengthMid":"密码必须为8-32位字母和数字的组合","base.passwordStartOrEndWithSpace":"密码首尾不能为空格","base.passwordWithForbiddenCharacter":"密码包含不允许的字符(<a href=\"http://help.fangcloud.com/posts/view/57412/\" target=\"_blank\">了解详情</a>)","base.time.justNow":"刚刚","base.time.beforeSeconds":"{0}秒前","base.time.beforeMinutes":"{0}分钟前","base.time.beforeHours":"{0}小时前","base.time.yesterday":"昨天","base.time.theDayBefore":"前天","unit.second":"{0}秒","unit.minute":"{0}分钟","unit.hour":"{0}小时","unit.day":"{0}天","base.hidePassword":"隐藏密码","base.showPassword":"显示密码","encryption.versionLimit":"你所在的企业开启了文件防泄漏，需下载特定版本客户端方可使用","encryption.downloadNow":"立即下载","base.passwordWithCaptial":"密码长度为{0}-32个字符，必须包含大写字母、小写字母和数字","base.passwordWithSpecial":"密码长度为{0}-32个字符，必须包含字母、数字和特殊字符(<a class=\"get-detail\" href=\"http://help.fangcloud.com/posts/view/57412/\" target=\"_blank\">了解详情</a>)","base.passwordWithCaptialAndSpecial":"密码长度为{0}-32个字符，必须包含大写字母、小写字母、数字和特殊字符(<a class=\"get-detail\" href=\"http://help.fangcloud.com/posts/view/57412/\" target=\"_blank\">了解详情</a>)","base.passwordNormal":"密码长度为{0}-32个字符，必须包含字母和数字","base.countrySelectorPlaceholder":"搜索你的国家/地区、区号","base.searchEmpty":"无符合条件的搜索结果","base.cancel":"取消","base.picCaptchaPlaceholder":"请输入图形验证码","base.smsCaptchaPlaceholder":"请输入{0}位验证码","base.getVerificationCode":"获取验证码","base.retrieveAfter":"{0}秒后重新获取","base.retrieve":"重新获取验证码","base.getVoice":"接收语音验证码","base.voiceSMSCaptcha_send":"系统将通过免费电话给您发送语音验证码，60s内未收到可重新获取","base.voiceSMSCaptchaConfirm":"我们将以电话的方式发送语音验证码。请注意接听并记下{0}位验证码。","base.receiveVoiceConfirm":"接收语音验证码","settings.confirmLoginPassword":"验证身份","settings.confirmLoginPasswordHint":"为保障你的帐号安全，请输入登录密码进行身份验证。","base.inputLoginPassword":"请输入你的登录密码","base.next":"下一步","settings.setLoginPassword":"设置独立登录密码","settings.setLoginPasswordHint1":"设置独立登录密码，可配合绑定的邮箱/手机登录。","settings.pleaseSetPassword":"独立登录密码","settings.twoStepVerification.selectVerifyTitle":"选择二次验证方式","settings.twoStepVerification.wechat":"微信","settings.twoStepVerification.wechatSetDes":"使用微信扫描并关注我们的微信公众号，我们会将验证码通过公众号发送给你","settings.twoStepVerification.sms":"手机短信","settings.twoStepVerification.smsSetDes":"设置一个手机号，我们会将验证码通过短信方式发送给你。","settings.twoStepVerification.google":"谷歌身份验证器","settings.twoStepVerification.googleSetDes":"你可以下载身份验证器应用来获取验证码，即使手机未连接网络也无妨。","settings.twoStepVerification.recommend":"推荐","settings.twoStepVerification.wechatDes1":"1. 使用微信扫描二维码并关注我们的微信公众号，你将会收到验证码。","settings.twoStepVerification.wechatDes2":"如已关注公众号，可直接扫描","settings.twoStepVerification.wechatDes3":"2. 输入微信公众号发给你的6位验证码。","base.twoStepVerification.setSubmit":"完成设置","base.previous":"上一步","settings.twoStepVerification.setByWechatTitle":"设置微信","settings.twoStepVerification.setBySMSTitle":"设置手机号","settings.twoStepVerification.googleIos":"iPhone用户","settings.twoStepVerification.googleIosDes1":"1.在iPhone上，打开App Store。","settings.twoStepVerification.googleIosDes2":"2.搜索谷歌身份验证器(Google Authenticator)","settings.twoStepVerification.googleIosDes3":"3.下载并安装应用。","settings.twoStepVerification.googleAndroid":"Android用户","twoStepVerification.googleAndroidDes1":"1.在手机上访问Google Play或其它商店。","twoStepVerification.googleAndroidDes2":"2.搜索谷歌身份验证器(Google Authenticator)","twoStepVerification.googleAndroidDes3":"3.下载并安装应用。","settings.twoStepVerification.googleHelp":"如何安装google身份验证器？","settings.twoStepVerification.downloadGoogleTitle":"下载谷歌身份验证器","settings.twoStepVerification.googleVerifyQRcodeIosDes":"1. 打开谷歌身份验证器，点击+通过扫描二维码完成设置","settings.twoStepVerification.googleVerifyQRcodeDes":"1.打开应用，点击菜单，选择“设置帐户”，再扫描条形码即可。","settings.twoStepVerification.googleVerifyCantQRcode":"无法扫描？","settings.twoStepVerification.googleVerifyToManually":"手动设置","settings.twoStepVerification.googleVerifyManuallyIosDes1":"1.打开谷歌身份验证器，点击+","settings.twoStepVerification.googleVerifyManuallyIosDes2":"建议你在“帐户”中输入产品和帐户名，如“{0}：<EMAIL>”","settings.twoStepVerification.googleVerifyManuallyIosDes3":"在“密钥”中输入16位密钥：","settings.twoStepVerification.googleVerifyManuallyAndroidDes1":"1.打开应用，点击菜单，选择“设置账户”","settings.twoStepVerification.googleVerifyManuallyAndroidDes2":"建议你在“账户”中输入产品和帐户名，如“{0}：<EMAIL>”","settings.twoStepVerification.googleVerifyManuallyAndroidDes3":"在“密钥”中输入16位密钥","settings.twoStepVerification.googleVerifyToQRcode":"返回扫描设置","settings.twoStepVerification.googleVerifyInputCaptcha":"2. 输入谷歌验证器的6位验证码","settings.twoStepVerification.setByGoogleTitle":"设置谷歌身份验证器","settings.twoStepVerification.closeVerifySuccess":"二次验证已关闭"},"en":{"activate.enterPhoneSmsValidate":"Enter the SMS code sent to phone number {0}","base.Iknow":"OK","base.confirmToContinue":"Since you have not operated for a long time, please refresh or click to confirm and continue to use","base.confirm":"Yes","base.loginFail":"Your session has expired. Please log in again","base.alert.server500.title":"The server is down, and the programmer has been informed, please wait a moment or refresh to try again.","base.alert.server500.content":"If you have any question, please contact the customer service","base.alert.server502":"Server error (502). Please try again","base.alert.server404":"Network error (404), please check and try again.","base.alert.server403":"403-Forbidden:Access is denied.","base.pageExpired":"Current session has expired, please click {0} to retry","base.ok":"Yes","base.passwordStrengthMid":"Your password must be a combination of 8-32 letters and figures","base.passwordStartOrEndWithSpace":"There must be no space at the beginning and end of your password","base.passwordWithForbiddenCharacter":"Your password contains character(s) that aren’t allowed (<a class=\"get-detail\" href=\"http://help.fangcloud.com/posts/view/57412/\" target=\"_blank\">Learn more</a>)","base.time.justNow":"Just now","base.time.beforeSeconds":"{0} second(s) ago","base.time.beforeMinutes":"{0} minute(s) ago","base.time.beforeHours":"{0} hour(s) ago","base.time.yesterday":"Yesterday","base.time.theDayBefore":"The day before yesterday","unit.second":"{0} second(s)","unit.minute":"{0} minute(s)","unit.hour":"{0} hour(s)","unit.day":"{0} day(s)","base.hidePassword":"Hide the password","base.showPassword":"Show the password","encryption.versionLimit":"Your company has turned on File Leakage Prevention System and needs to download a specific version of PC client to continue using","encryption.downloadNow":"Download now","base.passwordWithCaptial":"Your password must be a combination of {0}-32 characters, including upper-case letter(s), lower-case letter(s) and figures","base.passwordWithSpecial":"Your password must be a combination of {0}-32 characters, including letter(s), number(s) and special character(s) (<a class=\"get-detail\" href=\"http://help.fangcloud.com/posts/view/57412/\" target=\"_blank\">Learn more</a>)","base.passwordWithCaptialAndSpecial":"Your password must be a combination of {0}-32 characters, including upper-case letter(s), lower-case letter(s), figures and special character(s) (<a class=\"get-detail\" href=\"http://help.fangcloud.com/posts/view/57412/\" target=\"_blank\">Learn more</a>)","base.passwordNormal":"Your password must be a combination of {0}-32 characters, including letter(s) and number(s)","forgot.byPhone":"Reset your password via mobile phone number {0}","forgot.byEmail":"Reset your password via email address {0}","forgot.validateMode":"Verificaiton method:","forgot.phoneValidate":"Via verified phone number","forgot.emailValidate":"Via verified email","forgot.resetSuccess":"Reset successful! Please log in with the new password","forgot.login":"Log in now","forgot.resetEmailSended":"The link for password reset has been sent to {0}<br />Please check your email and follow the instructions.","forgot.resetEmailSendedTips":"The email may be blocked by your email account. If you can’t find it in your inbox, you may need to check spam.","forgot.returnLogin":"Back to log in","loginVerify.helperDes1":"1. Please confirm whether your WeChat account for receiving verification code has followed the FangCloud WeChat Official Account. If not, please search again and follow “亿方云官方服务平台”. Follow and then click “Resend verification code” to finish verification.","loginVerify.helperDes2":"2. You can also contact your enterprise admin or customer service team to help disable it","validate.wrongPhone":"Enter a valid mobile number","auth.register.freeTrial":"Free of charge, try it now","auth.register.freeExperienceFor15Days":"15 days' free trial","auth.register.leaveMessageForContact":"Please provide your contact information and we will contact you as soon as possible","auth.register.normalTip":"If your enterprise (team) has finished registration, please contact the administrator for invitation. Individual registration isn’t necessary","auth.register.enterpirseContactNumber":"You may also call <b>************</b> to contact us","base.needAcceptProtocal":"Please read and accept Terms & Conditions of Services and Service Level Agreement","base.emptyValue":"Please try again after entering all the information properly","auth.register.phoneRegistered":"This phone number has been used for registration. Please <a href=\"{0}\"> log in </a> or contact Customer Services.","auth.register.phoneRegisteredAsPersonal":"You have already registered as a personal account. Please <a href=\"{0}\"> log in </a> or go to the website or PC client to login and uupdate your plan.","settings.confirmLoginPassword":"Identity verification","settings.confirmLoginPasswordHint":"To ensure security of your account, please enter your login password for identity verification","base.inputLoginPassword":"Enter your login password","base.next":"Next","settings.setLoginPassword":"Set an individual login password","settings.setLoginPasswordHint1":"Set your individual login password and you may log in with the login password and the linked email address/phone number.","settings.pleaseSetPassword":"Individual login password","settings.twoStepVerification.selectVerifyTitle":"Choose Two-step verification method","settings.twoStepVerification.wechat":"WeChat","settings.twoStepVerification.wechatSetDes":"Use your WeChat app to scan and follow our WeChat Official Account, and we'll send the verification code to you via WeChat.","settings.twoStepVerification.sms":"Text message","settings.twoStepVerification.smsSetDes":"Set a phone number, and we'll send you the verification code via text message.","settings.twoStepVerification.google":"Google Authenticator","settings.twoStepVerification.googleSetDes":"You can download a Google Authenticator app to get the verification code, even if your phone is not connected to the network.","settings.twoStepVerification.recommend":"Recommend","settings.twoStepVerification.wechatDes1":"1. Use your WeChat app to scan and follow our WeChat Official Account, and you will receive the verification code.","settings.twoStepVerification.wechatDes2":"If you have followed our WeChat Official Account, you can scan directly.","settings.twoStepVerification.wechatDes3":"2. Enter the 6-digit verification code sent by the FangCloud WeChat Official Account.","base.twoStepVerification.setSubmit":"Complete","base.previous":"Back","settings.twoStepVerification.setByWechatTitle":"Set WeChat","settings.twoStepVerification.setBySMSTitle":"Set phone number","settings.twoStepVerification.googleIos":"iPhone user","settings.twoStepVerification.googleIosDes1":"1. Open App Store on iPhone.","settings.twoStepVerification.googleIosDes2":"2. Search Google Authenticator","settings.twoStepVerification.googleIosDes3":"3. Download and install the app","settings.twoStepVerification.googleAndroid":"Android user","twoStepVerification.googleAndroidDes1":"1. Visit Google Play or other app stores on your phone.","twoStepVerification.googleAndroidDes2":"2. Search Google Authenticator","twoStepVerification.googleAndroidDes3":"3. Download and install the app","settings.twoStepVerification.googleHelp":"How to install Google Authenticator app?","settings.twoStepVerification.downloadGoogleTitle":"Download Google Authenticator app","settings.twoStepVerification.googleVerifyQRcodeIosDes":"1. Open Google Authenticator app, click + and scan the QR code to complete the configuration","settings.twoStepVerification.googleVerifyQRcodeDes":"1. Open Google Authenticator app, click the menu and choose \"Setting Account\", then scan the QR code.","settings.twoStepVerification.googleVerifyCantQRcode":"Can't scan?","settings.twoStepVerification.googleVerifyToManually":"Set manually","settings.twoStepVerification.googleVerifyManuallyIosDes1":"1. Open Google Authenticator app and click +","settings.twoStepVerification.googleVerifyManuallyIosDes2":"It is recommended that you enter the product and account name in \"Account\", such as \"{0}: <EMAIL>\"","settings.twoStepVerification.googleVerifyManuallyIosDes3":"Enter a 16-digit secret key in “Secret Key”:","settings.twoStepVerification.googleVerifyManuallyAndroidDes1":"1. Open Google Authenticator app, click the menu and choose \"Setting Account\"","settings.twoStepVerification.googleVerifyManuallyAndroidDes2":"It is recommended that you enter the product and account name in \"Account\", such as \"{0}: <EMAIL>\"","settings.twoStepVerification.googleVerifyManuallyAndroidDes3":"Enter a 16-digit secret key in “Secret Key”","settings.twoStepVerification.googleVerifyToQRcode":"Or scan a QR code instead","settings.twoStepVerification.googleVerifyInputCaptcha":"2. Enter 6-digit code generated by Google Authenticator app","settings.twoStepVerification.setByGoogleTitle":"Set Google Authenticator app","settings.twoStepVerification.modifyVerifySuccessTitle":"Two-step verification has been changed","settings.twoStepVerification.setVerifySuccessTitle":"Two-step verification is enabled","settings.twoStepVerification.setVerifySuccessDes":"Every time you sign in {0}, you need to enter the verification code in addition to your account and password.","settings.twoStepVerification.closeVerifySuccess":"Two-step verification has been disabled","settings.thirdPartyBindFailure":"Linking failed","settings.editPassword":"Change password","settings.oldPassword":"Old password","settings.newPassword":"New password","settings.confirmPassword":"Confirm password","settings.editPasswordSuccess":"Password changed successfully. Please login again.","settings.email":"Email:","settings.phone":"Mobile phone number:","settings.emptyEmailInfo":"The email address can be used for login after being linked and verified","settings.emptyPhoneInfo":"The mobile phone number can be used for login after being linked and verified","settings.unvalidate":"Unverified","settings.unvalidateCredentialUsage":"Can be used for login and retrieving your password after verification","settings.validateTime":"Time of verification: {0}","settings.validateCredentialUsage":"Can be used for login and retrieving password","settings.validate":"Verify now","settings.bindImmediately":"Link now","settings.phoneNotAvaliable":"This phone number has been used by another account number. Please change it.","settings.emailNotAvaliable":"This email address has been linked to another account. Please change it.","base.cancelText":"Later","settings.editNow":"Modify now","settings.forbiddenEditEmail":"You can’t change the email address for now. Please try again after successful linking to your phone number.","settings.editPhone":"Change mobile phone number","settings.validatePhone":"Verify mobile phone number","settings.bindPhone":"Link mobile phone number","settings.editPhoneSuccess":"Your mobile phone number is successfully changed","settings.validatePhoneSuccess":"The mobile phone number is successfully verified","settings.bindPhoneSuccess":"The mobile phone number is successfully linked","settings.currentCredential":"The existing linked number: {0}","settings.mobilePlaceholder":"Enter your mobile phone number","base.submit":"Submit","settings.editEmail":"Change email address","settings.validateEmail":"Verify email","settings.bindEmail":"Link email address","settings.enterNewEmail":"Enter a new email address","settings.emailSended":"A verification email has been sent to {0}. Please check your inbox. This email address can be used for login and retrieving your password after verification.","settings.emailSendedTips":"The email may be blocked by your email account. If you can’t find it in your inbox, you may need to check spam.","settings.unbind":"Unlink","settings.bind":"Link","settings.qiyeweixinLabel":"Enterprise WeChat:","settings.dingdingLabel":"DingTalk:","settings.dingding":"DingTalk","settings.qiyeweixin":"Enterprise WeChat","settings.unbindThirdConfirmMain":"Are you sure that you want to unlink {0} account “{1}”?","settings.unbindConfirmSub":"After being unlinked, the account number can’t be used for login","settings.device.fromAndroid":"(Android App)","settings.device.fromIos":"(iOS App)","settings.device.fromWindowsSync":"(Sync for Windows)","settings.device.fromMacSync":"(Sync for Mac)","settings.device.fromMacDesktop":"(FangCloud for Mac)","settings.device.fromWindowsDesktop":"(FangCloud for Windows)","settings.device.deviceName":"Device name","settings.device.deivceLastLogin":"Recent access time","settings.device.deivceLastLocation":"Recent access location","settings.device.deivceOperation":"Operation","settings.device.creditable":"Trust","settings.device.current":"Current","settings.device.ip":"IP address: {0}","base.delete":"Delete","settings.device.noresult":"No data for now","settings.device.showMore":"Expand all","settings.device.deleteIsCreditable":"Your account will be signed out of this device, and you need to login again. Two-step verification is required for next login.","settings.device.deleteNotCreditable":"Your account will be signed out of this device, and you need to login again.","settings.device.deleteDevice":"Delete device","settings.device.hideMore":"Hide","settings.twoStepVerification.typePhone":"Text message","settings.twoStepVerification.typePhoneNumber":"(ending with {0})","settings.twoStepVerification.typeWechat":"WeChat Official Account","settings.twoStepVerification.typeGoogle":"Google Authenticator","settings.twoStepVerification.hasOpen":"Two-step verification is enabled","settings.twoStepVerification.validateTime":"(Set on {0})","settings.twoStepVerification.toClose":"Disable Two-step verification","settings.twoStepVerification.type":"Verificaiton method:","base.modify":"Edit","settings.twoStepVerification.info":"The two-step verification adds a layer of security to your account. Once two-step verification is enabled, you will need to enter a verification code in addition to the account number and password when you log into your FangCloud account (the verification code will be sent to your mobile phone).","settings.twoStepVerification.toSet":"Click to enable","settings.twoStepVerification.disableToClose":"The administrator has enabled \"Two-step verification of enterprise members\", and you can't disable Two-step verification by yourself.","base.yfy":"FangCloud","base.countrySelectorPlaceholder":"Search your country/region and area code","base.searchEmpty":"No eligible search results","base.cancel":"Cancel","base.picCaptchaPlaceholder":"Enter captcha code","base.smsCaptchaPlaceholder":"{0}-digit verification code","base.getVerificationCode":"Get a verification code","base.retrieveAfter":"{0}s","base.retrieve":"Resend","base.getVoice":"Use voice verification code","base.voiceSMSCaptcha_send":"System will send you verification code via free call. You can retry after 60s.","base.voiceSMSCaptchaConfirm":"We will send you a voice verification code to your registered phone number. Please answer the call and remember the {0}-digit verification code.","base.receiveVoiceConfirm":"Send"},"zh-TW":{"activate.enterPhoneSmsValidate":"請輸入手機{0}獲取的短信驗證碼","base.Iknow":"知道了","base.confirmToContinue":"由於妳長時間未進行操作，請刷新或點擊確認後繼續使用","base.confirm":"確定","base.loginFail":"登入已失效，請重新登入","base.alert.server500.title":"伺服器開了一點小差，已通知程式猿小哥處理，請稍等片刻或重新整理重試。","base.alert.server500.content":"如有疑問，請聯絡客服","base.alert.server502":"伺服器錯誤，請重試 (502)","base.alert.server404":"網路出錯(404)，請檢查後重試","base.alert.server403":"你無許可權訪問","base.pageExpired":"當前頁面已失效，請點擊{0}重試","base.ok":"確定","base.passwordStrengthMid":"密碼必須為8-32位字母和數字的組合","base.passwordStartOrEndWithSpace":"密碼首尾不能為空格","base.passwordWithForbiddenCharacter":"密碼包含不允許的字符(<a class=\"get-detail\" href=\"http://help.fangcloud.com/posts/view/57412/\" target=\"_blank\">了解詳情</a>)","base.time.justNow":"剛剛","base.time.beforeSeconds":"{0}秒前","base.time.beforeMinutes":"{0}分鐘前","base.time.beforeHours":"{0}小時前","base.time.yesterday":"昨天","base.time.theDayBefore":"前天","unit.second":"{0}秒","unit.minute":"{0}分鐘","unit.hour":"{0}小時","unit.day":"{0}天","base.hidePassword":"隱藏密碼","base.showPassword":"顯示密碼","encryption.versionLimit":"你所在的企業開啟了檔案防洩漏，需下載特定版本客戶端方可使用","encryption.downloadNow":"立即下載","base.passwordWithCaptial":"密碼長度為{0}-32個字符，必須包含大寫字母、小寫字母和數字","base.passwordWithSpecial":"密碼長度為{0}-32個字符，必須包含字母、數字和特殊字符(<a class=\"get-detail\" href=\"http://help.fangcloud.com/posts/view/57412/\" target=\"_blank\">了解詳情</a>)","base.passwordWithCaptialAndSpecial":"密碼長度為{0}-32個字符，必須包含大寫字母、小寫字母、數字和特殊字符(<a class=\"get-detail\" href=\"http://help.fangcloud.com/posts/view/57412/\" target=\"_blank\">了解詳情</a>)","base.passwordNormal":"密碼長度為{0}-32個字符，必須包含字母和數字","forgot.byPhone":"通過手機號{0}找回密碼","forgot.byEmail":"通過郵箱{0}找回密碼","forgot.validateMode":"驗證方式：","forgot.phoneValidate":"手機驗證","forgot.emailValidate":"郵箱驗證","forgot.resetSuccess":"重設成功！請使用新密碼登入","forgot.login":"立即登入","forgot.resetEmailSended":"重設密碼鏈接已發送至{0}<br />請登入妳的郵箱查看並按照郵件內容操作","forgot.resetEmailSendedTips":"郵件可能會被妳的郵箱攔截，如在收件箱中未找到，可嘗試在垃圾郵件中查看。","forgot.returnLogin":"返回到登入","loginVerify.helperDes1":"1.請確認妳設置接收驗證碼的微信號是否關註了億方雲公眾號，如未關註，請重新搜索並關註“億方雲官方服務平臺”。關註後點擊“重新發送驗證碼”完成驗證即可。","loginVerify.helperDes2":"2.妳也可以聯系企業管理員或客服幫妳關閉二次驗證","validate.wrongPhone":"請輸入有效的手機號","auth.register.freeTrial":"免費使用，立即體驗","auth.register.freeExperienceFor15Days":"免費體驗15天","auth.register.leaveMessageForContact":"請留下聯系方式，我們將盡快聯系妳","auth.register.normalTip":"若妳的企業(團隊)已註冊，聯系管理員邀請即可，無需單獨註冊","auth.register.enterpirseContactNumber":"妳也可以撥打 <b>************</b> 聯系我們","base.needAcceptProtocal":"請閱讀並接受服務條款和服務等級協議","base.emptyValue":"請有效填寫所有信息後再試","auth.register.phoneRegistered":"該手機號已被註冊，請直接<a href=\"{0}\">登入</a>或聯系客服","auth.register.phoneRegisteredAsPersonal":"你已註冊個人帳號，請直接<a href=\"{0}\">登入</a>或前往網頁版、PC客戶端登入後進行套餐升級","settings.confirmLoginPassword":"驗證身份","settings.confirmLoginPasswordHint":"為保障妳的帳號安全，請輸入登入密碼進行身份驗證。","base.inputLoginPassword":"請輸入妳的登入密碼","base.next":"下壹步","settings.setLoginPassword":"設置獨立登入密碼","settings.setLoginPasswordHint1":"設置獨立登入密碼，可配合綁定的郵箱/手機登入。","settings.pleaseSetPassword":"獨立登入密碼","settings.twoStepVerification.selectVerifyTitle":"選擇二次驗證方式","settings.twoStepVerification.wechat":"微信","settings.twoStepVerification.wechatSetDes":"使用微信掃描並關註我們的微信公眾號，我們會將驗證碼通過公眾號發送給妳","settings.twoStepVerification.sms":"手機短信","settings.twoStepVerification.smsSetDes":"設置壹個手機號，我們會將驗證碼通過短信方式發送給妳。","settings.twoStepVerification.google":"谷歌身份驗證器","settings.twoStepVerification.googleSetDes":"妳可以下載身份驗證器應用來獲取驗證碼，即使手機未連接網絡也無妨。","settings.twoStepVerification.recommend":"推薦","settings.twoStepVerification.wechatDes1":"1. 使用微信掃描二維碼並關註我們的微信公眾號，妳將會收到驗證碼。","settings.twoStepVerification.wechatDes2":"如已關註公眾號，可直接掃描","settings.twoStepVerification.wechatDes3":"2. 輸入微信公眾號發給妳的6位驗證碼。","base.twoStepVerification.setSubmit":"完成設置","base.previous":"上壹步","settings.twoStepVerification.setByWechatTitle":"設置微信","settings.twoStepVerification.setBySMSTitle":"設置手機號","settings.twoStepVerification.googleIos":"iPhone用護","settings.twoStepVerification.googleIosDes1":"1.在iPhone上，打開App Store。","settings.twoStepVerification.googleIosDes2":"2.搜索谷歌身份驗證器(Google Authenticator)","settings.twoStepVerification.googleIosDes3":"3.下載並安裝應用。","settings.twoStepVerification.googleAndroid":"Android用護","twoStepVerification.googleAndroidDes1":"1.在手機上訪問Google Play或其它商店。","twoStepVerification.googleAndroidDes2":"2.搜索谷歌身份驗證器(Google Authenticator)","twoStepVerification.googleAndroidDes3":"3.下載並安裝應用。","settings.twoStepVerification.googleHelp":"如何安裝google身份驗證器？","settings.twoStepVerification.downloadGoogleTitle":"下載谷歌身份驗證器","settings.twoStepVerification.googleVerifyQRcodeIosDes":"1. 打開谷歌身份驗證器，點擊+通過掃描二維碼完成設置","settings.twoStepVerification.googleVerifyQRcodeDes":"1.打開應用，點擊菜單，選擇“設置帳戶”，再掃描條形碼即可。","settings.twoStepVerification.googleVerifyCantQRcode":"無法掃描？","settings.twoStepVerification.googleVerifyToManually":"手動設置","settings.twoStepVerification.googleVerifyManuallyIosDes1":"1.打開谷歌身份驗證器，點擊+","settings.twoStepVerification.googleVerifyManuallyIosDes2":"建議妳在“帳戶”中輸入產品和帳戶名，如“{0}：<EMAIL>”","settings.twoStepVerification.googleVerifyManuallyIosDes3":"在“密鑰”中輸入16位密鑰：","settings.twoStepVerification.googleVerifyManuallyAndroidDes1":"1.打開應用，點擊菜單，選擇“設置賬戶”","settings.twoStepVerification.googleVerifyManuallyAndroidDes2":"建議妳在“賬戶”中輸入產品和帳戶名，如“{0}：<EMAIL>”","settings.twoStepVerification.googleVerifyManuallyAndroidDes3":"在“密鑰”中輸入16位密鑰","settings.twoStepVerification.googleVerifyToQRcode":"返回掃描設置","settings.twoStepVerification.googleVerifyInputCaptcha":"2. 輸入谷歌驗證器的6位驗證碼","settings.twoStepVerification.setByGoogleTitle":"設置谷歌身份驗證器","settings.twoStepVerification.modifyVerifySuccessTitle":"二次驗證已修改","settings.twoStepVerification.setVerifySuccessTitle":"二次驗證已開啟","settings.twoStepVerification.setVerifySuccessDes":"二次驗證已開啟，每次登入{0}時，除帳號密碼之外，還需要輸入安全驗證碼。","settings.twoStepVerification.closeVerifySuccess":"二次驗證已關閉","settings.thirdPartyBindFailure":"綁定失敗","settings.editPassword":"修改密碼","settings.oldPassword":"舊密碼","settings.newPassword":"新密碼","settings.confirmPassword":"確認密碼","settings.editPasswordSuccess":"密碼修改成功，請重新登入","settings.email":"郵箱：","settings.phone":"手機：","settings.emptyEmailInfo":"綁定且驗證後可用郵箱登入","settings.emptyPhoneInfo":"綁定後可用手機登入","settings.unvalidate":"未驗證","settings.unvalidateCredentialUsage":"驗證後可用於登入、找回密碼","settings.validateTime":"驗證時間：{0}","settings.validateCredentialUsage":"可用於登入、找回密碼","settings.validate":"立即驗證","settings.bindImmediately":"立即綁定","settings.phoneNotAvaliable":"該手機號已被其它帳號占用，請修改。","settings.emailNotAvaliable":"該郵箱已被其它帳號占用，請修改。","base.cancelText":"下次再說","settings.editNow":"立即修改","settings.forbiddenEditEmail":"暫時無法修改郵箱。請在手機號綁定成功後再試。","settings.editPhone":"修改手機","settings.validatePhone":"驗證手機","settings.bindPhone":"綁定手機","settings.editPhoneSuccess":"手機號已修改成功","settings.validatePhoneSuccess":"手機號已驗證成功","settings.bindPhoneSuccess":"手機號已綁定成功","settings.currentCredential":"當前綁定：{0}","settings.mobilePlaceholder":"請輸入妳的手機號","base.submit":"提交","settings.editEmail":"修改郵箱","settings.validateEmail":"驗證郵箱","settings.bindEmail":"綁定郵箱","settings.enterNewEmail":"請輸入新的郵箱","settings.emailSended":"驗證郵件已發送至{0}，請登入妳的郵箱查看。驗證後，該郵箱可以用於登入、找回密碼。","settings.emailSendedTips":"郵件可能會被攔截，如未在收件箱中找到，可嘗試在垃圾郵件中查找","settings.unbind":"解綁","settings.bind":"綁定","settings.qiyeweixinLabel":"企業微信：","settings.dingdingLabel":"釘釘：","settings.dingding":"釘釘","settings.qiyeweixin":"企業微信","settings.unbindThirdConfirmMain":"妳確定要解綁{0}帳號“{1}”嗎？","settings.unbindConfirmSub":"解綁後，將無法使用該帳號登入","settings.device.fromAndroid":"（Android App）","settings.device.fromIos":"（iOS App）","settings.device.fromWindowsSync":"（Windows同步端）","settings.device.fromMacSync":"（Mac同步端）","settings.device.fromMacDesktop":"（Mac客護端）","settings.device.fromWindowsDesktop":"（Windows客護端）","settings.device.deviceName":"設備名稱","settings.device.deivceLastLogin":"最近訪問","settings.device.deivceLastLocation":"最近訪問地","settings.device.deivceOperation":"操作","settings.device.creditable":"可信","settings.device.current":"當前","settings.device.ip":"IP地址：{0}","base.delete":"刪除","settings.device.noresult":"暫無數據","settings.device.showMore":"展開全部","settings.device.deleteIsCreditable":"在該設備上將退出登入，妳需要重新登入才能繼續訪問，再次登入需要進行二次驗證。","settings.device.deleteNotCreditable":"在該設備上將退出登入，妳需要重新登入才能繼續訪問。","settings.device.deleteDevice":"刪除此設備","settings.device.hideMore":"收起","settings.twoStepVerification.typePhone":"手機短信驗證","settings.twoStepVerification.typePhoneNumber":"（尾號為 {0}）","settings.twoStepVerification.typeWechat":"微信公眾號驗證","settings.twoStepVerification.typeGoogle":"谷歌驗證器驗證","settings.twoStepVerification.hasOpen":"二次驗證已開啟","settings.twoStepVerification.validateTime":"（設置於 {0}）","settings.twoStepVerification.toClose":"關閉二次驗證","settings.twoStepVerification.type":"驗證方式：","base.modify":"修改","settings.twoStepVerification.info":"二次驗證為妳的帳戶增加壹層安全保護。啟用二次驗證後，每次登入億方雲時，除帳號密碼之外，還需要輸入安全驗證碼（驗證碼將發送到妳的手機上）。","settings.twoStepVerification.toSet":"開啟二次驗證","settings.twoStepVerification.disableToClose":"管理員開啟了“企業成員二次驗證”功能，妳無法單獨關閉二次驗證。","base.yfy":"億方雲","base.countrySelectorPlaceholder":"搜索妳的國家/地區、區號","base.searchEmpty":"無符合條件的搜索結果","base.cancel":"取消","base.picCaptchaPlaceholder":"請輸入圖形驗證碼","base.smsCaptchaPlaceholder":"請輸入{0}位驗證碼","base.getVerificationCode":"獲取驗證碼","base.retrieveAfter":"{0}秒後重新獲取","base.retrieve":"重新獲取驗證碼","base.getVoice":"接收語音驗證碼","base.voiceSMSCaptcha_send":"系統將通過免費電話給您發送語音驗證碼，60s內未收到可重新獲取","base.voiceSMSCaptchaConfirm":"我們將以電話的方式發送語音驗證碼。請註意接聽並記下{0}位驗證碼。","base.receiveVoiceConfirm":"接收語音驗證碼"},"zh-CN-education":{"loginVerify.helperDes2":"2.你也可以联系学校管理员或客服帮你关闭二次验证","auth.register.normalTip":"若你的学校(团队)已注册，联系管理员邀请即可，无需单独注册"},"zh-TW-education":{"loginVerify.helperDes2":"2.妳也可以聯系學校管理員或客服幫妳關閉二次驗證","auth.register.normalTip":"若妳的學校(團隊)已註冊，聯系管理員邀請即可，無需單獨註冊"},"en-education":{"loginVerify.helperDes2":"2. You can also contact your school admin or customer service team to help disable it","auth.register.normalTip":"If your school (team) has finished registration, please contact the administrator for invitation. Individual registration isn’t necessary"}}

/***/ }),

/***/ 14:
/***/ (function(module, exports) {

/* (ignored) */

/***/ }),

/***/ 149:
/***/ (function(module, exports, __webpack_require__) {

"use strict";


var _extends2 = __webpack_require__(18);

var _extends3 = _interopRequireDefault(_extends2);

var _common = __webpack_require__(1);

var _common2 = _interopRequireDefault(_common);

var _Dialog = __webpack_require__(50);

var _Dialog2 = _interopRequireDefault(_Dialog);

var _CountryPhoneGroup = __webpack_require__(46);

var _CountryPhoneGroup2 = _interopRequireDefault(_CountryPhoneGroup);

var _SmsValidate = __webpack_require__(47);

var _SmsValidate2 = _interopRequireDefault(_SmsValidate);

var _Notification = __webpack_require__(119);

var _Notification2 = _interopRequireDefault(_Notification);

var _fangIntl = __webpack_require__(3);

var _twoStepVerify = __webpack_require__(120);

var _twoStepVerify2 = _interopRequireDefault(_twoStepVerify);

var _API = __webpack_require__(7);

var API = _interopRequireWildcard(_API);

var _tooltip = __webpack_require__(150);

var _tooltip2 = _interopRequireDefault(_tooltip);

var _jsCookie = __webpack_require__(45);

var _jsCookie2 = _interopRequireDefault(_jsCookie);

function _interopRequireWildcard(obj) { if (obj && obj.__esModule) { return obj; } else { var newObj = {}; if (obj != null) { for (var key in obj) { if (Object.prototype.hasOwnProperty.call(obj, key)) newObj[key] = obj[key]; } } newObj.default = obj; return newObj; } }

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

// import './css/user_settings.scss';
var thirdPartyQuery = {
    'wechat': 0,
    'dingtalk': 1
};

var credentials = void 0;

_common2.default.ready(function () {
    if (!document.getElementById('hide-account-info')) {
        renderAccountInfo();
        document.querySelector('.edit-password').addEventListener('click', editPassword);
        document.querySelector('.account-settings').addEventListener('click', handleEdit);
        if (document.querySelector('.third-party')) {
            thirdPartyInit();
        }
        postMessageListener();
        initTwoStepVerify();

        // 客户端绑定第三方提示
        var bindInfo = _jsCookie2.default.get('bindInfo');
        if (bindInfo) {
            handleBindData(bindInfo);
            var domain_array = window.location.host.split('.');
            var domain = '.' + domain_array.slice(domain_array.length - 2).join('.');
            _jsCookie2.default.remove('bindInfo', { path: '/', domain: domain });
        }

        // 混合云未设置密码 隐藏修改密码按钮
        if (!!document.getElementById('set-password')) {
            document.querySelector('.edit-password').style.display = 'none';
        }
    }

    initDeviceManage();

    var twoStepType = _jsCookie2.default.get('set_two_step_type');
    if (twoStepType) {
        var product_name = _common2.default.config.product_name;

        _jsCookie2.default.remove('set_two_step_type');
        if (twoStepType === 'modify') {
            _common2.default.alert({
                main: (0, _fangIntl.defineMessage)({
                    'id': 'settings.twoStepVerification.modifyVerifySuccessTitle',
                    'defaultMessage': '\u4E8C\u6B21\u9A8C\u8BC1\u5DF2\u4FEE\u6539'
                })
            });
        } else {
            _common2.default.alert({
                main: (0, _fangIntl.defineMessage)({
                    'id': 'settings.twoStepVerification.setVerifySuccessTitle',
                    'defaultMessage': '\u4E8C\u6B21\u9A8C\u8BC1\u5DF2\u5F00\u542F'
                }),
                sub: (0, _fangIntl.defineMessage)({
                    'id': 'settings.twoStepVerification.setVerifySuccessDes',
                    'defaultMessage': '\u4E8C\u6B21\u9A8C\u8BC1\u5DF2\u5F00\u542F\uFF0C\u6BCF\u6B21\u767B\u5F55{0}\u65F6\uFF0C\u9664\u5E10\u53F7\u5BC6\u7801\u4E4B\u5916\uFF0C\u8FD8\u9700\u8981\u8F93\u5165\u5B89\u5168\u9A8C\u8BC1\u7801\u3002'
                }, product_name)
            });
        }
    }
});

function handleBindData(data, render) {
    var res = void 0;
    if (data && typeof data === 'string') {
        res = JSON.parse(data);
        if (res.errors) {
            _common2.default.alert({
                main: (0, _fangIntl.defineMessage)({
                    'id': 'settings.thirdPartyBindFailure',
                    'defaultMessage': '\u7ED1\u5B9A\u5931\u8D25'
                }),
                sub: decodeURIComponent(res.errors.error_msg)
            });
        } else {
            if (render) {
                renderThirdParty();
            }
            setTimeout(function () {
                new _Notification2.default({
                    message: res.message
                });
            }, 300);
        }
    }
}

function postMessageListener() {
    window.addEventListener('message', function (e) {
        var origin = e.origin || e.originalEvent.origin;
        var data = e.data;

        handleBindData(data, true);
    });
}

function editPassword(e) {
    e.preventDefault();
    var dialog = new _Dialog2.default({
        title: (0, _fangIntl.defineMessage)({
            'id': 'settings.editPassword',
            'defaultMessage': '\u4FEE\u6539\u5BC6\u7801'
        }),
        content: '<div class="form password-edit">\n            <p class="label-hint">' + _common2.default.passwordStrength() + '</p>\n            <div class="form-group">\n                <div class="input-group">\n                    <input type="password" name="old_password" class="password text form-control" placeholder="' + (0, _fangIntl.defineMessage)({
            'id': 'settings.oldPassword',
            'defaultMessage': '\u65E7\u5BC6\u7801'
        }) + '" autocomplete="off">\n                </div>\n            </div>\n            <div class="form-group">\n                <div class="input-group">\n                    <input type="password" name="password" class="password text form-control" placeholder="' + (0, _fangIntl.defineMessage)({
            'id': 'settings.newPassword',
            'defaultMessage': '\u65B0\u5BC6\u7801'
        }) + '" autocomplete="off">\n                </div>\n            </div>\n            <div class="form-group">\n                <div class="input-group">\n                    <input type="password" name="password_confirmation" class="password text form-control" placeholder="' + (0, _fangIntl.defineMessage)({
            'id': 'settings.confirmPassword',
            'defaultMessage': '\u786E\u8BA4\u5BC6\u7801'
        }) + '" autocomplete="off">\n                </div>\n            </div>\n        </div>',
        confirmText: (0, _fangIntl.defineMessage)({
            'id': 'base.submit',
            'defaultMessage': '\u63D0\u4EA4'
        }),
        success: function success() {
            _common2.default.ajax({
                url: API.CHANGE_PASSWORD,
                data: {
                    old_password: _common2.default.trim(dialog.layer.querySelector('[name=old_password]').value),
                    password: _common2.default.trim(dialog.layer.querySelector('[name=password]').value),
                    password_confirmation: _common2.default.trim(dialog.layer.querySelector('[name=password_confirmation]').value)
                },
                accept_all_errors: true,
                parser: function parser(res) {
                    if (res.success) {
                        // window.location.href = '/register';
                        dialog.destroy();
                        _common2.default.alert((0, _fangIntl.defineMessage)({
                            'id': 'settings.editPasswordSuccess',
                            'defaultMessage': '\u5BC6\u7801\u4FEE\u6539\u6210\u529F\uFF0C\u8BF7\u91CD\u65B0\u767B\u5F55'
                        }), function () {
                            if (window.fangcloud || window.sync_v2) {
                                if (window.fangcloud) {
                                    window.fangcloud.userRelogin();
                                } else {
                                    window.sync_v2.userRelogin();
                                }
                            } else {
                                window.location.reload();
                            }
                        }, (0, _fangIntl.defineMessage)({
                            'id': 'base.confirm',
                            'defaultMessage': '\u786E\u8BA4'
                        }));
                    } else {
                        res.errors.forEach(function (error) {
                            _common2.default.insertError(error.field, error.error_msg, dialog.$('.form'));
                        });
                    }
                }
            });
        }
    }).show();

    // var validate_tip = new Tip({
    //     trigger: '[data-title]',
    //     delegateNode: this.$node,
    //     themeClass: 'tip-w250 validate-tip',
    //     width: 'auto',
    //     pos: 'bottom'
    // });
    dialog.$('.get-detail') && dialog.$('.get-detail').addEventListener('click', function () {
        if (window.fangcloud && window.fangcloud.popWebUrl) {
            window.fangcloud.popWebUrl(this.getAttribute('href'));
        }
    });

    _common2.default.initFormEvents(dialog.layer);
}

function renderAccountInfo() {
    var credentialTypes = {
        '1': (0, _fangIntl.defineMessage)({
            'id': 'settings.email',
            'defaultMessage': '\u90AE\u7BB1\uFF1A'
        }),
        '2': (0, _fangIntl.defineMessage)({
            'id': 'settings.phone',
            'defaultMessage': '\u624B\u673A\uFF1A'
        })
    };

    var emptyCredentialText = {
        '1': (0, _fangIntl.defineMessage)({
            'id': 'settings.emptyEmailInfo',
            'defaultMessage': '\u7ED1\u5B9A\u4E14\u9A8C\u8BC1\u540E\u53EF\u7528\u90AE\u7BB1\u767B\u5F55'
        }),
        '2': (0, _fangIntl.defineMessage)({
            'id': 'settings.emptyPhoneInfo',
            'defaultMessage': '\u7ED1\u5B9A\u540E\u53EF\u7528\u624B\u673A\u767B\u5F55'
        })
    };

    // status 0 2 -1
    var template = function template(credentials) {
        return '\n        <ul class="settings-form">\n        ' + credentials.map(function (_ref) {
            var formatted_identifier = _ref.formatted_identifier,
                status = _ref.status,
                type = _ref.type,
                verified_at = _ref.verified_at;
            return '<li class="form-group">\n                <label for="" class="label">' + credentialTypes[type] + '</label>\n                <div class="info-list">\n                    <div class="info-item">\n                        ' + (formatted_identifier ? '<svg class="type-icon tip" aria-hidden="true"\n                                data-title=\'' + (status == 2 || status == -1 ? '<span class="validate-status">' + (0, _fangIntl.defineMessage)({
                'id': 'settings.unvalidate',
                'defaultMessage': '\u672A\u9A8C\u8BC1'
            }) + '</span><span class="plain-text">' + (0, _fangIntl.defineMessage)({
                'id': 'settings.unvalidateCredentialUsage',
                'defaultMessage': '\u9A8C\u8BC1\u540E\u53EF\u7528\u4E8E\u767B\u5F55\u3001\u627E\u56DE\u5BC6\u7801'
            }) + '</span>' : '<span class="validate-status">' + (0, _fangIntl.defineMessage)({
                'id': 'settings.validateTime',
                'defaultMessage': '\u9A8C\u8BC1\u65F6\u95F4\uFF1A{0}'
            }, _common2.default.formatDate(verified_at * 1000, '%YY-%MM-%DD %hh:%mm')) + '</span><span class="plain-text">' + (0, _fangIntl.defineMessage)({
                'id': 'settings.validateCredentialUsage',
                'defaultMessage': '\u53EF\u7528\u4E8E\u767B\u5F55\u3001\u627E\u56DE\u5BC6\u7801'
            }) + '</span>') + '\'\n                            >\n                                <use xlink:href="#icon-' + (status == 2 || status == -1 ? 'unverification1' : 'verification') + '"></use>\n                            </svg>' : '') + '\n                        ' + (formatted_identifier ? '<span>' + formatted_identifier + '</span>' : '<span class="unformatted">' + emptyCredentialText[type] + '</span>') + '\n                        ' + (formatted_identifier && (status == 2 || status == -1) ? '<a href="#" class="link-button action-icon edit" data-credential="' + formatted_identifier + '" data-role="' + type + '" data-action="validate" data-status="' + status + '">' + (0, _fangIntl.defineMessage)({
                'id': 'settings.validate',
                'defaultMessage': '\u7ACB\u5373\u9A8C\u8BC1'
            }) + '</a>' : '') + '\n                        ' + (formatted_identifier /*&& status == 2*/ ? '<a href="#" class="link-button action-icon edit" data-credential="' + formatted_identifier + '" data-role="' + type + '" data-action="edit" data-status="' + status + '"><i class="iconfont icon-list-edit"></i></a>' : '') + '\n                        ' + (!formatted_identifier ? '<a href="#" class="link-button action-icon edit" data-role="' + type + '" data-action="bind" data-status="' + status + '">' + (0, _fangIntl.defineMessage)({
                'id': 'settings.bindImmediately',
                'defaultMessage': '\u7ACB\u5373\u7ED1\u5B9A'
            }) + '</a>' : '') + '\n                    </div>\n                </div>\n            </li>';
        }).join('') + '\n    </ul>';
    };

    _common2.default.ajax({
        method: 'GET',
        url: API.GET_LOGIN,
        parser: function parser(res) {
            credentials = res.credentials;
            var types = [1, 2];

            credentials.forEach(function (_ref2) {
                var type = _ref2.type;

                var index = types.indexOf(type);
                if (index >= 0) {
                    types.splice(index, 1);
                }
            });

            if (types.length) {
                types.forEach(function (type) {
                    credentials.push({
                        type: type,
                        formatted_identifier: '',
                        status: 2
                    });
                });
            }

            document.querySelector('.account-settings').innerHTML = template(credentials);
            [].forEach.call(document.querySelectorAll('.tip'), function (tip) {
                new _tooltip2.default(tip, {
                    html: true,
                    title: tip.getAttribute('data-title')
                });
            });
        }
    });
}

function handleEdit(e, status, action) {
    e.preventDefault();
    var target = _common2.default.matchSelector(e.target, '[data-role]');
    if (!target) {
        return false;
    }
    var role = target.getAttribute('data-role'); //phone, email
    action = action ? action : target.getAttribute('data-action'); // edit, validate, bind, unbind
    var credential = target.getAttribute('data-credential');
    status = status ? status : target.getAttribute('data-status');

    // 账号未验证 且是老账号迁移数据
    if (action === 'validate' && status == -1) {
        var url = role == 2 ? API.PRE_VERIFY_PHONE : API.PRE_VERIFY_EMAIL;
        _common2.default.ajax({
            url: url,
            data: { credential: credential },
            accept_all_errors: true,
            parser: function parser(res) {
                if (!res.success) {
                    var msg = role == 2 ? (0, _fangIntl.defineMessage)({
                        'id': 'settings.phoneNotAvaliable',
                        'defaultMessage': '\u8BE5\u624B\u673A\u53F7\u5DF2\u88AB\u5176\u5B83\u5E10\u53F7\u5360\u7528\uFF0C\u8BF7\u4FEE\u6539\u3002'
                    }) : (0, _fangIntl.defineMessage)({
                        'id': 'settings.emailNotAvaliable',
                        'defaultMessage': '\u8BE5\u90AE\u7BB1\u5DF2\u88AB\u5176\u5B83\u5E10\u53F7\u5360\u7528\uFF0C\u8BF7\u4FEE\u6539\u3002'
                    });

                    new _Dialog2.default({
                        message: {
                            main: msg
                        },
                        showTitle: false,
                        cancelText: (0, _fangIntl.defineMessage)({
                            'id': 'base.cancelText',
                            'defaultMessage': '\u4E0B\u6B21\u518D\u8BF4'
                        }),
                        confirmText: (0, _fangIntl.defineMessage)({
                            'id': 'settings.editNow',
                            'defaultMessage': '\u7ACB\u5373\u4FEE\u6539'
                        }),
                        confirm: function confirm() {
                            handleEdit(e, 2, 'edit');
                        }
                    }).show();
                } else {
                    handleEdit(e, 2);
                }
            }
        });

        // 直接在请求里面继续
        return false;
    }

    // confirmPassword(role, action, credential);
    var callback = void 0;
    // if(action === 'unbind') {
    //     callback = handleUnbind.bind(null, role);
    //     handleUnbind(type);
    // } else {
    // phone
    if (role == 2) {
        callback = hanleEditPhone.bind(null, action, credential);
    } else if (role == 1) {
        if (action === 'validate') {
            callback = bindEmail.bind(null, credential, true);
        } else if (action === 'edit' || action === 'bind') {
            if (action === 'edit' && credentials[1].status !== 0) {
                _common2.default.alert((0, _fangIntl.defineMessage)({
                    'id': 'settings.forbiddenEditEmail',
                    'defaultMessage': '\u6682\u65F6\u65E0\u6CD5\u4FEE\u6539\u90AE\u7BB1\u3002\u8BF7\u5728\u624B\u673A\u53F7\u7ED1\u5B9A\u6210\u529F\u540E\u518D\u8BD5\u3002'
                }));
                return false;
            }
            callback = hanleEditEmail.bind(null, action, credential);
        }
    }
    // }
    new _twoStepVerify2.default(callback, action !== 'validate');
}

var countryPhoneGroup = void 0;
var smsValidate = void 0;
function hanleEditPhone(action, credential) {
    var messages = {
        edit: (0, _fangIntl.defineMessage)({
            'id': 'settings.editPhone',
            'defaultMessage': '\u4FEE\u6539\u624B\u673A'
        }),
        validate: (0, _fangIntl.defineMessage)({
            'id': 'settings.validatePhone',
            'defaultMessage': '\u9A8C\u8BC1\u624B\u673A'
        }),
        bind: (0, _fangIntl.defineMessage)({
            'id': 'settings.bindPhone',
            'defaultMessage': '\u7ED1\u5B9A\u624B\u673A'
        })
    };

    var toastMessages = {
        edit: (0, _fangIntl.defineMessage)({
            'id': 'settings.editPhoneSuccess',
            'defaultMessage': '\u624B\u673A\u53F7\u5DF2\u4FEE\u6539\u6210\u529F'
        }),
        validate: (0, _fangIntl.defineMessage)({
            'id': 'settings.validatePhoneSuccess',
            'defaultMessage': '\u624B\u673A\u53F7\u5DF2\u9A8C\u8BC1\u6210\u529F'
        }),
        bind: (0, _fangIntl.defineMessage)({
            'id': 'settings.bindPhoneSuccess',
            'defaultMessage': '\u624B\u673A\u53F7\u5DF2\u7ED1\u5B9A\u6210\u529F'
        })
    };

    var content = '<div class="form phone-set ' + (action === 'validate' ? ' phone-validate' : '') + '">\n        ' + (action === 'validate' ? '' : '<p class="label-hint">' + (0, _fangIntl.defineMessage)({
        'id': 'settings.currentCredential',
        'defaultMessage': '\u5F53\u524D\u7ED1\u5B9A\uFF1A{0}'
    }, credential) + '</p>') + '\n        <div class="form-group phone">\n            <div class="input-group">\n                <input type="text" class="username text form-control" id="phone" name="phone" value="' + (action === 'validate' ? credential : '') + '" placeholder="' + (0, _fangIntl.defineMessage)({
        'id': 'settings.mobilePlaceholder',
        'defaultMessage': '\u8BF7\u8F93\u5165\u4F60\u7684\u624B\u673A\u53F7'
    }) + '">\n            </div>\n        </div>\n    </div>';

    var dialog = new _Dialog2.default({
        title: messages[action],
        content: content,
        needValidate: true,
        confirmText: (0, _fangIntl.defineMessage)({
            'id': 'base.submit',
            'defaultMessage': '\u63D0\u4EA4'
        }),
        success: function success() {
            var data = {
                sms_captcha: smsValidate.getValue()
            };
            if (countryPhoneGroup) {
                data.phone = countryPhoneGroup.getValue();
            }
            _common2.default.ajax({
                url: action === 'validate' ? API.VERIFY_PHONE_CREDENTIAL : API.UPDATE_PHONE,
                data: data,
                accept_all_errors: true,
                parser: function parser(res) {
                    if (res.success) {
                        // window.location.href = '/register';
                        dialog.destroy();
                        new _Notification2.default({
                            message: toastMessages[action]
                        });
                        renderAccountInfo();
                    } else {
                        res.errors.forEach(function (error) {
                            _common2.default.insertError(error.field, error.error_msg, dialog.$('.form'));
                        });
                    }
                }
            });
        }
    }).show();

    var layer = dialog.layer;

    countryPhoneGroup = new _CountryPhoneGroup2.default({
        element: layer.querySelector('.phone .input-group'),
        showCountry: true,
        countrySelectOpitons: {
            showSelector: true,
            default_code: 86
            // align_element: $('.register-form .phone .input-group')
        }
    });

    smsValidate = new _SmsValidate2.default({
        syncs: function syncs() {
            var data = {
                type: 'modify_phone',
                phone: countryPhoneGroup.getValue()
            };
            return data;
        }
    });

    if (action === 'validate') {
        countryPhoneGroup.setDisabled();
        smsValidate.send();
    }

    smsValidate.on('destroy', function () {
        _common2.default.removeElement(this.$el);
    });

    smsValidate.on('send_error', function (res) {
        res.errors.forEach(function (error) {
            _common2.default.insertError(error.field, error.error_msg, dialog.$('.form'));
        });
    });

    var $form = layer.querySelector('.form');
    $form.insertAdjacentElement('beforeend', smsValidate.$el);
    _common2.default.initFormEvents(dialog.layer);
}

function hanleEditEmail(action, credential) {
    var messages = {
        edit: (0, _fangIntl.defineMessage)({
            'id': 'settings.editEmail',
            'defaultMessage': '\u4FEE\u6539\u90AE\u7BB1'
        }),
        validate: (0, _fangIntl.defineMessage)({
            'id': 'settings.validateEmail',
            'defaultMessage': '\u9A8C\u8BC1\u90AE\u7BB1'
        }),
        bind: (0, _fangIntl.defineMessage)({
            'id': 'settings.bindEmail',
            'defaultMessage': '\u7ED1\u5B9A\u90AE\u7BB1'
        })
    };
    var dialog = new _Dialog2.default({
        title: messages[action],
        needValidate: true,
        content: '<div class="form email-set">\n            ' + (action === 'edit' ? '<p class="label-hint">' + (0, _fangIntl.defineMessage)({
            'id': 'settings.currentCredential',
            'defaultMessage': '\u5F53\u524D\u7ED1\u5B9A\uFF1A{0}'
        }, credential) + '</p>' : '') + '\n            <div class="form-group email">\n                <div class="input-group">\n                    <input type="text" class="username text form-control" id="email" name="email" value="" placeholder="' + (0, _fangIntl.defineMessage)({
            'id': 'settings.enterNewEmail',
            'defaultMessage': '\u8BF7\u8F93\u5165\u65B0\u7684\u90AE\u7BB1'
        }) + '">\n                </div>\n            </div>\n        </div>',
        confirmText: (0, _fangIntl.defineMessage)({
            'id': 'base.submit',
            'defaultMessage': '\u63D0\u4EA4'
        }),
        success: function success() {
            var email = _common2.default.trim(dialog.layer.querySelector('[name=email]').value);
            _common2.default.ajax({
                url: API.UPDATE_EMAIL,
                data: {
                    email: email
                },
                accept_all_errors: true,
                parser: function parser(res) {
                    if (res.success) {
                        bindEmail(email, false);
                        dialog.destroy();
                    } else {
                        res.errors.forEach(function (error) {
                            _common2.default.insertError(error.field, error.error_msg, dialog.$('.form'));
                        });
                    }
                }
            });
        }
    }).show();

    _common2.default.initFormEvents(dialog.layer);
}

function bindEmail(email, validate) {
    new _Dialog2.default({
        title: (0, _fangIntl.defineMessage)({
            'id': 'settings.bindEmail',
            'defaultMessage': '\u7ED1\u5B9A\u90AE\u7BB1'
        }),
        content: '<div class="validate-email">\n            <div class="content">' + (0, _fangIntl.defineMessage)({
            'id': 'settings.emailSended',
            'defaultMessage': '\u9A8C\u8BC1\u90AE\u4EF6\u5DF2\u53D1\u9001\u81F3{0}\uFF0C\u8BF7\u767B\u5F55\u4F60\u7684\u90AE\u7BB1\u67E5\u770B\u3002\u9A8C\u8BC1\u540E\uFF0C\u8BE5\u90AE\u7BB1\u53EF\u4EE5\u7528\u4E8E\u767B\u5F55\u3001\u627E\u56DE\u5BC6\u7801\u3002'
        }, email) + '</div>\n            <div class="hint">' + (0, _fangIntl.defineMessage)({
            'id': 'settings.emailSendedTips',
            'defaultMessage': '\u90AE\u4EF6\u53EF\u80FD\u4F1A\u88AB\u62E6\u622A\uFF0C\u5982\u672A\u5728\u6536\u4EF6\u7BB1\u4E2D\u627E\u5230\uFF0C\u53EF\u5C1D\u8BD5\u5728\u5783\u573E\u90AE\u4EF6\u4E2D\u67E5\u627E'
        }) + '</div>\n        </div>',
        alert: function alert() {}
    }).show();

    if (validate) {
        _common2.default.ajax({
            url: API.VERIFY_EMAIL,
            data: { email: email },
            parser: function parser() {
                renderAccountInfo();
            }
        });
    } else {
        renderAccountInfo();
    }
}

// function handleUnbind(type) {
//     const typeList = {
//         // 0: defineMessage({id: 'base.thirdParty', defaultMessage: '第三方帐号'}),
//         1: defineMessage({id: 'base.email', defaultMessage: '邮箱'}),
//         2: defineMessage({id: 'base.phone', defaultMessage: '手机'})
//     };

//     let dialog = new Dialog({
//         title: defineMessage({id: 'settings.unbind', defaultMessage: '解绑'}),
//         message: {
//             main: defineMessage({id: 'settings.unbindConfirmMain', defaultMessage: '你确定要解绑吗？'}),
//             sub: defineMessage({id: 'settings.unbindConfirmSub', defaultMessage: '解除绑定后将无法使用该{0}进行登录'}, typeList[type])
//         },
//         showTitle: false,
//         cancelText: defineMessage({id: 'base.cancelText', defaultMessage: '下次再说'}),
//         confirm: function() {
//             fc.ajax({
//                 url: API.UNBIND_ACCOUNT,
//                 data: {type},
//                 parser: (res) => {
//                     if(res.success) {
//                         dialog.destroy();
//                         renderAccountInfo();
//                     }
//                 }
//             });
//         }
//     }).show();
// }

function renderThirdParty() {
    var template = function template(thirdPartys) {
        return '\n        <ul class="third-party-list settings-form">\n            ' + thirdPartys.map(function (_ref3) {
            var id = _ref3.id,
                icon = _ref3.icon,
                bind = _ref3.bind,
                nick = _ref3.nick,
                name = _ref3.name;
            return id === 'dingtalk' && _common2.default.config.login_type === 'sync' && !window.IS_WEBENGINE ? '' : '<li>\n                    <label class="label">' + name + '</label>' + (bind ? '<span class="account">' + nick + '</span>' : '') + '\n                    <div class="actions">\n                        ' + (bind ? '<a href="#" class="link-button action-icon unbind" data-type="' + id + '" data-action="unbind">' + (0, _fangIntl.defineMessage)({
                'id': 'settings.unbind',
                'defaultMessage': '\u89E3\u7ED1'
            }) + '</a>' : '<a ' + (_common2.default.config.login_type === 'sync' ? '' : 'target="_blank"') + ' href="' + API.THIRD + '/' + id + 'login?' + (id === 'dingtalk' ? 'qrcode=true' : '') + '&scene=bind&login_type=' + _common2.default.config.login_type + '&redirect=/login_settings" class="link-button action-icon bind" data-action="bind">' + (0, _fangIntl.defineMessage)({
                'id': 'settings.bindImmediately',
                'defaultMessage': '\u7ACB\u5373\u7ED1\u5B9A'
            }) + '</a>') + '\n                    </div>\n                </li>';
        }).join('') + '\n        </ul>\n    ';
    };

    _common2.default.ajax({
        url: API.GET_THIRD_LOGIN,
        parser: function parser(res) {
            var thirdPartys = [{
                id: 'wechat',
                bind: false,
                icon: 'enterprise-wechat',
                name: (0, _fangIntl.defineMessage)({
                    'id': 'settings.qiyeweixinLabel',
                    'defaultMessage': '\u4F01\u4E1A\u5FAE\u4FE1\uFF1A'
                })

            }, {
                id: 'dingtalk',
                bind: false,
                icon: 'dingtalk',
                name: (0, _fangIntl.defineMessage)({
                    'id': 'settings.dingdingLabel',
                    'defaultMessage': '\u9489\u9489\uFF1A'
                })
            }];
            if (res.accounts) {
                res.accounts.forEach(function (_ref4) {
                    var type = _ref4.type,
                        nick = _ref4.nick;

                    var index = thirdPartyQuery[type];
                    if (index >= 0) {
                        thirdPartys[index].nick = nick;
                        thirdPartys[index].bind = true;
                    }
                });
            }
            document.querySelector('.third-party').innerHTML = template(thirdPartys);
        }
    });
}

function thirdPartyInit() {
    renderThirdParty();
    var $thirdParty = document.querySelector('.third-party');
    var thirdTypes = {
        dingtalk: (0, _fangIntl.defineMessage)({
            'id': 'settings.dingding',
            'defaultMessage': '\u9489\u9489'
        }),
        wechat: (0, _fangIntl.defineMessage)({
            'id': 'settings.qiyeweixin',
            'defaultMessage': '\u4F01\u4E1A\u5FAE\u4FE1'
        })
    };
    $thirdParty.addEventListener('click', function (e) {
        var target = e.target;

        if (_common2.default.hasClass(target, 'link-button') && target.getAttribute('data-action') === 'unbind') {
            e.preventDefault();
            var type = target.getAttribute('data-type');
            var account = _common2.default.previousElementSibling(target.parentNode).innerText;

            var dialog = new _Dialog2.default({
                title: (0, _fangIntl.defineMessage)({
                    'id': 'settings.unbind',
                    'defaultMessage': '\u89E3\u7ED1'
                }),
                message: {
                    main: (0, _fangIntl.defineMessage)({
                        'id': 'settings.unbindThirdConfirmMain',
                        'defaultMessage': '\u4F60\u786E\u5B9A\u8981\u89E3\u7ED1{0}\u5E10\u53F7\u201C{1}\u201D\u5417\uFF1F'
                    }, thirdTypes[type], account),
                    sub: (0, _fangIntl.defineMessage)({
                        'id': 'settings.unbindConfirmSub',
                        'defaultMessage': '\u89E3\u7ED1\u540E\uFF0C\u5C06\u65E0\u6CD5\u4F7F\u7528\u8BE5\u5E10\u53F7\u767B\u5F55'
                    })
                },
                showTitle: false,
                cancelText: (0, _fangIntl.defineMessage)({
                    'id': 'base.cancelText',
                    'defaultMessage': '\u4E0B\u6B21\u518D\u8BF4'
                }),
                confirm: function confirm() {
                    _common2.default.ajax({
                        url: API.UNBIND_THIRD_LOGIN,
                        data: { type: type },
                        parser: function parser(res) {
                            if (res.success) {
                                dialog.destroy();
                                renderThirdParty();
                            }
                        }
                    });
                }
            }).show();
        }
    });
}

function initDeviceManage() {
    var maxSize = 5;

    var deviceTypeName = {
        'Android App': (0, _fangIntl.defineMessage)({
            'id': 'settings.device.fromAndroid',
            'defaultMessage': '\uFF08Android App\uFF09'
        }),
        'Egeio IOS App': (0, _fangIntl.defineMessage)({
            'id': 'settings.device.fromIos',
            'defaultMessage': '\uFF08iOS App\uFF09'
        }),
        'Windows Sync': (0, _fangIntl.defineMessage)({
            'id': 'settings.device.fromWindowsSync',
            'defaultMessage': '\uFF08Windows\u540C\u6B65\u7AEF\uFF09'
        }),
        'Mac Sync': (0, _fangIntl.defineMessage)({
            'id': 'settings.device.fromMacSync',
            'defaultMessage': '\uFF08Mac\u540C\u6B65\u7AEF\uFF09'
        }),
        'Windows Desktop': (0, _fangIntl.defineMessage)({
            'id': 'settings.device.fromWindowsDesktop',
            'defaultMessage': '\uFF08Windows\u5BA2\u6237\u7AEF\uFF09'
        }),
        'Mac Desktop': (0, _fangIntl.defineMessage)({
            'id': 'settings.device.fromMacDesktop',
            'defaultMessage': '\uFF08Mac\u5BA2\u6237\u7AEF\uFF09'
        })
    };

    var iconListName = {
        'Egeio IOS App': 'icon-nav-download-pc',
        'Android App': 'icon-nav-download-pc',
        'Windows Sync': 'icon-navtables',
        'Mac Sync': 'icon-computer',
        'Mac Desktop': 'icon-computer',
        'Windows Desktop': 'icon-computer'
    };

    _common2.default.ajax({
        url: API.GET_DEVICES,
        method: 'POST',
        parser: function parser(res) {
            var devices = res.devices;

            var template = '<div class="device-list">\n                <div class="list-head">\n                    <div class="list-row">\n                        <div class="list-col device-name">' + (0, _fangIntl.defineMessage)({
                'id': 'settings.device.deviceName',
                'defaultMessage': '\u8BBE\u5907\u540D\u79F0'
            }) + '</div>\n                        <div class="list-col device-last-login">' + (0, _fangIntl.defineMessage)({
                'id': 'settings.device.deivceLastLogin',
                'defaultMessage': '\u6700\u8FD1\u8BBF\u95EE'
            }) + '</div>\n                        <div class="list-col device-last-location">' + (0, _fangIntl.defineMessage)({
                'id': 'settings.device.deivceLastLocation',
                'defaultMessage': '\u6700\u8FD1\u8BBF\u95EE\u5730'
            }) + '</div>\n                        <div class="list-col device-operation">' + (0, _fangIntl.defineMessage)({
                'id': 'settings.device.deivceOperation',
                'defaultMessage': '\u64CD\u4F5C'
            }) + '</div>\n                    </div>\n                </div>\n                <div class="list-body devices-container">\n                    ' + (devices && devices.length ? devices.map(function (_ref5) {
                var creditable = _ref5.creditable,
                    current = _ref5.current,
                    device_name = _ref5.device_name,
                    device_type = _ref5.device_type,
                    updated = _ref5.updated,
                    _ref5$ip_address = _ref5.ip_address,
                    ip = _ref5$ip_address.ip,
                    location = _ref5$ip_address.location,
                    id = _ref5.id;
                return '<div class="device-item list-row">\n                                <div class="list-col device-name">\n                                    <div class="device-name-detail" title="' + device_name + (deviceTypeName[device_type] || '') + '">\n                                        <i class="iconfont ' + (iconListName[device_type] || 'icon-anzhuoduanliulanqidakai') + '"></i>\n                                        <span class="current-name ellipsis" title="">' + device_name + '</span>\n                                        <span class="device-from">' + (deviceTypeName[device_type] || '') + '</span>\n                                        ' + (creditable ? '<span class="creditable">' + (0, _fangIntl.defineMessage)({
                    'id': 'settings.device.creditable',
                    'defaultMessage': '\u53EF\u4FE1'
                }) + '</span>' : '') + '\n                                        ' + (current ? '<span class="current">' + (0, _fangIntl.defineMessage)({
                    'id': 'settings.device.current',
                    'defaultMessage': '\u5F53\u524D'
                }) + '</span>' : '') + '\n                                    </div>\n                                </div>\n                                <div class="list-col device-last-login">' + _common2.default.formatDate(updated * 1000, '%R') + '</div>\n                                <div class="list-col device-last-location">\n                                    ' + location + '\n                                    <i class="iconfont icon-ip" data-title="' + (0, _fangIntl.defineMessage)({
                    'id': 'settings.device.ip',
                    'defaultMessage': 'IP\u5730\u5740\uFF1A{0}'
                }, ip) + '"></i>\n                                </div>\n                            ' + (!current ? '<div class="list-col device-operate action-box">\n                                    <svg class="type-icon" aria-hidden="true" data-role="delete" data-id="' + id + '" title="' + (0, _fangIntl.defineMessage)({
                    'id': 'base.delete',
                    'defaultMessage': '\u5220\u9664'
                }) + '">\n                                        <use xlink:href="#icon-delete"></use>\n                                    </svg>\n                                </div>' : '') + '\n                            </div>';
            }).join('') : '<div class="empty-item"><p>' + (0, _fangIntl.defineMessage)({
                'id': 'settings.device.noresult',
                'defaultMessage': '\u6682\u65E0\u6570\u636E'
            }) + '</p></div>') + '\n                </div>\n                ' + (devices.length > maxSize ? '<a class="show-more-button action-icon" data-role="show_more">' + (0, _fangIntl.defineMessage)({
                'id': 'settings.device.showMore',
                'defaultMessage': '\u5C55\u5F00\u5168\u90E8'
            }) + '</a>' : '') + '\n            </div>';

            var $container = document.querySelector('.device-manage');
            document.querySelector('.device-manage').innerHTML = template;

            [].forEach.call(document.querySelectorAll('.icon-ip'), function (tip) {
                new _tooltip2.default(tip, {
                    html: true,
                    title: tip.getAttribute('data-title')
                });
            });

            $container.addEventListener('click', function (e) {
                var deleteTarget = _common2.default.matchSelector(e.target, '[data-role=delete]');
                var showMoreTarget = _common2.default.matchSelector(e.target, '.show-more-button');

                if (deleteTarget) {
                    var is_creditable = void 0,
                        id = deleteTarget.getAttribute('data-id') - 0;
                    var msg = is_creditable ? (0, _fangIntl.defineMessage)({
                        'id': 'settings.device.deleteIsCreditable',
                        'defaultMessage': '\u5728\u8BE5\u8BBE\u5907\u4E0A\u5C06\u9000\u51FA\u767B\u5F55\uFF0C\u4F60\u9700\u8981\u91CD\u65B0\u767B\u5F55\u624D\u80FD\u7EE7\u7EED\u8BBF\u95EE\uFF0C\u518D\u6B21\u767B\u5F55\u9700\u8981\u8FDB\u884C\u4E8C\u6B21\u9A8C\u8BC1\u3002'
                    }) : (0, _fangIntl.defineMessage)({
                        'id': 'settings.device.deleteNotCreditable',
                        'defaultMessage': '\u5728\u8BE5\u8BBE\u5907\u4E0A\u5C06\u9000\u51FA\u767B\u5F55\uFF0C\u4F60\u9700\u8981\u91CD\u65B0\u767B\u5F55\u624D\u80FD\u7EE7\u7EED\u8BBF\u95EE\u3002'
                    });

                    var dialog = new _Dialog2.default({
                        message: {
                            main: (0, _fangIntl.defineMessage)({
                                'id': 'settings.device.deleteDevice',
                                'defaultMessage': '\u5220\u9664\u6B64\u8BBE\u5907'
                            }),
                            sub: msg
                        },
                        showTitle: false,
                        confirmText: (0, _fangIntl.defineMessage)({
                            'id': 'base.delete',
                            'defaultMessage': '\u5220\u9664'
                        }),
                        cancelText: (0, _fangIntl.defineMessage)({
                            'id': 'base.cancelText',
                            'defaultMessage': '\u4E0B\u6B21\u518D\u8BF4'
                        }),
                        confirm: function confirm() {
                            _common2.default.ajax({
                                url: API.DELETE_DEVICE,
                                data: { id: id },
                                parser: function parser() {
                                    dialog.destroy();
                                    _common2.default.removeElement(deleteTarget.parentNode.parentNode);
                                }
                            });
                        }
                    }).show();
                } else if (showMoreTarget) {
                    var container = document.querySelector('.devices-container');
                    if (_common2.default.hasClass(container, 'show-more')) {
                        _common2.default.removeClass(container, 'show-more');
                        showMoreTarget.innerText = (0, _fangIntl.defineMessage)({
                            'id': 'settings.device.showMore',
                            'defaultMessage': '\u5C55\u5F00\u5168\u90E8'
                        });
                    } else {
                        _common2.default.addClass(container, 'show-more');
                        showMoreTarget.innerText = (0, _fangIntl.defineMessage)({
                            'id': 'settings.device.hideMore',
                            'defaultMessage': '\u6536\u8D77'
                        });
                    }
                }
            });
        }
    });
}

function initTwoStepVerify() {
    var $container = document.querySelector('.two-step-verification');
    if (!$container) return false;

    var twoStepVerifyStatus = {
        data: {
            // has_validate: true,//mod.user.two_step_status,
            // validate_time: fc.formatDate( 1514256157 * 1000, '%YY-%MM-%DD'),
            // validate_type: 'sms',//mod.user.two_step_method,
        },

        init: function init() {
            var _this = this;

            _common2.default.ajax({
                url: API.GET_TWO_STEP_METHOD,
                method: 'GET',
                parser: function parser(res) {
                    var method = res.method,
                        created = res.created,
                        identity = res.identity;

                    _this.data.has_validate = method !== 'none';
                    _this.data.validate_time = _common2.default.formatDate(created * 1000, '%YY-%MM-%DD');
                    _this.data.validate_type = method;
                    _this.data.identity = identity;
                    _this.reset();
                }
            });
        },

        validateTypeTemplate: function validateTypeTemplate(_ref6) {
            var validate_type = _ref6.validate_type,
                identity = _ref6.identity;

            var validateTypeList = {
                sms: '<span class="validate-type">' + (0, _fangIntl.defineMessage)({
                    'id': 'settings.twoStepVerification.typePhone',
                    'defaultMessage': '\u624B\u673A\u77ED\u4FE1\u9A8C\u8BC1'
                }) + '<span class="validata-type-hint">' + (0, _fangIntl.defineMessage)({
                    'id': 'settings.twoStepVerification.typePhoneNumber',
                    'defaultMessage': '\uFF08\u5C3E\u53F7\u4E3A {0}\uFF09'
                }, identity) + '</span></span>',
                wechat: '<span class="validate-type">' + (0, _fangIntl.defineMessage)({
                    'id': 'settings.twoStepVerification.typeWechat',
                    'defaultMessage': '\u5FAE\u4FE1\u516C\u4F17\u53F7\u9A8C\u8BC1'
                }) + '</span>',
                google: '<span class="validate-type">' + (0, _fangIntl.defineMessage)({
                    'id': 'settings.twoStepVerification.typeGoogle',
                    'defaultMessage': '\u8C37\u6B4C\u9A8C\u8BC1\u5668\u9A8C\u8BC1'
                }) + '</span>'
            };
            return validateTypeList[validate_type];
        },

        template: function template(_ref7) {
            var has_validate = _ref7.has_validate,
                validate_time = _ref7.validate_time;

            return has_validate ? '<div class="info-item">\n                    <span class="validate-success">\n                        <svg class="type-icon" aria-hidden="true">\n                            <use xlink:href="#icon-verification"></use>\n                        </svg>\n                        ' + (0, _fangIntl.defineMessage)({
                'id': 'settings.twoStepVerification.hasOpen',
                'defaultMessage': '\u4E8C\u6B21\u9A8C\u8BC1\u5DF2\u5F00\u542F'
            }) + '\n                    </span>\n                    <span class="validata-time">' + (0, _fangIntl.defineMessage)({
                'id': 'settings.twoStepVerification.validateTime',
                'defaultMessage': '\uFF08\u8BBE\u7F6E\u4E8E {0}\uFF09'
            }, validate_time) + '</span>\n                    <a class="action-button action-icon" href="#" data-role="close">' + (0, _fangIntl.defineMessage)({
                'id': 'settings.twoStepVerification.toClose',
                'defaultMessage': '\u5173\u95ED\u4E8C\u6B21\u9A8C\u8BC1'
            }) + '</a>\n                </div>\n                <div class="info-item">\n                    <span class="validate-type-title">' + (0, _fangIntl.defineMessage)({
                'id': 'settings.twoStepVerification.type',
                'defaultMessage': '\u9A8C\u8BC1\u65B9\u5F0F\uFF1A'
            }) + '</span>\n                    ' + this.validateTypeTemplate(this.data) + '\n                    <a class="action-button action-icon" href="#" data-role="change" title="' + (0, _fangIntl.defineMessage)({
                'id': 'base.modify',
                'defaultMessage': '\u4FEE\u6539'
            }) + '"><i class="iconfont icon-list-edit"></i></a>\n                </div>' : '\n                    <p class="hint">' + (0, _fangIntl.defineMessage)({
                'id': 'settings.twoStepVerification.info',
                'defaultMessage': '\u4E8C\u6B21\u9A8C\u8BC1\u4E3A\u4F60\u7684\u5E10\u6237\u589E\u52A0\u4E00\u5C42\u5B89\u5168\u4FDD\u62A4\u3002\u542F\u7528\u4E8C\u6B21\u9A8C\u8BC1\u540E\uFF0C\u6BCF\u6B21\u767B\u5F55\u4EBF\u65B9\u4E91\u65F6\uFF0C\u9664\u5E10\u53F7\u5BC6\u7801\u4E4B\u5916\uFF0C\u8FD8\u9700\u8981\u8F93\u5165\u5B89\u5168\u9A8C\u8BC1\u7801\uFF08\u9A8C\u8BC1\u7801\u5C06\u53D1\u9001\u5230\u4F60\u7684\u624B\u673A\u4E0A\uFF09\u3002'
            }) + '</p>\n                    <a class="action-button action-icon" href="#" data-role="set">' + (0, _fangIntl.defineMessage)({
                'id': 'settings.twoStepVerification.toSet',
                'defaultMessage': '\u5F00\u542F\u4E8C\u6B21\u9A8C\u8BC1'
            }) + '</a>\n                ';
        },

        reset: function reset(data) {
            this.data = (0, _extends3.default)({}, this.data, data);
            $container.innerHTML = this.template(this.data);
        }
    };

    twoStepVerifyStatus.init();

    $container.addEventListener('click', function (e) {
        e.preventDefault();
        var target = _common2.default.matchSelector(e.target, '[data-role]');
        if (!target) {
            return;
        }

        var role = target.getAttribute('data-role');

        if (role === 'close') {
            if (!!(document.querySelector('[name=is_two_step_enabled]').value - 0)) {
                _common2.default.alert({
                    main: (0, _fangIntl.defineMessage)({
                        'id': 'settings.twoStepVerification.toClose',
                        'defaultMessage': '\u5173\u95ED\u4E8C\u6B21\u9A8C\u8BC1'
                    }),
                    sub: (0, _fangIntl.defineMessage)({
                        'id': 'settings.twoStepVerification.disableToClose',
                        'defaultMessage': '\u7BA1\u7406\u5458\u5F00\u542F\u4E86\u201C\u4F01\u4E1A\u6210\u5458\u4E8C\u6B21\u9A8C\u8BC1\u201D\u529F\u80FD\uFF0C\u4F60\u65E0\u6CD5\u5355\u72EC\u5173\u95ED\u4E8C\u6B21\u9A8C\u8BC1\u3002'
                    })
                });
            } else {
                new _twoStepVerify2.default('close_two_step', function () {
                    return twoStepVerifyStatus.init();
                });
            }
        } else if (role === 'set') {
            new _twoStepVerify2.default('set_two_step');
        } else {
            new _twoStepVerify2.default('modify_two_step');
        }
    });
}

/***/ })

},[149]);