[{"id": "settings.confirmLoginPassword", "defaultMessage": "验证身份"}, {"id": "settings.confirmLoginPasswordHint", "defaultMessage": "为保障你的帐号安全，请输入登录密码进行身份验证。"}, {"id": "base.inputLoginPassword", "defaultMessage": "请输入你的登录密码"}, {"id": "base.next", "defaultMessage": "下一步"}, {"id": "settings.setLoginPassword", "defaultMessage": "设置独立登录密码"}, {"id": "settings.setLoginPasswordHint1", "defaultMessage": "设置独立登录密码，可配合绑定的邮箱/手机登录。"}, {"id": "settings.pleaseSetPassword", "defaultMessage": "独立登录密码"}, {"id": "settings.twoStepVerification.selectVerifyTitle", "defaultMessage": "选择二次验证方式"}, {"id": "settings.twoStepVerification.wechat", "defaultMessage": "微信"}, {"id": "settings.twoStepVerification.wechatSetDes", "defaultMessage": "使用微信扫描并关注我们的微信公众号，我们会将验证码通过公众号发送给你"}, {"id": "settings.twoStepVerification.sms", "defaultMessage": "手机短信"}, {"id": "settings.twoStepVerification.smsSetDes", "defaultMessage": "设置一个手机号，我们会将验证码通过短信方式发送给你。"}, {"id": "settings.twoStepVerification.google", "defaultMessage": "谷歌身份验证器"}, {"id": "settings.twoStepVerification.googleSetDes", "defaultMessage": "你可以下载身份验证器应用来获取验证码，即使手机未连接网络也无妨。"}, {"id": "settings.twoStepVerification.recommend", "defaultMessage": "推荐"}, {"id": "settings.twoStepVerification.wechatDes1", "defaultMessage": "1. 使用微信扫描二维码并关注我们的微信公众号，你将会收到验证码。"}, {"id": "settings.twoStepVerification.wechatDes2", "defaultMessage": "如已关注公众号，可直接扫描"}, {"id": "settings.twoStepVerification.wechatDes3", "defaultMessage": "2. 输入微信公众号发给你的6位验证码。"}, {"id": "base.twoStepVerification.setSubmit", "defaultMessage": "完成设置"}, {"id": "base.previous", "defaultMessage": "上一步"}, {"id": "settings.twoStepVerification.setByWechatTitle", "defaultMessage": "设置微信"}, {"id": "settings.twoStepVerification.setBySMSTitle", "defaultMessage": "设置手机号"}, {"id": "settings.twoStepVerification.googleIos", "defaultMessage": "iPhone用户"}, {"id": "settings.twoStepVerification.googleIosDes1", "defaultMessage": "1.在iPhone上，打开App Store。"}, {"id": "settings.twoStepVerification.googleIosDes2", "defaultMessage": "2.搜索谷歌身份验证器(Google Authenticator)"}, {"id": "settings.twoStepVerification.googleIosDes3", "defaultMessage": "3.下载并安装应用。"}, {"id": "settings.twoStepVerification.googleAndroid", "defaultMessage": "Android用户"}, {"id": "twoStepVerification.googleAndroidDes1", "defaultMessage": "1.在手机上访问Google Play或其它商店。"}, {"id": "twoStepVerification.googleAndroidDes2", "defaultMessage": "2.搜索谷歌身份验证器(Google Authenticator)"}, {"id": "twoStepVerification.googleAndroidDes3", "defaultMessage": "3.下载并安装应用。"}, {"id": "settings.twoStepVerification.googleHelp", "defaultMessage": "如何安装google身份验证器？"}, {"id": "settings.twoStepVerification.downloadGoogleTitle", "defaultMessage": "下载谷歌身份验证器"}, {"id": "settings.twoStepVerification.googleVerifyQRcodeIosDes", "defaultMessage": "1. 打开谷歌身份验证器，点击+通过扫描二维码完成设置"}, {"id": "settings.twoStepVerification.googleVerifyQRcodeDes", "defaultMessage": "1.打开应用，点击菜单，选择“设置帐户”，再扫描条形码即可。"}, {"id": "settings.twoStepVerification.googleVerifyCantQRcode", "defaultMessage": "无法扫描？"}, {"id": "settings.twoStepVerification.googleVerifyToManually", "defaultMessage": "手动设置"}, {"id": "settings.twoStepVerification.googleVerifyManuallyIosDes1", "defaultMessage": "1.打开谷歌身份验证器，点击+"}, {"id": "settings.twoStepVerification.googleVerifyManuallyIosDes2", "defaultMessage": "建议你在“帐户”中输入产品和帐户名，如“{0}：<EMAIL>”"}, {"id": "settings.twoStepVerification.googleVerifyManuallyIosDes3", "defaultMessage": "在“密钥”中输入16位密钥："}, {"id": "settings.twoStepVerification.googleVerifyManuallyAndroidDes1", "defaultMessage": "1.打开应用，点击菜单，选择“设置账户”"}, {"id": "settings.twoStepVerification.googleVerifyManuallyAndroidDes2", "defaultMessage": "建议你在“账户”中输入产品和帐户名，如“{0}：<EMAIL>”"}, {"id": "settings.twoStepVerification.googleVerifyManuallyAndroidDes3", "defaultMessage": "在“密钥”中输入16位密钥"}, {"id": "settings.twoStepVerification.googleVerifyToQRcode", "defaultMessage": "返回扫描设置"}, {"id": "settings.twoStepVerification.googleVerifyInputCaptcha", "defaultMessage": "2. 输入谷歌验证器的6位验证码"}, {"id": "settings.twoStepVerification.setByGoogleTitle", "defaultMessage": "设置谷歌身份验证器"}, {"id": "settings.twoStepVerification.modifyVerifySuccessTitle", "defaultMessage": "二次验证已修改"}, {"id": "settings.twoStepVerification.setVerifySuccessTitle", "defaultMessage": "二次验证已开启"}, {"id": "settings.twoStepVerification.setVerifySuccessDes", "defaultMessage": "二次验证已开启，每次登录{0}时，除帐号密码之外，还需要输入安全验证码。"}, {"id": "settings.twoStepVerification.closeVerifySuccess", "defaultMessage": "二次验证已关闭"}]