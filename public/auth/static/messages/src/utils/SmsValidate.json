[{"id": "base.smsCaptchaPlaceholder", "defaultMessage": "请输入{0}位验证码"}, {"id": "base.getVerificationCode", "defaultMessage": "获取验证码"}, {"id": "base.retrieveAfter", "defaultMessage": "{0}秒后重新获取"}, {"id": "base.retrieve", "defaultMessage": "重新获取验证码"}, {"id": "base.getVoice", "defaultMessage": "接收语音验证码"}, {"id": "base.voiceSMSCaptcha_send", "defaultMessage": "系统将通过免费电话给您发送语音验证码，60s内未收到可重新获取"}, {"id": "base.voiceSMSCaptchaConfirm", "defaultMessage": "我们将以电话的方式发送语音验证码。请注意接听并记下{0}位验证码。"}, {"id": "base.cancelText", "defaultMessage": "下次再说"}, {"id": "base.receiveVoiceConfirm", "defaultMessage": "接收语音验证码"}]