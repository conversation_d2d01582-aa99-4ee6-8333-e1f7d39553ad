[{"id": "settings.twoStepVerification.modifyVerifySuccessTitle", "defaultMessage": "二次验证已修改"}, {"id": "settings.twoStepVerification.setVerifySuccessTitle", "defaultMessage": "二次验证已开启"}, {"id": "settings.twoStepVerification.setVerifySuccessDes", "defaultMessage": "二次验证已开启，每次登录{0}时，除帐号密码之外，还需要输入安全验证码。"}, {"id": "settings.thirdPartyBindFailure", "defaultMessage": "绑定失败"}, {"id": "settings.editPassword", "defaultMessage": "修改密码"}, {"id": "settings.oldPassword", "defaultMessage": "旧密码"}, {"id": "settings.newPassword", "defaultMessage": "新密码"}, {"id": "settings.confirmPassword", "defaultMessage": "确认密码"}, {"id": "base.submit", "defaultMessage": "提交"}, {"id": "settings.editPasswordSuccess", "defaultMessage": "密码修改成功，请重新登录"}, {"id": "base.confirm", "defaultMessage": "确认"}, {"id": "settings.email", "defaultMessage": "邮箱："}, {"id": "settings.phone", "defaultMessage": "手机："}, {"id": "settings.emptyEmailInfo", "defaultMessage": "绑定且验证后可用邮箱登录"}, {"id": "settings.emptyPhoneInfo", "defaultMessage": "绑定后可用手机登录"}, {"id": "settings.unvalidate", "defaultMessage": "未验证"}, {"id": "settings.unvalidateCredentialUsage", "defaultMessage": "验证后可用于登录、找回密码"}, {"id": "settings.validateTime", "defaultMessage": "验证时间：{0}"}, {"id": "settings.validateCredentialUsage", "defaultMessage": "可用于登录、找回密码"}, {"id": "settings.validate", "defaultMessage": "立即验证"}, {"id": "settings.bindImmediately", "defaultMessage": "立即绑定"}, {"id": "settings.phoneNotAvaliable", "defaultMessage": "该手机号已被其它帐号占用，请修改。"}, {"id": "settings.emailNotAvaliable", "defaultMessage": "该邮箱已被其它帐号占用，请修改。"}, {"id": "base.cancelText", "defaultMessage": "下次再说"}, {"id": "settings.editNow", "defaultMessage": "立即修改"}, {"id": "settings.forbiddenEditEmail", "defaultMessage": "暂时无法修改邮箱。请在手机号绑定成功后再试。"}, {"id": "settings.editPhone", "defaultMessage": "修改手机"}, {"id": "settings.validatePhone", "defaultMessage": "验证手机"}, {"id": "settings.bindPhone", "defaultMessage": "绑定手机"}, {"id": "settings.editPhoneSuccess", "defaultMessage": "手机号已修改成功"}, {"id": "settings.validatePhoneSuccess", "defaultMessage": "手机号已验证成功"}, {"id": "settings.bindPhoneSuccess", "defaultMessage": "手机号已绑定成功"}, {"id": "settings.currentCredential", "defaultMessage": "当前绑定：{0}"}, {"id": "settings.mobilePlaceholder", "defaultMessage": "请输入你的手机号"}, {"id": "settings.editEmail", "defaultMessage": "修改邮箱"}, {"id": "settings.validateEmail", "defaultMessage": "验证邮箱"}, {"id": "settings.bindEmail", "defaultMessage": "绑定邮箱"}, {"id": "settings.enterNewEmail", "defaultMessage": "请输入新的邮箱"}, {"id": "settings.emailSended", "defaultMessage": "验证邮件已发送至{0}，请登录你的邮箱查看。验证后，该邮箱可以用于登录、找回密码。"}, {"id": "settings.emailSendedTips", "defaultMessage": "邮件可能会被拦截，如未在收件箱中找到，可尝试在垃圾邮件中查找"}, {"id": "settings.unbind", "defaultMessage": "解绑"}, {"id": "settings.qiyeweixinLabel", "defaultMessage": "企业微信："}, {"id": "settings.dingdingLabel", "defaultMessage": "钉钉："}, {"id": "settings.qihoo360Label", "defaultMessage": "360账号："}, {"id": "settings.dingding", "defaultMessage": "钉钉"}, {"id": "settings.qi<PERSON><PERSON><PERSON>", "defaultMessage": "企业微信"}, {"id": "settings.qihoo360", "defaultMessage": "360"}, {"id": "settings.unbindThirdConfirmMain", "defaultMessage": "你确定要解绑{0}帐号“{1}”吗？"}, {"id": "settings.unbindConfirmSub", "defaultMessage": "解绑后，将无法使用该帐号登录"}, {"id": "settings.device.fromAndroid", "defaultMessage": "（Android App）"}, {"id": "settings.device.fromIos", "defaultMessage": "（iOS App）"}, {"id": "settings.device.fromWindowsSync", "defaultMessage": "（Windows同步端）"}, {"id": "settings.device.fromMacSync", "defaultMessage": "（Mac同步端）"}, {"id": "settings.device.fromWindowsDesktop", "defaultMessage": "（Windows客户端）"}, {"id": "settings.device.fromMacDesktop", "defaultMessage": "（Mac客户端）"}, {"id": "settings.device.deviceName", "defaultMessage": "设备名称"}, {"id": "settings.device.deivceLastLogin", "defaultMessage": "最近访问"}, {"id": "settings.device.deivceLastLocation", "defaultMessage": "最近访问地"}, {"id": "settings.device.deivceOperation", "defaultMessage": "操作"}, {"id": "settings.device.creditable", "defaultMessage": "可信"}, {"id": "settings.device.current", "defaultMessage": "当前"}, {"id": "settings.device.ip", "defaultMessage": "IP地址：{0}"}, {"id": "base.delete", "defaultMessage": "删除"}, {"id": "settings.device.noresult", "defaultMessage": "暂无数据"}, {"id": "settings.device.showMore", "defaultMessage": "展开全部"}, {"id": "settings.device.deleteIsCreditable", "defaultMessage": "在该设备上将退出登录，你需要重新登录才能继续访问，再次登录需要进行二次验证。"}, {"id": "settings.device.deleteNotCreditable", "defaultMessage": "在该设备上将退出登录，你需要重新登录才能继续访问。"}, {"id": "settings.device.deleteDevice", "defaultMessage": "删除此设备"}, {"id": "settings.device.hideMore", "defaultMessage": "收起"}, {"id": "settings.twoStepVerification.typePhone", "defaultMessage": "手机短信验证"}, {"id": "settings.twoStepVerification.typePhoneNumber", "defaultMessage": "（尾号为 {0}）"}, {"id": "settings.twoStepVerification.typeWechat", "defaultMessage": "微信公众号验证"}, {"id": "settings.twoStepVerification.typeGoogle", "defaultMessage": "谷歌验证器验证"}, {"id": "settings.twoStepVerification.hasOpen", "defaultMessage": "二次验证已开启"}, {"id": "settings.twoStepVerification.validateTime", "defaultMessage": "（设置于 {0}）"}, {"id": "settings.twoStepVerification.toClose", "defaultMessage": "关闭二次验证"}, {"id": "settings.twoStepVerification.type", "defaultMessage": "验证方式："}, {"id": "base.modify", "defaultMessage": "修改"}, {"id": "settings.twoStepVerification.info", "defaultMessage": "二次验证为你的帐户增加一层安全保护。启用二次验证后，每次登录亿方云时，除帐号密码之外，还需要输入安全验证码（验证码将发送到你的手机上）。"}, {"id": "settings.twoStepVerification.toSet", "defaultMessage": "开启二次验证"}, {"id": "settings.twoStepVerification.disableToClose", "defaultMessage": "管理员开启了“企业成员二次验证”功能，你无法单独关闭二次验证。"}]