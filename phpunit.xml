<?xml version="1.0" encoding="UTF-8"?>
<phpunit backupGlobals="false"
         backupStaticAttributes="false"
         bootstrap="vendor/autoload.php"
         colors="true"
         convertErrorsToExceptions="true"
         convertNoticesToExceptions="true"
         convertWarningsToExceptions="true"
         processIsolation="false"
         stopOnFailure="false"
         beStrictAboutOutputDuringTests="false"
         timeoutForSmallTests="1"
         timeoutForMediumTests="10"
         timeoutForLargeTests="60">
    <testsuites>
        <testsuite name="Feature">
            <directory suffix="Test.php">./tests/Feature</directory>
        </testsuite>

        <testsuite name="Unit">
            <directory suffix="Test.php">./tests/Unit</directory>
        </testsuite>
    </testsuites>
    <filter>
        <whitelist processUncoveredFilesFromWhitelist="true">
            <directory suffix=".php">./app</directory>
        </whitelist>
    </filter>

    <logging>
        <log type="coverage-html" target="./tests/Result/report" lowUpperBound="35"
             highLowerBound="70"/>
        <!--<log type="coverage-clover" target="./tests/Result/coverage.xml"/>-->
        <!--<log type="coverage-php" target="./tests/Result/coverage.serialized"/>-->
        <log type="coverage-text" target="php://stdout" showUncoveredFiles="false"/>
        <!--<log type="junit" target="./tests/Result/logfile.xml" logIncompleteSkipped="false"/>-->
        <!--<log type="testdox-html" target="./tests/Result/testdox.html"/>-->
        <!--<log type="testdox-text" target="./tests/Result/testdox.txt"/>-->
    </logging>

    <php>
        <env name="APP_ENV" value="testing"/>
        <env name="CACHE_DRIVER" value="array"/>
        <env name="SESSION_DRIVER" value="array"/>
        <env name="QUEUE_DRIVER" value="sync"/>
    </php>
</phpunit>
