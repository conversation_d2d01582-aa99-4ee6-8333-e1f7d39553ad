<?php

namespace Tests\Unit\Api;

use App\Constants\Http;
use App\Library\HttpClient\Guard;
use App\Models\AuthToken;
use App\Models\ClientDevice;
use App\Models\Service;
use App\Models\User;
use App\Services\Security\ClientDeviceDelete;
use Tests\TestCase;

class SecurityControllerTest extends TestCase
{
    /**
     * @test
     * @group Security
     */
    public function testCreateAuthToken()
    {
        $response = $this->createAuthToken();

        $response
            ->assertStatus(200)
            ->assertJsonStructure($this->getAuthTokenStruct());

        return $response->json();
    }

    private function createAuthToken()
    {
        $response = $this
            ->withHeaders($this->getGuardHeaders($this->web_guard, config('services.internal_service_id')))
            ->json('POST', '/api/v2/security/create_auth_token', [
                'user_id' => $this->getTestUser()->user_id,
                'service_id' => $this->getTestService()->id,
            ]);
        return $response;
    }

    /**
     * @test
     * @group Security
     * @depends testCreateAuthToken
     *
     * @param array $auth_token
     * @return array
     */
    public function testCheckDevice(array $auth_token)
    {
        $auth_token = $auth_token['auth_token']['auth_token'];

        $device_type = ClientDevice::IOS_APP_NAME;
        $response = $this->checkDevice($auth_token, $device_type);

        $response
            ->assertStatus(200)
            ->assertJsonStructure($this->getSuccessStruct());

        return ['auth_token' => $auth_token, 'device_type' => $device_type];
    }

    /**
     * @test
     * @group Security
     * @depends testCheckDevice
     *
     * @param array $params
     * @throws \App\Exceptions\ValidationException
     * @throws \Exception
     * @throws \Throwable
     */
    public function testCheckDeviceDeleted(array $params)
    {
        $auth_token = $params['auth_token'];
//        $device_type = $params['device_type'];

        $this->actingAs($this->getTestUser());

        $client_device = ClientDevice::findByAuthToken(AuthToken::findByAuthToken($auth_token)->id);

        (new ClientDeviceDelete())->handle(['id' => $client_device->id]);

        $this->assertDatabaseMissing('auth_tokens', ['auth_token' => $auth_token]);
        $client_device = ClientDevice::findById($client_device->id);
        $this->assertGreaterThan(0, $client_device->deleted);
    }

    /**
     * 发送一个检查设备的 request
     *
     * @param $auth_token
     * @param string $device_type
     * @return \Illuminate\Foundation\Testing\TestResponse
     */
    private function checkDevice($auth_token, $device_type = ClientDevice::IOS_APP_NAME)
    {
        $device_token = $this->getDeviceInfo($device_type)['device_token'];
        return $this
            ->withHeaders($this->getGuardHeaders($this->web_guard, config('services.internal_service_id')))
            ->withHeader(Http::HEADER_DEVICE_TOKEN, $device_token)
            ->withHeader(Http::HEADER_AUTH_TOKEN, $auth_token)
            ->json('POST', '/api/v2/security/check_device', [
                'auth_token' => $auth_token,
                'device_token' => $device_token,
                'additional_info' => $this->getDeviceInfo($device_type)['egeio_client_info'],
            ]);
    }

    /**
     * 典型的几种设备信息
     *
     * @param $device_type
     * @return mixed
     */
    private function getDeviceInfo($device_type)
    {
        if (!in_array($device_type, [ClientDevice::IOS_APP_NAME, ClientDevice::ANDROID_APP_NAME, ClientDevice::MAC_DESKTOP_NAME, ClientDevice::WIN_DESKTOP_NAME])) {
            throw new \RuntimeException('invalid device type');
        }
        return [
            ClientDevice::IOS_APP_NAME => [
                'egeio_client_info' => "{\"scale\":2,\"deviceName\":\"%E5%B0%8F%E5%B0%8Fiphone7-10\",\"appName\":\"%E4%BA%BF%E6%96%B9%E4%BA%91V2\",\"Device-ID\":\"D9606C73-5CF2-4909-B42B-178FE60708B1\",\"deviceType\":\"iPhone\",\"language\":\"zh-CN\",\"bundleId\":\"com.egeio.fangcloud\",\"systemVersion\":\"iOS 10.3.2\",\"buildId\":\"10422\",\"versionNumber\":\"3.5.1\"}",
                'device_token' => 'D9606C73-5CF2-4909-B42B-178FE60708B1',
            ],
            ClientDevice::ANDROID_APP_NAME => [
                'egeio_client_info' => "{\"deviceType\":\"Android\",\"Device-ID\":\"864854034229224\",\"appName\":\"%E4%BA%BF%E6%96%B9%E4%BA%91V2\",\"version_check_enabled\":false,\"scale\":2.625,\"language\":\"zh-CN\",\"systemVersion\":\"Android 8.0.0\",\"deviceName\":\"ONEPLUS+A3010\",\"versionNumber\":\"3.6.0\"}",
                'device_token' => '860955022947513',
            ],
            ClientDevice::MAC_DESKTOP_NAME => [
                'egeio_client_info' => "{\"is_enable_trial\": false, \"mac_system_version\": \"10.13.3\", \"platform_id\": 4, \"appName\": \"sync\", \"versionNumber\": \"5.4.00095\", \"cpuInfo\": \"i386\", \"product_id\": \"fangcloud_v2\", \"deviceName\": \"kangsunlei\", \"systemVersion\": \"Darwin-17.4.0-x86_64-i386-64bit\", \"version_check_enabled\": true, \"deviceType\": \"Mac OSX 10.13.3\", \"language\": \"zh-CN\", \"is_enterprise_version\": false}",
                'device_token' => 'd0a637eeffeb',
            ],
            ClientDevice::WIN_DESKTOP_NAME => [
                'egeio_client_info' => "{\"deviceType\": \"windows 7\", \"systemVersion\": \"Windows-7-6.1.7601-SP1\", \"appName\": \"sync\", \"is_enable_trial\": false, \"language\": \"zh-CN\", \"product_id\": \"fangcloud_v2\", \"version_check_enabled\": true, \"platform_id\": 3, \"versionNumber\": \"6.0.00100\", \"is_enterprise_version\": false, \"windows_system_version\": \"6.1\", \"cpuInfo\": \"Intel64 Family 6 Model 61 Stepping 4, GenuineIntel\", \"deviceName\": \"PC-20180130CNLW\"}",
                'device_token' => '1002b5cb91d9',
            ],
        ][$device_type];
    }

    /**
     * @test
     * @group Security
     *
     * @return array
     */
    public function testRefreshAuthToken()
    {
        $auth_token = $this->createAuthToken()->json();

        $refresh_token = $auth_token['auth_token']['refresh_token'];

        $response = $this
            ->withHeaders($this->getGuardHeaders($this->web_guard, config('services.internal_service_id')))
            ->json('POST', '/api/v2/security/refresh_auth_token', [
                'refresh_token' => $refresh_token
            ]);

        $response
            ->assertStatus(200)
            ->assertJsonStructure($this->getAuthTokenStruct());

        $this->assertDatabaseMissing('auth_tokens', ['refresh_token' => $refresh_token]);

        return $response->json();
    }

    /**
     * @test
     * @group Security
     * @depends testRefreshAuthToken
     *
     * @param array $auth_token
     */
    public function testRevokeAuthToken(array $auth_token)
    {
        $auth_token = $auth_token['auth_token']['auth_token'];

        $response = $this
            ->withHeaders($this->getGuardHeaders($this->web_guard, config('services.internal_service_id')))
            ->json('POST', '/api/v2/security/revoke_auth_token', [
                'auth_token' => $auth_token
            ]);

        $response
            ->assertStatus(200)
            ->assertJsonStructure($this->getSuccessStruct());

        $this->assertDatabaseMissing('auth_tokens', ['auth_token' => $auth_token]);
    }

    private function getAuthTokenStruct()
    {
        return [
                'auth_token' => [
                'auth_token',
                'refresh_token',
                'expires_at'
            ]];
    }

    private function getSuccessStruct()
    {
        return [
            'success'
        ];
    }

    private static $guards = [];
    private function getGuard($service_id)
    {
        if (!isset(static::$guards[$service_id])) {
            static::$guards[$service_id] = new Guard($service_id);
        }
        return static::$guards[$service_id];
    }

    private function getTestUser()
    {
        return User::findByMainUserId(792343);
    }

    private function getTestService()
    {
        return Service::findById(2);
    }

    private $web_guard = null;

    public function setUp()
    {
        parent::setUp();

        if ($this->web_guard == null) {
            $this->web_guard = $this->getGuardAsWeb();
        }
    }

    private function getGuardAsWeb()
    {
        return $this->getGuard(config('services.web_service_id'));
    }

    private function getGuardHeaders($guard, $service_id)
    {
        $reflection_guard = new \ReflectionClass(Guard::class);
        $method = $reflection_guard->getMethod('getAuthHeaders');
        return $method->getClosure($guard)($service_id);
    }
}
