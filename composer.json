{"name": "laravel/laravel", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "type": "project", "repositories": [{"type": "composer", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/qiyiyun_composer/"}, {"type": "composer", "url": "http://s7.qbuild.corp.qihoo.net:9081/nexus/repository/packagist/"}], "require": {"php": ">=7.0.0", "fangcloud/ip": "^1.0", "fideloper/proxy": "~3.3", "guzzlehttp/guzzle": "^6.3", "illuminate/log": "********", "jenssegers/agent": "^2.6", "laravel/framework": "5.5.21", "laravel/tinker": "~1.0", "league/oauth2-client": "^2.2", "mews/captcha": "*******", "predis/predis": "^1.1", "simplesoftwareio/simple-qrcode": "^2.0", "spomky-labs/otphp": "^9.0", "vladimir-yuldashev/laravel-queue-rabbitmq": "5.5", "fangcloud/content-detection": "1.0.0"}, "require-dev": {"doctrine/dbal": "~2.3", "barryvdh/laravel-ide-helper": "~2.4", "filp/whoops": "~2.0", "fzaninotto/faker": "~1.4", "mockery/mockery": "~1.0", "phpunit/phpunit": "~6.0"}, "autoload": {"classmap": ["database/seeds", "database/factories"], "psr-4": {"App\\": "app/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "extra": {"laravel": {"dont-discover": []}}, "scripts": {"post-root-package-install": ["@php -r \"file_exists('.custom.env') || copy('.env.example', '.custom.env');\""], "post-create-project-cmd": ["@php artisan key:generate"], "post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "php artisan ide-helper:generate", "php artisan ide-helper:meta", "@php artisan package:discover"]}, "config": {"preferred-install": "dist", "sort-packages": true, "optimize-autoloader": true, "secure-http": false}}