<?php

$phoenix_url = env('SERVICE_PHOENIX_BASE_URI', 'http://staging.fangcloud.net:9898/');
if(env('APP_ENV','local') === 'profession') {
    $phoenix_url = 'http://phoenix-svc:9898/';
}

return [

    /*
    |--------------------------------------------------------------------------
    | 亿方云相关 'services' 的配置
    |--------------------------------------------------------------------------
    | 常见于原cloudoffice services.php
    */

    'internal_service_id' => env('INTERNAL_AUTH_SERVICE_ID', 294),
    'guard' => [
        'secret_expiration' => env('SERVICE_GUARD_SECRET_EXPIRATION', 86400),
    ],
    'phoenix' => env('SERVICE_PHOENIX_BASE_URI', 'http://staging.fangcloud.net:9898/'),
    'web_service_id' => env('WEB_AUTH_SERVICE_ID', 1),
    'internal_services' => [
        'web' => env('WEB_AUTH_SERVICE_ID'),
    ],

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Stripe, Mailgun, SparkPost and others. This file provides a sane
    | default location for this type of information, allowing packages
    | to have a conventional place to find your various credentials.
    |
    */

    'mailgun' => [
        'domain' => env('MAILGUN_DOMAIN'),
        'secret' => env('MAILGUN_SECRET'),
    ],

    'ses' => [
        'key' => env('SES_KEY'),
        'secret' => env('SES_SECRET'),
        'region' => 'us-east-1',
    ],

    'sparkpost' => [
        'secret' => env('SPARKPOST_SECRET'),
    ],

    'stripe' => [
        'model' => App\User::class,
        'key' => env('STRIPE_KEY'),
        'secret' => env('STRIPE_SECRET'),
    ],

    'telegram-bot-api' => [
        'token' => env('TELEGRAM_BOT_TOKEN', 'YOUR BOT TOKEN HERE')
    ],
];
