<?php
/**
 * 注：该文件中的变量会业务逻辑中所规定的各种参数，常见于原 config.php / application.php
 *
 * 在该文件中的 default 值应该为测试环境的值。
 * 生产环境的值请设置 env 并且放在 env.app 文件中。
 */

return [
    'auth_token' => [
        'expires_at' => env('COMMON_AUTH_TOKEN_EXPIRES_AT_DURATION', 3600),
    ],

    'service' => [
        'web_service_id' => env('COMMON_SERVICE_WEB', 0),
        'mobile_app_service_ids' => env('COMMON_SERVICE_MOBILE_APP_SERVICE_IDS', '1,2'),
        'ios_app_service_ids' => env('COMMON_SERVICE_IOS_APP_SERVICE_IDS', '1'),
        'android_app_service_ids' => env('COMMON_SERVICE_ANDROID_APP_SERVICE_IDS', '2'),
        'sync_client_service_ids' => env('COMMON_SERVICE_SYNC_CLIENT_SERVICE_IDS', '6,8'),
        'mac_sync_client_service_ids' => env('COMMON_SERVICE_MAC_SYNC_CLIENT_SERVICE_IDS', '5'),
        'win_sync_client_service_ids' => env('COMMON_SERVICE_WIN_SYNC_CLIENT_SERVICES_IDS', '6'),
    ],

    'two_step' => [
        'creditable_duration' => env('COMMON_TWO_STEP_CREDITABLE_DURATION', 86400),
        'device_last_login_period' => env('COMMON_TWO_STEP_DEVICE_LAST_LOGIN_PERIOD', 43200),
        'wechat_permanent_qrcode_ticket' => env('COMMON_TWO_STEP_WECHAT_PERMANENT_QRCODE_TICKET'),
    ],

    'content_detection' => [
        'text_scan' => env('COMMON_CONTENT_DETECTION_TEXT_SCAN', true),
    ],

    'encryption_min_client_version_limit' => [
        \App\Constants\Platform::PLATFORM_ID_SYNC_WIN => env('ENCRYPTION_MIN_CLIENT_VERSION_PLATFORM_WINDOWS'),
    ],

    'jd_storage_id' => env('COMMON_JD_STORAGE_ID'),
    'jd_file_server_id' => env('COMMON_JD_FILE_SERVER_ID'),
];
