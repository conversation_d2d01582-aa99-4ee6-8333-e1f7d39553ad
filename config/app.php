<?php

$api_url = env('API_URL', 'http://localhost');
if(env('APP_ENV','local') === 'profession') {
    $api_url = 'http://v2-svc/';
}

return [

    /*
    |--------------------------------------------------------------------------
    | Application Name
    |--------------------------------------------------------------------------
    |
    | This value is the name of your application. This value is used when the
    | framework needs to place the application's name in a notification or
    | any other location as required by the application or its packages.
    |
    */

    'name' => env('APP_NAME', 'Fangcloud Account'),

    /*
    |--------------------------------------------------------------------------
    | Application Environment
    |--------------------------------------------------------------------------
    |
    | This value determines the "environment" your application is currently
    | running in. This may determine how you prefer to configure various
    | services your application utilizes. Set this in your ".env" file.
    |
    */

    'env' => env('APP_ENV', 'production'),

    /*
    |--------------------------------------------------------------------------
    | Application Debug Mode
    |--------------------------------------------------------------------------
    |
    | When your application is in debug mode, detailed error messages with
    | stack traces will be shown on every error that occurs within your
    | application. If disabled, a simple generic error page is shown.
    |
    */

    'debug' => env('APP_DEBUG', false),

    /*
    |--------------------------------------------------------------------------
    | Application URL
    |--------------------------------------------------------------------------
    |
    | This URL is used by the console to properly generate URLs when using
    | the Artisan command line tool. You should set this to the root of
    | your application so that it is used when running Artisan tasks.
    |
    */

    'url' => getenv('EXTERNAL_ACCOUNT_URL') ? getenv('EXTERNAL_ACCOUNT_URL') . '/' : env('ACCOUNT_BASE_URL', 'https://account.fangcloud.com'),

    // TODO different URL
    'static' => env('APP_STATIC', ''),
    'static_url' => getenv('EXTERNAL_ACCOUNT_URL') ? getenv('EXTERNAL_ACCOUNT_URL') .'/' : env('STATIC_URL', ''),
    'dev_asset_url' => env('DEV_ASSET_URL_AUTH', ''),
    'asset_path' => env('ASSET_PATH_AUTH', ''),
    'assets_hash' => 'assets-hash.json',
    'analytic_enable' => env('analytic_enable', ''),

    'default_page' => getenv('EXTERNAL_WWW_URL') ? getenv('EXTERNAL_WWW_URL').'/apps/files/desktop' : env('DEFAULT_PAGE', '/'),
    'default_h5_page' => env('DEFAULT_H5_PAGE', '/'),
    'h5_download_notice' => env('H5_DOWNLOAD_NOTICE', '/'),

    'api_url' => $api_url,

    'v1_url' => getenv('EXTERNAL_WWW_URL') ? getenv('EXTERNAL_WWW_URL') . '/' : env('V1_URL', 'http://localhost'),
    'disable_v1' => env('DISABLE_V1', false),

    'fangcloud_url' => getenv('EXTERNAL_WWW_URL') ? getenv('EXTERNAL_WWW_URL') . '/' : env('FANGCLOUD_URL', 'https://www.fangcloud.com/'),
    'help_url' => getenv('EXTERNAL_HELP_URL') ? getenv('EXTERNAL_HELP_URL') . '/' : env('HELP_URL', 'https://help.fangcloud.com/'),

    'app_download_url' => getenv('EXTERNAL_APP_DOWNLOAD_URL') ?: (env('FANGCLOUD_URL', 'https://www.fangcloud.com/'). '/home/<USER>'),

    'product_id' => env('PRODUCT_ID'),

    'yidun_url' => env('YiDun_URL', 'https://ye.dun.163yun.com/v1/oneclick/check'),

    /*
    |--------------------------------------------------------------------------
    | Application Timezone
    |--------------------------------------------------------------------------
    |
    | Here you may specify the default timezone for your application, which
    | will be used by the PHP date and date-time functions. We have gone
    | ahead and set this to a sensible default for you out of the box.
    |
    */

    'timezone' => 'Asia/Shanghai',

    /*
    |--------------------------------------------------------------------------
    | Application Locale Configuration
    |--------------------------------------------------------------------------
    |
    | The application locale determines the default locale that will be used
    | by the translation service provider. You are free to set this value
    | to any of the locales which will be supported by the application.
    |
    */

    'locale' => env('LOCALE', 'zh-CN'),

    /*
    |--------------------------------------------------------------------------
    | Application Fallback Locale
    |--------------------------------------------------------------------------
    |
    | The fallback locale determines the locale to use when the current one
    | is not available. You may change the value to correspond to any of
    | the language folders that are provided through your application.
    |
    */

    'fallback_locale' => 'en',

    /*
    |--------------------------------------------------------------------------
    | Encryption Key
    |--------------------------------------------------------------------------
    |
    | This key is used by the Illuminate encrypter service and should be set
    | to a random, 32 character string, otherwise these encrypted strings
    | will not be safe. Please do this before deploying an application!
    |
    */

    'key' => env('APP_KEY'),

    'cipher' => 'AES-256-CBC',

    /*
    |--------------------------------------------------------------------------
    | Logging Configuration
    |--------------------------------------------------------------------------
    |
    | Here you may configure the log settings for your application. Out of
    | the box, Laravel uses the Monolog PHP logging library. This gives
    | you a variety of powerful log handlers / formatters to utilize.
    |
    | Available Settings: "single", "daily", "syslog", "errorlog"
    |
    */

    'log' => env('APP_LOG', 'daily'),

    'log_level' => env('APP_LOG_LEVEL', 'debug'),

    'log_exception_trace_chars' => env('EXCEPTION_TRACE_LINES', 2000),

    'log_channel' => 'account',

    /*
    |--------------------------------------------------------------------------
    | Autoloaded Service Providers
    |--------------------------------------------------------------------------
    |
    | The service providers listed here will be automatically loaded on the
    | request to your application. Feel free to add your own services to
    | this array to grant expanded functionality to your applications.
    |
    */

    'providers' => [

        /*
         * Laravel Framework Service Providers...
         */
        Illuminate\Auth\AuthServiceProvider::class,
        Illuminate\Broadcasting\BroadcastServiceProvider::class,
        Illuminate\Bus\BusServiceProvider::class,
        Illuminate\Cache\CacheServiceProvider::class,
        Illuminate\Foundation\Providers\ConsoleSupportServiceProvider::class,
        Illuminate\Cookie\CookieServiceProvider::class,
        Illuminate\Database\DatabaseServiceProvider::class,
        Illuminate\Encryption\EncryptionServiceProvider::class,
        Illuminate\Filesystem\FilesystemServiceProvider::class,
        Illuminate\Foundation\Providers\FoundationServiceProvider::class,
        Illuminate\Hashing\HashServiceProvider::class,
        Illuminate\Mail\MailServiceProvider::class,
        Illuminate\Notifications\NotificationServiceProvider::class,
        Illuminate\Pagination\PaginationServiceProvider::class,
        Illuminate\Pipeline\PipelineServiceProvider::class,
        Illuminate\Queue\QueueServiceProvider::class,
        Illuminate\Redis\RedisServiceProvider::class,
        Illuminate\Auth\Passwords\PasswordResetServiceProvider::class,
        Illuminate\Session\SessionServiceProvider::class,
        Illuminate\Translation\TranslationServiceProvider::class,
        Illuminate\Validation\ValidationServiceProvider::class,
        Illuminate\View\ViewServiceProvider::class,

        /*
         * Package Service Providers...
         */

        /*
         * Application Service Providers...
         */
        App\Providers\AppServiceProvider::class,
        App\Providers\AuthServiceProvider::class,
        // App\Providers\BroadcastServiceProvider::class,
        App\Providers\EventServiceProvider::class,
        App\Providers\RouteServiceProvider::class,
    ],

    /*
    |--------------------------------------------------------------------------
    | Class Aliases
    |--------------------------------------------------------------------------
    |
    | This array of class aliases will be registered when this application
    | is started. However, feel free to register as many as you wish as
    | the aliases are "lazy" loaded so they don't hinder performance.
    |
    */

    'aliases' => [

        'App' => Illuminate\Support\Facades\App::class,
        'Artisan' => Illuminate\Support\Facades\Artisan::class,
        'AuthHelper' => Illuminate\Support\Facades\Auth::class,
        'Blade' => Illuminate\Support\Facades\Blade::class,
        'Broadcast' => Illuminate\Support\Facades\Broadcast::class,
        'Bus' => Illuminate\Support\Facades\Bus::class,
        'Cache' => Illuminate\Support\Facades\Cache::class,
        'Config' => Illuminate\Support\Facades\Config::class,
        'Cookie' => Illuminate\Support\Facades\Cookie::class,
        'Crypt' => Illuminate\Support\Facades\Crypt::class,
        'DB' => Illuminate\Support\Facades\DB::class,
        'Eloquent' => Illuminate\Database\Eloquent\Model::class,
        'Event' => Illuminate\Support\Facades\Event::class,
        'File' => Illuminate\Support\Facades\File::class,
        'Gate' => Illuminate\Support\Facades\Gate::class,
        'Hash' => Illuminate\Support\Facades\Hash::class,
        'Lang' => Illuminate\Support\Facades\Lang::class,
        'Log' => Illuminate\Support\Facades\Log::class,
        'Mail' => Illuminate\Support\Facades\Mail::class,
        'Notification' => Illuminate\Support\Facades\Notification::class,
        'Password' => Illuminate\Support\Facades\Password::class,
        'Queue' => Illuminate\Support\Facades\Queue::class,
        'Redirect' => Illuminate\Support\Facades\Redirect::class,
        'Redis' => Illuminate\Support\Facades\Redis::class,
        'Request' => Illuminate\Support\Facades\Request::class,
        'Response' => Illuminate\Support\Facades\Response::class,
        'Route' => Illuminate\Support\Facades\Route::class,
        'Schema' => Illuminate\Support\Facades\Schema::class,
        'Session' => Illuminate\Support\Facades\Session::class,
        'Storage' => Illuminate\Support\Facades\Storage::class,
        'URL' => Illuminate\Support\Facades\URL::class,
        'Validator' => Illuminate\Support\Facades\Validator::class,
        'View' => Illuminate\Support\Facades\View::class,
        'ViewHelper' => App\Util\ViewHelper::class,

    ],


    /*
     * some cookie settings
     */

    'egeio_client_info_cookie_name' => 'egeio-client-info',
    'language_cookie_name' => 'lang',
    'client_lang_cookie_name' => 'client_lang',
    'lang_cookie_expire' => 2592000,

    'sem_cookie_name' => 'sem',
    'sem_cookie_expire' => 1209600,

    'register_origin_cookie_name' => 'register_origin',
    'register_origin_cookie_expire' => 1209600,

    'path' => [
        'storage' => env('PATH_STORAGE', null),
    ],

    'skip_captcha_verify' => env('SKIP_CAPTCHA_VERIFY', false),
    'skip_yidun_verify' => env('SKIP_YIDUN_VERIFY', false),
    'yidun_secret_id' => '9a41b0fcf99bff23378674c0f1215d5f',
    'yidun_business_id' => 'fd7b01e63319488fbbf33da281eee9e1',
    'yidun_secret_key' => 'bfbba8b45b44e9f24f2551c3f54b4518',

    'skip_crm_import' => env('SKIP_CRM_IMPORT', false),
    'is_profession' => env('APP_ENV','local') === 'profession',
    'env_config' => [
        'env' => env('APP_ENV','local'),
        'config' => [
            'is_dlp_enabled' => getenv('IS_DLP_ENABLED') === 'true',
            'is_third_login_enabled' => getenv('IS_THIRD_LOGIN_ENABLED') === 'true',
            'is_special_preview_enabled' => getenv('IS_SPECIAL_PREVIEW_ENABLED') === 'true',
            'is_admin_show_service_plan' => getenv('IS_ADMIN_SHOW_SERVICE_PLAN') === 'true',
            'is_show_help_chat' => getenv('IS_SHOW_HELP_CHAT') === 'true',
            'is_show_calendar' => getenv('IS_SHOW_CALENDAR') === 'true',
            'is_multi_storage_enabled' => getenv('IS_MULTI_STORAGE_ENABLED') === 'true',
            'is_xiaoshouyi_enabled' => getenv('IS_XIAOSHOUYI_ENABLED') === 'true',
            'is_overseas_acceleration_enabled' => getenv('IS_OVERSEAS_ACCELERATION_ENABLED') === 'true',
            'is_qiye_wechat_share_enabled' => getenv('IS_QIYE_WECHAT_SHARE_ENABLED') === 'true',
            'is_sensitive_info_detect_enabled' => getenv('IS_SENSITIVE_INFO_DETECT_ENABLED') === 'true',
            'is_feed_enabled' => getenv('IS_FEED_ENABLED') === 'true',
            'is_virus_scan_enabled' => getenv('IS_VIRUS_SCAN_ENABLED') === 'true',
            'is_yiqixie_enabled' => getenv('IS_YIQIXIE_ENABLED') === 'true',
            'is_wps_preview_enabled' => getenv('IS_WPS_PREVIEW_ENABLED') === 'true',
            'is_wps_edit_enabled' => getenv('IS_WPS_EDIT_ENABLED') === 'true',
            'is_unzip_enabled' => getenv('IS_UNZIP_ENABLED') === 'true',
        ],
    ],
];
