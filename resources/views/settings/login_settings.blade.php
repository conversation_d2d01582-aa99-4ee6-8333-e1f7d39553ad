@extends('layouts.settings')
@section('pageTitle', __('view.LOGIN_SETTINGS'))

@section('content')
@if(!$user['enterprise'] ||
    $user['enterprise']['id'] == 0 ||
    ($user['enterprise'] && $user['enterprise']['id'] != 0 && !$user['enterprise']['is_sso_enterprise'] ||
    ($user['enterprise']['is_sso_enterprise'] && $user['enterprise']['is_public_login_available']))
)
<!-- 企业微信注册过的用户隐藏相关按钮，标记点-->
<!-- $user['enterprise']['id'] != 0 不是个人用户 -->
@if($user['enterprise'] && $user['enterprise']['id'] != 0 && $user['enterprise']['is_from_wechat_enterprise']) 
    <input type="hidden" id="is_from_wechat_enterprise" name="is_from_wechat_enterprise" />
@endif
    <div class="fieldset login-credentials-filed">
        <h4>{{__('view.LOGIN_CREDENTIALS')}}</h4>
        <div class="account-settings field-section"></div>
        @if(ViewHelper::isFangcloud()&&!(ViewHelper::env_is_profession() && !ViewHelper::get_env_config('is_third_login_enabled')))
        <h5 class="title-hint">{{__('view.THIRD_PARTY_DESCRIPTIONS')}}</h5>
        <div class="third-party field-section"></div>
        @endif
    </div>

    <div class="fieldset password-setting-filed">
        <h4>{{__('view.PASSWORD_SETTINGS')}}</h4>
        <div class="password-settings field-section">
            <ul class="settings-form">
                <li class="form-group">
                    <a href="#" class="edit-password btn btn-default">{{__('view.MOIDFY_PASSWORD')}}</a>
                </li>
            </ul>
        </div>
    </div>
    @if(($user['enterprise']['id'] == 0 && $user['created'] < **********) || ($user['enterprise']['id'] != 0 && $user['enterprise']['plan_id'] != 25 && $user['enterprise']['plan_id'] != 27 && $user['enterprise']['plan_id'] != 28))
        <!-- 2020-04-16 之后的个人用户和免费版和新入门版   2023免费版 没有二次验证 -->
        <div class="fieldset two-step-verification-filed">
            <h4>{{__('view.TWO_STEP_VERIFICATION')}}</h4>
            <div class="two-step-verification field-section"></div>
        </div>
    @endif
@else
    @if($user['is_two_step_enabled'])
        @if(($user['enterprise']['id'] == 0 && $user['created'] < **********) || ($user['enterprise']['id'] != 0 && $user['enterprise']['plan_id'] != 25 && $user['enterprise']['plan_id'] != 27 && $user['enterprise']['plan_id'] != 28))
            <div class="fieldset two-step-verification-filed">
                <h4>{{__('view.TWO_STEP_VERIFICATION')}}</h4>
                <div class="two-step-verification field-section"></div>
            </div>
        @endif
    @else
        <input type="hidden" name="hide-account-info" id="hide-account-info">
    @endif
@endif

<div class="fieldset api-token-filed" id='j_api_token'>
    <h4>API Token</h4>
    <div class="api-token field-section"></div>
</div>

<div class="fieldset">
    <h4>{{__('view.DEVICES_MANAGE')}}</h4>
    <h5 class="title-hint">{{__('view.DEVICES_MANAGE_DES')}}</h5>
    <div class="device-manage"></div>
</div>
@if($user['enterprise'])
<input type="hidden" name="password_length_min" value="{{ $user['enterprise']['password_length_min'] }}">
<input type="hidden" name="password_strength_require_special_symbol" value="{{ $user['enterprise']['password_strength_require_special_symbol'] }}">
<input type="hidden" name="password_strength_require_capital_letter" value="{{ $user['enterprise']['password_strength_require_capital_letter'] }}">
<input type="hidden" name="is_two_step_enabled" value="{{ $user['enterprise']['id'] != 0 ? $user['enterprise']['is_two_step_enabled'] : 0 }}">
@else
<input type="hidden" name="password_length_min" value="8">
<input type="hidden" name="password_strength_require_special_symbol" value="0">
<input type="hidden" name="password_strength_require_capital_letter" value="0">
<input type="hidden" name="is_two_step_enabled" value="0">
@endif
@if($user['enterprise'] && $user['enterprise']['id'] != 0 && $user['enterprise']['is_sso_enterprise'] && $user['enterprise']['is_public_login_available'] && $user['is_password_unset'])
<input type="hidden" name="set-password" id="set-password">
@endif
@endsection('content')