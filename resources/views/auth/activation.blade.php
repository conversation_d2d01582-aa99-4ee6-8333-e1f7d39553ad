@extends('layouts.auth')
@section('pageTitle', __('view.ACTIVE') )

@section('content')
@if(isset($errors))
    @include('auth.components.error_status', ['title' => __('view.ACTIVATE_FAILED_TITLE'), 'message' => __('view.ACTIVATE_FAILED_SUB', ['login_url' => \App\Util\CommonHelper::getLoginUrl()]), '$errors' => $errors])
@else
<style>
    .logo-large {
        display: none;
    }

    .wrapper {
        padding: 0;
    }
</style>
<input class="j_username" type="hidden" value="{{$admin['name']}}" />
<div class="active-info-wp">
    <div class="aiw-content">
        <div class="aiwc-guide-img"></div>
        <div class="aiwc-text at-pc">
            @if(!empty($admin['profile_pic_key']))
                <img class="up-photo j_profile_img"  src="{{ config('app.fangcloud_url') }}/apps/users/pic_download?user_id={{ $admin['id'] }}&profile_pic_key={{ $admin['profile_pic_key'] }}" />
            @else
                <div class="user-avatar j_avatar"></div>
            @endif
            <span class="at-admin-name">{{ $admin['name'] }}</span>
            <span class="aiwc-text-main">{{ __('view.ACTIVATION_INVITE') }}“{{ $user['company_name'] }}”</span>
        </div>
        <div class="aiwc-text at-wap">
            <div class="user-profile">
                @if(!empty($admin['profile_pic_key']))
                    <img class="up-photo j_profile_img"  src="{{ config('app.fangcloud_url') }}/apps/users/pic_download?user_id={{ $admin['id'] }}&profile_pic_key={{ $admin['profile_pic_key'] }}" />
                @else
                    <div class="user-avatar j_avatar"></div>
                @endif
                <span class="up-admin">{{ $admin['name'] }}</span>
            </div>
            <div class="aiwc-text-main">{{ __('view.ACTIVATION_INVITE') }}“{{ $user['company_name'] }}”</div>
        </div>
        <div class="aiwc-text-sub">{{ __('view.ACTIVATION_INVITE_TIP') }}</div>
    </div>
</div>
<div class="activation-box">
    @if($platform !== 'mobile')<h3 class="auth-top">{{ __('view.ACTIVATION') }}</h3>@endif
    <form action="" class="form {{$platform}}-form" onSubmit="return false;">
        {{ csrf_field() }}
        {{ \App\Util\ViewHelper::fstate_field() }}

        <input type="hidden" name="password_length_min" value="{{ $user['enterprise']['password_length_min'] }}">
        <input type="hidden" name="password_strength_require_special_symbol" value="{{ $user['enterprise']['password_strength_require_special_symbol'] }}">
        <input type="hidden" name="password_strength_require_capital_letter" value="{{ $user['enterprise']['password_strength_require_capital_letter'] }}">

        <div class="fullfill-account">
            <ul class="user-info">
                <!-- <li>
                    <span class="label">{{ __('view.COMPANY_LABEL') }}</span>
                    <span class="content">{{ $user['company_name'] }}</span>
                </li> -->
                @if(isset($user['departments']) && isset($user['departments'][0]))
                <li>
                    <span class="label">{{ __('view.DEPARTMENT_LABLE') }}</span>
                    <span class="content">{{ $user['departments'][0]['name'] }}</span>
                </li>
                @endif
                <li>
                    <span class="label">{{ __('view.ACCOUNT_LABEL') }}</span>
                    <span class="content">{{ $user['username'] }}</span>
                </li>
            </ul>
            <div class="form-group">
                <div class="input-group">
                    <input type="text" name="name" class="username text form-control" placeholder="{{ __('view.YOUR_NAME') }}">
                </div>
            </div>
            @if($user['email'])
            <div class="form-group phone">
                <div class="input-group">
                    <input type="text" name="phone" class="text form-control" placeholder="{{ __('view.MOBILE_PLACEHOLDER') }}">
                </div>
            </div>
            @endif
            <div class="form-group">
                <div class="input-group">
                    <input type="password" name="password" class="password text form-control" placeholder="{{ __('view.PASSWORD') }}" autocomplete="new-password">
                </div>
                <div class="hint password-hint"></div>
            </div>
            <div class="form-group action-group next-step">
                <button class="btn btn-primary" type="submit" disabled="disabled">{{ __('view.ACTIVE') }}</button>
            </div>
            @if($platform !== 'mobile' && !$user['email'])<a href="{{ route('activate_by_phone') }}" class="go-back action-icon"><i class="iconfont icon-backward"></i>{{ __('view.RETURN_BEFORE') }}</a>@endif
        </div>
        <div class="activation-help ah-wap">
            <span>{{ __('view.ACTIVATION_INVITE_HELP') }}{{ $admin['name'] }}（{{ $admin['phone'] }}）</span>
            @if(config('sso.product_name_' . config('app.locale')) !== '埃默云')
                <span>
                    或<a href="https://ykf-webchat.7moor.com/wapchat.html?accessId=b011c960-2af2-11eb-97a1-0b02dd54b185&fromUrl=http://sj&urlTitle=sj&language=ZHCN&otherParams={%22peerId%22:%2210043061%22}" target="_blank">{{ __('view.ACTIVATION_SERVICE') }}</a>
                </span>
            @endif
        </div>
    </form> 
</div>
<div class="activation-help ah-pc">
    <span>{{ __('view.ACTIVATION_INVITE_HELP') }}{{ $admin['name'] }}（{{ $admin['phone'] }}）</span>
    @if(config('sso.product_name_' . config('app.locale')) !== '埃默云')
        <span>
            或<a href="https://ykf-webchat.7moor.com/wapchat.html?accessId=b011c960-2af2-11eb-97a1-0b02dd54b185&fromUrl=http://sj&urlTitle=sj&language=ZHCN&otherParams={%22peerId%22:%2210043061%22}" target="_blank">{{ __('view.ACTIVATION_SERVICE') }}</a>
        </span>
    @endif
</div>
<script>
    var nameEl = document.querySelector('.j_username');
    var avatarEl = document.querySelectorAll('.j_avatar');
    if (nameEl && avatarEl) {
        var letter = nameEl.value.charAt(0);
        if (letter >= 'a' && letter <= 'z') {
            letter = letter.toLocaleUpperCase();
        }
        avatarEl[0].innerText = letter;
        avatarEl[1].innerText = letter;
    }
</script>
@endif
@endsection
