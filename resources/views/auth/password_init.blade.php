@extends('layouts.auth')
@section('pageTitle', __('view.PASSWORD_INIT') )

@section('content')
@if($platform !== 'mobile')
<div class="auth-top">{{ __('view.PASSWORD_INIT') }}</div>
@endif
<div class="login-box">
    <form action="" class="form {{$platform}}-form password-init-form" onSubmit="return false;">
        {{ csrf_field() }}
        {{ \App\Util\ViewHelper::fstate_field() }}
        <input type="hidden" name="password_length_min" value="{{ $user['enterprise']['password_length_min'] }}">
        <input type="hidden" name="password_strength_require_special_symbol" value="{{ $user['enterprise']['password_strength_require_special_symbol'] }}">
        <input type="hidden" name="password_strength_require_capital_letter" value="{{ $user['enterprise']['password_strength_require_capital_letter'] }}">

        <div class="form-group">
            <div class="input-group">
                <input type="password" name="password" class="password text form-control" placeholder="{{ __('view.PASSWORD_PLACEHOLDER') }}" id="password"  autocomplete="off">
            </div>
            <div class="hint password-hint"></div>
        </div>
        <div class="form-group action-group">
            <button class="btn btn-primary" type="submit">{{ __('view.LOGIN') }}</button>
        </div>
    </form>
</div>
@endsection


