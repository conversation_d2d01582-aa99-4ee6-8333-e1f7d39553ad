@extends('layouts.auth')
@section('pageTitle', __('view.PHONE_ACTIVATION') )

@section('content')
<div class="activation-box">
    @if($platform !== 'mobile')<h3 class="auth-top">{{ __('view.PHONE_ACTIVATION') }}</h3>@endif
    <input type="hidden" id="query-phone-num" value="{{request() -> query('phone')}}">
    <form action="" class="form {{$platform}}-form" onSubmit="return false;">
        {{ csrf_field() }}
        {{ \App\Util\ViewHelper::fstate_field() }}
        <div class="step-1 confirm-account">
            <div class="form-group activation">
                <div class="input-group">
                    <input type="text" id="phone" name="phone" class="username text form-control" placeholder="{{ __('view.MOBILE_PLACEHOLDER')}} ">
                </div>
            </div>
            <div class="form-group action-group next-step">
                <button class="btn btn-primary" data-role="check-phone" type="submit" disabled="disabled">{{ __('view.NEXT') }}</button>
            </div>
            @if($platform !== 'mobile')<a href="{{ \App\Util\CommonHelper::getLoginUrl() }}" class="go-back action-icon"><i class="iconfont icon-backward"></i>{{ __('view.RETURN') }}</a>@endif
        </div>
        <div class="step-2">
            <div class="form-group verify-account">
            </div>
            <div class="form-group action-group next-step">
                <button class="btn btn-primary" data-role="submit-phone" type="submit" disabled="disabled">{{ __('view.NEXT') }}</button>
            </div>
            <div class="form-group">
                <a href="#" class="go-back go-back-confirm-account action-icon"><i class="iconfont icon-backward"></i>{{ __('view.RETURN_BEFORE') }}</a>
            </div>
        </div>
    </form>
</div>
@endsection
