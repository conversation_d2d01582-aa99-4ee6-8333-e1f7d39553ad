<div class="failure-state {{isset($className) ? $className : ''}}">
@if(isset($icon))
    <div class="iconfont {{$icon}}"></div>
@else
    <div class="error-icon "></div>
@endif
    @if(isset($errors) && $errors['error_code'] == 1100013)
    <!-- 邀请已过期 -->
        <p class="error-message-14">{{ __('view.ACTIVATE_FAILED_ALREADY_EXPIRED') }}</p>
    @elseif(isset($errors) && $errors['error_code'] == 1600003)
    <!-- 账号已激活 -->
        <p class="error-message-14">{!! __('view.ACTIVATE_FAILED_ALREADY_ACTIVATED', ['login_url' => \App\Util\CommonHelper::getLoginUrl()]) !!}</p>
    @else
        <div class="error-title">{{ $title }}</div>
        @if(isset($message))
            <div class="error-message">{!! $message !!}</div>
        @endif
    @endif
</div>