@if($platform === 'web')
<div class="header{{isset($oauth) ? ' open-header' : ''}}">
    <div class="container">
        <div class="account-box">
            <div class="language-switch dropdown">
                <span>
                    <i class="iconfont icon-language"></i>
                    <span>{{ __('view.LANGUAGE_' . app()->getLocale() ) }}</span>
                    <i class="iconfont icon-arrow"></i>
                </span>
                <ul class="language-switch-dropdown">
                    <li data-by="zh-CN">简体中文</li>
                    <li data-by="zh-TW">繁體中文</li>
                    <li data-by="en">English</li>
                </ul>
            </div>
        @if(ViewHelper::isExternalLink())
        <em></em>
        @else
            @guest
                @if(!isset($register) && env('APP_ENV', 'local') != 'profession' && ViewHelper::isFangcloud())
                <a class="btn btn-default btn-register" href="{{ route('register') }}">{{ __('view.REGISTER') }}</a>
                @endif
                <a class="btn btn-default btn-login" href="{{ \App\Util\CommonHelper::getLoginUrl() }}">{{ __('view.LOGIN') }}</a>
            @endguest
        @endif
        </div>
    </div>
</div>
@elseif ($platform === 'sync')
<div class="center-logo-mini client-dragable">
    @if(isset($_GET['is_ai_app']))
        <span class="default-logo ai"></span>
    @else
        @if(ViewHelper::isFangcloud())
            <span class="default-logo"></span>
        @else
            <img class="fangcloud-logo" src="{{ ViewHelper::logoURI('web.logo.w232') }}" alt="{{ config('sso.product_name_' . config('app.locale')) }}">
        @endif
    @endif
</div>
@elseif ($platform === 'mobile')
    @if(!isset($mobile_register))
        <div class="logo-large{{isset($register) ? ' register-logo' : ''}}">
            @if(ViewHelper::isFangcloud())
                <span class="default-logo"></span>
            @else
                <img class="fangcloud-logo" src="{{ ViewHelper::logoURI('web.logo.w232') }}" alt="{{ config('sso.product_name_' . config('app.locale')) }}">
            @endif
        </div>
    @endif
@endif