<div class="form-footer">
    @if($platform == 'web' && !isset($oauth) && !(ViewHelper::isExternalLink()))
    <div class="form-group inline activate-form">
        <div class="label">{{ __('view.RECIEVE_MESSAGE_ACTION') }}</div>
        <div class="form-control">
            <a href="{{ route('activate_by_phone') }}">{{ __('view.PHONE_ACTIVATION') }}</a>
        </div>
    </div>
    @endif
    @if(!(ViewHelper::isExternalLink()))
        @if(!(ViewHelper::env_is_profession() && !ViewHelper::get_env_config('is_third_login_enabled')))
        <div class="other-login-container">
            @if(!isset($oauth) && ViewHelper::isFangcloud() && $platform != 'mobile')
            <div class="other-login">
                <div class="prompt-title"><span>{{ __('view.THIRD_LOGIN')}}:</span></div>
                <div class="other-login-content">
                    <a class="bind-link action-icon only-v2" href="{{ route('qihoo360login') }}?scene=login">
                        <svg class="type-icon" aria-hidden="true">
                            <use xlink:href="#icon-icon-test"></use>
                        </svg>
                        <span>{{ __('view.QIHOO_ACCOUNT') }}</span>
                    </a>
                    <a class="bind-link action-icon only-v2" href="{{ route('wechatlogin') }}?scene=login">
                        <svg class="type-icon" aria-hidden="true">
                            <use xlink:href="#icon-enterprise-wechat1"></use>
                        </svg>
                        <span>{{ __('view.ENTERPRISE_WEIXIN') }}</span>
                    </a>
                    @if((isset($platform) && $platform != 'sync') || (isset($platform) && $platform == 'sync' && (!ViewHelper::is_sync_client('WebKit') || ViewHelper::is_sync_client('Electron'))))
                    <a class="bind-link action-icon only-v2" href="{{ route('dingtalklogin') }}?scene=login&qrcode=true">
                        <i class="iconfont icon-dingding" style="color: #3296FA;"></i>
                        <span>{{ __('view.DING_TALK') }}</span>
                    </a>
                    @endif
                </div>
            </div>
            @endif
            <div class="other-login">
                <div class="prompt-title"><span>{{ __('view.OTHER_LOGIN')}}:</span></div>
                <div class="other-login-content">
                    @if(!isset($oauth) && ViewHelper::isFangcloud() && $platform == 'mobile')
                    <a class="bind-link action-icon only-v2" data-title=""  href="{{ route('qihoo360login') }}?scene=login">
                        <svg class="type-icon" aria-hidden="true">
                            <use xlink:href="#icon-icon-test"></use>
                        </svg>
                        <span>{{ __('view.QIHOO_ACCOUNT') }}</span>
                    </a>
                    @endif
                    <a class="action-icon{{isset($oauth) ? ' open-none' : ''}}" href="{{ \App\Util\ViewHelper::routeWithState('international_login') }}">
                        <i class="iconfont icon-int-number"></i>
                        <span>{{ __('view.INTERNAL_PHONE') }}</span>
                    </a>
                    @if(!isset($oauth))
                    <a class="action-icon{{isset($oauth) ? ' open-none' : ''}}" href="{{ \App\Util\ViewHelper::routeWithState('other_enterprise') }}">
                        <i class="iconfont icon-special-enterprise"></i>
                        <span>{{ __('view.DEDICATED_ENTRY_1') }}</span>
                    </a>
                    @endif
                </div>
            </div>

            <script>
                var login_type = 'web';
                @if($platform == 'sync')
                    login_type = 'sync';
                @elseif($platform == 'mobile')
                    if(window.fangcloud) {
                        login_type = 'app';
                    }
                @endif
                var _fstate = document.querySelector('[name=_fstate]').value;
                [].forEach.call(document.querySelectorAll('.bind-link'), function(link) {
                    var href = link.getAttribute('href');
                    link.setAttribute('href', href + '&login_type=' + login_type + '&_fstate=' + _fstate);
                });
            </script>
        </div>
        @endif
    @endif
    <script type="text/javascript" src="https://at.alicdn.com/t/font_414128_dpyixg5zlm6.js"></script>
</div>
