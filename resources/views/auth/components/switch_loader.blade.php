<div class="switch-box">
    <a class="tab active" href="#form" data-view="form">{{ __('view.PASSWORD_LOGIN') }}</a>
    <a class="tab" href="#qrcode-login" data-view="qrcode-login">{{ __('view.QRCODE_LOGIN') }}</a>
</div>
@if(isset($oauth))
<p class="oauth-des">{!! __('view.OAUTH_LOGIN_APP', ['link' => Request::query('index_url'), 'app' => Request::query('client_name')]) !!}</p>
@endif
<div class="qrcode-login">
    <div class="qrcode-container">
        <div class="qr-code">
            <img data-role="qrcode" src="" alt="">
        </div>
        <p class="qr-header-hint">
            <span class="scan-tip">
                @if(ViewHelper::isFangcloud())
                {!! __('view.QRCODE_LOGIN_TIPS_1', ['download_link' => config('app.app_download_url')]) !!}
                @endif
                {{ __('view.QRCODE_LOGIN_TIPS_2') }}
                <i class="iconfont icon-retry action-icon"></i>
            </span>
            <span class="qr-expire-tip">
                {{ __('view.QRCODE_EXPIRE_TIP') }}
            </span>
        </p>
    </div>

    <div class="scan-success-container">
        <div class="scan-success"><i class="iconfont icon-success"></i></div>
        <p class="success-title">{{ __('view.SCAN_SUCCESS') }}</p>
        <p class="success-message">{{ __('view.CONFIRM_LOGIN_ON_PHONE') }}</p>
        <a href="#" class="btn btn-default return-scan">{{ __('view.RETURN_TO_QRCODE') }}</a>
    </div>

    <div class="login-expire-container">
        <div class="login-expire"></div>
        <p class="expire-title">{{ __('view.LOGIN_EXPIRE') }}</p>
        <a href="#" class="btn btn-default refresh-scan">{{ __('view.REFRESH_TO_QRCODE') }}</a>
    </div>
</div>