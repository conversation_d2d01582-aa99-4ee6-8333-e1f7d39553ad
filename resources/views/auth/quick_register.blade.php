@extends('layouts.register')
@section('pageTitle', __($platform === 'mobile' ? 'view.REGISTER' : 'view.REGISTER_TITLE_' . $plan_id) )
@section('pageKeywords', __('view.REGISTER_KEYWORDS_' . $plan_id) )
@section('pageDescription', __('view.REGISTER_DESCRIPTION_' . $plan_id) )

@section('step')
<div class="quick-register">
    <!-- <div class="product-bg">
        <div class="top">
            <?php
                $img_map = array(
                    // 免费版
                    '25' => array(
                        'top_left_img' => ViewHelper::asset_m('images/quick-register-13-bg-tl.png'),
                        'bottom_left_img' => ViewHelper::asset_m('images/quick-register-13-bg-bl.png'),
                    ),
                    // 高级版
                    '24' => array(
                        'top_left_img' => ViewHelper::asset_m('images/quick-register-15-bg-tl.png'),
                        'bottom_left_img' => ViewHelper::asset_m('images/quick-register-15-bg-bl.png'),
                    ),
                    // 旗舰版
                    '26' => array(
                        'top_left_img' => ViewHelper::asset_m('images/quick-register-16-bg-tl.png'),
                        'bottom_left_img' => ViewHelper::asset_m('images/quick-register-16-bg-bl.png'),
                    ),
                    // 入门版
                    '27' => array(
                        'top_left_img' => ViewHelper::asset_m('images/quick-register-15-bg-tl.png'),
                        'bottom_left_img' => ViewHelper::asset_m('images/quick-register-15-bg-bl.png'),
                    ),
                    // 免费版
                    '28' => array(
                        'top_left_img' => ViewHelper::asset_m('images/quick-register-15-bg-tl.png'),
                        'bottom_left_img' => ViewHelper::asset_m('images/quick-register-15-bg-bl.png'),
                    ),
                    // 小微版
                    '29' => array(
                        'top_left_img' => ViewHelper::asset_m('images/quick-register-15-bg-tl.png'),
                        'bottom_left_img' => ViewHelper::asset_m('images/quick-register-15-bg-bl.png'),
                    ),
                    '30' => array(
                        'top_left_img' => ViewHelper::asset_m('images/quick-register-15-bg-tl.png'),
                        'bottom_left_img' => ViewHelper::asset_m('images/quick-register-15-bg-bl.png'),
                    ),
                    '31' => array(
                        'top_left_img' => ViewHelper::asset_m('images/quick-register-15-bg-tl.png'),
                        'bottom_left_img' => ViewHelper::asset_m('images/quick-register-15-bg-bl.png'),
                    ),
                    '32' => array(
                        'top_left_img' => ViewHelper::asset_m('images/quick-register-15-bg-tl.png'),
                        'bottom_left_img' => ViewHelper::asset_m('images/quick-register-15-bg-bl.png'),
                    ),
                    '33' => array(
                        'top_left_img' => ViewHelper::asset_m('images/quick-register-15-bg-tl.png'),
                        'bottom_left_img' => ViewHelper::asset_m('images/quick-register-15-bg-bl.png'),
                    )
                );
            ?>
            <img class="left 123" src="{{ $img_map[$plan_id]['top_left_img'] }}">
            <img class="center" src="{{ ViewHelper::asset_m('images/quick-register-bg-tc.png') }}">
            <img class="right" src="{{ ViewHelper::asset_m('images/quick-register-bg-tr.png') }}">
        </div>
        <div class="center">
            <img class="left" src="{{ ViewHelper::asset_m('images/quick-register-bg-cl.png') }}">
        </div>
        <div class="bottom">
            <img class="left" src="{{ $img_map[$plan_id]['bottom_left_img'] }}">
        </div>

    </div> -->
    <div class="product-bg">
        <div class="header">
            <span class="logo">
                <img height="24" src="{{ViewHelper::asset_m('images/enterprise_profile_pic.jpg')}}"/>
            </span>
            <div class="search">
                <i class="new-iconfont icon-message_search"></i>
                <span>输入并搜索文件...</span>
            </div>
            <div class="header-right">
                <i class="new-iconfont icon-act_temporary"></i>
                <span>企业控制台</span>
                <div class="user"></div>
                <img src="https://v2static.fangcloud.net/assets_new/desktop/dist/images/ai-application-header-img-new_6f74b9d.png">
            </div>
        </div>
        <div class="content-wrapper">
            <div class="first-nav">
                <div class="nav-item">
                    <img class="icon-image" src="https://v3static.fangcloud.com/assets/desktop/dist/images/ai-home-nav-unselect2_3639dbc.png">
                </div>
                <div class="nav-item">
                    <i class="new-iconfont icon-homepage"></i>
                    <span class="nav-text">门户</span>
                </div>
                <div class="nav-item active">
                    <i class="new-iconfont icon-new-nav-files-active"></i>
                    <span class="nav-text">文件</span>
                </div>
                <div class="nav-item">
                    <i class="new-iconfont icon-new-nav-sync"></i>
                    <span class="nav-text">同步</span>
                </div>
                <div class="nav-item">
                    <i class="new-iconfont icon-new-nav-msg"></i>
                    <span class="nav-text">消息</span>
                </div>
                <div class="nav-item">
                    <i class="new-iconfont icon-new-doclib"></i>
                    <span class="nav-text">知识</span>
                </div>
                <div class="nav-bottom">
                    <div class="nav-action nav-item">
                        <i class="new-iconfont icon-app1"></i>
                        <span class="nav-text">应用</span>
                    </div>
                </div>

            </div>
            <div class="second-nav">
                <div class="second-nav-header">
                    <span>全部文件</span>
                    <span>
                        <i class="new-iconfont icon-department1"></i>
                        <i class="new-iconfont icon-more-ui"></i>
                    </span>
                </div>
                <div class="second-list">
                    <ul>
                        <li class="active">
                            <svg class="type-icon" aria-hidden="true"><use xlink:href="#icon-msg-department"></use></svg>
                            <span>企业名称</span>
                        </li>
                        <li>
                            <svg class="type-icon" aria-hidden="true"><use xlink:href="#icon-type-folder"></use></svg>
                            <span>个人文件</span>
                        </li>
                        <li>
                            <i class="new-iconfont icon-side_collab"></i>
                            <span>与我协作</span>
                        </li>
                        @if($plan_id == '26')
                        <li>
                            <svg class="type-icon" aria-hidden="true"><use xlink:href="#icon-type_archive"></use></svg>
                            <span>归档空间</span>
                        </li>
                        @endif
                        <li>
                            <i class="new-iconfont icon-nav-recent"></i>
                            <span>最近使用</span>
                        </li>
                        <li>
                            <i class="new-iconfont icon-nav-frequent"></i>
                            <span>常用文件</span>
                        </li>
                        <li>
                            <i class="new-iconfont icon-nav-tags"></i>
                            <span>常用标签</span>
                        </li>
                        <li>
                            <i class="new-iconfont icon-nav-trash"></i>
                            <span>回收站</span>
                        </li>
                        <li>
                            <i class="new-iconfont icon-nav-virus"></i>
                            <span>隔离区</span>
                        </li>
                    </ul>
                </div>
            </div>
            <div class="main-content-wrapper ">
                <div class="main-wrapper">
                    <div class="file-list-wrapper">
                        <div class="file-list">
                            <div class="action-btn">
                                <div class="btn-list">
                                    <div class="btn"><span>新建</span><i class="new-iconfont icon-arrow_down"></i></div>
                                    <div class="btn upload"><span>上传</span><i class="new-iconfont icon-arrow_down"></i></div>
                                </div>
                                <div class="path">
                                    <span class="path-name">企业名称</span>
                                    <span class="switch">
                                        <i class="new-iconfont icon-message_search" style="font-size: 24px; margin-right: 8px;"></i>
                                        <i class="new-iconfont icon-act_list_s"></i><i class="new-iconfont icon-arrow_down"></i>
                                    </span>
                                </div>
                            </div>
                            <div class="list-container">
                                <div class="list-header">
                                    <ul>
                                        <li class="header-cell cell-main">
                                            <span>名称</span>
                                            <svg class="sort-icon" aria-hidden="true"><use xlink:href="#icon-sort-desc"></use></svg>
                                        </li>
                                        <li class="header-cell cell-status has-sort">
                                            <span>修改时间 (修改人)</span>
                                            <svg class="sort-icon" aria-hidden="true"><use xlink:href="#icon-sort-desc"></use></svg>
                                        </li>
                                        <li class="header-cell cell-size has-sort">
                                            <span>大小</span>
                                            <svg class="sort-icon" aria-hidden="true"><use xlink:href="#icon-sort-desc"></use></svg>
                                        </li>
                                    </ul>
                                </div>
                                <div class="list-body">
                                    <ul>
                                        <li class="header-cell cell-main">
                                            <div class="svg-icon thumb-icon">
                                                <svg class="type-icon" aria-hidden="true"><use xlink:href="#icon-collab-folder"></use></svg><!-- react-empty: 6436 -->
                                            </div>
                                            <div class="name">共享资料</div>
                                        </li>
                                        <li class="header-cell cell-status has-sort">
                                            <span class="cnt-left">1小时前(Lily)</span>
                                        </li>
                                        <li class="header-cell cell-size has-sort">
                                            <span class="cnt-size">5GB</span>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="file-side">
                            <div class="pithy-side-close-box"><i class="new-iconfont detail-side-close icon-act_module"></i></div>
                            <div class="pithy-side-wrap">
                                <i class="new-iconfont icon-act_dpt"></i>
                                <i class="new-iconfont icon-act_set"></i>
                                <i class="new-iconfont icon-more-ui"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="register-modal">
        <div class="register-dialog form normal-register">
            <div class="language-switch dropdown">
                <span>
                    <i class="iconfont icon-language"></i>
                    <i class="iconfont icon-arrow"></i>
                </span>
                <ul class="language-switch-dropdown">
                    <li data-by="zh-CN">简体中文</li>
                    <li data-by="zh-TW">繁體中文</li>
                    <li data-by="en">English</li>
                </ul>
            </div>
            <div class="title">{{ __('view.QUICK_REGISTER_TITLE') }}</div>
            <input type="hidden" name="plan_id" value="{{ $plan_id }}">
            <input type="hidden" name="register_position" value="{{ $register_position }}">
            <input type="hidden" name="invite_code" value="{{ $invite_code }}">
            <input type="hidden" name="redirect" value="{{ $redirect }}">
            <script type="text/javascript" src="https://at.alicdn.com/t/c/font_2860007_3164yorinbn.js"></script>
            <div class="form-group phone">
                <div class="input-group">
                    <input type="text" class="username text form-control" id="phone" name="phone" value="{{ old('phone') }}" placeholder="{{ __('view.MOBILE_PLACEHOLDER') }}" maxlength="11">
                </div>
            </div>
            <div class="form-group email hidden">
                <div class="input-group">
                    <input type="text" name="email" id="email" class="text form-control" value="" placeholder="{{ __('view.EMAIL_PLACEHOLDER') }}">
                </div>
            </div>
            <div class="form-group password-group hidden">
                <div class="input-group">
                    <input type="password" name="password" class="text form-control" placeholder="{{ __('view.PASSWORD_PLACEHOLDER_WITH_STRENTH') }}" autocomplete="new-password" maxlength="32">
                </div>
                <span class="hint hidden">{{ __('view.PASSWORD_STRENTH_MID') }}</span>
            </div>
            <div class="form-group action-group register-group hidden">
                <button class="btn btn-primary register-btn">{{ __('view.GET_VERIFICATION_SUBMIT') }}</button>
            </div>
            <div class="form-group action-group get-captcha-group">
                <button class="btn btn-primary get-captcha-btn">{{ __('view.GET_VERIFICATION_CODE') }}</button>
            </div>
            <p class="register-hint">{{ __('view.REGISTER_TIPS') }}</p>
            <div class="form-group fangcloud-protocol hidden">
                <label for="protocol">
                    {!! __('view.REGISTER_CLAUSE_AND_AGREEMENT_1') !!}
                </label>
            </div>
        </div>
    </div>
</div>
@endsection