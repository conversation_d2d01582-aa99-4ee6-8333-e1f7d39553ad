@extends('layouts.auth')
@section('pageTitle', __('view.TWO_STEP_VERIFY') )

@section('content')
<div class="auth-top two-step-top">{{ __('view.TWO_STEP_VERIFY') }}</div>
@if(isset($force_login))
<div class="auth-top-tip">{{ __('view.TWO_STEP_VERIFY_TIP') }}</div>
@endif
<div class="force-set-two-step dialog-two-step-verify">
    {{ csrf_field() }}
    @if(isset($force_login))
        {{ \App\Util\ViewHelper::fstate_field() }}
        <input type="hidden" id="force-login" value="1">
    @endif
    <form action="" class="form {{$platform}}-form verify-form" onSubmit="return false;">
    </form>
</div>
@endsection


