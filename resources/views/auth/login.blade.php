@extends('layouts.auth')
@section('pageTitle', __($platform === 'mobile' ? 'view.LOGIN' : 'view.LOGIN_TITLE') )
@section('pageKeywords', __('view.LOGIN_KEYWORDS') )
@section('pageDescription', __('view.LOGIN_DESCRIPTION', ['product_name' => config('sso.product_name_' . config('app.locale'))]) )



@section('content')
@if(isset($international) && $platform !== 'mobile')
<div class="auth-top">{{ __('view.INTERNAL_PHONE_LOGIN') }}</div>
@endif
@if(isset($other) && $platform !== 'mobile')
<div class="auth-top">{{ __('view.DEDICATED_ENTRY') }}</div>
@endif
<div class="login-box">
    @includeWhen($platform !== 'mobile' && !isset($international), 'auth.components.switch_loader')
    <form action="" class="form {{$platform}}-form{{isset($international) ? ' international-form' : '' }}" onSubmit="return false;">
        {{ csrf_field() }}
        {{ \App\Util\ViewHelper::fstate_field() }}
        @include('auth.components.account_password')
        @includeWhen($platform !== 'mobile', 'auth.components.rememberme')
        <div class="form-group action-group">
            <button class="btn btn-primary" type="submit">{{ __('view.LOGIN') }}</button>
        </div>

        @if($platform === 'sync')
            <div class="form-group action-group" style="text-align: center;">
                登录表示您已阅读并同意&nbsp;
                <a href="https://www.fangcloud.com/home/<USER>" target="blank">服务条款</a>&nbsp;|&nbsp;<a href="https://www.fangcloud.com/home/<USER>" target="blank">隐私政策</a>
            </div>
        @endif

        @if($platform === 'mobile')
        <div class="form-group fangcloud-protocol">
            <label>
                <i class="checkbox-inner"></i>
                <input type="checkbox" name="protocol" id="protocol">
            </label>
            <div>
                {!! __('view.MOBILE_LOGIN_CLAUSE_AND_AGREEMENT') !!}
            </div>
        </div>

        <div class="form-group activate-account">
            @if(!isset($oauth))
            <a class="action-icon" href="{{ \App\Util\ViewHelper::routeWithState('activate_by_phone') }}">{{ __('view.PHONE_ACTIVATION') }}</a>
            @endif
            <a class="action-icon" href="{{ isset($international) ? \App\Util\ViewHelper::routeWithState('password.forgot_for_international') : \App\Util\ViewHelper::routeWithState('password.forgot') }}">{{ __('view.FORGOT_PASSWORD') }}</a>
        </div>
        @endif
        @if(isset($international))
           @if($platform !== 'mobile')<a href="{{ \App\Util\CommonHelper::getLoginUrl() }}" class="go-back action-icon"><i class="iconfont icon-backward"></i>{{ __('view.RETURN') }}</a>@endif
        @else
            @include('auth.components.other')
        @endif
    </form>

    @if($platform === 'sync')
        <div class="sync-register">
            @if(isset($_GET['is_ai_app']))
                <a class="client-register action-icon" href="/new_register?plan_id=30&register_position=2">{{ __('view.FREE_REGISTER') }}</a>
            @else
                <a class="client-register action-icon" href="{{ route('register') }}">{{ __('view.FREE_REGISTER') }}</a>
            @endif
            <a class="action-icon" href="{{ \App\Util\ViewHelper::routeWithState('activate_by_phone') }}">{{ __('view.PHONE_ACTIVATION') }}</a>
        </div>
    @endif

    @if(isset($oauth))
    <input type="hidden" id="oauth_login" value="1">
    @endif

    @if(request()->header('ApiKey'))
    <input type="hidden" id="api_key" value="{{ request()->header('ApiKey') }}">

    @endif
    @if(isset($oauth))
    <div class="oauth-tip">{{ __('view.OAUTH_LOGIN_TIP') }}</div>
    @endif

</div>
<!-- @if(isset($oauth))
<div class="oauth-tip">{{ __('view.OAUTH_LOGIN_TIP') }}</div>
@endif -->
@endsection
