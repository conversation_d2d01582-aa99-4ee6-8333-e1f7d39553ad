@extends('layouts.auth')
@section('pageTitle', __('view.TWO_STEP_VERIFY') )

@section('content')
@if($platform !== 'mobile')
<div class="auth-top">{{ __('view.TWO_STEP_VERIFY') }}</div>
@endif
<div class="login-box">
    <form action="" class="form {{$platform}}-form verify-form" onSubmit="return false;">
        {{ csrf_field() }}
        {{ \App\Util\ViewHelper::fstate_field() }}
        <p class="desc">
            {{ __('view.MOBILE_SET_TWO_STEP_DES') }}
        </p>
        <div class="form-group phone">
            <div class="input-group">
                <input type="text" class="username text form-control" id="phone" name="phone" value="{{ old('phone') }}" placeholder="{{ __('view.MOBILE_PLACEHOLDER') }}">
            </div>
        </div>
        <div class="form-group action-group">
            <button class="btn btn-primary" type="submit">{{ __('view.SET_SUBMIT') }}</button>
        </div>
        <input type="hidden" name="validate_type" value="{{$type}}">
    </form>
</div>
@endsection


