@extends('layouts.base')
@section('pageTitle', __('view.HYBRID_ENTRY') )

@section('main')

@if(isset($hybrid) && $platform !== 'mobile')
<div class="hybrid-top">
    <div class="auth-top">{{ __('view.HYBRID_ENTRY') }}</div>
    <p class="auth-top-tip">{{ __('view.CONTACT_ADMIN') }}</p>
</div>
@endif
<div class="login-box">
    <form action="" class="form {{$platform}}-form hybrid-login-form" onSubmit="return false;">
        {{ csrf_field() }}
        {{ \App\Util\ViewHelper::fstate_field() }}
        <div class="form-group">
            <div class="input-group">
                <input type="text" class="text form-control" id="enterprise_key" name="enterprise_key" placeholder="{{ __('view.ENTERPRISE_ID_PLACEHOLDER') }}">
                <input type="hidden" id="key_type" name="key_type" value="id" placeholder="{{ __('view.ENTERPRISE_ID_PLACEHOLDER') }}">
            </div>
        </div>
        <div class="form-group action-group">
            <button class="btn btn-primary" type="submit">{{ __('view.CONFIRM') }}</button>
        </div>
    </form>
</div>
@endsection
