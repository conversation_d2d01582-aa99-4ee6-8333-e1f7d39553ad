@extends('layouts.base')
@section('main')
    @if(isset($json))
    <input type="hidden" name="results" id="results" value='@json($json)'>
    @endif
    @if(isset($post))

    <script type="text/javascript">
        function setCookie(name, value, Days){
            if(Days == null || Days == ''){
                Days = 300;
            }
            var exp  = new Date();
            exp.setTime(exp.getTime() + Days*24*60*60*1000);
            var domain_array = window.location.host.split('.');
            var domain = "." + domain_array.slice(domain_array.length - 2).join('.');
            document.cookie = name + "="+ escape (value) + "; path=/;domain=" + domain + ";expires=" + exp.toGMTString();
            //document.cookie = name + "="+ escape (value) + ";expires=" + exp.toGMTString();
        }
        var message = document.getElementById('results').value;
        if(document.getElementById('is_sync')) {
            setCookie('bindInfo', message, 1);
            window.location.href = '{{ route("login_settings")}}';
        } else {
            if(window.opener) {
                window.opener.postMessage(message, window.location.origin);
                window.opener.focus && window.opener.focus();

            }
            window.close();
        }
    </script>
    @endif
@endsection('main')
