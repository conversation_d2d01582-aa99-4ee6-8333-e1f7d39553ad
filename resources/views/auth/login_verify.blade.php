@extends('layouts.auth')
@section('pageTitle', __('view.TWO_STEP_VERIFY') )

@section('content')
@if($platform !== 'mobile')
<div class="auth-top">{{ __('view.TWO_STEP_VERIFY') }}</div>
@endif
<div class="login-box">
    <form action="" class="form {{$platform}}-form verify-form" onSubmit="return false;">
        {{ csrf_field() }}
        {{ \App\Util\ViewHelper::fstate_field() }}
        <p class="desc">
        @if($type === 'sms')
            {{ __('view.VERIFY_SMS_CAPTCHA_DES', ['tail' => $identity]) }}
        @elseif($type === 'wechat')
            {{ __('view.VERIFY_WECHAT_CAPTCHA_DES') }}
        @elseif($type === 'google')
            {{ __('view.VERIFY_GOOGLE_CAPTCHA_DES') }}
        @endif
        </p>
        <div class="form-group mark-credit">
            @if($platform === 'web')
            <label for="mark_credit">
                <i class="checkbox-inner"></i>
                <input type="checkbox" name="mark_credit"" id="mark_credit" value="1">
                {{ __('view.TRUST_DEVICE', ['tail' => $identity]) }}
            </label>
            @endif
            <a class="form-right get-help" href="#">{{__('view.GET_HELP') }}</a>
        </div>
        <div class="form-group action-group">
            <button class="btn btn-primary" type="submit">{{ __('view.VERIFY_LOGIN') }}</button>
        </div>
        @if($platform !== 'mobile')
        <div class="form-group">
            <a href="{{ \App\Util\CommonHelper::getLoginUrl() }}" class="back login-verify-back action-box"><i class="iconfont icon-backward"></i>{{ __('view.RETURN') }}</a>
        </div>
        @endif

        <input type="hidden" name="validate_type" value="{{$type}}">
    </form>
</div>
@endsection


