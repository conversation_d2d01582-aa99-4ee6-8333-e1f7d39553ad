@extends('layouts.recommend')
@section('pageTitle',  '企业版注册')

@section('content')
<div class="new-enterprise-register">
    <div class="page-recommend-head">
        <div class="head-logo">
            <a class="head-link" href="https://yunpan.360.cn/disk/enterprise.html"><img class="logo ai-logo" title="360企业网盘" src="https://p4.ssl.qhimg.com/t110b9a93017d8973c74c797024.png" alt="360AI网盘/企业云盘办公平台"></a>
        </div>
        <div>
            <a class="login" href="{{ config('app.fangcloud_url') }}" target="_blank">登录</a>
        </div>
    </div>
    <div class="page-common">
        <div class="step-image">
            <img class="img-1 active" src="https://p1.ssl.qhimg.com/t110b9a930109b950f564363c25.png" alt="">
            <img src="https://p5.ssl.qhimg.com/t110b9a9301abbae3d753e4a7b9.png" alt="">
            <img class="img-3" src="https://p3.ssl.qhimg.com/t110b9a93016b1f7a33ec0c94d2.png" alt="">
        </div>

        <div class="step-common">
            <!-- <div class="step-header">
                <p class="has-line active">
                    <span>1</span>
                    手机验证
                </p>
                <p class="has-line active">
                    <span>2</span>
                    账号设置
                </p>
                <p class="">
                    <span>2</span>
                    完成注册
                </p>
            </div> -->
            <div class="signup-common">
                <div class="step-one active form normal-register">
                    <input type="hidden" name="plan_id" value="{{ $plan_id }}">
                    <input type="hidden" name="register_position" value="{{ $register_position }}">
                    <p class="title">立即注册，免费试用</p>
                    <div class="form-group phone">
                        <div class="input-group">
                            <input type="text" class="username text form-control" id="phone" name="phone" value="{{ old('phone') }}" placeholder="{{ __('view.MOBILE_PLACEHOLDER') }}" maxlength="11">
                        </div>
                    </div>
                    <div class="form-group password-group">
                        <div class="input-group">
                            <input type="password" name="password" class="text form-control" placeholder="{{ __('view.PASSWORD_PLACEHOLDER_WITH_STRENTH') }}" autocomplete="new-password" maxlength="32">
                        </div>
                        <span class="hint hidden">{{ __('view.PASSWORD_STRENTH_MID') }}</span>
                    </div>
                    <div class="form-group action-group register-group">
                        <button class="btn btn-primary submit-btn register-btn active">{{ __('view.GET_VERIFICATION_SUBMIT') }}</button>
                    </div>
                    <div class="form-group fangcloud-protocol">
                        <label for="protocol">
                            {!! __('view.REGISTER_CLAUSE_AND_AGREEMENT_1_1') !!}
                        </label>
                    </div>
                </div>
                <!-- <div class="step-two">
                    <p class="title">完善信息，高效办公</p>
                    <div class="team-name">
                        <p>企业/团队名称</p>
                        <input id="teamName" type="text" placeholder="请输入企业/团队名称">
                    </div>
                    <div class="section-title">你计划和多少人一起使用亿方云？</div>
                    <div class="section-content enterprise-scale">
                        <div class="item" data-scale="team" data-value="2">2-5</div>
                        <div class="item" data-scale="team" data-value="3">6-10</div>
                        <div class="item" data-scale="team" data-value="4">11-30</div>
                        <div class="item" data-scale="team" data-value="5">31-50</div>
                        <div class="item" data-scale="enterprise" data-value="6">50-200</div>
                        <div class="item" data-scale="enterprise" data-value="7">201-1000</div>
                        <div class="item" data-scale="enterprise" data-value="8">1000以上</div>
                        <div class="item" data-scale="personal" data-value="1">个人用</div>
                    </div>
                    <div id="step-two-submit-btn" class="submit-btn">
                        下一步
                    </div>
                </div> -->
                <div class="step-three">
                    <p class="title">注册成功，立即下载客户端体验吧~</p>
                    <a href="https://yunpan.360.cn/disk/download2.html" target="_blank"><img src="https://p2.ssl.qhimg.com/t01cc6c15ab1314540d.png" alt=""></a>
                    <a href="{{ config('app.fangcloud_url') }}" class="submit-btn active">
                        进入网页版
                    </a>
                </div>
            </div>

            <div class="step-login">
                已有账号？<a href="{{ config('app.fangcloud_url') }}" target="_blank">立即登录</a>
            </div>
        </div>
    </div>
    <div class="page-recommend-foot">
        <p>版权所有：北京鸿享技术服务有限公司</p>
        <p>Copyright©2023 360.CN All Rights Reserved 360互联网安全中心 京公网安备 11000002000006号 京ICP证080047号[京ICP备08010314号-6]</p>
    </div>
</div>

@endsection