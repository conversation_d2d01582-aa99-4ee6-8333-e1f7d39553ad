@extends('layouts.auth')
@section('pageTitle', __('view.REGISTER_SUCCESS') )

@section('content')
<div class="register-done">
    <div class="status">
        <div class="iconfont icon-success"></div>
    </div>
    <p class="title">{{ __('view.REGISTER_DONE') }}</p>
    <a href="{{ config('app.default_page') }}" class="btn btn-primary"> {{ __('view.START_USING') }}</a>
</div>
<div class="v2-download-container">
    <div class="pc-client">
        <h4>
            {{ __('view.PC_CLIENT') }}
            <p class="sub">{!! __('view.FEATURES') !!}</p>
        </h4>
        <div class="thumb-pic"></div>
        <a id="winLink" href="{{ ViewHelper::getWindowsDownloadLink() }}" class="pc-download-btn win"><i class="iconfont icon-win"></i>{{ __('view.WINDOWS') }}</a>
        <a href="{{ ViewHelper::ViewHelper::getMacDownloadLink() }}" class="pc-download-btn mac"><i class="iconfont icon-iphone"></i>{{ __('view.MAC') }}</a>
        <p class="already">{{ __('view.DOWNLOADED') }}</p>
        <script>
            if (navigator.userAgent.toLowerCase().indexOf('windows nt 5.1') > 0 && document.getElementById('winLink')) {
                document.getElementById('winLink').href = "{{ ViewHelper::getDownloadLink('windows_xp_download_link')}}?t={{ time() }}&filename=installer.exe";
            }
        </script>
    </div>
    <div class="app">
        <h4>
            {{ __('view.DOWNLOAD_APP') }}
            <p class="sub">{{ __('view.SLOGAN_2') }}</p>
        </h4>
        <div class="qrcode">
            <img src="{{config('app.fangcloud_url')}}qr_code/get_qr_code?text={{config('app.fangcloud_url')}}mobile">
            <p>{{ __('view.SCAN') }}</p>
        </div>
        <a href="{{ ViewHelper::getDownloadLink('ios_download_link') }}" class="app-download-btn iphone"><i class="iconfont icon-iphone"></i>{{ __('view.IOS') }}</a>
        <a href="{{ ViewHelper::getDownloadLink('android_download_link') }}" class="app-download-btn android"><i class="iconfont icon-android"></i>{{ __('view.ANDROID') }}</a>
    </div>
</div>
@endsection