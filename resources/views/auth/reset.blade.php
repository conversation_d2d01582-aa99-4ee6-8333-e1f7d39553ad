@extends('layouts.auth')
@section('pageTitle', __('view.RESET_PASSWORD'))

@section('content')
@if(isset($errors))
    @include('auth.components.error_status', ['title' => $errors['error_msg']])
@else
    @if($platform === 'web')
    <div class="progress forgot-progress">
        <h3 class="auth-top">{{ __('view.FORGOT_PASSWORD') }}</h3>
        <div class="step-info">
            <div class="radius-box">
                <i class="radius"></i>
                <div class="text">1</div>
            </div>
            <div class="explanation">{{ __('view.CONFIRM_ACCOUNT') }}</div>
        </div>
        <div class="line"></div>
        <div class="step-info">
            <div class="radius-box">
                <i class="radius"></i>
                <div class="text">2</div>
            </div>
            <div class="explanation">{{ __('view.IDENTITY_VERIFICATION') }}</div>
        </div>
        <div class="line"></div>
        <div class="step-info current">
            <div class="radius-box">
                <i class="radius"></i>
                <div class="text">3</div>
            </div>
            <div class="explanation">{{ __('view.RESET_PASSWORD') }}</div>
        </div>
    </div>
    @endif
    <div class="forgot-box">
        <form action="" class="form {{$platform}}-form" onSubmit="return false;">
            {{ csrf_field() }}
            {{ \App\Util\ViewHelper::fstate_field() }}
            <input type="hidden" name="code" value="{{ $code }}">
            <input type="hidden" name="password_length_min" value="{{ $user['enterprise']['password_length_min'] }}">
            <input type="hidden" name="password_strength_require_special_symbol" value="{{ $user['enterprise']['password_strength_require_special_symbol'] }}">
            <input type="hidden" name="password_strength_require_capital_letter" value="{{ $user['enterprise']['password_strength_require_capital_letter'] }}">

            <div class="reset-password">
                <div class="form-group">
                    <div class="input-group">
                        <input type="password" name="password" class="text form-control" placeholder="{{ __('view.NEW_PASSWORD') }}" maxlength="32">
                    </div>
                    <div class="hint password-hint"></div>
                </div>
                <div class="form-group">
                    <div class="input-group">
                        <input type="password" name="password_confirmation" class="text form-control" placeholder="{{ __('view.CONFIRM_NEW_PASSWORD') }}" maxlength="32">
                    </div>
                </div>
                <div class="form-group action-group">
                    <button class="btn btn-primary" type="submit" disabled="disabled">{{ __('view.SUBMIT') }}</button>
                </div>
            </div>
        </div>
    </div>
@endif
@endsection
