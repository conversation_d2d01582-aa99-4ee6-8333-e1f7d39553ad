@extends('layouts.auth')
@section('pageTitle', __($platform === 'mobile' ? 'view.LOGIN' : 'view.LOGIN_TITLE') )
@section('pageKeywords', __('view.LOGIN_KEYWORDS') )
@section('pageDescription', __('view.LOGIN_DESCRIPTION', ['product_name' => config('sso.product_name_' . config('app.locale'))]) )



@section('content')
@if(isset($international) && $platform !== 'mobile')
<div class="auth-top">{{ __('view.INTERNAL_PHONE_LOGIN') }}</div>
@endif
@if(isset($other) && $platform !== 'mobile')
<div class="auth-top">{{ __('view.DEDICATED_ENTRY') }}</div>
@endif
@if($isReg)
<div class="reg-modal"></div>
@endif
<div class="login-box">
    @includeWhen($platform !== 'mobile' && $platform !== 'sync' && !isset($international), 'auth.components.switch_loader_v3')
    @includeWhen($platform === 'sync' && !isset($international), 'auth.components.switch_loader_v3_sync')
    <form action="" class="form account-login {{$platform}}-form{{isset($international) ? ' international-form' : '' }}" onSubmit="return false;">
        {{ csrf_field() }}
        {{ \App\Util\ViewHelper::fstate_field() }}
        @include('auth.components.account_password')
        @includeWhen($platform !== 'mobile', 'auth.components.rememberme')
        <div class="form-group action-group">
            <button class="btn btn-primary" type="submit">{{ __('view.LOGIN') }}</button>
        </div>

        @if($platform === 'sync' || $platform === 'web')
            <div class="form-group statement-group">
                登录表示您已阅读并同意&nbsp;
                <a href="https://www.fangcloud.com/home/<USER>" target="blank">服务条款</a>&nbsp;|&nbsp;<a href="https://www.fangcloud.com/home/<USER>" target="blank">隐私政策</a>
                <br/>
                <span>&nbsp;</span>
            </div>
        @endif

        @if($platform === 'mobile')
        <div class="form-group fangcloud-protocol">
            <label>
                <i class="checkbox-inner"></i>
                <input type="checkbox" name="protocol" id="protocol">
            </label>
            <div>
                {!! __('view.MOBILE_LOGIN_CLAUSE_AND_AGREEMENT') !!}
            </div>
        </div>

        <div class="form-group activate-account">
            @if(!isset($oauth))
            <a class="action-icon" href="{{ \App\Util\ViewHelper::routeWithState('activate_by_phone') }}">{{ __('view.PHONE_ACTIVATION') }}</a>
            @endif
            <a class="action-icon" href="{{ isset($international) ? \App\Util\ViewHelper::routeWithState('password.forgot_for_international') : \App\Util\ViewHelper::routeWithState('password.forgot') }}">{{ __('view.FORGOT_PASSWORD') }}</a>
        </div>
        @endif
        @if(isset($international))
           @if($platform !== 'mobile')<a href="{{ \App\Util\CommonHelper::getLoginUrl() }}" class="go-back action-icon"><i class="iconfont icon-backward"></i>{{ __('view.RETURN') }}</a>@endif
        @else
            @include('auth.components.other')
        @endif
    </form>
    <form action="" class="form phone-login {{$platform}}-form{{isset($international) ? ' international-form' : '' }}" onSubmit="return false;">
        {{ csrf_field() }}
        {{ \App\Util\ViewHelper::fstate_field() }}
        @include('auth.components.phone_login')
        @includeWhen($platform !== 'mobile', 'auth.components.rememberme')
        <div class="form-group action-group">
            <button class="btn btn-primary" type="submit">
                {{ $isReg ? __('view.REGISTER_NOW') : __('view.LOGIN') }}
            </button>
        </div>

        @if($platform === 'sync' || $platform === 'web')
            <div class="form-group statement-group">
                {{ $isReg ? '注册' : '登录' }}表示您已阅读并同意&nbsp;
                <a href="https://www.fangcloud.com/home/<USER>" target="blank">服务条款</a>&nbsp;|&nbsp;<a href="https://www.fangcloud.com/home/<USER>" target="blank">隐私政策</a>
                <br/>
                @if(!$isReg)
                    未注册的手机号通过短信验证后将自动注册
                @endif
            </div>
        @endif

        @if($platform === 'mobile')
        <div class="form-group fangcloud-protocol">
            <label>
                <i class="checkbox-inner"></i>
                <input type="checkbox" name="protocol" id="protocol">
            </label>
            <div>
                {!! __('view.MOBILE_LOGIN_CLAUSE_AND_AGREEMENT') !!}
            </div>
        </div>

        <div class="form-group activate-account">
            @if(!isset($oauth))
            <a class="action-icon" href="{{ \App\Util\ViewHelper::routeWithState('activate_by_phone') }}">{{ __('view.PHONE_ACTIVATION') }}</a>
            @endif
            <a class="action-icon" href="{{ isset($international) ? \App\Util\ViewHelper::routeWithState('password.forgot_for_international') : \App\Util\ViewHelper::routeWithState('password.forgot') }}">{{ __('view.FORGOT_PASSWORD') }}</a>
        </div>
        @endif
        @if(isset($international))
           @if($platform !== 'mobile')<a href="{{ \App\Util\CommonHelper::getLoginUrl() }}" class="go-back action-icon"><i class="iconfont icon-backward"></i>{{ __('view.RETURN') }}</a>@endif
        @else
            @include('auth.components.other')
        @endif
    </form>
    @if(isset($oauth))
    <input type="hidden" id="oauth_login" value="1">
    @endif

    @if(request()->header('ApiKey'))
    <input type="hidden" id="api_key" value="{{ request()->header('ApiKey') }}">
    @elseif(request()->input('api_key'))
    <input type="hidden" id="api_key" value="{{ request()->input('api_key') }}">
    @endif

    @if(request()->input('from') == 'wxapp')
    <input type="hidden" id="is_wxapp">
    <script src="//res.wx.qq.com/open/js/jweixin-1.4.0.js"></script>
    @endif

    @if(isset($oauth))
    <div class="oauth-tip">{{ __('view.OAUTH_LOGIN_TIP') }}</div>
    @endif

</div>
<!-- @if(isset($oauth))
<div class="oauth-tip">{{ __('view.OAUTH_LOGIN_TIP') }}</div>
@endif -->
@endsection