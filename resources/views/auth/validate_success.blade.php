@extends('layouts.auth')
@section('pageTitle', __(isset($errors) ? 'view.VALIDATE_FAILED' :
                    (isset($register) ? 'view.REGISTER_SUCCESS' : 'view.VALIDATE_SUCCESS')) )

@section('content')
@if(isset($errors))
<div class="validate-done">
    <div class="status">
        <div class="iconfont icon-fail"></div>
    </div>
    <p class="title">
        {{ $errors['error_msg'] }}
    </p>
    <a href="{{ route('login_settings') }}" class="btn btn-default">{{ __('view.RETRIEVE_VALIDATE_EMAIL') }}</a>
</div>
@elseif(isset($name) && isset($space))
<style>
    .logo-large {
        display: none;
    }

    .wrapper {
        padding: 0;
    }
</style>
<div class="validate-done">
    <div class="vd-content-wp">
        <div class="vdcw-left"></div>
        <div class="vdcw-right">
            <div class="vdcw-tip">
                <!-- <i class="iconfont icon-success"></i> -->
                <svg class="icon-success" aria-hidden="true">
                    <use xlink:href="#icon-success"></use>
                </svg>
                <span class="vdcw-welcome-msg">{{ __('view.ACTIVATION_INVITE_SUCCESS') }}“{{ $name }}”</span>
            </div>

            <div class="vdcw-tip1 vdcw-pc">{{ __('view.ACTIVATION_INVITE_FEATURE_TIP') }}</div>
            <ul class="vdcw-feature-list">
                <li class="vdcwf-item">{{ $space / 1024 / 1024 / 1024 }}G{{ __('view.ACTIVATION_INVITE_FEATURE1') }}</li>
                <li class="vdcwf-item">{{ __('view.ACTIVATION_INVITE_FEATURE2') }}</li>
                <li class="vdcwf-item">{{ __('view.ACTIVATION_INVITE_FEATURE3') }}</li>
                <li class="vdcwf-item">{{ __('view.ACTIVATION_INVITE_FEATURE4') }}</li>
            </ul>
            <div class="vdcw-pc">
                <div class="vdcw-tip2">{{ __('view.ACTIVATION_INVITE_CLIENT_TIP') }}</div>
                <a class="vdcw-download-btn j_pc_download" target="_blank">{{ __('view.ACTIVATION_INVITE_CLIENT_DOWNLOAD') }} </a>
                <a class="vdcw-v2-link" href={{ config('app.default_page') }} target="_blank">{{ __('view.ACTIVATION_INVITE_CLIENT_REJECT') }}</a>
            </div>
            <div class="vdcw-wap">
                <div class="vdcw-download-wp">
                    <div class="vdcw-download-item vdcw-download-pc">
                        <svg class="vdcw-download-icon" aria-hidden="true">
                            <use xlink:href="#icon-pc"></use>
                        </svg>
                        <div class="vdcw-download-main">
                            <p class="vdcw-tip1">{{ __('view.ACTIVATION_INVITE_MOBILE_TIP1') }}</p>
                            <p class="vdcw-tip2">{{ __('view.ACTIVATION_INVITE_MOBILE_TIP2') }}</p>
                            <span class="vdcw-tip3-wp">
                                <textarea class="vdcw-tip3 j_copy_target" disabled>{{ config('app.fangcloud_url') }}home/download</textarea>
                                <span class="span-helper">{{ config('app.fangcloud_url') }}home/download</span>
                            </span>
                            <a class="vdcw-copy j_copy_download">{{ __('view.ACTIVATION_INVITE_MOBILE_COPY_LINK') }}</a>
                        </div>
                    </div>
                    <div class="vdcw-download-item vdcw-download-wap">
                        <svg class="vdcw-download-icon type-icon" aria-hidden="true">
                            <use xlink:href="#icon-phone"></use>
                        </svg>
                        <div class="vdcw-download-main">
                            <p class="vdcw-tip1">{{ __('view.ACTIVATION_INVITE_MOBILE_TIP3') }}</p>
                            <p class="vdcw-tip2">{{ __('view.ACTIVATION_INVITE_MOBILE_TIP4') }}</p>
                            <a target="_blank" href="{{ config('app.fangcloud_url') }}applink/download?base_url={{ config('app.fangcloud_url') }}/" class="vdcw-download-btn">{{ __('view.DOWNLOAD_APP_BUTTON') }}</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
var pcDownloadBtn = document.querySelector('.j_pc_download');
if (!!navigator.userAgent.toLowerCase().match(/macintosh|mac os x/)) {
    pcDownloadBtn.href = "{{ ViewHelper::getMacDownloadLink() }}";
}
else if (navigator.userAgent.toLowerCase().indexOf('windows nt 5.1') > 0) {
    pcDownloadBtn.href = "{{ ViewHelper::getDownloadLink('windows_xp_download_link')}}?t={{ time() }}&filename=installer.exe";
} else {
    pcDownloadBtn.href = "{{ ViewHelper::getWindowsDownloadLink()}}?t={{ time() }}&filename=installer.exe";
}
</script>
@else
<div class="validate-done">
    <div class="status">
        <div class="iconfont icon-success"></div>
    </div>
    <p class="title">{{ __(isset($register) ? 'view.REGISTER_DONE' : 'view.VALIDATE_DONE') }}</p>
    <a href="{{ config('app.default_page') }}" class="btn btn-primary"> {{ __('view.START_USING') }}</a>
</div>
@endif
@endsection
<script type="text/javascript" src="//at.alicdn.com/t/font_414128_7bm74zm7v6.js"></script>
