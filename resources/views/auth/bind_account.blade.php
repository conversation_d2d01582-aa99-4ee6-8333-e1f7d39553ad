@extends('layouts.auth')
@section('pageTitle', __('view.BIND_ACCOUNT') )

@section('content')
<div class="login-box bind-login">
    <form action="" class="form {{$platform}}-form bind-form" onSubmit="return false;">
        {{ csrf_field() }}
        <div class="logos-header">
            <div class="bind-logo logo-{{Request::query('bind_type')}}"></div>
            <h3>{{ __('view.BIND_ACCOUNT_FIRST') }}</h3>
        </div>
        @include('auth.components.account_password')
        @includeWhen($platform !== 'mobile', 'auth.components.rememberme')

        <div class="form-group action-group">
            <button class="btn btn-primary" type="submit">{{ __('view.LOGIN_AND_BIND') }}</button>
        </div>
        @if($platform !== 'sync')
        <a class="bind-register action-icon" href="{{ route('register') }}?bind=1">{{ __('view.BIND_ACCOUNT_REGISTER') }}</a>
        @endif
    </form>
</div>

@endsection('content')
