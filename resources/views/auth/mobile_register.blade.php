@extends('layouts.register')
@section('pageTitle', __(($platform === 'mobile' || $personal_register) ? 'view.REGISTER' : 'view.REGISTER_TITLE_' . $plan_id) )
@section('pageKeywords', __('view.REGISTER_KEYWORDS_' . $plan_id) )
@section('pageDescription', __('view.REGISTER_DESCRIPTION_' . $plan_id) )

@section('step')

<div class="mobile-register_div {{!isset($register) || $platform != 'mobile' ? ' padding-left_right' : ''}}">
    @if(isset($aliyun_register))
    <h3 class="register-top">{{ __('view.ALI_MARKET_REGISTER_TIPS') }}</h3>
    @elseif($platform === 'web' && !$personal_register)
    <h3 class="register-top">{{ __('view.REGISTER_TOP_' . $plan_id . ($source == 'efpg' && $plan_id == 15 ? '_TAISU' : '')) }}</h3>
    <p class="register-hint">{!! __('view.REGISTER_HINT_' . $plan_id) !!}</p>
    @endif
    <div class="register_success">
        <div>
            <img src="{{ ViewHelper::asset_m('images/register_success.png')}}" alt="">
            <p class="register_success_text">登录电脑端享更多服务</p>
            <div class="register_success_tips">
                <img src="{{ ViewHelper::asset_m('images/register_tips.png')}}" alt="">
                初始密码已发送你手机!
            </div>
            <span class="register_close_1">我知道了</span>
            <div class="register_close_2"></div>
        </div>
    </div>

    <div class="register-box">

        @if(isset($aliyun_register) || $platform === 'mobile' || ($plan_id != 'custom' && $plan_id != 'cooperate'))
        <div class="register_top">
            @if($plan_id == 27)
                <img src="{{ ViewHelper::asset_m('images/register-top-27.png')}}" alt="">
            @elseif ($plan_id == 28)
                <img src="{{ ViewHelper::asset_m('images/register-top-28.png')}}" alt="">
            @elseif ($plan_id == 29)
                <img src="{{ ViewHelper::asset_m('images/register-top-29.png')}}" alt="">
            @else
                <img src="{{ ViewHelper::asset_m('images/register-top-other.png')}}" alt="">
            @endif
        </div>
        <form action="{{ route('register') }}" method="POST" class="form {{$platform}}-form normal-register mobile-register" onSubmit="return false;">
            <input type="hidden" name="plan_id" value="{{ $plan_id }}">
            <input type="hidden" name="invite_code" value="{{ $invite_code }}">
            <input type="hidden" name="redirect" value="{{ $redirect }}">

            <p>安全、易用的企业内外部共享云盘</p>

            <div class="form-group phone">
                <div class="input-group">
                    <input type="text" class="username text form-control" id="phone" name="phone" value="{{ old('phone') }}" placeholder="{{ __('view.MOBILE_PLACEHOLDER') }}" maxlength="11">
                </div>
            </div>

            <div class="form-group password">
                <div class="input-group">
                    <input type="password" name="password" class="text form-control" placeholder="{{ __('view.PASSWORD_PLACEHOLDER_WITH_STRENTH') }}" autocomplete="new-password" maxlength="32">
                </div>
                <span class="hint hidden">{{ __('view.PASSWORD_STRENTH_MID') }}</span>
            </div>

            <div class="form-group action-group">
                <button class="btn btn-primary" type="submit">{{ __('view.REGISTER_NOW') }}</button>
            </div>

            <div class="form-group fangcloud-protocol">
                <label>
                    <i class="checkbox-inner"></i>
                    <input type="checkbox" name="protocol" id="protocol">
                </label>
                <div class="clause">
                    我已阅读并同意&nbsp;<a href="https://www.fangcloud.com/home/<USER>">服务条款</a>、<a href="https://www.fangcloud.com/home/<USER>">服务等级协议</a>、<a href="https://www.fangcloud.com/home/<USER>">隐私政策</a>
                </div>
            </div>
        </form>
        <div class="register_bottom">
            @if($plan_id == 27)
                <img src="{{ ViewHelper::asset_m('images/register-27.png')}}" alt="">
            @elseif ($plan_id == 28)
                <img src="{{ ViewHelper::asset_m('images/register-28.png')}}" alt="">
            @elseif ($plan_id == 29)
                <img src="{{ ViewHelper::asset_m('images/register-29.png')}}" alt="">
            @else
                <img src="{{ ViewHelper::asset_m('images/register-other.png')}}" alt="">
            @endif
        </div>
        <div class="show_img" style="display: none;"></div>
        @else
        <form action="{{ route('register') }}" method="POST" class="form {{$platform}}-form enterprise-register" onSubmit="return false;">
            {{ csrf_field() }}
            {{ \App\Util\ViewHelper::fstate_field() }}
            <input type="hidden" name="plan_id" value="{{ $plan_id }}">
            <div class="form-group">
                <div class="input-group">
                    <input type="text" name="username" class="text form-control" placeholder="{{ __('view.NAME_PLACE_HOLDER') }}">
                </div>
            </div>

            <div class="form-group">
                <div class="input-group">
                    <input type="text" name="enterprise_name" class="text form-control" placeholder="{{__('view.ENTERPRISE_PLACEHOLDER') }}">
                </div>
            </div>

            <div class="form-group phone">
                <div class="input-group">
                    <input type="text" class="text form-control" name="phone" id="phone" value="" placeholder="{{ __('view.MOBILE_PLACEHOLDER') }}" maxlength="11">
                </div>
            </div>

            <div class="form-group">
                <div class="input-group">
                    <input type="text" class="text form-control" name="email" id="email" value="" placeholder="{{ __('view.EMAIL_PLACEHOLDER') }}">
                </div>
            </div>

            @if($plan_id != 'cooperate')
            <div class="form-group">
                <div class="input-group">
                    <div class="select-group enterprise-size">
                        <div class="select-group-content">
                            <input type="hidden" name="enterprise_size">
                            <div class="select"><span class="placeholder">{{ __('view.SELECT_YOUR_ENTERPRISE_SIZE') }}</span></div>
                            <i class="iconfont select-arrow icon-arrow-down"></i>
                        </div>
                        <!-- <ul class="select-group-options">
                            <li data-value="500">{{ __('view.AUTH_REGISTER_BELOW_500_PEOPLE') }}</li>
                            <li data-value="750">{{ __('view.AUTH_REGISTER_BELOW_1000_PEOPLE') }}</li>
                            <li data-value="1000">{{ __('view.AUTH_REGISTER_MORE_THAN_1000_PEOPLE') }}</li>
                        </ul> -->
                        <ul class="select-group-options">
                            <li data-value="0">{{ __('view.AUTH_REGISTER_SIZE_SELECT_0') }}</li>
                            <li data-value="1">{{ __('view.AUTH_REGISTER_SIZE_SELECT_1') }}</li>
                            <li data-value="2">{{ __('view.AUTH_REGISTER_SIZE_SELECT_2') }}</li>
                            <li data-value="3">{{ __('view.AUTH_REGISTER_SIZE_SELECT_3') }}</li>
                            <li data-value="4">{{ __('view.AUTH_REGISTER_SIZE_SELECT_4') }}</li>
                            <li data-value="5">{{ __('view.AUTH_REGISTER_SIZE_SELECT_5') }}</li>
                        </ul>
                    </div>
                </div>
            </div>
            @endif

            <div class="form-group">
                <div class="input-group">
                    <input type="text" class="text form-control" name="position_name" placeholder="{{__('view.POSITION_NAME_PLACEHOLDER') }}">
                </div>
            </div>

            <div class="form-group action-group">
                <button class="btn btn-primary submit" type="submit">
                    {{ __('view.SEND_TO_FANGCLOUD') }}
                </button>
            </div>
        </form>
        @endif

    </div>

    @if($platform === 'web')
    <div class="register-qrcode-modal">
        <div class="qrcode-dialog">
            <div class="title">{{ __('view.REGISTER_QRCODE_TITLE_' . $plan_id) }}</div>
            <div class="hint">{{ __('view.REGISTER_QRCODE_HINT_' . $plan_id) }}</div>
            <img class="qrcode-img" src="" alt="">
            <a class="btn" href="{{isset($source) && $source == 'efpg' ? 'javascript:' : config('app.fangcloud_url')}}">← 返回首页</a>
            <div class="close">
                <a href="{{isset($source) && $source == 'efpg' ? 'javascript:' : config('app.fangcloud_url')}}">
                    <i class="iconfont icon-close-big"></i>
                </a>
            </div>
        </div>
    </div>
    @endif
</div>
@endsection