@extends('layouts.register')
@section('pageTitle', __(($platform === 'mobile' || $personal_register) ? 'view.REGISTER' : 'view.REGISTER_TITLE_' . $plan_id) )
@section('pageKeywords', __('view.REGISTER_KEYWORDS_' . $plan_id) )
@section('pageDescription', __('view.REGISTER_DESCRIPTION_' . $plan_id) )

@section('step')
@if($platform === 'web')
    @if($personal_register)
    <ul class="plan-list personal-plan">
        <li class="plan plan-selector active">
            <div class="plan-head">
                <span class="edition">{{ __('view.PERSONAL_PLAN') }}</span>
                <span class="hint">{{ __('view.PERSONAL_PLAN_FEE') }}</span>
            </div>
            <ul class="plan-info">
                <li><span>{{ __('view.REGISTER_STORAGE_SPACE', ['storage' => '20GB']) }}</span></li>
                <li><span>{{ __('view.REGISTER_MOST_HISTORICAL_VERSION', ['count' => '10']) }}</span></li>
            </ul>
        </li>
    </ul>
    @elseif(isset($aliyun_register))
    <ul class="plan-list aliyun-plan">
        <li class="plan plan-selector">
            <div class="plan-head">
                <span class="edition">{{ $regInfo['plan_info']['description'] }}</span>
                <span class="hint">{{ __('view.ALIPAY_ACCOUNTS', ['seat_limit' => $regInfo['user_info']['account_quality']]) }}</span>
                <span class="deadline">{{ __('view.ALIPAY_EXPIRED_DATE', ['date' => date("Y-m-d", $regInfo['user_info']['expires_at'])]) }}</span>
            </div>
        </li>
    </ul>
    @endif
@endif

@if(isset($aliyun_register))
<h3 class="register-top">{{ __('view.ALI_MARKET_REGISTER_TIPS') }}</h3>
@elseif($platform === 'web' && !$personal_register)
<h3 class="register-top">{{ __('view.REGISTER_TOP_' . $plan_id . ($source == 'efpg' && $plan_id == 15 ? '_TAISU' : '')) }}</h3>
<p class="register-hint">{!! __('view.REGISTER_HINT_' . $plan_id) !!}</p>
@endif
<div class="register-box">
    @if(isset($aliyun_register) || $platform === 'mobile' || ($plan_id != 'custom' && $plan_id != 'cooperate'))
    <form action="{{ route('register') }}" method="POST" class="form {{$platform}}-form normal-register" onSubmit="return false;">
        {{ csrf_field() }}
        {{ \App\Util\ViewHelper::fstate_field() }}

        <input type="hidden" name="plan_id" value="{{ $plan_id }}">
        <input type="hidden" name="invite_code" value="{{ $invite_code }}">
        <input type="hidden" name="redirect" value="{{ $redirect }}">

        @if($personal_register)
        <input type="hidden" name="personal_register" value="1">
        @endif

        <div class="form-group phone">
            <div class="input-group">
                <input type="text" class="username text form-control" id="phone" name="phone" value="{{ old('phone') }}" placeholder="{{ __('view.MOBILE_PLACEHOLDER') }}" maxlength="11">
            </div>
        </div>
        <div class="form-group">
            <div class="input-group">
                <input type="password" name="password" class="text form-control" placeholder="{{ __('view.PASSWORD_PLACEHOLDER_WITH_STRENTH') }}" autocomplete="new-password" maxlength="32">
            </div>
            <span class="hint hidden">{{ __('view.PASSWORD_STRENTH_MID') }}</span>
        </div>
        <div class="form-group">
            <div class="input-group">
                <input type="text" name="name" class="text form-control" placeholder="{{ __('view.NAME_PLACE_HOLDER') }}" maxlength="30">
            </div>
        </div>

        <div class="form-group email hidden">
            <div class="input-group">
                <input type="text" name="email" id="email" class="text form-control" value="" placeholder="{{ __('view.EMAIL_PLACEHOLDER') }}">
            </div>
        </div>

        @unless($personal_register)
        <div class="form-group">
            <div class="input-group">
                <input type="text" name="company_name" class="text form-control" placeholder="{{__('view.ENTERPRISE_TEAM_PLACEHOLDER') }}" maxlength="30">
            </div>
        </div>
        @endunless

        @if(!$personal_register)
        <div class="form-group radiobox-group register-purpose">
            <label class="label">{{ __('view.REGISTER_PURPOSE') }}</label>
            <label class="radiobox">
                <i class="radiobox-inner"></i>
                <input type="radio" name="motivation" value="1"></input>
                {{ __('view.REGISTER_FOR_PERSONAL_STORAGE') }}
            </label>
            <label class="radiobox">
                <i class="radiobox-inner"></i>
                <input type="radio" name="motivation" value="2"></input>
                {{ __('view.REGISTER_FOR_COLLABORATE') }}
            </label>
        </div>
        @endif

        @if($platform === 'web' && !isset($aliyun_register))
        <div class="form-group fangcloud-protocol">
            <label for="protocol">
                <i class="checkbox-inner"></i>
                <input type="checkbox" name="protocol" id="protocol" checked="checked">
                {!! __('view.REGISTER_CLAUSE_AND_AGREEMENT') !!}
            </label>
        </div>
        @endif

        <div class="form-group action-group">
            <button class="btn btn-primary" type="submit">{{ __('view.REGISTER_NOW') }}</button>
        </div>

        @if($platform === 'mobile')
        <div class="form-group fangcloud-protocol">
            <label>
                <i class="checkbox-inner"></i>
                <input type="checkbox" name="protocol" id="protocol">
            </label>
            <div>
                {!! __('view.MOBILE_REGISTER_CLAUSE_AND_AGREEMENT') !!}
            </div>
        </div>
        @endif
    </form>
    @else
    <form action="{{ route('register') }}" method="POST" class="form {{$platform}}-form enterprise-register" onSubmit="return false;">
        {{ csrf_field() }}
        {{ \App\Util\ViewHelper::fstate_field() }}
        <input type="hidden" name="plan_id" value="{{ $plan_id }}">
        <div class="form-group">
            <div class="input-group">
                <input type="text" name="username" class="text form-control" placeholder="{{ __('view.NAME_PLACE_HOLDER') }}">
            </div>
        </div>

        <div class="form-group">
            <div class="input-group">
                <input type="text" name="enterprise_name" class="text form-control" placeholder="{{__('view.ENTERPRISE_PLACEHOLDER') }}">
            </div>
        </div>

        <div class="form-group phone">
            <div class="input-group">
                <input type="text" class="text form-control" name="phone" id="phone" value="" placeholder="{{ __('view.MOBILE_PLACEHOLDER') }}" maxlength="11">
            </div>
        </div>

        <div class="form-group">
            <div class="input-group">
                <input type="text" class="text form-control" name="email" id="email" value="" placeholder="{{ __('view.EMAIL_PLACEHOLDER') }}">
            </div>
        </div>

        @if($plan_id != 'cooperate')
        <div class="form-group">
            <div class="input-group">
                <div class="select-group enterprise-size">
                    <div class="select-group-content">
                        <input type="hidden" name="enterprise_size">
                        <div class="select"><span class="placeholder">{{ __('view.SELECT_YOUR_ENTERPRISE_SIZE') }}</span></div>
                        <i class="iconfont select-arrow icon-arrow-down"></i>
                    </div>
                    <!-- <ul class="select-group-options">
                        <li data-value="500">{{ __('view.AUTH_REGISTER_BELOW_500_PEOPLE') }}</li>
                        <li data-value="750">{{ __('view.AUTH_REGISTER_BELOW_1000_PEOPLE') }}</li>
                        <li data-value="1000">{{ __('view.AUTH_REGISTER_MORE_THAN_1000_PEOPLE') }}</li>
                    </ul> -->
                    <ul class="select-group-options">
                        <li data-value="0">{{ __('view.AUTH_REGISTER_SIZE_SELECT_0') }}</li>
                        <li data-value="1">{{ __('view.AUTH_REGISTER_SIZE_SELECT_1') }}</li>
                        <li data-value="2">{{ __('view.AUTH_REGISTER_SIZE_SELECT_2') }}</li>
                        <li data-value="3">{{ __('view.AUTH_REGISTER_SIZE_SELECT_3') }}</li>
                        <li data-value="4">{{ __('view.AUTH_REGISTER_SIZE_SELECT_4') }}</li>
                        <li data-value="5">{{ __('view.AUTH_REGISTER_SIZE_SELECT_5') }}</li>
                    </ul>
                </div>
            </div>
        </div>
        @endif

        <div class="form-group">
            <div class="input-group">
                <input type="text" class="text form-control" name="position_name" placeholder="{{__('view.POSITION_NAME_PLACEHOLDER') }}">
            </div>
        </div>

        <div class="form-group action-group">
            <button class="btn btn-primary submit" type="submit">
                {{ __('view.SEND_TO_FANGCLOUD') }}
            </button>
        </div>
    </form>
    @endif

</div>
@endsection

@if($platform === 'web')
<div class="register-qrcode-modal">
    <div class="qrcode-dialog">
        <div class="title">{{ __('view.REGISTER_QRCODE_TITLE_' . $plan_id) }}</div>
        <div class="hint">{{ __('view.REGISTER_QRCODE_HINT_' . $plan_id) }}</div>
        <img class="qrcode-img" src="" alt="">
        <a class="btn" href="{{isset($source) && $source == 'efpg' ? 'javascript:' : config('app.fangcloud_url')}}">← 返回首页</a>
        <div class="close">
            <a href="{{isset($source) && $source == 'efpg' ? 'javascript:' : config('app.fangcloud_url')}}">
                <i class="iconfont icon-close-big"></i>
            </a>
        </div>
    </div>
</div>
@endif