@extends('layouts.auth')
@section('pageTitle', __('view.DEDICATED_ENTRY') )

@section('content')

@if(isset($other) && $platform !== 'mobile')
<div class="auth-top">{{ __('view.DEDICATED_ENTRY') }}</div>
@endif
<div class="login-box">
    <form action="" class="form {{$platform}}-form ohter-login-form" onSubmit="return false;">
        {{ csrf_field() }}
        {{ \App\Util\ViewHelper::fstate_field() }}
        <p class="hint">{{ __('view.CONTACT_ADMIN') }}</p>
        <div class="form-group">
            <div class="input-group">
                <input type="text" class="text form-control" id="enterprise_name" name="enterprise_name" placeholder="{{ __('view.ENTERPRISE_PLACEHOLDER') }}">
            </div>
        </div>
        <div class="form-group action-group">
            <button class="btn btn-primary" type="submit">{{ __('view.NEXT') }}</button>
        </div>

        @if($platform !== 'mobile')
        <div class="form-group">
            <a href="{{ \App\Util\CommonHelper::getLoginUrl() }}" class="go-back action-icon"><i class="iconfont icon-backward"></i>{{ __('view.RETURN') }}</a>
        </div>
        @endif
    </form>

    <input type="hidden" name="fangcloud_url" id="fangcloud_url" value="{{config('app.fangcloud_url')}}">
</div>
@endsection


