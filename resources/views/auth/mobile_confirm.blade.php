@extends('layouts.base')
@section('pageTitle', __('view.MOBILE_CONFIRM_LOGIN') )

@section('main')
<div class="login-box main-container confirm-login form">
    {{ \App\Util\ViewHelper::fstate_field() }}
    <input type="hidden" id="token" value="{{$token}}">
    <h3>
        <i class="iconfont icon-login-confirmation"></i>
        <p>{{ __('view.MOBILE_LOGIN_CONFIRM', ['product_name' => ViewHelper::isFangcloud() ? __('view.FANGCLOUD_V2') : '' , 'platform' => __($from === 'web' ? 'view.WEB' : 'view.SYNC')]) }}</p>
    </h3>
    <div class="expire-box {{ (!isset($success) && isset($errors)) ? 'show': ''}}">
        <div class="warning">{{ (!isset($success) && isset($errors)) ? $errors[0]['error_msg'] : __('view.MOBILE_CONFIRM_EXPIRE_WARNING') }}</div>
        <a class="re-scan btn" href="#">{{ __('view.RE_SCAN') }}</a>
    </div>

    @if(isset($success))
    <div class="confirm-box">
        <div class="form-group">
            <button class="btn btn-primary" type="submit">{{ __('view.CONFIRM_LOGIN') }}</button>
        </div>
        <div class="form-group">
            <a class="cancel" href="#">{{ __('view.CANCEL') }}</a>
        </div>
    </div>
    @endif
</div>
@endsection


