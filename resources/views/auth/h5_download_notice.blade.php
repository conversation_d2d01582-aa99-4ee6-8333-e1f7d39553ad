@extends('layouts.base')
@section('pageTitle', __('view.DOWNLOAD') )

@section('main')
<div class="login-box main-container download-notice form">
    {{ \App\Util\ViewHelper::fstate_field() }}
    <div id="tooltip"></div>

    <h3>
        <div class="app-icon"></div>
        <p class="download-tip">{!! __('view.USE_FANGCLOUD_APP_SCAN') !!}</p>
    </h3>
    <a href="{{ config('app.fangcloud_url') }}mobile" class="btn btn-primary">{{ __('view.DOWNLOAD_APP_BUTTON') }}</a>
    <a href="{{ config('app.h5_download_notice') }}" class="btn btn-default">{{ __('view.APP_DOWNLOADED') }}</a>
</div>


<script>
    var ua = navigator.userAgent;
    var isAndroid = !!ua.match(/(Android);?[\s\/]+([\d.]+)?/);
    var isSafari = !isAndroid && !!ua.match(/Version\/([\d.]+)([^S](Safari)|[^M]*(Mobile)[^S]*(Safari))/);

    var tooltip_1_ios = '{{ __("view.APP_INSTALLED") }} {{ __("view.OPEN_IN_SAFARI") }}';
    var tooltip_1_android = '{{ __("view.APP_INSTALLED") }} {{ __("view.OPEN_IN_BROWSER") }}';
    var tooltip_2 = '{{ __("view.APP_INSTALLED") }} {{ __("view.SWIPE_DOWN_OPEN") }}';
    var tooltip_3 = '{{ __("view.NO_RES_ON_SWIPE") }} <a href="{{ $scheme_url }}">{{ __("view.CLICK_TO_OPEN") }}</a>';
    var tooltip_4 = '{{ __("view.APP_INSTALLED") }} <a href="{{ $scheme_url }}">{{ __("view.CLICK_TO_OPEN") }}</a>';
    var tooltipEl = document.getElementById('tooltip');
    var isWeixinBrowser = (/micromessenger/i).test(ua);
    var isQQ = (/qq/i).test(ua);

    var tooltip = isSafari ? tooltip_2 : tooltip_4;
    if (isWeixinBrowser || isQQ) { tooltip = isAndroid ? tooltip_1_android : tooltip_1_ios ; }
    // tooltipEl.innerHTML = tooltip;

    if(isAndroid) {
        document.querySelector('.btn-default').style.display = 'none';
        // tooltipEl.innerHTML = tooltip;
    }
    // swipe
    // if (isSafari) {
    //     var startY, endY;
    //     document.ontouchstart = function (e) { startY = e.touches[0].clientY; }
    //     document.ontouchmove = function (e) { endY = e.touches[0].clientY; }
    //     document.ontouchend = function (e) {
    //         if (endY - startY > 100) {
    //             tooltipEl.innerHTML = tooltip_3;
    //         }
    //     }
    // }

</script>
@endsection


