@extends('layouts.auth')
@section('pageTitle',  __('view.REGISTER') )
@section('content')
<h3 class="aliyunbind-top">绑定阿里云OSS</h3>
<ul class="bind-info">
    <li>1. 请确保你已开通阿里云OSS服务</li>
    <li>2. 建议创建阿里云RAM子帐号，授权子帐号拥有相应bucket的访问权限</li>
    <li>3. 输入阿里云帐号的access key信息</li>
</ul>
<div class="register-box">
    <form action="{{ route('register') }}" method="POST" class="form {{$platform}}-form normal-register" onSubmit="return false;">
        {{ csrf_field() }}
        {{ \App\Util\ViewHelper::fstate_field() }}
        <input type="hidden" name="plan_id" value="1">

        <div class="form-group">
            <div class="input-group">
                <input type="text" class="text form-control" id="bucket" name="bucket" value="" placeholder="bucket">
            </div>
        </div>
        <div class="form-group">
            <div class="input-group">
                <input type="text" name="key" class="text form-control" placeholder="建议填写子帐号的key">
            </div>
        </div>
        <div class="form-group">
            <div class="input-group">
                <input type="text" name="secret" class="text form-control" placeholder="建议填写子帐号的secret">
            </div>
        </div>

        <div class="form-group action-group">
            <button class="btn btn-primary" type="submit">完成绑定</button>
            <a href="" class="bind-help action-icon">查看帮助</a>
        </div>
    </form>
</div>
@endsection
