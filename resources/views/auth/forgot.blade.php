@extends('layouts.auth')
@section('pageTitle', __('view.FORGOT_PASSWORD') )

@section('content')
@if($platform === 'web')
<div class="progress forgot-progress">
    <h3 class="auth-top">{{ __('view.FORGOT_PASSWORD') }}</h3>
    <div class="step-info current">
        <div class="radius-box">
            <i class="radius"></i>
            <div class="text">1</div>
        </div>
        <div class="explanation">{{ __('view.CONFIRM_ACCOUNT') }}</div>
    </div>
    <div class="line"></div>
    <div class="step-info">
        <div class="radius-box">
            <i class="radius"></i>
            <div class="text">2</div>
        </div>
        <div class="explanation">{{ __('view.IDENTITY_VERIFICATION') }}</div>
    </div>
    <div class="line"></div>
    <div class="step-info">
        <div class="radius-box">
            <i class="radius"></i>
            <div class="text">3</div>
        </div>
        <div class="explanation">{{ __('view.RESET_PASSWORD') }}</div>
    </div>
</div>
@endif
<div class="forgot-box">
    <form action="" class="form {{$platform}}-form" onSubmit="return false;">
        {{ csrf_field() }}
        {{ \App\Util\ViewHelper::fstate_field() }}
        <div class="step-1 confirm-account">
            <div class="form-group">
                <div class="input-group">
                    <input type="text" id="login" name="login" class="username text form-control" placeholder="{{__('view.ACCOUNT_PLACEHOLDER')}}">
                </div>
            </div>
            <div class="form-group action-group next-step">
                <button class="btn btn-primary" type="submit" disabled="disabled">{{ __('view.NEXT') }}</button>
            </div>
            @if($platform !== 'mobile')
            <div class="form-group">
                <a href="{{\App\Util\CommonHelper::getLoginUrl()}}" class="go-back action-icon"><i class="iconfont icon-backward"></i>{{ __('view.RETURN_LOGIN') }}</a>
            </div>
            @endif
        </div>

        <div class="step-2 identity-verification">
            <div class="form-group radiobox-group validate-account">
            </div>
            <div class="form-group action-group next-step">
                <button class="btn btn-primary" type="submit" disabled="disabled">{{ __('view.NEXT') }}</button>
            </div>
            <div class="form-group">
                <a href="" class="go-back action-icon"><i class="iconfont icon-backward"></i>{{ __('view.RETURN_BEFORE') }}</a>
            </div>
        </div>
    </div>
</div>
@if(Request::path() == 'password/forgot_for_international')
    <input type="hidden" name="is_international" id="is_international" value="1">
@endif

@endsection
