<!DOCTYPE html>
<html lang="{{ app()->getLocale() }}">
<head>
    <meta charset="utf-8">
    <!-- 防百度转码 -->
    <meta name="applicable-device" content="pc,mobile">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="renderer" content="webkit">
    <meta http-equiv="Cache-Control" content="no-siteapp"/>
    <!-- CSRF Token -->
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <meta name="keywords" content="@yield('pageKeywords')">
    <meta name="description" content="@yield('pageDescription')">
    @if(request() -> segment(1) === 'hybrid_enterprise')
    <title> @yield('pageTitle')</title>
    @else
    <title> @yield('pageTitle') </title>
    @endif
    @if(ViewHelper::isFangcloud())
        <!-- @if(str_contains(request()->path(), 'login_v3'))
            <link rel="shortcut icon" href="{{ config('app.url') }}favicon_nami.ico" type="image/x-icon" />
        @else
            <link rel="shortcut icon" href="{{ config('app.url') }}favicon.ico" type="image/x-icon" />
            @endif -->
        <link rel="shortcut icon" href="{{ config('app.url') }}favicon.ico" type="image/x-icon" />
    @else
    <link rel="shortcut icon" href="{{ ViewHelper::logoURI('favicon', 'zh-CN') }}" type="image/x-icon" />
    @endif
    <!-- dns prefetch -->
    <link rel="preconnect" href="{{ ViewHelper::static_url() }}">
    <link rel="dns-prefetch" href="{{ ViewHelper::static_url() }}">
    @if(isset($platform) && $platform === 'mobile')
    <script>
        !function(a,b){function c(){var b=f.getBoundingClientRect().width;b/i>540&&(b=540*i);var c=b/10;f.style.fontSize=c+"px",k.rem=a.rem=c}var d,e=a.document,f=e.documentElement,g=e.querySelector('meta[name="viewport"]'),h=e.querySelector('meta[name="flexible"]'),i=0,j=0,k=b.flexible||(b.flexible={});if(g){console.warn("将根据已有的meta标签来设置缩放比例");var l=g.getAttribute("content").match(/initial\-scale=([\d\.]+)/);l&&(j=parseFloat(l[1]),i=parseInt(1/j))}else if(h){var m=h.getAttribute("content");if(m){var n=m.match(/initial\-dpr=([\d\.]+)/),o=m.match(/maximum\-dpr=([\d\.]+)/);n&&(i=parseFloat(n[1]),j=parseFloat((1/i).toFixed(2))),o&&(i=parseFloat(o[1]),j=parseFloat((1/i).toFixed(2)))}}if(!i&&!j){var p=(a.navigator.appVersion.match(/android/gi),a.navigator.appVersion.match(/iphone/gi)),q=a.devicePixelRatio;i=p?q>=3&&(!i||i>=3)?3:q>=2&&(!i||i>=2)?2:1:1,j=1/i;window.mobileTransform=i;}if(f.setAttribute("data-dpr",i),!g)if(g=e.createElement("meta"),g.setAttribute("name","viewport"),g.setAttribute("content","initial-scale="+j+", maximum-scale="+j+", minimum-scale="+j+", user-scalable=no"),f.firstElementChild)f.firstElementChild.appendChild(g);else{var r=e.createElement("div");r.appendChild(g),e.write(r.innerHTML)}a.addEventListener("resize",function(){clearTimeout(d),d=setTimeout(c,300)},!1),a.addEventListener("pageshow",function(a){a.persisted&&(clearTimeout(d),d=setTimeout(c,300))},!1),"complete"===e.readyState?e.body.style.fontSize=12*i+"px":e.addEventListener("DOMContentLoaded",function(){e.body.style.fontSize=12*i+"px"},!1),c(),k.dpr=a.dpr=i,k.refreshRem=c,k.rem2px=function(a){var b=parseFloat(a)*this.rem;return"string"==typeof a&&a.match(/rem$/)&&(b+="px"),b},k.px2rem=function(a){var b=parseFloat(a)/this.rem;return"string"==typeof a&&a.match(/px$/)&&(b+="rem"),b}}(window,window.lib||(window.lib={}));
    </script>
    @else
    <script>
        if ((function (ua) {
            var browser = {},
                ie = ua.match(/MSIE\s([\d.]+)/) || ua.match(/Trident\/[\d](?=[^\?]+).*rv:([0-9.].)/);
            if (ie) browser.ie = true, browser.version = Number(ie[1]);
            return browser.ie && (browser.version < 10);
        })(navigator.userAgent)) { document.location.href = "{{ config('app.fangcloud_url') }}apps/files/incompatible"; }
    </script>
    @endif
    <!-- Styles -->
    @isset($css)
    <link href="{{ ViewHelper::asset_m($css) }}" rel="stylesheet">
    @endisset

</head>
<body class="i18n-{{ app()->getLocale() }}">
    @if(isset($platform) && $platform == 'mobile')
    <input type="hidden" id="is_mobile" value="1">
    @if(request()->header('ApiKey'))
    <input type="hidden" id="api_key" value="{{ request()->header('ApiKey') }}">
    @elseif(request()->query('api_key'))
    <input type="hidden" id="api_key" value="{{ request()->query('api_key') }}">
    @endif
    <script>
    // iOS升级改掉了window.fangcloud的调用方式，所以这边要特殊处理一些
    if(window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.isFangcloud) {
        window.fangcloud = {};
        ['close', 'login'].forEach(function(fn) {
            if (!!window.webkit.messageHandlers[fn]) {
                window.fangcloud[fn] = function(data) {
                    window.webkit.messageHandlers[fn].postMessage(data || '{}');
                }
            }
        });
    }
    </script>
    @elseif(isset($platform) && $platform == 'sync')
    <input type="hidden" id="is_sync" value="1">
        @if(ViewHelper::is_sync_client('WebEngine'))
        <script type="text/javascript">
            window.IS_WEBENGINE = true;
        </script>
        @endif
    @endif

    <div class="wrapper{{isset($platform) && $platform == 'sync' ? ' sync-wrapper' : ''}}{{request() -> query('embed') ? ' sem-wrapper' : ''}}{{isset($oauth) ? ' open-wrapper' : ''}}{{isset($mobile_register) ? ' mobile-register' : ''}}">
        @yield('main')

        @if(!isset($mobile_register) && !(isset($platform) && $platform == 'mobile') && !isset($new_register) && !(isset($platform) && $platform == 'sync') && !isset($login_v3))
        {!! ViewHelper::copyright() !!}
        @endif
    </div>

    <input type="hidden" id="asset_url" value="{{ ViewHelper::asset_url() }}">
    <input type="hidden" id="product_name" value="{{ config('sso.product_name_' . config('app.locale')) }}">
    <input type="hidden" id="Language" value="{{ config('app.locale') }}">
    <input type="hidden" id="api_url" value="{{ config('app.fangcloud_url') }}">

    <script type="text/javascript" src="{{ ViewHelper::asset_m('js/vendor.js') }}"></script>
    @isset($js)
    <script type="text/javascript" src="{{ ViewHelper::asset_m($js) }}"></script>
    @else
    <script type="text/javascript" src="{{ ViewHelper::asset_m('js/base.js') }}"></script>
    @endisset

    <!-- <script src='https://s.ssl.qhres2.com/pkg/jszt_captcha/captcha.min.1.0.0.js'></script> -->

    <!-- 阿里验证码接入 -->
    <script type="text/javascript" src="https://s3.ssl.qhres2.com/static/ed1b22a79207cc8a.js"></script>

    {!! ViewHelper::baidu_tongji('762d2bc251bef4b42a758268dc7edda3') !!}


    @if(config('sso.product_name_' . config('app.locale')) == '保利云')
        <div style="position: absolute; left: 0; bottom: 10px; width: 100%; text-align: center;">
            Copyright © 2020 北京世博宏业房地产开发有限公司门户网站 <a href="https://beian.miit.gov.cn/#/Integrated/index" target="_blank" class="beian">京ICP备2020046253号-1</a>
        </div>
    @endif

</body>
</html>
