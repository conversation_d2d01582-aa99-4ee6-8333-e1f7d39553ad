@extends('layouts.base')
@section('main')
    @if($platform != 'sync')
    <div class="header">
        <a href="{{config('app.default_page')}}" class="logo">
            @if($user['enterprise'] && $user['enterprise']['id'] != 0 && $user['enterprise']['profile_pic_key'])
                <img class="fangcloud-logo" src="{{ config('app.fangcloud_url') }}enterprises/pic_download?enterprise_id={{ $user['enterprise']['id'] }}&profile_pic_key={{ $user['enterprise']['profile_pic_key'] }}" alt="{{ __('view.FANGCLOUD') }}">
            @elseif(ViewHelper::isFangcloud())
                <span class="default-logo"></span>
            @else
                <img class="fangcloud-logo" src="{{ ViewHelper::logoURI('web.logo.w232') }}" alt="{{ __('view.FANGCLOUD') }}">
            @endif
        </a>
    </div>
    <div class="side-nav">
        <ul class="nav-list">
            <li class="nav-item">
                <a href="{{ config('app.fangcloud_url') }}user_settings/index/account" data-scenario="account">
                <i class="iconfont icon-own edit"></i>
                {{ __('view.USER_SETTINGS' )}}</a>
            </li>
            <li class="nav-item{{Request::path() === 'login_settings' ? ' selected' : ''}}">
                <a href="{{ route('login_settings') }}" data-scenario="login">
                <i class="iconfont icon-encrypt edit"></i>
                {{ __('view.LOGIN_SETTINGS' )}}</a>
            </li>
            <!-- <li class="nav-item{{ Request::path() === 'security_settings' ? ' selected' : ''}}">
                <a href="/security_settings" data-scenario="security">
                <i class="iconfont icon-security edit"></i>
                {{ __('view.SECURITY_SETTINGS' )}}</a>
            </li> -->
            <li class="nav-item">
                <a href="{{ config('app.fangcloud_url') }}user_settings/index/oauth" data-scenario="oauth">
                <i class="iconfont icon-oauth edit"></i>
                {{ __('view.SETTINGS_OAUTH' )}}</a>
            </li>
        </ul>
    </div>
    @endif
    <div class="container-main{{ $platform == 'sync' ? ' sync-container' : ''}}">
        @yield('content')
    </div>
    <script type="text/javascript" src="https://at.alicdn.com/t/font_414128_dpyixg5zlm6.js"></script>
@endsection('main')