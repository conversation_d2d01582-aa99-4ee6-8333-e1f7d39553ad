<?php

use App\Constants\ErrorCode;

return [

    ErrorCode::REMOTE_SERVICE_EXCEPTION => 'System is busy, please try again later.',
    ErrorCode::PERMISSION_DENIED => 'Permission denied. Cannot complete this operation',
    ErrorCode::CSRF_TOKEN_INVALID => 'Current page has expired, please refresh',

    ErrorCode::ENTERPRISE_NAME_TOO_LONG => 'Enterprise name is too long',
    ErrorCode::USER_NAME_TOO_LONG => 'Name is too long',
    ErrorCode::INVALID_EMAIL => 'Please enter a valid email',
    ErrorCode::INVALID_PHONE => 'Please enter valid phone number',
    ErrorCode::INVALID_IDENTIFIER => 'Please enter valid account',
    ErrorCode::INVALID_TEXT => "Contains illegal content. If you have any questions, please contact customer service",
    ErrorCode::INVALID_PASSWORD => 'Password must be 8-32 characters, containing letters and numbers.',
    ErrorCode::PASSWORD_NOT_MATCH_ENTERPRISE_SETTING => 'Password doesn\'t meet enterprise\'s requirement',


    ErrorCode::INVALID_ENTERPRISE_NAME => 'Enterprise (team) name cannot contain characters like \ / ? : * " > < | ^ $ and the end cannot be "."',
    ErrorCode::INVALID_USER_NAME => 'Name cannot contain characters like \ / ? : * " > < | ^ $ and the end cannot be "."',

    ErrorCode::ACCOUNT_NOT_FOUND => 'Account doesn\'t exist',

    ErrorCode::LOGIN_PASSWORD_INVALID => 'Invalid account or password',
    ErrorCode::SMS_CAPTCHA_INVALID => 'Incorrect verification code or it has expired',
    ErrorCode::PIC_CAPTCHA_REQUIRED => 'captcha required',
    ErrorCode::PIC_CAPTCHA_INVALID => 'Incorrect captcha code or it has expired',
    ErrorCode::EMAIL_CAPTCHA_INVALID => 'Incorrect verification code or it has expired',
    ErrorCode::PASSWORD_ERROR_FROZEN => 'Password error 5 times, account has been locked, please try again in 15 minutes',
    ErrorCode::LOGIN_PASSWORD_INVALID_BY_TIMES => 'Password input error, you still have :count chances',
    ErrorCode::BINDED_THIRD_ACCOUNT => 'This third-party account number has been linked to another account. Please use the other account, or unlink the third-party account and try again.',
    ErrorCode::UPDATE_EMAIL_EXCEED_LIMIT => '修改邮箱已达到当日最大修改次数限制，请明日重试。',
    ErrorCode::REGISTER_EXCEED_LIMIT => '注册过于频繁，请明日重试。',
    ErrorCode::USER_NOT_IN_LOCAL_ENTERPRISE => 'The account does not belong to the current enterprise and cannot log in.',

    ErrorCode::PHONE_ALREADY_OCCUPIED => 'The phone number has been occupied. Please try another one or contact customer service team.',
    ErrorCode::EMAIL_ALREADY_OCCUPIED => 'The email has been occupied. Please try another one or contact customer service team.',
    ErrorCode::PHONE_ALREADY_OCCUPIED_WHEN_VERIFY => 'This phone number has been used by another account number. Please change it.',
    ErrorCode::EMAIL_ALREADY_OCCUPIED_WHEN_VERIFY => 'This email address has been linked to another account. Please change it.',
    ErrorCode::PHONE_UNBIND_WHEN_UPDATE_EMAIL => 'You can’t change the email address for now. Please try again after successful linking to your phone number.',
    ErrorCode::PHONE_UNBIND_WHEN_UNBIND_EMAIL => 'You can’t change the email address for now. Please try again after successful linking to your phone number.',
    ErrorCode::PHONE_NOT_CHANGED => 'Please enter a mobile phone number different from the linked phone number',
    ErrorCode::EMAIL_NOT_CHANGED => 'Please enter an email address different from the linked email address',
    ErrorCode::CHANNEL_COUPON_TICKET_ALREADY_USED => 'Coupon already used',
    ErrorCode::CHANNEL_COUPON_TICKET_ALREADY_EXPIRED => 'Coupon already expired or not exist',
    ErrorCode::CHANNEL_COUPON_TICKET_ID_NOT_EXIST => 'Coupon not exist',

    ErrorCode::LOGIN_UNVERIFIED => 'Log in unverified',
    ErrorCode::CREDENTIAL_EMAIL_UNVERIFIED => 'Your email address hasn\'t been verified',
    ErrorCode::CREDENTIAL_PHONE_UNVERIFIED => 'Your phone number hasn\'t been verified',
    ErrorCode::V1_LOGIN_NOT_AllOWED => 'Please log in with the old APP client',
    ErrorCode::V1_LOGIN_NOT_AllOWED_FOR_THIRD => 'V1 users are not supported to login with a third-party account',
    ErrorCode::V1_ACTIVATE_NOT_AllOWED => 'FangCloud of your company has not been upgraded to the new version. Please activate your account on the website.',

    ErrorCode::CREDENTIAL_VERIFICATION_CODE_INVALID => 'The link has expired',
    ErrorCode::CREDENTIAL_NOT_FOUND => '凭据不存在',
    ErrorCode::CREDENTIAL_NOT_NEED_VALIDATED => '凭据已验证',

    ErrorCode::FORGOT_PASSWORD_INFO_EXPIRED => 'Current page has expired, please refresh',
    ErrorCode::FORGOT_PASSWORD_CODE_INVALID => 'The link has expired, please reset password again.',

    ErrorCode::DLP_CLIENT_COUNT_EXCEED => 'The number of device licenses is full, and the client cannot be logged in temporarily. Please contact the administrator.',

    ErrorCode::PASSWORD_NOT_CONFIRMED => 'Please enter the same password',
    ErrorCode::PASSWORD_INCORRECT => 'Incorrect account or password',
    ErrorCode::PASSWORD_VERIFY_STATUS_EXPIRED => '状态已过期，请重试',
    ErrorCode::OLD_PASSWORD_INCORRECT => '旧密码错误',
    ErrorCode::PASSWORD_CHECK_FAILED => 'Incorrect password',

    ErrorCode::ACTIVATION_CODE_INVALID => 'The link has expired or activated',
    ErrorCode::ACTIVATION_INFO_EXPIRED => 'Current page has expired, please refresh',
    ErrorCode::CREDENTIAL_TO_BE_ACTIVATED_NOT_FOUND => 'The mobile phone number has not been invited, please contact the administrator.',

    ErrorCode::QR_TOKEN_INVALID => 'The QR code has become invalid. Please refresh the QR code and scan again',

    ErrorCode::USER_HAS_BEEN_FROZEN => 'Your account has been frozen, please contact your administrator',
    ErrorCode::ENTERPRISE_HAS_BEEN_FROZEN => 'Your account has been frozen, please contact your administrator',
    ErrorCode::ENTERPRISE_HAS_BEEN_EXPIRED => 'The package of your company has expired, please contact the administrator for renewal.',
    ErrorCode::ENTERPRISE_HAS_BEEN_EXPIRED_ADMIN => 'The package of your company has expired, please log in to the website to renew.',
    ErrorCode::ENTERPRISE_HAS_BEEN_FORBIDDEN_LOGIN => 'Your company has been forbidden login, please contact your administrator',
    ErrorCode::USER_LOGIN_HAS_BEEN_DISABLED => 'Your account has been forbidden login, please contact your administrator',
    ErrorCode::ENTERPRISE_HAS_BEEN_EXPIRED_NORMAL_USER => 'The package of your company has expired, please contact the administrator for renewal.',

    ErrorCode::OAUTH_INVALID_STATE => 'Current page has expired, please refresh',

    ErrorCode::TWO_STEP_SMS_LIMIT => 'Too many times, please try again 10 minutes later',
    ErrorCode::TWO_STEP_VERIFY_FAILED => 'Incorrect or expired verification code',
    ErrorCode::TWO_STEP_FORCE_TWO_STEP => 'Please upgrade to the latest version or goto the website for second verification',
    ErrorCode::TWO_STEP_CANNOT_CLOSE => "The administrator has enabled 'Two-step verification of enterprise members', and you can't disable Two-step verification by yourself.",
    ErrorCode::ALREADY_BIND_DINGTALK_ACCOUNT => 'This account has been linked to another DingTalk account',
    ErrorCode::ALREADY_BIND_WECHAT_ACCOUNT => 'This account has been linked to another Enterprise WeChat account',
    ErrorCode::ALREADY_BIND_360_ACCOUNT => 'This account has been linked to another 360 account',

    ErrorCode::LOGIN_DENIED_FOR_EMPTY_ENTERPRISE => 'As you\'re using Alibaba Cloud Plan,please go to the web for the first time,after improving the information you can use our service normally in all ends',

    ErrorCode::SECURITY_LOGIN_EMPTY_SN_CODE => 'The administrator has turned on the device and terminal login restrictions. It needs to be upgraded to the latest client to use normally',
    ErrorCode::SECURITY_LOGIN_FORBIDDEN_IP => 'The current login IP is not allowed. If you have any questions, please contact the administrator',
    ErrorCode::SECURITY_LOGIN_FORBIDDEN_WEB => 'The administrator has opened the ban on login through the web version,',
    ErrorCode::SECURITY_LOGIN_FORBIDDEN_SYNC => 'The login device is not allowed. If you have any questions, please contact the administrator',
    ErrorCode::SECURITY_LOGIN_FORBIDDEN_MOBILE => 'The login device is not allowed. If you have any questions, please contact the administrator',

    ErrorCode::FORGOT_PASSWORD_BY_PHONE_SIGN_ERROR => 'Forgot password verity error',
    ErrorCode::TWO_STEP_VERITY_SIGN_ERROR => 'two step verify error',
    ErrorCode::SIGNATURE_VERITY_ERROR => 'Check Signature error',
    ErrorCode::UPDATE_PHONE_EXCEED_LIMIT => '修改手机号已达到当日最大修改次数限制，请明日重试。',
];
