<?php

namespace App\Models;

use App\Models\Formatter\AuthTokenFormatter;

class AuthToken extends BaseModel
{
    use AuthTokenFormatter;

    protected $table = 'auth_tokens';

    protected $visible = [
        'auth_token', 'user_id', 'expires_at', 'refresh_token'
    ];

    public static $frontend_object_keys = [
        'auth_token',
        'refresh_token',
        'expires_at',
    ];

    private static $infinite_expires = 0;

    /**
     * 新建并保存一个auth_token
     *
     * @param User $user
     * @param Service $service
     * @return AuthToken
     */
    public static function build(User $user, Service $service): AuthToken
    {
        $attributes = [
            'user_id' => $user->user_id,
            'login_user_id' => $user->id,
            'service_id' => $service->id,
            'auth_token' => self::generateAuthToken(),
            'refresh_token' => self::generateRefreshToken(),
            'expires_at' => self::generateExpiresAt($service),
        ];

        $auth_token = new AuthToken($attributes);
        $auth_token->save();
        return $auth_token;
    }

    /**
     * 根据 api_key 生成一个相应的 auth_token
     *
     * @param User $user
     * @param string $api_key
     * @return AuthToken
     */
    public static function buildFromApiKey(User $user, string $api_key)
    {
        $service = Service::findByApiKey($api_key);
        return self::build($user, $service);
    }

    /**
     * 删除旧 auth_token 并且生成一个新的
     *
     * @param AuthToken $old
     * @return AuthToken
     * @throws \Exception
     */
    public static function reBuild(AuthToken $old): AuthToken
    {
        $new = self::build(User::findbyId($old->user_id), Service::findById($old->service_id));
        $old->delete();
        return $new;
    }

    /**
     * 刷新当前 auth_token。
     * 注意: refresh_token 也会刷新
     */
    public function refresh()
    {
        $this->auth_token = self::generateAuthToken();
        $this->refresh_token = self::generateRefreshToken();
        $this->expires_at = self::generateExpiresAt(Service::findById($this->service_id));
        $this->save();
    }

    /**
     * 判断当前 auth_token 是否有效
     * 注: expires_at 为 0 的 auth_token 永久有效
     * @return bool
     */
    public function isValid()
    {
        return $this->expires_at > time() || $this->expires_at === self::$infinite_expires;
    }

    private static function generateAuthToken()
    {
        return md5(mt_rand() . microtime());
    }

    private static function generateRefreshToken()
    {
        return self::generateAuthToken();
    }

    private static function generateExpiresAt(?Service $service)
    {
        $expires_at = time();
        $expires_at += config('common.auth_token.expires_at');
        
        return $expires_at;
    }

    /**
     * @return \App\Models\User
     */
    public function user()
    {
        return User::findByMainUserId($this->user_id);
    }

    public function isMacDesktop()
    {
        return in_array(
            $this->service_id,
            explode(',', config('common.service.mac_sync_client_service_ids'))
        );
    }

    public function isWinDesktop()
    {
        return in_array(
            $this->service_id,
            explode(',', config('common.service.win_sync_client_service_ids'))
        );
    }

    public function isIos()
    {
        return in_array(
            $this->service_id,
            explode(',', config('common.service.ios_app_service_ids'))
        );
    }

    public function isAndroid()
    {
        return in_array(
            $this->service_id,
            explode(',', config('common.service.android_app_service_ids'))
        );
    }

    public function isMobile()
    {
        return in_array(
            $this->service_id,
            explode(',', config('common.service.mobile_app_service_ids'))
        );
    }

    public function isSync()
    {
        return in_array(
            $this->service_id,
            explode(',', config('common.service.sync_client_service_ids'))
        );
    }

    public static function findByAuthToken(string $auth_token): AuthToken
    {
        return self::where('auth_token', $auth_token)->firstOrFail();
    }

    public static function findByAuthTokenGently(string $auth_token): ?AuthToken
    {
        return self::where('auth_token', $auth_token)->first();
    }

    public static function findByRefreshToken(string $refresh_token): AuthToken
    {
        return self::where('refresh_token', $refresh_token)->firstOrFail();
    }

    public static function findCountByServiceIds(int $user_id, array $service_ids = [])
    {
        $handler = self::where('user_id', $user_id);
        if ($service_ids) {
            $handler->whereIn('service_id', $service_ids);
        }
        return $handler->count();
    }
}