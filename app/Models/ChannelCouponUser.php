<?php

namespace App\Models;


use App\Models\Formatter\ChannelCouponUserFormatter;

class <PERSON><PERSON><PERSON>ponUser extends BaseModel
{
    use ChannelCouponUserFormatter;

    protected $table = 'channel_coupon_user';

    protected $visible = [
        'id', 'channel_coupon_id', 'enterprise_id', 'ticket_id', 'created', 'updated'
    ];

    public static function findByTicketId(string $ticket_id): ?ChannelCouponUser
    {
        return self::where('ticket_id', $ticket_id)->first();
    }

    /// ---------------------------------------------------------------
    /// End of proxy functions of eloquent
    /// ---------------------------------------------------------------

}