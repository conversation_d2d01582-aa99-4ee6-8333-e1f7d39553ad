<?php

namespace App\Models;

use App\Constants\ErrorCode;
use App\Exceptions\ExternalException;
use App\Exceptions\InternalException;
use App\Models\Formatter\CredentialFormatter;
use App\Util\CommonHelper;
use App\Remote\User as RemoteUser;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Str;

/**
 * Class Credential
 * @package App\Models
 *
 * @property int id
 * @property int login_user_id
 * @property string identifier
 * @property string ext_identifier
 * @property int type
 * @property int status
 * @property string activation_code
 * @property int activation_expires_at
 * @property int settings
 * @property string value_box
 * @property int created
 * @property int updated
 */
class Credential extends BaseModel
{
    use BitmaskTrait, ValueBoxTrait, CredentialFormatter;

    protected $table = 'credentials';

    const STATUS_VALID = 0;
    const STATUS_TO_BE_VALIDATED = 2;
    const STATUS_VIRTUAL_INVALID = -1;

    const TYPE_EMAIL = 1;
    const TYPE_PHONE = 2;
    const TYPE_WECHAT = 3;
    const TYPE_DINGTALK = 4;
    const TYPE_QIHOO360 = 5;
    const TYPE_360YUNPAN = 6;

    const EMAIL = 'email';
    const PHONE = 'phone';

    public static $public_login_types = [
        self::TYPE_EMAIL,
        self::TYPE_PHONE,
    ];

    public static $third_login_types = [
        self::TYPE_WECHAT,
        self::TYPE_DINGTALK,
        self::TYPE_QIHOO360,
    ];

    protected static $bitmask_fields = [
        'settings' => [
        ],
    ];

    public const VALUE_VERIFIED_AT = 'verified_at';
    public const VALUE_WECHAT = 'wechat';
    public const VALUE_DINGTALK = 'dingtalk';
    public const VALUE_QIHOO360 = 'qihoo360';

    protected static $value_box_fields = [
        'value_box' => [
            self::VALUE_VERIFIED_AT,
            self::VALUE_DINGTALK,
            self::VALUE_WECHAT,
            self::VALUE_QIHOO360,
        ],
    ];

    public function getUser() : ?User
    {
        return User::find($this->login_user_id);
    }

    public static function checkCredentialExistence($identifier, $type=null, $ext_identifier='') : ?Credential
    {
        $credential = static::getUniqueCredentialByIdentifier($identifier, $type, $ext_identifier);
        if($credential) {
            return $credential;
        }
        $origin_identifier = $identifier;
        // TODO: 兼容v1
        if($type == self::TYPE_PHONE) {
            if($ext_identifier){
                $identifier = CommonHelper::concatCountryCodeAndPhone($ext_identifier, $identifier);
            }
        }
        $check_result = RemoteUser::checkIdentifierExistence($identifier);
        $fake_credential = null;
        if(isset($check_result['user_id'])) {
            $fake_credential = new Credential();
            $fake_credential->login_user_id = -1;
            $fake_credential->identifier = $origin_identifier;
            $fake_credential->ext_identifier = $ext_identifier;
        }
        return $fake_credential;
    }

    public static function getUniqueCredentialByIdentifier($identifier, $type=null, $ext_identifier='') : ?Credential
    {
        if(!$type) {
            if(CommonHelper::isValidInternationalPhone($identifier)) {
                list($country_code, $phone) = CommonHelper::extractCountryCodeAndPhone($identifier);
                $credentials = Credential::where([
                    'identifier' => $phone,
                    'ext_identifier' => $country_code,
                    'type' => Credential::TYPE_PHONE])->get();
            }
            else {
                $credentials = Credential::where([
                    'identifier' => $identifier,
                    'ext_identifier' => ''])->whereIn('type', Credential::$public_login_types)->get();
            }
        }
        else {
            if(!$ext_identifier && $type == self::TYPE_PHONE
                && CommonHelper::isValidInternationalPhone($identifier)){
                list($ext_identifier, $identifier) = CommonHelper::extractCountryCodeAndPhone($identifier);
            }
            $credentials = Credential::where([
                'identifier' => $identifier,
                'ext_identifier' => $ext_identifier,
                'type' => $type])->get();
        }
        if($credentials->count() > 1) {
            throw new InternalException('more than one result found');
        }
        return $credentials->first();
    }

    public static function getCredentialByActivationCode($code) : ?Credential
    {
        $credential = Credential::where([
            'activation_code' => $code,
        ])->first();
        return $credential;

    }

    public static function getUserCredentials($user_id, $types) : Collection
    {
        $credentials = Credential::where([
            'login_user_id' => $user_id,
        ])->whereIn('type', $types)->get();
        return $credentials;
    }

    public static function getUserCredential($user_id, $type) : ?Credential
    {
        $credential = Credential::where([
            'login_user_id' => $user_id,
            'type' => $type
        ])->first();
        return $credential;
    }

    public static function getUserCredentialsExceptType($user_id, $type) : ?Collection
    {
        $credentials = Credential::where([
            'login_user_id' => $user_id,
            ['type', '!=', $type],
        ])->get();
        return $credentials;
    }

    public static function getUsersCredentials($user_ids)// : Collection
    {
        $credentials = Credential::whereIn(
            'login_user_id', $user_ids
        )->get();
        return $credentials;
    }

    public function regenerateActivationCode()
    {
        if($this->isValid()) {
            return;
        }
        $this->activation_code = strtolower(Str::random(32));
        $this->activation_expires_at = time() + config('auth.active_expire');
        $this->save();
    }

    public function renewExpiresAt()
    {
        if($this->isValid()) {
            return;
        }
        $this->activation_expires_at = time() + config('auth.active_expire');
        $this->save();
    }

    public function isValid() :bool
    {
        return $this->status == self::STATUS_VALID;
    }

    public static function getCredentialsByIdentifiers(array $identifiers, $type = null) : array
    {
        $credentials_indexed_by_identifier = [];
        if(!$type || $type == self::TYPE_PHONE) {
            foreach ($identifiers as $key => $identifier) {
                if(CommonHelper::isValidInternationalPhone($identifier)) {
                    unset($identifiers[$key]);
                    list($country_code, $phone) = CommonHelper::extractCountryCodeAndPhone($identifier);
                    $credential = Credential::where([
                        'identifier' => $phone,
                        'ext_identifier' => $country_code,
                        'type' => Credential::TYPE_PHONE])->first();
                    if($credential) {
                        $credentials_indexed_by_identifier[$identifier] = $credential;
                    }
                }
            }
        }
        $credentials = Credential::whereIn('identifier', $identifiers)
                            ->where(['ext_identifier' => ''])
                            ->whereIn('type', Credential::$public_login_types)
                            ->get();
        foreach ($credentials as $credential) {
            $credentials_indexed_by_identifier[$credential->getIdentifier()] = $credential;
        }
        return $credentials_indexed_by_identifier;
    }

    public static function getThirdCredentialsByIdentifiers(array $identifiers, $type = null) : array
    {
        $credentials_indexed_by_identifier = [];
        $credentials = Credential::whereIn('identifier', $identifiers)
            ->where(['ext_identifier' => ''])
            ->whereIn('type', Credential::$third_login_types)
            ->get();
        foreach ($credentials as $credential) {
            $credentials_indexed_by_identifier[$credential->getIdentifier()] = $credential;
        }
        return $credentials_indexed_by_identifier;
    }

    public function getIdentifier() : string
    {
        $identifier = $this->identifier;
        if($this->type == self::TYPE_PHONE) {
            if($this->ext_identifier){
                $identifier = CommonHelper::concatCountryCodeAndPhone($this->ext_identifier, $identifier);
            }
        }
        return $identifier;
    }

    public function getValidationUrl() : string
    {
        return $this->isValid() ? '' : route('credential.validation', ['code' => $this->activation_code]);
    }

    public function getActivationUrl() : string
    {
        return $this->isValid() ? '' : route('activation', ['code' => $this->activation_code]);
    }

    public function isTypeEmail() : bool
    {
        return $this->type == self::TYPE_EMAIL;
    }

    public function isTypePhone() : bool
    {
        return $this->type == self::TYPE_PHONE;
    }

    public static function deleteByUser(int $user_id) : void
    {
        static::where('login_user_id', $user_id)->delete();
    }

    public static function generateUnverifiedFakeCredential($user_id, $identifier, $type, $ext_identifier = '')
    {
        return static::generate($user_id, $identifier, $type, Credential::STATUS_VIRTUAL_INVALID, $ext_identifier);
    }

    public static function generate($user_id, $identifier, $type = null, $status = Credential::STATUS_VALID, $ext_identifier = '')
    {
        if($type && !$ext_identifier) {
            if($type == Credential::TYPE_PHONE) {
                list($ext_identifier, $identifier) = CommonHelper::extractCountryCodeAndPhone($identifier);
            }
        }
        elseif(!$type) {
            $ext_identifier = '';
            if(CommonHelper::isValidEmail($identifier)) {
                $type = Credential::TYPE_EMAIL;
            }
            elseif(CommonHelper::isValidPhoneNumber($identifier)) {
                $type = Credential::TYPE_PHONE;
                list($ext_identifier, $identifier) = CommonHelper::extractCountryCodeAndPhone($identifier);
            }
            elseif (CommonHelper::isValidWechat($identifier)) {
                $type = Credential::TYPE_WECHAT;
            }
        }
        $credential = new Credential([
            'login_user_id' => $user_id,
            'identifier' => $identifier,
            'ext_identifier' => $ext_identifier,
            'type' => $type,
            'status' => $status,
        ]);
        return $credential;
    }

    public static function updateThirdUsersCredential($old_identifier, $new_identifier, $types = null) : ?Credential
    {
        if(sizeof($types) == 0)
        {
            $types = Credential::$third_login_types;
        }
        $check_new_credential = Credential::where(['identifier' => $new_identifier])
            ->where(['ext_identifier' => ''])
            ->whereIn('type', $types)
            ->first();
        if($check_new_credential)
        {
            throw new ExternalException(ErrorCode::IDENTIFIER_ALREADY_OCCUPIED);
        }
        $credential = Credential::where(['identifier' => $old_identifier])
            ->where(['ext_identifier' => ''])
            ->whereIn('type', $types)
            ->first();
        if (!$credential)
        {
            throw new ExternalException(ErrorCode::CREDENTIAL_NOT_FOUND);
        }
        $credential->identifier = $new_identifier;
        $credential->save();
        $new_credential = Credential::where(['identifier' => $new_identifier])
            ->where(['ext_identifier' => ''])
            ->whereIn('type', $types)
            ->first();
        return $new_credential;
    }
}
