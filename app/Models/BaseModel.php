<?php
/**
 * Created by PhpStorm.
 * User: luther
 * Date: 25/08/2017
 * Time: 10:40
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class BaseModel
 * @package App\Models
 * @mixin \Eloquent
 */
class BaseModel extends Model
{
    /**
     * 默认所有字段都不能批量更新;
     * 使用白名单机制而不是黑名单, 不应该再次定义 $guarded
     * @var array $fillable
     */
    protected $guarded = [];

    /**
     * 默认关闭时间戳的自动更新
     * @var bool $timestamps
     */
    public $timestamps = true;

    protected $dateFormat = 'U';

    protected $primaryKey = 'id';

    protected $dates = [
        self::CREATED_AT,
        self::UPDATED_AT,
        self::DELETED_AT
    ];

    /**
     * 默认的 created updated deleted 数据库字段
     */
    const CREATED_AT = 'created';

    const UPDATED_AT = 'updated';

    const DELETED_AT = 'deleted';

    /**
     * 默认的场景值。
     * 该值用来标记 bulk format 时 model 之间的对象关联应该如何 format
     * @var string
     */
    protected $scene = 'normal';

    /**
     * @param array $ids
     * @return Collection
     */
    public static function findByIds(array $ids) : Collection
    {
        return static::whereIn('id', $ids)->get();
    }

    /**
     * @param int $id
     * @param array $columns
     * @return \Illuminate\Database\Eloquent\Collection|Model|null|static|static[]
     */
    public static function findById(int $id, $columns = ['*'])
    {
        return self::find($id, $columns);
    }
}