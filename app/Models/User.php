<?php

namespace App\Models;

use App\Constants\Common;
use App\Library\OAuth\Constants;
use App\Library\TwoStep;
use App\Models\Formatter\UserFormatter;
use App\Util\CommonHelper;
use Illuminate\Notifications\Notifiable;
use Illuminate\Contracts\Auth\Authenticatable as AuthenticatableContract;
use Illuminate\Auth\Authenticatable;
use App\Remote\User as RemoteUser;

/**
 * Class User
 * @package App\Models
 *
 * @property RemoteUser remote_user
 */
class User extends BaseModel implements AuthenticatableContract
{
    use Notifiable, Authenticatable;

    use BitmaskTrait, ValueBoxTrait, UserFormatter;

    protected $table = 'login_users';

    protected $remote_user;

    protected $hidden = [
        'value_box',
        'password',
        'remember_token',
    ];

    /**
     * TODO
     * @var array
     */
    protected $appends = [
//        'two_step_method',
//        'two_step_identifier',
//        'two_step_set_time',
//        'unverified_email',
//        'unverified_phone',
    ];

    protected static $bitmask_fields = array(
        'settings' => array(
            'password_require_init' => 0,
            'is_two_step_enabled' => 1,
            "douyin_register_un_active" => 2
        ),
    );

    /**
     * attention !
     * 这里的字段默认在序列化的时候拿不到。
     * 如果需要拿到请加载 $appends 数组中
     * 另外还需要增加一个访问器
     * https://d.laravel-china.org/docs/5.5/eloquent-serialization#appending-values-to-json
     *
     * @var array $value_box_fields
     */
    protected static $value_box_fields = array(
        'value_box' => array(
            'two_step_method',
            'two_step_secret',
            'two_step_identifier',
            'two_step_set_time',
            'unverified_email',
            'unverified_phone',
        ),
    );

    public function getUserId()
    {
        return $this->user_id;
    }

    public function getRemoteUser($fetch_info = true) : RemoteUser
    {
        if(!$this->remote_user) {
            $this->remote_user = new RemoteUser(['id' => $this->user_id]);
            if ($fetch_info) {
                $this->remote_user->getInfo();
            }
        }
        return $this->remote_user;
    }
    public function isActive()
    {
        return (bool) $this->activated;
    }

    public function activate($time = null)
    {
        $time = $time ?: time();
        $this->activated = $time;
    }

    public function getName()
    {
        return $this->getRemoteUser()->getName();
    }

    public function setPassword($password)
    {
        $this->password = bcrypt($password);
    }

    public static function findByMainUserId($main_user_id) : ?User
    {
        return static::where('user_id', $main_user_id)->first();
    }

    public static function findByMainUserIds(array $main_user_ids) : iterable
    {
        if(!$main_user_ids) {
            return [];
        }
        return static::whereIn('user_id', $main_user_ids)->get();
    }

    public function authenticate($password) : bool
    {
        return app('hash')->check($password, $this->getAuthPassword());
    }

    public function isPasswordUnset() : bool
    {
        return !$this->password;
    }

    public function getDlpEnterpriseMaxDeviceCount()
    {
        $enterprise = $this->getRemoteUser()->getEnterprise();
        return $enterprise->getDlpEnterpriseMaxDeviceCount();
    }

    public function getMaxDeviceOnlineCount()
    {
        $enterprise = $this->getRemoteUser()->getEnterprise();
        return $enterprise->getMaxDeviceOnlineCount();
    }

    /**
     * @throws \Exception
     */
    public function deleteAll() : void
    {
        Credential::deleteByUser($this->id);
        PasswordReset::deleteByUser($this->id);
        $this->delete();
    }

    public function getUnverifiedFakeCredentials()
    {
        $credentials = [];
        if(($email = $this->getAdditionalValue('unverified_email'))) {
            $credential = Credential::generateUnverifiedFakeCredential($this->id, $email, Credential::TYPE_EMAIL);
            $credentials[] = $credential;
        }
        if(($phone = $this->getAdditionalValue('unverified_phone'))) {
            list($ext_identifier, $identifier) = CommonHelper::extractCountryCodeAndPhone($phone);
            $credential = Credential::generateUnverifiedFakeCredential($this->id, $identifier, Credential::TYPE_PHONE, $ext_identifier);
            $credentials[] = $credential;
        }
        return $credentials;
    }

    public function __get($key)
    {
        if(in_array($key, static::$remote_fields)){
            return $this->getRemoteUser()->$key;
        }

        return parent::__get($key);
    }

    public function verifyTwoStep($code)
    {
        $method = $this->getTwoStepMethodAttr();
        $secret = $this->getTwoStepSecretAttr();

        return (new TwoStep(['method' => $method, 'secret' => $secret]))->verify($code);
    }

    public const TWO_STEP_STATUS_NEED = -1;
    public const TWO_STEP_STATUS_NONE = 0;
    public const TWO_STEP_STATUS_OK = 1;

    public function getTwoStepStatus()
    {
        $enterprise = $this->getRemoteUser()->enterprise;

        $enterprise_setting = $enterprise->is_two_step_enabled;
        $user_setting = $this->getIsTwoStepEnabledAttr();

        if ($enterprise_setting) {
            if ($user_setting) {
                return self::TWO_STEP_STATUS_OK;
            } else {
                return self::TWO_STEP_STATUS_NEED;
            }
        } else {
            if ($user_setting) {
                return self::TWO_STEP_STATUS_OK;
            } else {
                return self::TWO_STEP_STATUS_NONE;
            }
        }
    }

    public function closeTwoStep()
    {
        $this->unsetIsTwoStepEnabledAttr();
        $this->unsetTwoStepIdentifierAttr();
        $this->unsetTwoStepMethodAttr();
        $this->unsetTwoStepSecretAttr();
        $this->unsetTwoStepSetTimeAttr();
    }

    public function getTwoStep($masked_phone = true)
    {
        $method = $this->getTwoStepMethodAttr();
        $identity = null;
        if ($method == TwoStep::METHOD_SMS) {
            $identity = $this->getTwoStepIdentifierAttr($masked_phone);
        }

        return ['method' => $method, 'identity' => $identity];
    }

    public function getLang($need_default = true)
    {
        $remote_user = $this->getRemoteUser();
        return $remote_user->lang ?: ($need_default ? Common::LANGUAGE_ZH_CN : null);
    }

}
