<?php

namespace App\Models;

use App\Constants\Platform;
use App\Models\Formatter\ServiceFormatter;

class Service extends BaseModel
{
    use ServiceFormatter;

    protected $table = 'services';

    protected $visible = [
        'id', 'name',
    ];

    public function isIos(): bool
    {
        return $this->platform_id === Platform::PLATFORM_ID_IOS;
    }

    public function isAndroid(): bool
    {
        return $this->platform_id === Platform::PLATFORM_ID_ANDROID;
    }

    public function isMobile(): bool
    {
        return $this->isIos() || $this->isAndroid();
    }

    public function isSync(): bool
    {
        return $this->platform_id === Platform::PLATFORM_ID_SYNC_WIN
            || $this->platform_id === Platform::PLATFORM_ID_SYNC_MAC
            || $this->platform_id === Platform::PLATFORM_ID_SYNC_WIN_XP;
    }

    public function isMacDesktop(): bool
    {
        return $this->platform_id === Platform::PLATFORM_ID_SYNC_MAC;
    }

    public function isWindowsDesktop(): bool
    {
        return $this->platform_id === Platform::PLATFORM_ID_SYNC_WIN
            || $this->platform_id === Platform::PLATFORM_ID_SYNC_WIN_XP;
    }

    /// ---------------------------------------------------------------
    /// Begin of proxy functions of eloquent
    /// ---------------------------------------------------------------

    /**
     * @param string $api_key
     * @return Service
     */
    public static function findByApiKey(string $api_key): Service
    {
        return self::where('api_key', $api_key)->firstOrFail();
    }

    /// ---------------------------------------------------------------
    /// End of proxy functions of eloquent
    /// ---------------------------------------------------------------

}