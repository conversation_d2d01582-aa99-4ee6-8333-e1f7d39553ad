<?php

namespace App\Models;
use App\Constants\Plan;
use Illuminate\Database\Eloquent\Collection;

/**
 * Class EnterprisePlanTraffic
 * @package App\Models
 * @property  int id
 * @property  int enterprise_id
 * @property  int created
 * @property  int updated
 * @property  int deleted
 * @property  int expires_at
 * @property  long max_upload_traffic
 * @property  long max_download_traffic
 * @property  long upload_traffic_used
 * @property  long download_traffic_used
 * @property  int external_collab_folder_used
 * @property  int plan_id
 */
class EnterprisePlanTraffic extends BaseModel
{
    protected $table = 'enterprise_plan_traffic';

    protected $fillable = [
        'id', 'enterprise_id', 'created', 'updated', 'deleted', 'expires_at','max_upload_traffic', 'max_download_traffic',
        'upload_traffic_used', 'download_traffic_used','external_collab_folder_used','plan_id'
    ];

    public static function enterprise_plan_register_count(): int
    {
        $current_month = date("Ym");
        $handler = self::where('plan_id', Plan::PLAN_TYPE_FREE_2023)->where('deleted', 0)->where('current_month', $current_month);
        return $handler->count();
    }
}