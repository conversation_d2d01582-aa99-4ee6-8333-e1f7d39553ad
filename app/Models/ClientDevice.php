<?php

namespace App\Models;

use App\Events\DeviceLoggedOut;
use App\Models\Formatter\ClientDeviceFormatter;
use App\Remote\AuthToken as RemoteAuthToken;
use App\Util\CacheHelper;
use App\Util\Context;
use App\Util\LogHelper;
use App\Util\LogType;

class ClientDevice extends BaseModel
{
    use ClientDeviceFormatter;

    use ValueBoxTrait;

    protected $table = 'client_devices';

    protected $visible = [
        'id', 'ip_address', 'user_id', 'device_type'
    ];

    public static $frontend_object_keys = [
        'id',
        'user_id',
        'ip_address',
        'device_type',
        'device_name',
        'creditable',
        'updated',
        'current',
    ];

    protected static $value_box_fields = [
        "value_box" => [
            "ip_list", // 老字段，目前已不用，换用新字段ip_info
            'ip_info'
        ],
    ];

    public const IOS_APP_NAME  = "Egeio IOS App";
    public const ANDROID_APP_NAME = "Android App";
    public const MAC_DESKTOP_NAME = 'Mac Desktop';
    public const WIN_DESKTOP_NAME = 'Windows Desktop';
    public const UNKNOWN_DEVICE = 'Unknown Device';

    private const IP_TIME_LIST_LIMIT = 5; // 最多保留5个最近登录IP

    // 最多保留500个最近登录IP
    public const IP_LIST_LIMIT = 500;

    public function isDeleted(): bool
    {
        return (int)$this->getDeletedAttr() > 0;
    }

    /**
     * 当前设备是否在线
     *
     * @return bool
     */
    public function isOnline(): bool
    {
        return ($this->isWeb() && (time() - $this->getUpdatedAttr() > config('session.lifetime') * 60)) || $this->logout_time === 0;
    }

    /**
     * 设备是否可信
     *
     * 可信设备持续一段时间，并不是永久
     *
     * @return bool
     */
    public function isCredit(): bool
    {
        return $this->credited_time && (time() - $this->credited_time) < config('common.two_step.creditable_duration');
    }

    public function isWeb(): bool
    {
        return !($this->isMobile() || $this->isSync());
    }

    public function isMobile(): bool
    {
        return in_array($this->device_type, [self::IOS_APP_NAME, self::ANDROID_APP_NAME]);
    }

    public function isSync(): bool
    {
        return in_array($this->device_type, [self::MAC_DESKTOP_NAME, self::WIN_DESKTOP_NAME]);
    }

    /**
     * @throws \Exception
     */
    public function setInvalid()
    {
        $this->setNotCredit();
        $this->deleted = time();
        $this->logout();
    }

    public function setNotCredit()
    {
        $this->credited_time = 0;
    }

    /**
     * @throws \Exception
     */
    public function logout()
    {
        $related_client_devices = $this->getRelatedClientDevice();
        if($related_client_devices) {
            foreach ($related_client_devices as $related_client_device) {
                $related_client_device->logout();
            }
        }
        $this->logout_time = time();
        if ($this->session_id) {
            CacheHelper::clearCachedInfo($this->session_id);
        }
        if ($this->auth_token_id) {
            LogHelper::info(LogType::WebAndServerService, 'client_device_logout_'.$this->user_id.
                ': time:'.time().
                ' id:'.$this->id.
                ' auth_token_id:'.$this->auth_token_id.
                ' device_token:'.$this->device_token.
                ' device_name:'.$this->device_name.
                ' device_type:'.$this->device_type.
                ' real_id:'.$this->real_id);
            RemoteAuthToken::delete($this->auth_token_id);
//            if ($auth_token = AuthToken::findById($this->auth_token_id)) {
//                $auth_token->delete();
//            }
        }
        $this->auth_token_id = null;
        $this->session_id = null;
        if($this->id) {
            $this->save();
        }
        event(new DeviceLoggedOut($this));
    }

    /**
     * 格式如下：
     * {
     *   "ip_info": {
     *        "*******": [time1, time2, time3],
     *        "*******": [time3, time4],
     *        "*******": [time5, time6, time7, time8 ,time9],
     *        ......
     *    }
     * }
     *
     * 保存每个ip的最近5次登录时间，最新的登录时间位于数组的末尾
     * 在 client_device 中的 setter 中，设置ip会自动调用
     *
     * @param $new_ip
     */
    private function addNewIp($new_ip)
    {
        $ips = $this->getAdditionalValue('ip_info');

        $current_time = time();
        if ($ips) {
            $is_new_ip_existed = false;
            foreach ($ips as $ip => &$time_list) {
                if ($ip == $new_ip) {
                    $is_new_ip_existed = true;
                    if (in_array($current_time, array_values($time_list))) {
                        // 同一个请求之内如果多次调用该方法的话，只会记录一次
                        continue;
                    }
                    $time_list[] = $current_time;
                    while (count($time_list) > self::IP_TIME_LIST_LIMIT) {
                        array_shift($time_list);
                    }
                    break;
                }
            }

            if (!$is_new_ip_existed) {
                $ips[$new_ip][] = $current_time;
            }
        } else {
            $ips[$new_ip][] = $current_time;
        }

        $this->setAdditionalValue('ip_info', array_slice($ips, -1 * self::IP_LIST_LIMIT));
    }

    public function getRelatedClientDevice()
    {
        if($this->id){
            return self::findByRealId($this->id);
        }
        else {
            return null;
        }
    }

    /**
     * find single
     *
     * @param int $user_id
     * @param string $device_token
     * @return \Illuminate\Database\Eloquent\Model|static
     */
    public static function findByUserIdDeviceToken(int $user_id, string $device_token)
    {
        return self::where('user_id', $user_id)->where('device_token', $device_token)->first();
    }

    /**
     * select for update
     *
     * @param int $user_id
     * @param string $device_token
     * @return ClientDevice|null
     */
    public static function findByUserIdDeviceTokenWithLock(int $user_id, string $device_token): ?ClientDevice
    {
        $db =  self::where('user_id', $user_id)->where('device_token', $device_token)->lockForUpdate()->get();
        return $db->count() ? $db[0] : null;
    }

    /**
     * @param int $user_id
     * @param bool $only_online
     * @param array $device_type
     * @return \Illuminate\Database\Eloquent\Collection|static[]
     */
    public static function findByUserId(int $user_id, $only_online = true, array $device_type = [])
    {
        $handler = self::where('user_id', $user_id)->where('deleted', 0);
        if ($only_online) {
            $handler->where('logout_time', 0);
        }
        if ($device_type) {
            $handler->whereIn('device_type', $device_type);
        }
        $handler->where('real_id', 0);
        $client_devices = $handler->get();

        if ($only_online && $client_devices) {
            foreach($client_devices as $key => $value) {
                // web 设备的 online 不能简单用 logout time 判断
                if (!$value->isOnline()) {
                    unset($client_devices[$key]);
                }
            }
        }

        return $client_devices;
    }

    /**
     * TODO find single
     * @param int $auth_token_id
     * @return \Illuminate\Database\Eloquent\Model|null|static
     */
    public static function findByAuthToken(int $auth_token_id)
    {
        return self::where('auth_token_id', $auth_token_id)->first();
    }

    public static function findByRealId(int $real_id)
    {
        return self::where('real_id', $real_id)->get();
    }

    public static function setNotCreditableByUserId(int $user_id)
    {
        self::where('user_id', $user_id)
            ->where('credited_time', '>', 0)
            ->update(['credited_time' => 0]);
    }

    public static function logoutUser(int $user_id)
    {
        $client_devices = static::where('user_id', $user_id)->get();
        $auth_token_ids = [];
        foreach ($client_devices as $client_device) {
            if ($client_device->session_id) {
                CacheHelper::clearCachedInfo($client_device->session_id);
            }
            if ($client_device->auth_token_id) {
                $auth_token_ids[] = $client_device->auth_token_id;
            }
        }
        if($auth_token_ids) {
            foreach ($auth_token_ids as $auth_token_id) {
                RemoteAuthToken::delete($auth_token_id);
            }
//            AuthToken::whereIn('id', $auth_token_ids)->delete();
        }
        static::where('user_id', $user_id)
            ->update(['logout_time' => time(), 'auth_token_id' => null, 'session_id' => null]);
    }
}