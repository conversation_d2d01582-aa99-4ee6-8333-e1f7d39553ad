<?php
namespace App\Models;

trait ValueBoxTrait
{
    public function getAdditionalValue($key)
    {
        foreach ( static::$value_box_fields as $db_field => $value_box_key_array ) {
            if (in_array($key, $value_box_key_array) && $this->$db_field) {
                $additional_values = json_decode($this->$db_field, true);
                return $additional_values[$key] ?? null;
            }
        }
        return null;
    }

    public function setAdditionalValue($key, $value)
    {
        foreach ( static::$value_box_fields as $db_field => $value_box_key_array ) {
            if(in_array($key, $value_box_key_array)) {
                if ($this->$db_field) {
                    $additional_values = json_decode($this->$db_field, true);
                } else {
                    $additional_values = [];
                }
                $additional_values[$key] = $value;
                $this->$db_field = json_encode($additional_values, JSON_UNESCAPED_UNICODE);
            }
        }
        return $this;
    }

    public function unsetAdditionalValue($key)
    {
        foreach ( static::$value_box_fields as $db_field => $value_box_key_array ) {
            if(in_array($key, $value_box_key_array)) {
                $additional_values = json_decode($this->$db_field, true);
                if (!$additional_values) {
                    $additional_values = [];
                }
                unset($additional_values[$key]);
                $this->$db_field = json_encode($additional_values, JSON_UNESCAPED_UNICODE);
            }
        }
        return $this;
    }
}
