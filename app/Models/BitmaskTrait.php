<?php

namespace App\Models;

trait BitmaskTrait
{
    protected static $default_bitmask_value = 0;

    public function getBitmaskField($field)
    {
        foreach ( static::$bitmask_fields as $db_field => $bitmask_key_array ) {
            if (array_key_exists($field, $bitmask_key_array)) {
                $db_field_value = $this->$db_field;
                $mask = pow(2, $bitmask_key_array[$field]);
                return (($db_field_value & $mask) > 0);
            }
        }
        return self::$default_bitmask_value;
    }

    public function setBitmaskField($field, $value = true)
    {
        foreach ( static::$bitmask_fields as $db_field => $bitmask_key_array ) {
            if (array_key_exists($field, $bitmask_key_array)) {
                if (empty($value) || ! $value || $value == '0') {
                    $value = 0;
                }
                elseif ($value || $value == '1') {
                    $value = 1;
                }
                $bitmask = $this->$db_field;
                $mask = pow(2, $bitmask_key_array[$field]);
                if ($value == 1)
                    $this->$db_field = $bitmask | $mask;
                if ($value == 0)
                    $this->$db_field = $bitmask & ~ $mask;
            }
        }
        return $this;
    }
}
