<?php 

namespace App\Models\Formatter;

/**
 * Trait ChannelCouponFormatter
 * 
 * @package App\Models\Formatter
 * @property int id
 * @property String channel_code
 * @property int plan_id
 * @property int days
 * @property int expired_at
 * @property int created
 * @property int deleted
 */
 trait ChannelCouponFormatter
 {
    use BaseFormatter;

    /// ----------------------------------------------------
    ///  Begin of getters and setters
    /// ----------------------------------------------------

    /**
     * get id
     * @return int|null
     */
    public function getIdAttr(): ?int
    {
        return $this->id;
    }

    /**
     * get channel_code
     * @return string|null
     */
    public function getChannelCodeAttr(): ?string
    {
        return $this->channel_code;
    }

     /**
      * get plan_id
      * @return int|null
      */
     public function getPlanIdAttr(): ?int
     {
         return $this->plan_id;
     }

    /**
     * get days
     * @return int|null
     */
    public function getDaysAttr(): ?int
    {
        return $this->days;
    }

    /**
     * get expired_at
     * @return int|null
     */
    public function getExpiredAtAttr(): ?int
    {
        return $this->expired_at;
    }

    /**
     * get created
     * @return int|null
     */
    public function getCreatedAttr(): ?int
    {
        return $this->created;
    }

    /**
     * get updated
     * @return int|null
     */
    public function getDeletedAttr(): ?int
    {
        return $this->deleted;
    }

    /// ----------------------------------------------------
    ///  End of getters and setters
    /// ----------------------------------------------------

}