<?php 

namespace App\Models\Formatter;

/**
 * Trait ServiceFormatter
 * 
 * auto generate by php artisan yfy:formatter
 * @package App\Models\Formatter
 * @property int id
 * @property string name
 * @property string api_key
 * @property int disabled
 * @property int settings
 * @property \Illuminate\Support\Carbon created
 * @property \Illuminate\Support\Carbon updated
 * @property int platform_id
 * @property int active_duration
 */
 trait ServiceFormatter
 {
    use BaseFormatter;

    /// ----------------------------------------------------
    ///  Begin of getters and setters
    /// ----------------------------------------------------

    /**
     * get id
     * @return int|null
     */
    public function getIdAttr(): ?int
    {
        return $this->id;
    }

    /**
     * get name
     * @return string|null
     */
    public function getNameAttr(): ?string
    {
        return $this->name;
    }

    /**
     * get api_key
     * @return string|null
     */
    public function getApiKeyAttr(): ?string
    {
        return $this->api_key;
    }

    /**
     * get disabled
     * @return int|null
     */
    public function getDisabledAttr(): ?int
    {
        return $this->disabled;
    }

    /**
     * get settings
     * @return int|null
     */
    public function getSettingsAttr(): ?int
    {
        return $this->settings;
    }

    /**
     * get created
     * @return int|null
     */
    public function getCreatedAttr(): ?int
    {
        $created = $this->created;
        return $created != null ? $created->timestamp : 0;
    }

    /**
     * get updated
     * @return int|null
     */
    public function getUpdatedAttr(): ?int
    {
        $updated = $this->updated;
        return $updated != null ? $updated->timestamp : 0;
    }

    /**
     * get platform_id
     * @return int|null
     */
    public function getPlatformIdAttr(): ?int
    {
        return $this->platform_id;
    }

    /**
     * get active_duration
     * @return int|null
     */
    public function getActiveDurationAttr(): ?int
    {
        return $this->active_duration;
    }

    /// ----------------------------------------------------
    ///  End of getters and setters
    /// ----------------------------------------------------

}