<?php 

namespace App\Models\Formatter;

/**
 * Trait ChannelCouponUserFormatter
 * 
 * @package App\Models\Formatter
 * @property int id
 * @property int channel_coupon_id
 * @property int enterprise_id
 * @property string ticket_id
 * @property int active_duration
 * @property int created
 * @property int updated
 */
 trait ChannelCouponUserFormatter
 {
    use BaseFormatter;

    /// ----------------------------------------------------
    ///  Begin of getters and setters
    /// ----------------------------------------------------

    /**
     * get id
     * @return int|null
     */
    public function getIdAttr(): ?int
    {
        return $this->id;
    }

    /**
     * get channel_coupon_id
     * @return int|null
     */
    public function getChannelCouponIdAttr(): ?int
    {
        return $this->channel_coupon_id;
    }

    /**
     * get enterprise_id
     * @return int|null
     */
    public function getEnterpriseIdAttr(): ?int
    {
        return $this->enterprise_id;
    }

    /**
     * get ticket_id
     * @return string|null
     */
    public function getTicketIdAttr(): ?string
    {
        return $this->ticket_id;
    }

    /**
     * get created
     * @return int|null
     */
    public function getCreatedAttr(): ?int
    {
        return $this->created;
    }

    /**
     * get updated
     * @return int|null
     */
    public function getUpdatedAttr(): ?int
    {
        return $this->updated;
    }

    /// ----------------------------------------------------
    ///  End of getters and setters
    /// ----------------------------------------------------

}