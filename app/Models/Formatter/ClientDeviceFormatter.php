<?php 

namespace App\Models\Formatter;

use App\Util\CommonHelper;
use App\Util\Context;

/**
 * Trait ClientDeviceFormatter
 * 
 * auto generate by php artisan yfy:formatter
 * @package App\Models\Formatter
 * @property string session_id
 * @property int id
 * @property string ip_address
 * @property string device_token
 * @property int auth_token_id
 * @property int real_id
 * @property int user_id
 * @property string device_type
 * @property string device_additional_info
 * @property int credited_time
 * @property int logout_time
 * @property \Illuminate\Support\Carbon created
 * @property \Illuminate\Support\Carbon updated
 * @property \Illuminate\Support\Carbon deleted
 * @property int settings
 * @property string value_box
 * @property string device_name
 */
 trait ClientDeviceFormatter
 {
    use BaseFormatter;

    public function getIpAddressAttr()
    {
        return [
            'ip' => $this->ip_address,
            'location' => CommonHelper::getFormattedIpLocation($this->ip_address)
        ];
    }

    public function getCreditableAttr(): bool
    {
        return $this->isCredit();
    }

    public function getCurrentAttr(): bool
    {
        return $this->device_token === Context::getDeviceToken();
    }

    public function getDeviceNameAttr(): string
    {
        return $this->getDeviceNameFromEgeioInfo() ?: $this->getDeviceTypeAttr();
    }

    public function getDeviceNameAttribute(): string
    {
        return $this->getDeviceNameAttr();
    }

    private function getDeviceNameFromEgeioInfo()
    {
        $egeio_info = $this->device_additional_info;

        $info = json_decode($egeio_info, true);

        if (!$info || !array_key_exists('deviceName', $info)) {
            return null;
        }

        $device_name = urldecode($info['deviceName']);

        if(!json_encode($device_name))
        {
            $device_name = null;
        }
        return $device_name;
    }

    /// ----------------------------------------------------
    ///  Begin of getters and setters
    /// ----------------------------------------------------

    /**
     * get id
     * @return int|null
     */
    public function getIdAttr(): ?int
    {
        return $this->id;
    }

    /**
     * get session_id
     * @return string|null
     */
    public function getSessionIdAttr(): ?string
    {
        return $this->session_id;
    }

    /**
     * get device_token
     * @return string|null
     */
    public function getDeviceTokenAttr(): ?string
    {
        return $this->device_token;
    }

    /**
     * get auth_token_id
     * @return int|null
     */
    public function getAuthTokenIdAttr(): ?int
    {
        return $this->auth_token_id;
    }

    /**
     * get real_id
     * @return int|null
     */
    public function getRealIdAttr(): ?int
    {
        return $this->real_id;
    }

    /**
     * get user_id
     * @return int|null
     */
    public function getUserIdAttr(): ?int
    {
        return $this->user_id;
    }

    /**
     * get device_type
     * @return string|null
     */
    public function getDeviceTypeAttr(): ?string
    {
        return trim($this->device_type) ?: self::UNKNOWN_DEVICE;
    }

    /**
     * get device_additional_info
     * @return string|null
     */
    public function getDeviceAdditionalInfoAttr(): ?string
    {
        return $this->device_additional_info;
    }

    /**
     * get credited_time
     * @return int|null
     */
    public function getCreditedTimeAttr(): ?int
    {
        return $this->credited_time;
    }

    /**
     * get logout_time
     * @return int|null
     */
    public function getLogoutTimeAttr(): ?int
    {
        return $this->logout_time;
    }

    /**
     * get created
     * @return int|null
     */
    public function getCreatedAttr(): ?int
    {
        $created = $this->created;
        return $created != null ? $created->timestamp : 0;
    }

    /**
     * get updated
     * @return int|null
     */
    public function getUpdatedAttr(): ?int
    {
        $updated = $this->updated;
        return $updated != null ? $updated->timestamp : 0;
    }

    /**
     * get deleted
     * @return int|null
     */
    public function getDeletedAttr(): ?int
    {
        $deleted = $this->deleted;
        return $deleted != null ? $deleted->timestamp : 0;
    }

    /**
     * get settings
     * @return int|null
     */
    public function getSettingsAttr(): ?int
    {
        return $this->settings;
    }

    /**
     * get value_box
     * @return string|null
     */
    public function getValueBoxAttr(): ?string
    {
        return $this->value_box;
    }

    /**
     * set ip_address
     * @param string $value
     */
    public function setIpAddressAttribute(string $value)
    {
        $this->attributes['ip_address'] = $value;
        $this->addNewIp($value);
    }

    /// ----------------------------------------------------
    ///  End of getters and setters
    /// ----------------------------------------------------

    /// ----------------------------------------------------
    ///  Begin of value box
    /// ----------------------------------------------------

    /**
     * get ip_list value box
     * @return mixed
     */
    public function getIpListAttr()
    {
        return $this->getAdditionalValue('ip_list');
    }
    
    /**
     * set ip_list value box
     * @param mixed $value
     */
    public function setIpListAttr($value)
    {
        $this->setAdditionalValue('ip_list', $value);
    }

    /**
     * unset ip_list value box
     */
    public function unsetIpListAttr()
    {
        $this->unsetAdditionalValue('ip_list');
    }

    /**
     * get ip_info value box
     * @return mixed
     */
    public function getIpInfoAttr()
    {
        return $this->getAdditionalValue('ip_info');
    }
    
    /**
     * set ip_info value box
     * @param mixed $value
     */
    public function setIpInfoAttr($value)
    {
        $this->setAdditionalValue('ip_info', $value);
    }

    /**
     * unset ip_info value box
     */
    public function unsetIpInfoAttr()
    {
        $this->unsetAdditionalValue('ip_info');
    }


    /// ----------------------------------------------------
    ///  End of value box
    /// ----------------------------------------------------
}