<?php 

namespace App\Models\Formatter;

/**
 * Trait AuthTokenFormatter
 * 
 * auto generate by php artisan yfy:formatter
 * @package App\Models\Formatter
 * @property int login_user_id
 * @property int id
 * @property string auth_token
 * @property int user_id
 * @property int service_id
 * @property int expires_at
 * @property \Illuminate\Support\Carbon created
 * @property \Illuminate\Support\Carbon updated
 * @property string refresh_token
 */
 trait AuthTokenFormatter
 {
    use BaseFormatter;

    /// ----------------------------------------------------
    ///  Begin of getters and setters
    /// ----------------------------------------------------

    /**
     * get login_user_id
     * @return int|null
     */
    public function getLoginUserIdAttr(): ?int
    {
        return $this->login_user_id;
    }

    /**
     * get id
     * @return int|null
     */
    public function getIdAttr(): ?int
    {
        return $this->id;
    }

    /**
     * get auth_token
     * @return string|null
     */
    public function getAuthTokenAttr(): ?string
    {
        return $this->auth_token;
    }

    /**
     * get user_id
     * @return int|null
     */
    public function getUserIdAttr(): ?int
    {
        return $this->user_id;
    }

    /**
     * get service_id
     * @return int|null
     */
    public function getServiceIdAttr(): ?int
    {
        return $this->service_id;
    }

    /**
     * get expires_at
     * @return int|null
     */
    public function getExpiresAtAttr(): ?int
    {
        return $this->expires_at;
    }

    /**
     * get created
     * @return int|null
     */
    public function getCreatedAttr(): ?int
    {
        $created = $this->created;
        return $created != null ? $created->timestamp : 0;
    }

    /**
     * get updated
     * @return int|null
     */
    public function getUpdatedAttr(): ?int
    {
        $updated = $this->updated;
        return $updated != null ? $updated->timestamp : 0;
    }

    /**
     * get refresh_token
     * @return string|null
     */
    public function getRefreshTokenAttr(): ?string
    {
        return $this->refresh_token;
    }

    /// ----------------------------------------------------
    ///  End of getters and setters
    /// ----------------------------------------------------

}