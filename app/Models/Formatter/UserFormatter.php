<?php 

namespace App\Models\Formatter;

use App\Library\TwoStep;
use App\Util\CommonHelper;

/**
 * Trait UserFormatter
 * 
 * auto generate by php artisan yfy:formatter
 * @package App\Models\Formatter
 * @property int id
 * @property int user_id
 * @property string password
 * @property int activated
 * @property string remember_token
 * @property int settings
 * @property string value_box
 * @property \Illuminate\Support\Carbon created
 * @property \Illuminate\Support\Carbon updated
 * @property string email
 * @property bool identity_type_email_available
 * @property bool is_demo_user
 * @property int enterprise_id
 */
trait UserFormatter
{
    use BaseFormatter;

    public static $internal_keys = ['id', 'user_id'];

    public static $internal_full_keys = ['id', 'user_id', 'credentials'];

    protected static $remote_fields = [
        'name',
        'username',
        'user_group',
        'email',
        'phone',
        'name_first_letter',
        'full_name_pinyin',
        'space_total',
        'enterprise_id',
        'space_used',
        'profile_pic_key',
        'company_name',
        'is_active',
        'current_user_type',
        'email_verified_at',
        'phone_verified_at',
        'identity_type_email_available',

        'trash_period',
        'unread_message_count',
        'colleague_count',
        'can_invite',
        'enterprise_admin_user_real_name_identity_status',
        'speed_limit_info',

        'is_demo_user',
        'is_phone_public',
//        'is_new_device_verification_enabled',
//        'is_collab_related_functions_restricted',
        'is_personal_space_closed',

        'enterprise' => 'app_enterprise_info',
        'has_department',

        'is_personal_user',
        'is_frozen',
        'is_login_disabled',
        'lang',
    ];

    public static $app_keys = [
        'id' => 'user_id',
        'name',
        'username',
        'user_group',
        'email',
        'phone',
        'name_first_letter',
        'full_name_pinyin',
        'space_total',
        'enterprise_id',
        'space_used',
        'profile_pic_key',
        'company_name',
        'is_active',
        'current_user_type',
        'email_verified_at',
        'phone_verified_at',

        'trash_period',
        'unread_message_count',
        'colleague_count',
        'can_invite',
        'enterprise_admin_user_real_name_identity_status',
        'speed_limit_info',

        'is_demo_user',
        'is_phone_public',

        // 已经废弃
//        'is_new_device_verification_enabled',
//        'is_collab_related_functions_restricted',

        'is_personal_space_closed',

        'enterprise' => 'app_enterprise_info',
        'has_department',
    ];

    public function preFormat($fields, $params = [])
    {
        $remote_fields = array_intersect(static::$remote_fields, $fields);
        if ($remote_fields) {
            $this->getRemoteUser()->getInfo($remote_fields);
        }
    }

    /// ----------------------------------------------------
    ///  Begin of bitmask
    /// ----------------------------------------------------

    /**
     * get password_require_init bitmask
     * @return bool
     */
    public function getPasswordRequireInitAttr(): bool
    {
        return (bool)$this->getBitmaskField('password_require_init');
    }
    
    /**
     * set password_require_init bitmask
     */
    public function setPasswordRequireInitAttr()
    {
        $this->setBitmaskField('password_require_init', true);
    }
    
    /**
     * unset password_require_init bitmask
     */
    public function unsetPasswordRequireInitAttr()
    {
        $this->setBitmaskField('password_require_init', false);
    }

    /**
     * get is_two_step_enabled bitmask
     * @return bool
     */
    public function getIsTwoStepEnabledAttr(): bool
    {
        return (bool)$this->getBitmaskField('is_two_step_enabled');
    }

    /**
     * set is_two_step_enabled bitmask
     */
    public function setIsTwoStepEnabledAttr()
    {
        $this->setBitmaskField('is_two_step_enabled', true);
    }
    
    /**
     * unset is_two_step_enabled bitmask
     */
    public function unsetIsTwoStepEnabledAttr()
    {
        $this->setBitmaskField('is_two_step_enabled', false);
    }


    /// ----------------------------------------------------
    ///  End of bitmask
    /// ----------------------------------------------------

    /// ----------------------------------------------------
    ///  Begin of value box
    /// ----------------------------------------------------

    /**
     * get unverified_email value box
     * @return mixed
     */
    public function getUnverifiedEmailAttr()
    {
        return $this->getAdditionalValue('unverified_email');
    }
    
    /**
     * set unverified_email value box
     * @param mixed $value
     */
    public function setUnverifiedEmailAttr($value)
    {
        $this->setAdditionalValue('unverified_email', $value);
    }

    /**
     * unset unverified_email value box
     */
    public function unsetUnverifiedEmailAttr()
    {
        $this->unsetAdditionalValue('unverified_email');
    }

    /**
     * get unverified_phone value box
     * @return mixed
     */
    public function getUnverifiedPhoneAttr()
    {
        return $this->getAdditionalValue('unverified_phone');
    }
    
    /**
     * set unverified_phone value box
     * @param mixed $value
     */
    public function setUnverifiedPhoneAttr($value)
    {
        $this->setAdditionalValue('unverified_phone', $value);
    }

    /**
     * unset unverified_phone value box
     */
    public function unsetUnverifiedPhoneAttr()
    {
        $this->unsetAdditionalValue('unverified_phone');
    }

    /**
     * get email_verified_at value box
     * @return mixed
     */
    public function getEmailVerifiedAtAttr()
    {
        return $this->getAdditionalValue('email_verified_at');
    }
    
    /**
     * set email_verified_at value box
     * @param mixed $value
     */
    public function setEmailVerifiedAtAttr($value)
    {
        $this->setAdditionalValue('email_verified_at', $value);
    }

    /**
     * unset email_verified_at value box
     */
    public function unsetEmailVerifiedAtAttr()
    {
        $this->unsetAdditionalValue('email_verified_at');
    }

    /**
     * get phone_verified_at value box
     * @return mixed
     */
    public function getPhoneVerifiedAtAttr()
    {
        return $this->getAdditionalValue('phone_verified_at');
    }
    
    /**
     * set phone_verified_at value box
     * @param mixed $value
     */
    public function setPhoneVerifiedAtAttr($value)
    {
        $this->setAdditionalValue('phone_verified_at', $value);
    }

    /**
     * unset phone_verified_at value box
     */
    public function unsetPhoneVerifiedAtAttr()
    {
        $this->unsetAdditionalValue('phone_verified_at');
    }

    /**
     * get two_step_method value box
     * @return mixed
     */
    public function getTwoStepMethodAttr()
    {
        return $this->getAdditionalValue('two_step_method') ?? TwoStep::METHOD_NONE;
    }
    
    /**
     * set two_step_method value box
     * @param mixed $value
     */
    public function setTwoStepMethodAttr($value)
    {
        $this->setAdditionalValue('two_step_method', $value);
    }

    /**
     * unset two_step_method value box
     */
    public function unsetTwoStepMethodAttr()
    {
        $this->unsetAdditionalValue('two_step_method');
    }

    /**
     * get two_step_secret value box
     * @return mixed
     */
    public function getTwoStepSecretAttr()
    {
        return $this->getAdditionalValue('two_step_secret');
    }
    
    /**
     * set two_step_secret value box
     * @param mixed $value
     */
    public function setTwoStepSecretAttr($value)
    {
        $this->setAdditionalValue('two_step_secret', $value);
    }

    /**
     * unset two_step_secret value box
     */
    public function unsetTwoStepSecretAttr()
    {
        $this->unsetAdditionalValue('two_step_secret');
    }

    /**
     * get two_step_identifier value box
     * @param bool $masked_phone
     * @return mixed
     */
    public function getTwoStepIdentifierAttr($masked_phone = true)
    {
        if ($masked_phone && $this->getTwoStepMethodAttr() === TwoStep::METHOD_SMS) {
            return CommonHelper::getMaskedPhone($this->getAdditionalValue('two_step_identifier'));
        } else {
            return $this->getAdditionalValue('two_step_identifier');
        }
    }
    
    /**
     * set two_step_identifier value box
     * @param mixed $value
     */
    public function setTwoStepIdentifierAttr($value)
    {
        $this->setAdditionalValue('two_step_identifier', $value);
    }

    /**
     * unset two_step_identifier value box
     */
    public function unsetTwoStepIdentifierAttr()
    {
        $this->unsetAdditionalValue('two_step_identifier');
    }

    /**
     * get two_step_set_time value box
     * @return mixed
     */
    public function getTwoStepSetTimeAttr()
    {
        return $this->getAdditionalValue('two_step_set_time');
    }
    
    /**
     * set two_step_set_time value box
     * @param mixed $value
     */
    public function setTwoStepSetTimeAttr($value)
    {
        $this->setAdditionalValue('two_step_set_time', $value);
    }

    /**
     * unset two_step_set_time value box
     */
    public function unsetTwoStepSetTimeAttr()
    {
        $this->unsetAdditionalValue('two_step_set_time');
    }


    /// ----------------------------------------------------
    ///  End of value box
    /// ----------------------------------------------------

    /// ----------------------------------------------------
    ///  Begin of getters and setters
    /// ----------------------------------------------------

    /**
     * get activated
     * @return int|null
     */
    public function getActivatedAttr(): ?int
    {
        return $this->activated;
    }

    /**
     * get id
     * @return int|null
     */
    public function getIdAttr(): ?int
    {
        return $this->id;
    }

    /**
     * get user_id
     * @return int|null
     */
    public function getUserIdAttr(): ?int
    {
        return $this->user_id;
    }

    /**
     * get password
     * @return string|null
     */
    public function getPasswordAttr(): ?string
    {
        return $this->password;
    }

    /**
     * get remember_token
     * @return string|null
     */
    public function getRememberTokenAttr(): ?string
    {
        return $this->remember_token;
    }

    /**
     * get settings
     * @return int|null
     */
    public function getSettingsAttr(): ?int
    {
        return $this->settings;
    }

    /**
     * get value_box
     * @return string|null
     */
    public function getValueBoxAttr(): ?string
    {
        return $this->value_box;
    }

    /**
     * get created
     * @return int|null
     */
    public function getCreatedAttr(): ?int
    {
        $created = $this->created;
        return $created != null ? $created->timestamp : 0;
    }

    /**
     * get updated
     * @return int|null
     */
    public function getUpdatedAttr(): ?int
    {
        $updated = $this->updated;
        return $updated != null ? $updated->timestamp : 0;
    }

    public function getIsPasswordUnsetAttr(): bool
    {
        return $this->isPasswordUnset();
    }



    /// ----------------------------------------------------
    ///  End of getters and setters
    /// ----------------------------------------------------
}