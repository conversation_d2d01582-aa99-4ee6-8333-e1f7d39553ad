<?php 

namespace App\Models\Formatter;

/**
 * Trait CredentialFormatter
 * 
 * auto generate by php artisan yfy:formatter
 * @package App\Models\Formatter
 */
trait CredentialFormatter
{
    use BaseFormatter;

    public static $internal_keys = ['login_user_id', 'formatted_identifier', 'type', 'status', 'verified_at', 'activation_url'];
    public static $frontend_keys = ['login_user_id', 'formatted_identifier', 'type', 'status', 'verified_at'];

    /// ----------------------------------------------------
    ///  Begin of getters and setters
    /// ----------------------------------------------------

    public function getFormattedIdentifierAttribute() : string
    {
        return $this->getIdentifier();
    }

    public function getActivationUrlAttribute() : string
    {
        return $this->getActivationUrl();
    }

    public function getVerifiedAtAttribute() : int
    {
        return $this->getAdditionalValue('verified_at') ?: 0;
    }

    public function getNickAttr()
    {
        if ($this->type == self::TYPE_WECHAT) {
            $user = $this->getAdditionalValue(self::VALUE_WECHAT);
            return (new \App\Library\OAuth\Wechat\User($user))->getNick();
        } else if ($this->type == self::TYPE_DINGTALK) {
            $user = $this->getAdditionalValue(self::VALUE_DINGTALK);
            return (new \App\Library\OAuth\Dingtalk\User($user))->getNick();
        } else if ($this->type == self::TYPE_QIHOO360) {
            $user = $this->getAdditionalValue(self::VALUE_QIHOO360);
            return (new \App\Library\OAuth\Qihoo360\User($user))->getNick();
        } else {
            return $this->identifier;
        }
    }

    public function getTypeAttr()
    {
        if ($this->type == self::TYPE_WECHAT) {
            return self::VALUE_WECHAT;
        } else if ($this->type == self::TYPE_DINGTALK) {
            return self::VALUE_DINGTALK;
        } else if ($this->type == self::TYPE_QIHOO360) {
            return self::VALUE_QIHOO360;
        } else {
            return $this->type;
        }
    }

    /// ----------------------------------------------------
    ///  End of getters and setters
    /// ----------------------------------------------------
}