<?php

namespace App\Models\Formatter;

use Illuminate\Support\Str;

trait BaseFormatter
{
    /**
     * General formatter
     *
     * @param $objects
     * @param $fields
     *          如果存在 bulkGet{$field}Attr
     *          如果存在 get{$field}Attr
     *          $this->$field
     * @param array $params
     *      [index={primaryKey}] 返回的结果中需要为字典时，可以指定key的取值; 在bulk方法中返回的字段的key。
     *      [with_index=false] 返回的结果是否需要为字典
     * @return array
     * TODO: 不同的bulk使用不用的index
     */
    public static function bulkFormat($objects, $fields, $params = []): array
    {
        if (!is_iterable($objects)) {
            $objects = [$objects];
        }
        $reflector = new \ReflectionClass(static::class);

        $default_properties = $reflector->getDefaultProperties();
        $index = $params['index'] ?? $default_properties['primaryKey'];
        $preserve_index = $params['with_index'] ?? false;

        $bulk_params['index'] = $index;
        $bulk_params['scene'] = $params['scene'] ?? $default_properties['scene'];

        $results = [];

        foreach ($fields as $field) {
            $bulk_field = [];
            $bulk_function_name = 'bulkGet' . Str::studly($field) . 'Attr';

            if ($reflector->hasMethod($bulk_function_name)) {
                $bulk_field = static::$bulk_function_name($objects, $bulk_params);
            }

            $has_format_func = false;
            $format_function_name = 'get' . Str::studly($field) . 'Attr';
            if ($reflector->hasMethod($format_function_name)) {
                $has_format_func = true;
            }

            foreach ($objects as $object) {
                $results[$object->$index][$field] = $bulk_field[$object->$index]
                        ?? ($has_format_func ? $object->$format_function_name() : $object->$field);
            }
        }

        return $preserve_index ? $results : array_values($results);
    }

    /**
     * Format use api mini keys.
     * 因为 trait 里重复定义静态变量会有error，所以需要在类中定义 api_mini keys
     *
     * static  后期静态绑定
     *
     * @param $objects
     * @param array $params
     * @return array
     */
    public static function formatAsApiMini($objects, $params = []): array
    {
        return static::bulkFormat($objects, isset(static::$api_mini_keys) ? static::$api_mini_keys : ['id'], $params);
    }

    /**
     * Format as frontend detailed
     *
     * @param $objects
     * @param array $params
     * @return array
     */
    public static function formatAsFrontend($objects, $params = []): array
    {
        return static::bulkFormat($objects, isset(static::$frontend_object_keys) ? static::$frontend_object_keys : ['id'], $params);
    }

    /**
     * General formatter
     *
     * @param $fields
     * @param array $params
     *      [index={primaryKey}] 返回的结果中需要为字典时，可以指定key的取值; 在bulk方法中返回的字段的key。
     *      [with_index=false] 返回的结果是否需要为字典
     * @return array
     */
    public function format($fields, $params = []): array
    {
        if (method_exists($this, 'preFormat')) {
            $this->preFormat($fields, $params);
        }
        $result = [];
        $reflector = new \ReflectionClass(static::class);
        foreach ($fields as $key => $field) {
            if(is_numeric($key)) {
                $key = $field;
            }

            $has_format_func = false;
            $format_function_name = 'get' . Str::studly($field) . 'Attr';
            if ($reflector->hasMethod($format_function_name)) {
                $has_format_func = true;
            }

            $result[$key] = $has_format_func ? $this->$format_function_name() : $this->$field;
        }

        return $result;
    }
}