<?php

namespace App\Models;

use Illuminate\Notifications\Notifiable;
use Illuminate\Contracts\Auth\Authenticatable as AuthenticatableContract;
use Illuminate\Auth\Authenticatable;
use App\Remote\User as RemoteUser;
use Illuminate\Support\Str;

/**
 * Class PasswordReset
 * @package App\Models
 *
 * @property int id
 * @property int login_user_id
 * @property string unique_code
 * @property int expires_at
 * @property int created
 * @property int updated
 *
 */

class PasswordReset extends BaseModel
{
    protected $table = 'password_resets';

    protected $fillable = [
        'id', 'login_user_id', 'unique_code', 'expires_at', 'created', 'updated',
    ];

    public function getUser() : ?User
    {
        return User::find($this->login_user_id);
    }

    public static function getByUniqueCode($code) : ?PasswordReset
    {
        return static::where('unique_code', $code)->first();
    }

    public static function getByUserId($user_id) : ?PasswordReset
    {
        return static::where('login_user_id', $user_id)->first();
    }

    public static function createForUser($user_id) : PasswordReset
    {
        $password_reset = static::getByUserId($user_id);
        if($password_reset) {
            $password_reset->unique_code = strtolower(Str::random(32));
            $password_reset->expires_at = time() + config('auth.forgot_password_expire');
            $password_reset->save();
        }
        else {
            $password_reset = static::create([
                'login_user_id' => $user_id,
                'unique_code' => strtolower(Str::random(32)),
                'expires_at' => time() + config('auth.forgot_password_expire'),
            ]);
        }
        return $password_reset;
    }

    public function getPasswordResetUrl() : string
    {
        return route('password.reset', ['token' => $this->unique_code]);
    }

    public static function deleteByUser($user_id) : void
    {
        static::where('login_user_id', $user_id)->delete();
    }
}
