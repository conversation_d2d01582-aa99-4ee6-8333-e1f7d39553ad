<?php

namespace App\Util\UA\Converter;
use App\Remote\User as RemoteUser;

/**
 * 转换User
 * Class UserConverter
 * @package App\Util\UA\Converter
 */
class UserConverter
{
    /**
     * @param RemoteUser $user
     * @return array|null
     */
    public static function convert(RemoteUser $user)
    {
        if ($user === null) {
            return null;
        }
        return [
            'id' => (int)$user->id,
            'enterprise_id' => (int)$user->enterprise_id,
            'name' => $user->name,
            'email' => $user->email,
            'phone' => $user->phone,
        ];
    }
}