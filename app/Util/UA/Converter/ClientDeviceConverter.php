<?php

namespace App\Util\UA\Converter;


use App\Models\ClientDevice;

/**
 * 转换ClientDevice对象
 * Class ClientDeviceConverter
 * @package App\Util\UA\Converter
 */
class ClientDeviceConverter
{

    public static function convert(ClientDevice $client_device)
    {
        if ($client_device === null) {
            return null;
        }

        return [
            'id' => (int)$client_device->id,
            'device_type' => $client_device->device_type,
            'user_id' => (int)$client_device->user_id,
            'logout_time' => (int)$client_device->logout_time,
            'deleted' => (int)$client_device->deleted,
            'updated' => (int)$client_device->getUpdatedAttr(),
            'created' => (int)$client_device->getCreatedAttr()
        ];
    }

}