<?php

namespace App\Util\UA;


use App\Jobs\SendUA;
use App\Jobs\SendUAToXman;
use App\Remote\RequestContextTrait;
use App\Util\LogHelper;
use App\Util\LogType;
use App\Util\UA\Converter\ClientDeviceConverter;
use App\Util\UA\Converter\UserConverter;

class UAClient
{
    use RequestContextTrait;

    /**
     * 发送UA消息
     * @param string $message_type 都在UAMessageTypes类里面
     * @param array $message_body
     * @param array|null $context_overrides 用于覆盖request context里面的值，比如传actor_id、actor_enterprise_id等
     */
    public static function publish(string $message_type, array $message_body = [], array $context_overrides = null): void
    {
        $is_dev = app()->environment() !== 'production';

        // 预校验参数
        if ($message_type === null || !UAMessageTypes::isValidValue($message_type)) {
            $is_dev && trigger_error('Action type is invalid');
            return;
        }

        // 准备消息内容
        $request_context = self::buildRequestContext($context_overrides);
        $message_body = self::convertMessageBody($message_type, $message_body);

        $env_config = config('app.env_config');
        if ($env_config['env'] == 'profession' && $env_config['config']['is_feed_enabled'] == false) {

            try {
                $user_activity = (new UABuilder($message_type, $request_context, $message_body))->build();
            }catch (\Exception $e) {
                LogHelper::info(LogType::Exception, $e->getMessage(), ['exception' => LogHelper::serializeException($e)]);
            }


            if (empty($user_activity)) {
                return;
            }

            // 需要存储的信息都放在additional_info中
            $additional_info = [
                'user_activities'=>json_encode([$user_activity]),
            ];
            // 回调地址
            $job_specs['callback'] = config('app.fangcloud_url').'internal_api/jobs/save_ua';
            $job_specs['callback_service_id'] = 1;
            // 新的xman回调需要将所有的参数放在additional_infos中，支持批量job
            $job_specs['additional_infos'][] = $additional_info;
            $job_specs['job_count'] = count($job_specs['additional_infos']);

            $job = new SendUAToXman();
            $job->setBody($job_specs);
            $job->send();
            return;
        }

        // 构建消息
        $body['message_type'] = $message_type;
        $body['milli_timestamp'] = round(microtime(true) * 1000);
        $body['message_body'] = $message_body;
        $body['request_context'] = $request_context;

        // 发送消息
        (new SendUA($body))->send();
    }

    /**
     * 构建request context对象
     * @param array|null $context_overrides
     * @return array
     */
    private static function buildRequestContext(array $context_overrides = null): array
    {
        // build body
        $request_context = self::getRequestContext();
        if ($context_overrides === null || empty($context_overrides)) {
            return $request_context;
        }

        // context overrides
        foreach ($request_context as $key => &$value) {
            if (array_key_exists($key, $context_overrides) && $context_overrides[$key] !== null) {
                $value = $context_overrides[$key];
            }
        }

        return $request_context;
    }

    /**
     * 根据消息类型，选择不同的转换器转换消息内容
     * @param string $message_type
     * @param array $message_body
     * @return array
     */
    private static function convertMessageBody(string $message_type, array $message_body)
    {
        if ($message_body === null || empty($message_body)) {
            return $message_body;
        }
        switch ($message_type) {
            case UAMessageTypes::USER_CLIENT_DEVICE_LOGIN:
                $message_body['old'] = ClientDeviceConverter::convert($message_body['old']);
                $message_body['current'] = ClientDeviceConverter::convert($message_body['current']);
                break;
            case UAMessageTypes::USER_REGISTER:
                $message_body['user'] = UserConverter::convert($message_body['user']);
                break;
            default:
                break;
        }
        return $message_body;
    }
}