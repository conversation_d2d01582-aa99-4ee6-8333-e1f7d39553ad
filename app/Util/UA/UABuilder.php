<?php
/**
 * Created by IntelliJ IDEA.
 * User: qike
 * Date: 18/8/14
 * Time: 上午10:48
 */
namespace App\Util\UA;


class UABuilder
{
    protected $user_activity;

    protected $message_type;

    protected $context_name;
    protected $actor_id;
    protected $actor_type;
    protected $actor_enterprise_id;
    protected $ip_address;
    protected $timestamp;
    protected $service_id;
    protected $service_name;
    protected $additional_info;

    protected $email;
    protected $user;
    protected $from;
    protected $to;
    protected $old;
    protected $current;
    protected $device_last_login_period;

    protected $actor_type_map = [
        'user' => 1,
        'group' => 2,
        'department' => 3,
    ];

    public static $action_type_map = [
        UAMessageTypes::USER_ACTIVATION => ActionTypes::USER_ACTIVATION,
        UAMessageTypes::USER_CHANGE_PASSWORD => ActionTypes::USER_CHANGE_PASSWORD,
        UAMessageTypes::USER_DELETE_DEVICE => ActionTypes::USER_DELETE_DEVICE,
        UAMessageTypes::USER_CLOSE_TWO_STEP => ActionTypes::USER_CLOSE_TWO_STEP,
        UAMessageTypes::USER_EMAIL_VALIDATE => ActionTypes::USER_EMAIL_VALIDATE,
        UAMessageTypes::USER_FORGOT_PASSWORD => ActionTypes::USER_FORGOT_PASSWORD,
        UAMessageTypes::USER_LOGIN => ActionTypes::USER_LOGIN,
        UAMessageTypes::USER_LOGOUT => ActionTypes::USER_LOGOUT,
        UAMessageTypes::USER_MODIFY_TWO_STEP => ActionTypes::USER_MODIFY_TWO_STEP,
        UAMessageTypes::USER_OPEN_TWO_STEP => ActionTypes::USER_OPEN_TWO_STEP,
        UAMessageTypes::USER_RESET_PASSWORD => ActionTypes::USER_RESET_PASSWORD,
        UAMessageTypes::USER_UPDATE_EMAIL => ActionTypes::USER_UPDATE_EMAIL,
    ];

    /**
     * UABuilder constructor.
     * @param $message_type
     * @param $request_context
     * @param $message_body
     */
    public function __construct($message_type, $request_context,$message_body)
    {
        $this->message_type = $message_type;

        $this->context_name = $request_context['context_name'];
        $this->actor_id = $request_context['actor_id'];
        $this->actor_type = $request_context['actor_type'];
        $this->actor_enterprise_id = $request_context['actor_enterprise_id'];
        $this->ip_address = $request_context['ip_address'];
        $this->service_id = $request_context['service_id'];
        $this->service_name = $request_context['service_name'];
        $this->timestamp = $request_context['timestamp'];
        $this->additional_info = $request_context['additional_info'];

        if (!empty($message_body)) {
            $this->email = $message_body['email']?:null;
            $this->user = $message_body['user']?:null;
            $this->from = $message_body['from']?:null;
            $this->to = $message_body['to']?:null;
            $this->old = $message_body['old']?:null;
            $this->current = $message_body['current']?:null;
            $this->device_last_login_period = $message_body['device_last_login_period']?:null;
        }
    }

    public function build()
    {
        switch ($this->message_type) {
            case UAMessageTypes::USER_ACTIVATION:
            case UAMessageTypes::USER_CHANGE_PASSWORD:
            case UAMessageTypes::USER_DELETE_DEVICE:
            case UAMessageTypes::USER_CLOSE_TWO_STEP:
            case UAMessageTypes::USER_FORGOT_PASSWORD:
            case UAMessageTypes::USER_LOGIN:
            case UAMessageTypes::USER_LOGOUT:
            case UAMessageTypes::USER_MODIFY_TWO_STEP:
            case UAMessageTypes::USER_OPEN_TWO_STEP:
            case UAMessageTypes::USER_RESET_PASSWORD:
                $this->buildBaseInfo();
                break;
            case UAMessageTypes::USER_CLIENT_DEVICE_LOGIN:
                $this->buildBaseInfo()->buildClientDeviceLoginUa();
                break;
            case UAMessageTypes::USER_REGISTER:
                $this->buildBaseInfo()->buildUserCreateUA();
                break;
            case UAMessageTypes::USER_EMAIL_VALIDATE:
            case UAMessageTypes::USER_UPDATE_EMAIL:
                $this->buildBaseInfo()->addAdditionalInfo('email', $this->email);
                break;
            case UAMessageTypes::USER_UPDATE_PHONE:
                $this->buildBaseInfo()->buildUserUpdatePhoneUA();
                break;
            default:
                break;
        }

        if (empty($this->user_activity['action_type'])) {
            return null;
        }

        if (empty($this->user_activity['scene_enterprise_id'])) {
            $this->user_activity['scene_enterprise_id'] = $this->user_activity['enterprise_id'];
        }
        if (empty($this->user_activity['created'])) {
            $this->user_activity['created'] = round(microtime(true) * 1000);
        }
        if (empty($this->additional_info)) {
            $this->user_activity['additional_info'] = '';
        } else {
            $this->user_activity['additional_info'] = json_encode($this->additional_info);
        }

        return $this->user_activity;
    }

    private function buildBaseInfo()
    {
        $this->user_activity = [];

        $actor_type = $this->actor_type_map[$this->actor_type];
        if (empty($actor_type)) {
            $actor_type = 0;
        }
        $this->user_activity['user_id'] = $this->actor_id;
        $this->user_activity['actor_type'] = $actor_type;
        $this->user_activity['enterprise_id'] = $this->actor_enterprise_id;

        $this->user_activity['service_name'] = $this->service_name;
        $this->user_activity['service_id'] = $this->service_id;
        $this->user_activity['ip_address'] = $this->ip_address;
        $this->user_activity['created'] = $this->timestamp;
        $this->setActionType(self::$action_type_map[$this->message_type]);
        return $this;
    }

    private function setTarget($target_id, $target_type) {
        $this->user_activity['$target_id'] = $target_id;
        $this->user_activity['$target_type'] = $target_type;
    }
    private function setActionType($actionType) {
        $this->user_activity['action_type'] = $actionType;
    }
    private function setActor($actorId, $actorType, $actorEnterpriseId) {
        $this->user_activity['user_id'] = $actorId;
        $this->user_activity['actor_type'] = $actorType;
        $this->user_activity['enterprise_id'] = $actorEnterpriseId;
    }
    private function addAdditionalInfo($name, $data) {
        $this->additional_info[$name] = $data;
    }

    private function buildClientDeviceLoginUa()
    {
        if (empty($this->old) && !empty($this->current)) {
            $this->setActionType(ActionTypes::USER_LOGIN_WITH_NEW_DEVICE);
            $this->setTarget($this->current['id'], TargetTypes::DEVICE_TYPE);
            $this->addAdditionalInfo('device', ['id' => $this->current['id'], 'device_type' => $this->current['device_type']]);
            return $this;
        }
        if (!empty($this->old) && !empty($this->current)) {
            if ($this->old['deleted'] > 0 && $this->current['deleted'] == 0) {
                $this->setActionType(ActionTypes::USER_LOGIN_WITH_DELETED_DEVICE);
                $this->setTarget($this->current['id'], TargetTypes::DEVICE_TYPE);
                $this->addAdditionalInfo('device', ['id' => $this->current['id'], 'device_type' => $this->current['device_type']]);
                return $this;
            }
            if ($this->current['updated'] = $this->old['updated'] > $this->device_last_login_period) {
                $this->setActionType(ActionTypes::USER_LOGIN_WITH_LONG_TIME);
                $this->setTarget($this->current['id'], TargetTypes::DEVICE_TYPE);
                $this->addAdditionalInfo('device', ['id' => $this->current['id'], 'device_type' => $this->current['device_type']]);
                return $this;
            }
        }
        return $this;
    }

    private function buildUserCreateUA()
    {
        if ($this->context_name == 'register') {
            $this->buildUserCreateUARegister(ActionTypes::USER_REGISTER);
        } else if ($this->context_name == 'personal_register') {
            $this->buildUserCreateUARegister(ActionTypes::PERSONAL_USER_REGISTER);
        } else if ($this->context_name == 'mobile_register') {
            $this->buildUserCreateUARegister(ActionTypes::USER_MOBILE_REGISTER);
        } else {
            $this->setActionType(ActionTypes::USER_CREATE);
            $this->setTarget($this->user['id'], TargetTypes::USER_TYPE);
        }
        return $this;
    }

    private function buildUserCreateUARegister($actionType)
    {
        $this->setActionType($actionType);
        if (empty($this->actor_id)) {
            $this->setActor($this->user['id'], $this->actor_type_map['user'], $this->user['enterprise_id']);
        }
    }

    private function buildUserUpdatePhoneUA()
    {
        $this->setActionType(ActionTypes::USER_UPDATE_PHONE);
        $this->setTarget($this->actor_id, TargetTypes::USER_TYPE);
        $this->addAdditionalInfo('update',['phone'=>['from'=>$this->from,'to'=>$this->to]]);
    }
}