<?php
/**
 * Created by IntelliJ IDEA.
 * User: qike
 * Date: 18/8/14
 * Time: 上午11:49
 */

namespace App\Util\UA;


class ActionTypes
{
    const USER_LOGIN = 1; //用户登录
    const USER_LOGOUT = 2;
    const USER_LOGFAIL = 3;
    const USER_CHANGE_PASSWORD = 4;
    const USER_FORGOT_PASSWORD = 5;
    const USER_RESET_PASSWORD = 6;
    const USER_LOGIN_WITH_NEW_DEVICE = 7; // 用户使用新设备登录
    const USER_LOGIN_WITH_DELETED_DEVICE = 8; // 用户使用删除的设备登录
    const USER_LOGIN_WITH_LONG_TIME = 9;     //用户在长时间未活动之后登录6个月
    const USER_CREATE = 10; // 邀请公司成员
    const USER_DELETE = 11;
    const USER_SPACE_EDIT = 12; // 管理员编辑用户空间
    const USER_REGISTER = 13;
    const USER_ACTIVATION = 14;
    const USER_REGISTER_DEVICE_TOKEN = 15; // 用户唤起App时，注册设备token
    const PERSONAL_USER_REGISTER = 16; // 个人账号注册
    const USER_EMAIL_VALIDATE = 17; // 用户验证邮件
    const CHANGE_PERSONAL_TO_ENTERPRISE = 18; // 将个人账号转变成企业账号
    const USER_MOBILE_REGISTER = 19; // 移动官网注册
    const USER_TRANSFER_ADMIN = 20; // 移交管理员
    const USER_ROLE_EDIT = 21; // 管理员编辑用户权限
    const USER_REGISTER_FROM_INVITE_LINK = 22; // 通过邀请链接注册
    const USER_INVITE_LINK_CREATE = 23; // 创建邀请同事连接
    const USER_INVITE_LINK_UPDATE = 24; // 更新邀请同事连接
    const USER_INVITE_LINK_DELETE = 25; // 删除邀请同事连接
    const USER_DELETE_EXTERNAL_COLLAB_USER = 26; // 删除外部协作者
    const USER_CREATE_UNION_ADMIN = 27; // 添加联合管理员
    const USER_EDIT_UNION_ADMIN = 28; // 编辑联合管理员
    const USER_DELETE_UNION_ADMIN = 29; // 删除联合管理员
    const USER_NAME_EDIT = 30; // 编辑用户名称
    const USER_EMAIL_VERIFICATION = 31; // 验证邮箱
    const USER_UPDATE_EMAIL = 32; // 修改邮箱
    const USER_UPDATE_PHONE = 33; // 修改手机号
    const USER_HIDE_PHONE = 34;
    const USER_SHOW_PHONE = 35;
    const USER_OPEN_TWO_STEP = 36;
    const USER_MODIFY_TWO_STEP = 37;
    const USER_CLOSE_TWO_STEP = 38;
    const USER_HELP_CLOSE_TWO_STEP = 39;
    const USER_DELETE_DEVICE = 40;
    const USER_PIC_EDIT = 41;
    const USER_CHANGE_PHONE = 42;
    const USER_STORAGE_EDIT = 43;
    const USER_CREATE_ADMIN = 44;
    const USER_UPDATE_ADMIN = 45;
    const USER_DELETE_ADMIN = 46;

    const SYNC_CREATE_AUTH_TOKEN = 800;
    const SYNC_REFRESH_AUTH_TOKEN = 801;
}