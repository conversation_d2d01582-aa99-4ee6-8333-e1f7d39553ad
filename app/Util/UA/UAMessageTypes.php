<?php
/**
 * Created by PhpStorm.
 * User: CJY
 * Date: 2018/2/6
 * Time: 14:25
 */

namespace App\Util\UA;


use App\Library\BasicEnum;

class UAMessageTypes extends BasicEnum
{
    /**
     * message_types
     * 操作类型，需要和服务端提供的可选操作类型保持一致
     */
    public const USER_LOGIN = 'user_login';
    public const USER_LOGOUT = 'user_logout';
    public const USER_RESET_PASSWORD = 'user_reset_password';
    public const USER_CHANGE_PASSWORD = 'user_change_password';
    public const USER_FORGOT_PASSWORD = 'user_forgot_password';
    public const USER_CLIENT_DEVICE_LOGIN = 'user_client_device_login';
    public const USER_REGISTER = 'user_register';
    public const USER_ACTIVATION = 'user_activation';
    public const USER_DELETE_DEVICE = 'user_delete_device';
    public const USER_OPEN_TWO_STEP = 'user_open_two_step';
    public const USER_MODIFY_TWO_STEP = 'user_modify_two_step';
    public const USER_CLOSE_TWO_STEP = 'user_close_two_step';
    public const USER_UPDATE_EMAIL = 'user_update_email';
    public const USER_UPDATE_PHONE = 'user_update_phone';
    public const USER_EMAIL_VALIDATE = 'user_email_validate';


    public const SYNC_CREATE_AUTH_TOKEN = 'sync_create_auth_token';
    public const SYNC_REFRESH_AUTH_TOKEN = 'sync_refresh_auth_token';

}