<?php

namespace App\Util;

use App\Constants\Common;
use App\Constants\Http;
use App\Models\AuthToken;
use App\Models\ClientDevice;
use App\Models\Service;
use App\Remote\AuthToken as RemoteAuthToken;
use <PERSON><PERSON><PERSON>\Agent\Agent;

/**
 * Class UserAgent
 * 继承于 https://github.com/jenssegers/agent
 *
 * 亿方云相关的 user_agent。
 * 包含处理了 web 端、移动端、sync 端的设备判断。
 * web 端使用 HTTP_USER_AGENT
 * 移动端使用 HTTP HEADER Auth-Token
 * sync 端 api接口使用 HTTP HEADER Auth-Token，webview 请求使用 HTTP_USER_AGENT 判断
 *
 * @package App\Util
 */
class UserAgent extends Agent
{
    /**
     * 使用的 auth_token 取自于 HTTP HEADER Auth-Token
     *
     * @var AuthToken|null $auth_token
     * @var Service|null $service
     */
    private $auth_token = null;
    private $service = null;

    public const BROWSER_PLATFORM_WEB = 'web';

    public const BROWSER_PLATFORM_MOBILE = 'mobile';

    public const BROWSER_PLATFORM_SYNC = 'sync';

    private $request;

    public function __construct($user_agent = null, $auth_token = null, $api_key = null)
    {
        $this->request = request();
        if (!$user_agent) {
            $user_agent = $this->request->header(Http::HEADER_DEVICE_INFO);
        }
        if (!$auth_token) {
            $auth_token = Context::getAuthToken();
        }
        if (!$api_key) {
            $api_key = $this->request->header(Http::HEADER_API_KEY) ?: $this->request->query('api_key');
        }
        parent::__construct(null, $user_agent);

        if ($auth_token) {
            $this->auth_token = RemoteAuthToken::get($auth_token);
//            $this->auth_token = AuthToken::findByAuthTokenGently($auth_token);
        }
        if ($api_key) {
            $this->service = Service::findByApiKey($api_key);
        }
    }

    public function getBrowserPlatform()
    {
        if (($this->auth_token && $this->auth_token->isMobile())
            || ($this->service && $this->service->isMobile())
            || $this->isMobile()) {
            return self::BROWSER_PLATFORM_MOBILE;
        } elseif (($this->auth_token && $this->auth_token->isSync())
            || ($this->service && $this->service->isSync())
            || $this->isWebviewSync()) {
            return self::BROWSER_PLATFORM_SYNC;
        } else {
            return self::BROWSER_PLATFORM_WEB;
        }
    }

    /**
     * 获得 device type
     *
     * @return string
     */
    public function getDeviceType(): string
    {
        if ($this->isMacDesktop()) {
            $device_type = ClientDevice::MAC_DESKTOP_NAME;
        } else if ($this->isWinDesktop()) {
            $device_type = ClientDevice::WIN_DESKTOP_NAME;
        } else if ($this->isIosClientMobile()) {
            $device_type = ClientDevice::IOS_APP_NAME;
        } else if ($this->isAndroidClientMobile()) {
            $device_type = ClientDevice::ANDROID_APP_NAME;
        } else {
            $device_type = $this->getWebDeviceType();
        }

        return $device_type;
    }

    /**
     * 是否来自网页端
     *
     * @return bool
     */
    public function isWebBrowser()
    {
        return !in_array($this->getDeviceType(),
            [
                ClientDevice::MAC_DESKTOP_NAME,
                ClientDevice::WIN_DESKTOP_NAME,
                ClientDevice::IOS_APP_NAME,
                ClientDevice::ANDROID_APP_NAME,
            ]);
    }

    /**
     * 是否来自 sync desktop 中的 mac webview
     * @return bool
     */
    public function isMacDesktop(): bool
    {
        return ($this->isWebviewSync() && $this->match('mac'))
            || ($this->auth_token !== null && $this->auth_token->isMacDesktop())
            || ($this->service && $this->service->isMacDesktop());
    }

    /**
     * 是否来自 sync desktop 中的 windows webview
     * @return bool
     */
    public function isWinDesktop(): bool
    {
        return ($this->isWebviewSync() && !$this->isMacDesktop())
            || ($this->auth_token !== null && $this->auth_token->isWinDesktop())
            || ($this->service && $this->service->isWindowsDesktop());
    }

    /**
     * 是否来自 sync desktop 中的 webview
     *
     * @return bool
     */
    public function isWebviewSync(): bool
    {
        return $this->match('FangCloud|python|QtWebEngine|Electron');
    }

    public function isSameSiteNoneIncompatible(): bool
    {
        $agent = $_SERVER;
        if(array_key_exists('HTTP_USER_AGENT', $agent)) {
            $agent = $agent['HTTP_USER_AGENT'];
            if (preg_match("/FangCloud|python|QtWebEngine/i", $agent)) {
                return true;
            }
            return $this->hasWebKitSameSiteBug($agent) || $this->dropsUnrecognizedSameSiteCookies($agent);
        } else {
            return false;
        }
    }

    public function hasWebKitSameSiteBug($agent)
    {
        return $this->isIosVersion(12, $agent) ||
            ($this->isMacosxVersion(10, 14, $agent) &&
                ($this->isSafari($agent) || $this->isMacEmbeddedBrowser($agent)));
    }

    public function isIosVersion($major, $agent)
    {
        if (preg_match('/\(iP.+; CPU .*OS (\d+)[_\d]*.*\) AppleWebKit\//i', $agent, $regs)) {
            $browser_ver = $regs[1];
            return $major == intval($browser_ver);
        }
        return false;
    }

    public function isMacosxVersion($major, $minor, $agent)
    {
        if (preg_match('/\(Macintosh;.*Mac OS X (\d+)_(\d+)[_\d]*.*\) AppleWebKit\//i', $agent, $regs)) {
            $browser_ver = $regs[1];
            $browser_ver1 = $regs[2];
            return $major == intval($browser_ver) && $minor == intval($browser_ver1);
        }
        return false;
    }

    public function isSafari($agent)
    {
        if (preg_match("/Version\/.* Safari\//i", $agent) && !$this->isChromiumBased($agent)) {
            return true;
        }
        return false;
    }

    public function isChromiumBased($agent)
    {
        if (preg_match("/Chrom(e|ium)/i", $agent)) {
            return true;
        }
        return false;
    }

    public function isMacEmbeddedBrowser($agent)
    {
        if (preg_match("/^Mozilla\/[\.\d]+ \(Macintosh;.*Mac OS X [_\d]+\) AppleWebKit\/[\.\d]+ \(KHTML, like Gecko\)$/i", $agent)) {
            return true;
        }
        return false;
    }

    public function dropsUnrecognizedSameSiteCookies($agent)
    {
        if ($this->isUcBrowser($agent)) {
            return !$this->isUcBrowserVersionAtLeast(12, 13, 2, $agent);
        }
        return $this->isChromiumBased($agent) && $this->isChromiumVersionAtLeast(51, $agent) && !$this->isChromiumVersionAtLeast(67, $agent);
    }

    public function isUcBrowser($agent)
    {
        if (preg_match("/UCBrowser\//i", $agent)) {
            return true;
        }
        return false;
    }

    public function isUcBrowserVersionAtLeast($major, $minor, $build, $agent)
    {
        if (preg_match('/UCBrowser\/(\d+)\.(\d+)\.(\d+)[\.\d]*/i', $agent, $regs)) {
            $major_version = $regs[1];
            $minor_version = $regs[2];
            $build_version = $regs[3];
            if ($major != $major_version) {
                return intval($major_version) > $major;
            }
            if ($minor != $minor_version) {
                return intval($minor_version) > $minor;
            }
            return intval($build_version) > $build;
        }
        return true;
    }

    public function isChromiumVersionAtLeast($major, $agent)
    {
        if (preg_match('/Chrom[^\/]+\/(\d+)[\.\d]*/i', $agent, $regs)) {
            $major_version = $regs[1];
            return intval($major_version) > $major;
        }
        return true;
    }

    /**
     * 是否来自亿方云移动端
     *
     * @return bool
     */
    public function isClientMobile(): bool
    {
        return ($this->auth_token !== null && $this->auth_token->isMobile())
            || ($this->service && $this->service->isMobile());

    }

    /**
     * 是否来自亿方云 iOS 移动端
     *
     * @return bool
     */
    public function isIosClientMobile(): bool
    {
        return ($this->auth_token !== null && $this->auth_token->isIos())
            || ($this->service && $this->service->isIos());

    }

    /**
     * 是否来自亿方云 Android 移动端
     *
     * @return bool
     */
    public function isAndroidClientMobile(): bool
    {
        return ($this->auth_token !== null && $this->auth_token->isAndroid())
            || ($this->service && $this->service->isAndroid());

    }

    /**
     * 获取 web 端在 client_devices 表中存储的 device_type
     *
     * @return string
     */
    public function getWebDeviceType(): string
    {
        return $this->platform() . ' ' . $this->browser();
    }

    /**
     * 传给服务时，日志中需要的 service_name
     * @see \App\Remote\CommonService
     */
    public function getServiceName()
    {
        if ($this->auth_token) {
            if ($this->auth_token->isAndroid()) {
                return 'android';
            } else if ($this->auth_token->isIos()) {
                return 'ios';
            } else if ($this->auth_token->isWinDesktop()) {
                return 'windows sync';
            } else if ($this->auth_token->isMacDesktop()) {
                return 'mac sync';
            }
        }
        if ($this->service) {
            if ($this->service->isAndroid()) {
                return 'android';
            } else if ($this->service->isIos()) {
                return 'ios';
            } else if ($this->service->isWindowsDesktop()) {
                return 'windows sync';
            } else if ($this->service->isMacDesktop()) {
                return 'mac sync';
            }
        }

        return 'web';
    }

    /**
     * 传给服务时，日志中需要的 service_id
     * @see \App\Remote\CommonService
     */
    public function getServiceId()
    {
        if ($this->auth_token) {
            return $this->auth_token->service_id;
        }
        if ($this->service) {
            return $this->service->id;
        }
        // 0 for web
        return (int)config('common.service.web_service_id');
    }
}