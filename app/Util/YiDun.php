<?php

namespace App\Util;

use App\Constants\ErrorCode;
use App\Exceptions\ExternalException;

class YiDun
{
    public static function check($accessToken, $token)
    {
        if (config('app.skip_yidun_verify')) {
            return true;
        }

        $result = static::request($accessToken, $token);
        if ($result["code"] == 200) {
            $data = $result["data"];
            $phone = $data["phone"];
            if(!empty($phone)){
                // 取号成功, 执行登录等流程
                return $phone;
            }
        }
        return null;
    }

    public static function genSignature($secretKey, $params)
    {
        ksort($params);
        $buff = "";
        foreach ($params as $key => $value) {
            if ($value !== null) {
                $buff .= $key;
                $buff .= $value;
            }
        }
        $buff .= $secretKey;
        return md5($buff);
    }

    /**
     * 将输入数据的编码统一转换成utf8
     * @params 输入的参数
     */
    public static function toUtf8($params)
    {
        $utf8s = array();
        foreach ($params as $key => $value) {
            $utf8s[$key] = is_string($value) ? mb_convert_encoding($value, "utf8", "auto") : $value;
        }
        return $utf8s;
    }

    public static function request($accessToken, $token)
    {
        $params = array(
            // 运营商预取号获取到的token
            "accessToken" => $accessToken,
            // 易盾返回的token
            "token" => $token
        );
        $params["secretId"] = config('app.yidun_secret_id');
        $params["businessId"] = config('app.yidun_business_id');
        $params["version"] = "v1";
        $params["timestamp"] = sprintf("%d", round(microtime(true) * 1000));
        // time in milliseconds
        $params["nonce"] = substr(md5(time()), 0, 32);
        // random int
        $params = static::toUtf8($params);
        $params["signature"] = static::genSignature(config('app.yidun_secret_key'), $params);
        $options = array('http' => array(
            'header' => "Content-type: application/x-www-form-urlencoded\r\n",
            'method' => 'POST',
            'timeout' => 5,
            // read timeout in seconds
            'content' => http_build_query($params)
        ));

        LogHelper::info(LogType::WebAndServerService, 'yidun param = '.json_encode($options));
        $context = stream_context_create($options);
        $result = file_get_contents(config('app.yidun_url'), false, $context);
        LogHelper::info(LogType::WebAndServerService, 'yidun result = '.json_encode($result));

        if ($result === FALSE) {
            throw new ExternalException(ErrorCode::YIDUN_CHECK_INVALID);
        } else {
            return json_decode($result, true);
        }
    }
}
