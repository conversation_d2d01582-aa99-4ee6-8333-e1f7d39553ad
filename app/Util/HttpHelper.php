<?php

namespace App\Util;

use Illuminate\Support\Facades\Cookie;

class HttpHelper
{
    public static function addQuery($original_uri, $query_key, $query_value)
    {
        $new_url = strpos($original_uri, '?') !== FALSE ? $original_uri . "&{$query_key}={$query_value}" : $original_uri . "?{$query_key}={$query_value}";
        return $new_url;
    }

    public static function disableCache()
    {
        header("Cache-Control: no-store, no-cache, must-revalidate, max-age=0");
        header("Expires: Thu, 01 Jan 1970 00:00:00 GMT");
    }

    public static function setLanguage($lang)
    {
        $cookie_name = config('app.language_cookie_name');
        $expires = config('app.lang_cookie_expire');
        Cookie::queue($cookie_name, $lang, $expires / 60);
    }

    public static function getLanguage()
    {
        $cookie_name = config('app.language_cookie_name');
        return Cookie::get($cookie_name);
    }

    public static function setClientLanguageEnabled()
    {
        $cookie_name = config('app.client_lang_cookie_name');
        $expires = config('app.lang_cookie_expire');
        Cookie::queue($cookie_name, true, $expires / 60);
    }

    public static function isClientLanguageEnabled()
    {
        $cookie_name = config('app.client_lang_cookie_name');
        return Cookie::get($cookie_name, false);
    }

    public static function setClientInfo($client_info)
    {
        $cookie_name = config('app.egeio_client_info_cookie_name');
        $expires = config('app.lang_cookie_expire');
        Cookie::queue($cookie_name, $client_info, $expires / 60);
    }

    public static function setSource($source)
    {
        Cookie::queue(Cookie::forever('source', $source));
    }

    public static function setSem($sem)
    {
        $cookie_name = config('app.sem_cookie_name');
        $expires = config('app.sem_cookie_expire');
        Cookie::queue($cookie_name, $sem, $expires / 60);
    }

    public static function setRegisterFrom($from)
    {
        $cookie_name = config('app.register_origin_cookie_name');
        $expires = config('app.register_origin_cookie_expire');
        Cookie::queue($cookie_name, $from, $expires / 60);
    }
}