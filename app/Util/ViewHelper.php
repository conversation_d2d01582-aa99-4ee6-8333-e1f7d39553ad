<?php
namespace App\Util;

use App\Remote\Chameleon;
use Illuminate\Support\HtmlString;

class ViewHelper
{

    private static $assets_hashmap = null;
    private static $_cachedDomainConfig = [];

    public static function static_url($uri = '')
    {
        return config('app.static_url');
    }

    public static function asset_url()
    {
        if(config('app.dev_asset_url'))
        {
            return config('app.dev_asset_url');
        }
        else
        {
            return config('app.asset_path');
        }
    }

    public static function get_assets_hashmap(){
        if (is_null(self::$assets_hashmap))
        {
            $hashmap_file = config('app.asset_path') . config('app.assets_hash');

            if(file_exists($hashmap_file))
            {
                self::$assets_hashmap = json_decode(file_get_contents($hashmap_file), true);
            }
            else
            {
                self::$assets_hashmap = array();
            }
        }
        return self::$assets_hashmap;
    }

    public static function asset_new($file)
    {
        if(!config('app.dev_asset_url'))
        {
            if(Self::get_assets_hashmap()) {
                $hash = Self::get_assets_hashmap()[$file];
                if (isset($hash))
                {
                    $dotpos = strrpos($file, '.');
                    $file = substr($file, 0, $dotpos) . '_' . $hash . substr($file, $dotpos - strlen($file));
                }
            }
        }

        return static::static_url() . config('app.asset_path') . $file;
    }

    public static function asset_m($file)
    {

        if(config('app.dev_asset_url'))
        {
            return config('app.dev_asset_url') . $file;
        }
        else
        {
            return self::asset_new($file);
        }
    }

    public static function is_dev_env()
    {
        return config('app.env') === 'dev';
    }

    public static function is_sync_client($platform)
    {
        // $headers = get_all_headers();
        // return $headers['sync-venus-os-info'];
        if($platform == 'Electron')
        {
            return preg_match("/electron/i", $_SERVER['HTTP_USER_AGENT']);
        }

        if($platform == 'WebKit')
        {
            return preg_match("/FangCloud|python/i", $_SERVER['HTTP_USER_AGENT']);
        }

        if($platform == 'WebEngine')
        {
            return preg_match("/QtWebEngine/i", $_SERVER['HTTP_USER_AGENT']);
        }

        if($platform == 'Electron') {
            return preg_match("/Electron/i", $_SERVER['HTTP_USER_AGENT']);
        }
    }

    public static function fstate_field()
    {
        $state = preg_replace("/[&<>\"'`]/", "", Context::getFState());
        return new HtmlString('<input type="hidden" name="_fstate" value="'.$state.'">');
    }

    public static function routeWithState($route_name, $params = [], $absolute = true)
    {
        $state = Context::getFState();
        $params['_fstate'] = $state;
        return route($route_name, $params, $absolute);
    }

    public static function getDomainCustomResource($productId, $key, $lang = null) {
        if ($lang === null) {
            $lang = config('app.locale');
        }
        if (!isset(static::$_cachedDomainConfig[$productId])) {
            static::$_cachedDomainConfig[$productId] = Chameleon::get_domain_config($productId);
        }
        $domainConfig = static::$_cachedDomainConfig[$productId];
        return $domainConfig['domain_config']['dynamic_i18ns'][$key][$lang] ?? null;
    }

    public static function logoURI($logo_type, $lang = null)
    {
        if ($lang === null) {
            $lang = config('app.locale');
        }

        $logo_key = md5(config('sso.' . $logo_type . '_' . $lang));
        $logo_uri = config('app.fangcloud_url') . "apps/logos/download?logo_type=". $logo_type."&lang=". $lang ."&logo_key=". $logo_key;
        return $logo_uri;
    }

    public static function getProductName($lang = null)
    {
        if ($lang === null) {
            $lang = config('app.locale');
        }

        return config('sso.product_name_' . $lang);
    }

    public static function getDownloadLink($link_type)
    {
        return config('sso.' . $link_type);
    }

    public static function getWindowsDownloadLink() {
        return config('sso.nc_windows_download_link') ?? config('sso.windows_download_link');
    }

    public static function getMacDownloadLink() {
        return config('sso.mac_special_download_link') ?? config('sso.mac_download_link');
    }

    public static function isFangcloud()
    {
        return (bool)config('sso.is_fangcloud');
    }

    public static function env_is_profession()
    {
        return (bool)config('app.is_profession');
    }

    public static function get_env_config($name)
    {
        return config('app.env_config')['config'][$name];
    }

    public static function baidu_tongji($sideId='')
    {
        if(!config('app.analytic_enable')) {
            return '';
        }

        $out = '<script async>' . "\xA";
        $out .= 'var _hmt = _hmt || [];' . "\xA";
        $out .= '(function() {' . "\xA";
        $out .= 'var hm = document.createElement("script");' . "\xA";
        $out .= 'hm.src = "https://hm.baidu.com/hm.js?' . $sideId . '";' . "\xA";
        $out .= 'var s = document.getElementsByTagName("script")[0]; ' . "\xA";
        $out .= 's.parentNode.insertBefore(hm, s);' . "\xA";
        $out .= '})();' . "\xA";
        $out .= '</script>' . "\xA";

        $out .= '<script async>' . "\xA";
        $out .= 'var _hmt = _hmt || [];' . "\xA";
        $out .= '(function() {' . "\xA";
        $out .= 'var hm = document.createElement("script");' . "\xA";
        $out .= 'hm.src = "https://hm.baidu.com/hm.js?' . "8e27a678ff57ff111ef18e175dbb117b" . '";' . "\xA";
        $out .= 'var s = document.getElementsByTagName("script")[0]; ' . "\xA";
        $out .= 's.parentNode.insertBefore(hm, s);' . "\xA";
        $out .= '})();' . "\xA";
        $out .= '</script>' . "\xA";

        // qdas
        $out .= '<script async src="https://s5.ssl.qhres2.com/!c2c090e2/monitor_analytic.js"></script><script></script>' . "\xA";
        $out .= '<script type="text/javascript">' . "\xA";
        $out .= '(function(){' . "\xA";
            $out .= 'var tj = document.createElement("script");' . "\xA";
            $out .= 'tj.type = "text/javascript";' . "\xA";
            $out .= 'tj.src = "https://s5.ssl.qhres2.com/!c2c090e2/monitor_analytic.js";' . "\xA";
            $out .= 'tj.onload = function() {' . "\xA";
                $out .= 'monitor.setProject("QH_388_5").getTrack().getClickAndKeydown().getClickHeatmap(10, 1);' . "\xA";
            $out .= '};' . "\xA";
            $out .= 'var s = document.getElementsByTagName("script")[0];' . "\xA";
            $out .= 's.parentNode.insertBefore(tj, s);' . "\xA";
        $out .= '})();' . "\xA";
        $out .= '</script>' . "\xA";

        // 百度OCPC
        $out .= '<script type="text/javascript" async>' . "\xA";
        $out .= 'window._agl = window._agl||[];' . "\xA";
        $out .= '(function(){' . "\xA";
            $out .= '_agl.push(["production", "_f7L2XwGXjyszb4d1e2oxPybgD"]);' . "\xA";
            $out .= '(function(){' . "\xA";
                $out .= 'var agl = document.createElement("script");' . "\xA";
                $out .= 'agl.type = "text/javascript";' . "\xA";
                $out .= 'agl.async = true;' . "\xA";
                $out .= 'agl.src = "https://fxgate.baidu.com/angelia/fcagl.js?production=_f7L2XwGXjyszb4d1e2oxPybgD";' . "\xA";
                $out .= 'var s = document.getElementsByTagName("script")[0];' . "\xA";
                $out .= 's.parentNode.insertBefore(agl,s);' . "\xA";
            $out .= '})();' . "\xA";
        $out .= '})();' . "\xA";
        $out .= '</script>' . "\xA";

        // 头条
        $out .= '<script type="text/javascript">;' . "\xA";
        $out .= '(function(r,d,s){r._tt_config=true;var _baq=(r._baq=r._baq||[]);_baq.methods=["track","off","on"];' . "\xA";
        $out .= '_baq.factory=function(method){return function(){var args=Array.prototype.slice.call(arguments);' . "\xA";
        $out .= 'args.unshift(method);_baq.push(args);return _baq}};for(var i=0;i<_baq.methods.length;i++){' . "\xA";
        $out .= 'var key=_baq.methods[i];_baq[key]=_baq.factory(key)}_baq.load=function(){' . "\xA";
        $out .= 'var js,fjs=d.getElementsByTagName(s)[0];js=d.createElement(s);' . "\xA";
        $out .= 'js.src="https://analytics.oceanengine.com/api/v2/js/sdk";fjs.parentNode.insertBefore(js,fjs)};' . "\xA";
        $out .= '_baq.load();if(_baq.invoked){return}_baq.invoked=true;_baq.track("pageview")})(window,document,"script");' . "\xA";
        $out .= '</script>' . "\xA";

        // linkedIn
        $out .= '<script type="text/javascript"> _linkedin_partner_id = "3500826"; window._linkedin_data_partner_ids = window._linkedin_data_partner_ids || []; window._linkedin_data_partner_ids.push(_linkedin_partner_id); </script><script type="text/javascript"> (function(l) { if (!l){window.lintrk = function(a,b){window.lintrk.q.push([a,b])}; window.lintrk.q=[]} var s = document.getElementsByTagName("script")[0]; var b = document.createElement("script"); b.type = "text/javascript";b.async = true; b.src = "https://snap.licdn.com/li.lms-analytics/insight.min.js"; s.parentNode.insertBefore(b, s);})(window.lintrk); </script> <noscript> <img height="1" width="1" style="display:none;" alt="" src="https://px.ads.linkedin.com/collect/?pid=3500826&fmt=gif" /> </noscript>' . "\xA";

        // 360OCPC
        // $out .= '<script type="text/javascript async>' . "\xA";
        //     $out .= '(function(b,a,e,h,f,c,g,s){' . "\xA";
        //         $out .= 'b[h]=b[h]||function(){' . "\xA";
        //             $out .= '(b[h].c=b[h].c||[]).push(arguments)' . "\xA";
        //         $out .= '};' . "\xA";
        //         $out .= 'b[h].s=!!c;' . "\xA";
        //         $out .= 'g=a.getElementsByTagName(e)[0];' . "\xA";
        //         $out .= 's=a.createElement(e);' . "\xA";
        //         $out .= 's.src="//s.union.360.cn/"+f+".js";' . "\xA";
        //         $out .= 's.defer=!0;' . "\xA";
        //         $out .= 's.async=!0;' . "\xA";
        //         $out .= 'g.parentNode.insertBefore(s,g)' . "\xA";
        //     $out .= '})(window,document,"script","_qha",467296,false);' . "\xA";
        // $out .= '</script>' . "\xA";

        // 搜狗OCPC
        $out .= '<script type="text/javascript async">' . "\xA";
            $out .= '(function(w,d,t,s,q,m,n){' . "\xA";
                $out .= 'if(w.sguic)return;' . "\xA";
                $out .= 'q=w.sguic=function(){' . "\xA";
                    $out .= 'q.process?q.process.apply(null,arguments):q.queue.push(arguments);' . "\xA";
                $out .= '};' . "\xA";
                $out .= 'q.queue=[];' . "\xA";
                $out .= 'm=d.getElementsByTagName(t)[0];' . "\xA";
                $out .= 'n=d.createElement(t);' . "\xA";
                $out .= 'n.src=s;' . "\xA";
                $out .= 'n.async=true;' . "\xA";
                $out .= 'm.parentNode.insertBefore(n,m);' . "\xA";
            $out .= '})(window,document,“script”,“//jstatic.sogoucdn.com/sdk/uic-pub.js”);' . "\xA";
        $out .= '</script>' . "\xA";

        // 广点通OCPC
        $out .= '<script>' . "\xA";
            $out .= '!function(g,d,t,e,v,n,s){' . "\xA";
                $out .= 'if(g.gdt)return;' . "\xA";
                $out .= 'v=g.gdt=function(){' . "\xA";
                    $out .= 'v.tk?v.tk.apply(v,arguments):' . "\xA";
                    $out .= 'v.queue.push(arguments)' . "\xA";
                $out .= '};' . "\xA";
                $out .= 'v.sv="1.0";' . "\xA";
                $out .= 'v.bt=0;' . "\xA";
                $out .= 'v.queue=[];' . "\xA";
                $out .= 'n=d.createElement(t);' . "\xA";
                $out .= 'n.async=!0;' . "\xA";
                $out .= 'n.src=e;' . "\xA";
                $out .= 's=d.getElementsByTagName(t)[0];' . "\xA";
                $out .= 's.parentNode.insertBefore(n,s);' . "\xA";
            $out .= '}(window,document,"script","//qzonestyle.gtimg.cn/qzone/biz/gdt/dmp/user-action/gdtevent.min.js");' . "\xA";
            $out .= 'gdt("init","1200532573");' . "\xA";
            $out .= 'gdt("track","PAGE_VIEW");' . "\xA";
        $out .= '</script>' . "\xA";
        $out .= '<noscript>' . "\xA";
            $out .= '<img height="1" width="1" style="display:none" src="https://a.gdt.qq.com/pixel?user_action_set_id=1200532573&action_type=PAGE_VIEW&noscript=1"/>' . "\xA";
        $out .= '</noscript>' . "\xA";

        // 卫瓴willing
        $out .= '<script type="text/javascript">' . "\xA";
        $out .= '(function(){' . "\xA";
            $out .= 'let wl = document.createElement("script");' . "\xA";
            $out .= 'wl.type = "text/javascript";' . "\xA";
            $out .= 'wl.src = "https://material.weiling.cn/h5/willing-third-party-js/v1/register-comp-v1.min.js";' . "\xA";
            $out .= 'wl.id = "wlWXREG";' . "\xA";
            $out .= 'wl.onload = function() {' . "\xA";
                $out .= 'wlRegisterComp.init({' . "\xA";
                    $out .= 'wl_id: "ww4942374d9cd4a2f0",' . "\xA";
                    $out .= 'is_encrypt: true' . "\xA";
                    // $out .= 'wl_abbrev_name: ""' . "\xA";
                $out .= '});' . "\xA";
            $out .= '};' . "\xA";
            $out .= 'if (!document.getElementById("wlWXREG")) {' . "\xA";
                $out .= 'let s = document.getElementsByTagName("script")[0];' . "\xA";
                $out .= 's.parentNode.insertBefore(wl, s);' . "\xA";
            $out .= '}' . "\xA";
        $out .= '})();' . "\xA";
        $out .= '</script>' . "\xA";

        return $out;
    }
    public static function copyright() {

        $out =  '<div class="copyright" style="position: fixed; left: 0; bottom: 0; width: 100%; text-align: center;padding-bottom: 10px;background:#fff;display:none;">' . "\xA";
        if (config('sso.is_fangcloud')) {
            $out .= '<p style="margin-bottom: 10px;">' . "\xA";
            $out .= '<a style="color:#4a4a4a;" href="https://www.fangcloud.com/addons/cms/sitemap/index/type/all.xml" title="网站地图 |" target="_blank">网站地图 |</a>' . "\xA";
            $out .= '<a style="color:#4a4a4a;" href="https://www.fangcloud.com/cmshome/service.html" title="服务条款 |" target="_blank">服务条款 |</a>' . "\xA";
            $out .= '<a style="color:#4a4a4a;" href="https://www.fangcloud.com/cmshome/service_sla.html" title="SLA协议" target="_blank">SLA协议</a>' . "\xA";
            $out .= ' </p>' . "\xA";
        }

        //变色龙配置过版权信息
        if (!empty(config('sso.copyright_name')) && !empty(config('sso.filing_name'))) {
            $out .= ' <p style="margin-bottom: 10px;">Copyright © '. config('sso.copyright_name') .' ｜<a target="_blank" href="http://beian.miit.gov.cn" style="color:#079cda;" rel="nofollow">'. config('sso.filing_name') .'</a>' . "\xA";
            if (!empty(config('sso.netan_name')) && !empty(config('sso.netan_link')) ) {
                $out .= ' ｜                            <a target="_blank" href="' . config('sso.netan_link') . '" class="beian" style="color:#079cda;">' . "\xA";
                $out .= '           <img src="https://staticgw.fangcloud.com/images/hybrid/beian_d0289dc.png" style="vertical-align: middle;">' . "\xA";
                $out .= '            ' . config('sso.netan_name') . '</a>' . "\xA";
            }
            $out .= ' ｜                            <a target="_blank" href="'. config('sso.business_license_link') .'" style="color:#079cda;">                   工商营业执照                 </a>' . "\xA";
            $out .= '    </p>' . "\xA";
        } else {
            $out .= ' <p style="margin-bottom: 10px;">Copyright © 杭州三六零亿方智能有限公司 ｜<a target="_blank" href="http://beian.miit.gov.cn" style="color:#079cda;" rel="nofollow">浙ICP备20012079号-3</a> ｜' . "\xA";
            $out .= '                            <a target="_blank" href="http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=33011002015048" class="beian" style="color:#079cda;">' . "\xA";
            $out .= '           <img src="https://staticgw.fangcloud.com/images/hybrid/beian_d0289dc.png" style="vertical-align: middle;">' . "\xA";
            $out .= '            浙公网安备 33011002015048号</a> ｜' . "\xA";
            $out .= '                            <a target="_blank" href="https://www.fangcloud.com/cmshome/gsyyzz.html" style="color:#079cda;">                   工商营业执照                 </a>' . "\xA";
            $out .= '    </p>' . "\xA";
        }
        $out .= '</div><style>@media screen and (max-height: 620px) {.copyright { position: static !important;margin-top: 30px;}}.login-box~.copyright{display:block!important;}</style>' . "\xA";
        return $out;
    }

    public static function isExternalLink() {
        return isset($_GET['is_external']) ? $_GET['is_external'] : null;
    }

}
