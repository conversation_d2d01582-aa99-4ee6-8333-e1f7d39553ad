<?php
namespace App\Util;

use App\Constants\Common as Constants;
use Illuminate\Support\Str;

class ArrayHelper
{
    public static function indexObjectsByField($property, $objects)
    {
        $results = [];
        foreach ($objects as $object) {
            $results[$object->$property] = $object;
        }
        return $results;
    }

    public static function indexArraysByField($property, $arrays)
    {
        $results = [];
        foreach ($arrays as $arr) {
            $results[$arr[$property]] = $arr;
        }
        return $results;
    }

    public static function fetchPropertyFromArray($property, $arrays)
    {
        $result = [];
        foreach ($arrays as $array) {
            $result[] = $array[$property];
        }
        return $result;
    }

    public static function fetchPropertyFromObject($property, $objects)
    {
        $result = [];
        foreach ($objects as $object) {
            $result[] = $object->$property;
        }
        return $result;
    }
}
