<?php
namespace App\Util;

use App\Constants\Common;
use App\Constants\ErrorCode;
use App\Exceptions\ExternalException;
use App\Jobs\SendEmail;
use App\Library\Janitor\OneDayJanitor;

class Email
{
    const FROM_NAME = 'fangcloud';
    const FROM = '<EMAIL>';

    // 主题
    const SUBJECT_CAPTCHA = 'subject_captcha';
    const SUBJECT_REGISTER_VALIDATION = 'subject_register_validation';
    const SUBJECT_RESET_PASSWORD = 'subject_reset_password';
    const SUBJECT_ENTERPRISE_EDITION_REGISTERED = 'subject_enterprise_edition_registered';
    const SUBJECT_CUSTOM_ENTERPRISE_EDITION_REGISTERED = 'subject_custom_enterprise_edition_registered';

    // 模板
    const TYPE_CAPTCHA = 'email/captcha';
    const TYPE_REGISTER = 'email/register';
    const TYPE_RESET_PASSWORD = 'email/reset_password';
    const TYPE_REGISTER_ENTERPRISE_EDITION = 'email/register_enterprise_edition_notification_to_egeio';
    const TYPE_REGISTER_CUSTOM_ENTERPRISE_EDITION = 'email/register_custom_enterprise_edition_notification_to_egeio';

    const DAILY_LIMIT_KEY = 'email_daily_limit';
    const DAILY_LIMIT_CNT = 20;

    public static function sendCaptcha($captcha, $email, $lang = null, $product_id = null)
    {
        $type = self::TYPE_CAPTCHA;
        $params = [
            'captcha' => $captcha
        ];
        $subject = trans('email.'.self::SUBJECT_CAPTCHA);
        self::send($email, $type, $subject, $params, $lang, $product_id);
    }

    public static function send($email, $type, $subject, $params, $lang = null, $product_id = null)
    {
        // 发给销售的不限制
        $to = config('mail.custom_sales_email');
        if (!in_array(trim($email), $to)) {
            $janitor = static::getDailyJanitor(trim($email));
            if ($janitor->isFrozen()) {
                throw new ExternalException(ErrorCode::EMAIL_EXCEED_LIMIT, "email");
            }
            $janitor->hit();
        }

        $lang = $lang ?: config('app.locale');
        $product_id = $product_id ?: CommonHelper::getProductId();

        $email_meta_info['fromname'] = trans('email.'.self::FROM_NAME);
        $email_meta_info['from'] = getenv('PLUGIN_SENDCLOUD_FROM') ? getenv('PLUGIN_SENDCLOUD_FROM') : self::FROM;
        $email_meta_info['to'] = $email;
        $email_meta_info['subject'] = $subject;

        $params['lang'] = $lang;

        $email_content_info['type'] = $type;
        $email_content_info['params'] = $params;

        $email_info[] = [
            'email_meta_info' => json_encode($email_meta_info),
            'email_content_info' => json_encode($email_content_info),
            'lang' => $lang
        ];
        $base_api_url = config('app.api_url');
        $job_specs['callback'] = $base_api_url . '/internal_api/jobs/send_email_job';
        $job_specs['callback_service_id'] = 1;
        $job_specs['additional_infos'] = $email_info;
        $job_specs['headers'] []= [Common::HEADER_PRODUCT_ID => $product_id]; // 产品id, 区分sms的标签
        $job_specs['job_count'] = count($job_specs['additional_infos']);

        $email_send_job = new SendEmail();
        $email_send_job->setBody($job_specs);
        $email_send_job->send();
    }

    public static function isPicCaptchaRequired($captcha_type, $phone = null)
    {
        if(in_array($captcha_type, self::$captcha_types_no_need_pic_captcha))
        {
            return false;
        }
        if($phone) {
            $cached_captcha = Captcha::getCachedValidateCaptchaCode($captcha_type, Captcha::THROUGH_SMS, $phone);
            if($cached_captcha){
                return false;
            }
        }
        // TODO
        return ord(substr($phone, -1)) % 2 == 0;
    }

    private static function getDailyJanitor($identifier)
    {
        return (new OneDayJanitor(static::DAILY_LIMIT_KEY."_".$identifier))->setThresholdCount(static::DAILY_LIMIT_CNT);
    }
}
