<?php

namespace App\Util;

use App\Constants\Common;
use App\Jobs\SendSms;
use App\Library\Janitor\OneDayJanitor;
use App\Library\Janitor\RateWithFreezeJanitor;

class Sms
{
    const MESSAGE_TXT   = 'txt';
    const MESSAGE_VOICE = 'voice';

    const TYPE_CAPTCHA      = 1; // 验证码
    const TYPE_NOTIFICATION = 2; // 验证码外其他如 邀请链接、通知等

    const SAME_SENDING_COUNT_INCREMENT_PERIOD = 1800;
    const SAME_MAX_SENDING_COUNT              = 5; // 对同一个IP发送验证码的最大次数
    const SAME_FREEZE_PERIOD                  = 7200;

    const REGISTER_SAME_MAX_SENDING_COUNT     = 1;
    const REGISTER_SAME_FREEZE_PERIOD         = 14400;

    const REGISTER_SMS_SIGN_APP_ID = '3ada220dcc804a0bb74060ca39abc862';  // 针对注册短信被刷签名专用appId

    const FORGOT_PASSWORD_BY_PHONE_SIGN_APP_ID = '1bc1e7345f9f44a3a51b512a64acb58c';  // 针对注册短信被刷签名专用appId

    const BECOME_REFERRAL_SIGN_APP_ID = '680859a041914f2684941aa92cb32b4d';

    const PUBLIC_SIGNATURE_VERITY_APP_ID = '85fe58e8429c4892990c5ca7e8111499';

    const QUICK_LOGIN_SIGN_APP_ID = '49ab7a20075946a3bf0015985c4271ef';

    const REGISTER_SMS_MAX_TIME_DIFF = 300; // currentTime - signTime > 300 5分钟前后判断为刷子，正常情况下前端每次签名都是当前时间

    public static $captcha_types_need_ip_check = [
        Captcha::TYPE_REGISTER,
    ];

//    public static $captcha_types_no_need_pic_captcha = [
//        Captcha::TYPE_ACTIVATE,
//        Captcha::TYPE_ACTIVATE_BY_PHONE,
//        Captcha::TYPE_FORGOT_PASSWORD_BY_PHONE,
//        Captcha::TYPE_MODIFY_PHONE,
//        Captcha::TYPE_TWO_STEP,
//        Captcha::TYPE_TWO_STEP_LOGIN,
//    ];

    const DAILY_LIMIT_KEY = 'sms_daily_limit';
    const DAILY_LIMIT_CNT = 500;

    public static function sendCaptcha(
        $captcha,
        $captcha_type,
        $message_type,
        $phone,
        $ip,
        $lang,
        $product_id = null,
        $captcha_identifier = null
    ) {
        $janitor = static::getDailyJanitor();
        $janitor->hit();

        if ($captcha_identifier) {
            if (is_array($captcha_identifier)) {
                foreach ($captcha_identifier as $ident) {
                    $freezeJanitor = static::getFreezeJanitor($ident,$captcha_type);
                    $freezeJanitor->hit();
                }
            } else {
                $freezeJanitor = static::getFreezeJanitor($captcha_identifier,$captcha_type);
                $freezeJanitor->hit();
            }
        }

        $captcha_details = [
            'captcha_code' => $captcha, // 验证码的内容
            'captcha_type' => $captcha_type, // 验证码的类型
            'message_type' => $message_type, // 消息的类型
        ];
        $additional_info = [
            'sms_details' => json_encode($captcha_details),
            'sms_type' => self::TYPE_CAPTCHA, // sms类型为验证码sms
            'phone' => $phone,
            'ip' => $ip,
            'lang' => $lang, // 语言
            'user_id' => Context::user() ? Context::user()->user_id : null
        ];
        self::deliverySmsSendJob($additional_info, $product_id);
    }

    public static function sendRegister(
        $phone,
        $context,
        $lang
    ) {
        $content = [
            'pass' => $context,
        ];
        $notification_details = [
            'notification_type' => "notification_quick_register", // 类型
            'content' => $content, // 内容
        ];
        $additional_info = [
            'sms_details' => json_encode($notification_details),
            'sms_type' => self::TYPE_NOTIFICATION, // sms类型为验证码sms
            'phone' => $phone,
            'ip' => "0.0.0.0",
            'lang' => $lang, // 语言
        ];
        self::deliverySmsSendJob($additional_info);
    }

    private static function deliverySmsSendJob($additional_info, $product_id = null)
    {
        $base_api_url = config('app.api_url');
        $job_specs['callback'] = $base_api_url . '/internal_api/jobs/sms_send_job';
        $job_specs['callback_service_id'] = 1;
        $job_specs['additional_infos'] [] = $additional_info;
        $job_specs['headers'] [] = array(Common::HEADER_PRODUCT_ID => $product_id); // 产品id, 区分sms的标签
        $job_specs['job_count'] = count($job_specs['additional_infos']);
        $sms_send_job = new SendSms();
        $sms_send_job->setBody($job_specs);
        $sms_send_job->send();
    }

    public static function isPicCaptchaRequired($identifier)
    {
        if ($identifier) {
            if (is_array($identifier)) {
                foreach ($identifier as $ident) {
                    if($ident && self::checkPicCaptchaLimit($ident)) {
                        return true;
                    }
                }
                return false;
            } else {
                return self::checkPicCaptchaLimit($identifier);
            }
        }
        return static::hasExceedDayLimit();
//        if($phone) {
//            $cached_captcha = Captcha::getCachedValidateCaptchaCode($captcha_type, Captcha::THROUGH_SMS, $phone);
//            if($cached_captcha){
//                return false;
//            }
//        }
    }

    public static function registerPicCaptchaRequired($identifier)
    {
        if ($identifier) {
            if (is_array($identifier)) {
                foreach ($identifier as $ident) {
                    if($ident && self::checkRegisterPicCaptchaLimit($ident)) {
                        return true;
                    }
                }
                return false;
            } else {
                return self::checkRegisterPicCaptchaLimit($identifier);
            }
        }
        return static::hasExceedDayLimit();
    }

    public static function checkPicCaptchaLimit($identifier = null)
    {
        if (!$identifier) {
            return true;
        }
        $janitor = static::getFreezeJanitor($identifier, null);
        return $janitor->isFrozen();
    }


    public static function hasExceedDayLimit()
    {
        $janitor = static::getDailyJanitor();
        return $janitor->isFrozen();
    }

    private static function getDailyJanitor()
    {
        return (new OneDayJanitor(static::DAILY_LIMIT_KEY))->setThresholdCount(static::DAILY_LIMIT_CNT);
    }

    private static function getFreezeJanitor($identifier, $captcha_type)
    {
        $same_max_send_count = static::SAME_MAX_SENDING_COUNT;
        $same_freeze_period = static::SAME_FREEZE_PERIOD;
        if ($captcha_type && $captcha_type == Captcha::TYPE_REGISTER)
        {
            $same_max_send_count = self::REGISTER_SAME_MAX_SENDING_COUNT;
            $same_freeze_period = self::REGISTER_SAME_FREEZE_PERIOD;
        }
        return (new RateWithFreezeJanitor())->setIdentifier('captcha_sms_' . $identifier)
            ->setThresholdTime(static::SAME_SENDING_COUNT_INCREMENT_PERIOD)
            ->setThresholdCount($same_max_send_count)
            ->setFreezeTime($same_freeze_period);
    }

}
