<?php

namespace App\Util;

use App\Constants\CacheKey;
use App\Constants\Common as CommonConstants;
use App\Constants\Http;
use App\Models\User;
use App\Remote\Enterprise;

class Context
{
    use ContextAuthTrait;

    public static $supported_languages = [
        CommonConstants::LANGUAGE_EN,
        CommonConstants::LANGUAGE_ZH_CN,
        CommonConstants::LANGUAGE_ZH_TW,
    ];

    /**
     * @var \App\Remote\Enterprise $enterprise
     */
    protected static $enterprise = null;

    public static function setEnterprise(Enterprise $enterprise)
    {
        self::$enterprise = $enterprise;
    }

    /**
     * @return Enterprise
     */
    public static function getEnterprise()
    {
        return self::$enterprise;
    }

    public static function enterprise()
    {
        return self::getEnterprise();
    }

    protected static $user = null;

    public static function setUser(User $user)
    {
        self::$user = $user;
    }

    /**
     * @return User|null
     */
    public static function getUser()
    {
        return self::$user;
    }

    protected static $device_token = null;

    public static function getDeviceToken()
    {
        return self::$device_token;
    }

    public static function setDeviceToken($device_token)
    {
        self::$device_token = $device_token;
    }

    protected static $auth_token = null;

    public static function setAuthToken($auth_token)
    {
        self::$auth_token = $auth_token;
    }

    public static function getAuthToken()
    {
        return self::$auth_token;
    }

    private static $context = null;

    public static function isApiContext()
    {
        return self::$context === 'api';
    }

    public static function isWebContext()
    {
        return self::$context === 'web';
    }

    public static function setApiContext()
    {
        self::$context = 'api';
    }

    public static function setWebContext()
    {
        self::$context = 'web';
    }

    // 设计之初 希望放在session中。但是登录流程有些问题，二次验证时，进行了 user login + logout 操作，\
    // 导致登录前 session id 发生变化，故暂时放在redis中。
    // 其实是存在 redis 中。但是因为设计使用上存在于context中。故放在了context中。
    private static $fstate = null;

    public static function setFState($state)
    {
        self::$fstate = $state;
    }

    public static function getFState()
    {
        return self::$fstate ?: SecurityHelper::base32(12);
    }

    private static $fstate_content = null;

    /**
     * @param array $content
     * query  =>  默认 web query 参数
     * headers => 默认 web headers。 这个需要手动 set，但是会自动加载
     * _context => 发生上下文切换时的暂存状态。 @see \App\Http\Controllers\LoginTrait function doLogin
     * _login => 初始化密码时的上下文
     *
     * 其他的不要和这个key冲突
     * @param bool $array_replace
     * @return bool
     */
    public static function setFStateContent(array $content, $array_replace = true)
    {
        if (self::$fstate === null) {
            return false;
        }

        if ($array_replace) {
            $content = array_replace(self::$fstate_content ?? [], $content);
        }

        CacheHelper::cacheInfo(CacheKey::FSTATE . self::$fstate, $content, config('session.lifetime') * 60);
        self::$fstate_content = $content;

        return true;
    }

    public static function getFStateContent($key = null, $default = null)
    {
        $content = null;
        if (self::$fstate_content) {
            $content = self::$fstate_content;
        } else {
            $fstate = self::$fstate;
            if ($fstate === null) {
                $content = null;
            } else {
                $content = CacheHelper::getCachedInfo(CacheKey::FSTATE . $fstate);
                self::$fstate_content = $content;
            }
        }

        if ($key) {
            return $content[$key] ?? $default;
        } else {
            return $content;
        }
    }

    public static function clearFStateContent()
    {
        if (self::$fstate === null) {
            return ;
        }

        CacheHelper::clearCachedInfo(CacheKey::FSTATE . self::$fstate);
    }

    public static function setLanguage($lang)
    {
        app()->setLocale($lang);
    }

    public static function getLanguage()
    {
        return config('app.locale');
    }

    protected static $source = '';

    public static function setSource($source)
    {
        static::$source = $source;
    }

    public static function getSource()
    {
        return static::$source;
    }

    protected static $egeio_client_info = [];

    public static function getEgeioClientInfo($key = null)
    {
        if(!static::$egeio_client_info) {
            $client_info = request()->header(Http::HEADER_EGEIO_CLIENT_INFO)
                ?: request()->query('egeio-client-info');
            static::$egeio_client_info = @json_decode($client_info, true);
            // 兼容移动端传过来的egeio-client-info 被urlencode两遍的问题
            if(!static::$egeio_client_info) {
                static::$egeio_client_info = @json_decode(@urldecode($client_info), true);
            }
        }
        if($key) {
            return static::$egeio_client_info[$key] ?? null;
        }
        return static::$egeio_client_info;
    }
}