<?php

namespace App\Util;

class SecurityHelper
{

    const PRIVATE_KEY = '***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************';

    const PUBLIC_KEY = '-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAvikNrllO99UrXzzbJ2sY
Cv37fFeXXVgdTY3C/enrJtzkVWPGyzNiB38Ry0yaHnWDli5A+3H0UuK1OZUdr3Yo
VijJTYVrtJcAu3qZa2DwpicpQasTCaPHmwPdlGv29DyS3MAlUWi7Y0ScVpHItrn/
ORvqWs08cY/u8RolHwgZH4kAlpyC1JaToivWkmtcqMnmwf2STyvL/5nNujALIs4+
xsy1Jb8sYCPLQXAOHxNsKKrYzCvsh97F2eIh5SjBHsbbSXAAmiNfgyPTWIlLjDaI
nKKbyjmjJ/6BctY00xGjIvKRKvWOH+9Xz1wt2kZbbfh2m4r1PS6B6+n6+Trn650T
AQIDAQAB
-----END PUBLIC KEY-----';

    public static function base32($length = 16, $uppercase = true)
    {
        $lowers = 'abcdefghijklmnopqrstuvwxyz234567';
        $uppers = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567';
        $chars = $uppercase ? $uppers : $lowers;

        $result = '';
        $bytes = unpack('C*', openssl_random_pseudo_bytes($length));
        foreach($bytes as $byte)
        {
            $result .= $chars[$byte % $length];
        }
        return $result;
    }

    /*-----------------------------  公钥加密, 私钥解密 --------------------------------------*/
    /*
     * RSA公钥加密
     * 使用私钥解密
     */
    public static function rsaEncodePublic($aString) {
        $publicKey = openssl_pkey_get_public(self::PUBLIC_KEY);//这个函数可用来判断公钥是否是可用的
        openssl_public_encrypt($aString, $encrypted, $publicKey);//公钥加密，私钥解密
        $encrypted = base64_encode($encrypted);//加密后的内容通常含有特殊字符，需要编码转换下，在网络间通过url传输时要注意base64编码是否是url安全的
        return $encrypted;
    }
    /*
     * RSA私钥解密
     * 有可能传过来的aString是经过base64加密的，则传来前需先base64_decode()解密
     * 返回未经base64加密的字符串
     */
    public static function rsaDecodePrivate($aString) {
        $privateKey = openssl_pkey_get_private(self::PRIVATE_KEY);//这个函数可用来判断私钥是否是可用的
        openssl_private_decrypt(base64_decode($aString), $decrypted, $privateKey);//公钥加密，私钥解密
        return $decrypted;
    }

    /**
     * 使用加密字段替换用户输入的敏感信息
     * @param array $param 用户输入参数
     */
    public static function secureParamDecode(&$param) {
        if (empty($param['secure_zone'])) {
            return;
        }
        $secureJson = self::rsaDecodePrivate($param['secure_zone']);
        if (empty($secureJson)) {
            return;
        }
        $secureParam = json_decode($secureJson, true);
        if (empty($secureParam)) {
            return;
        }
        foreach ($secureParam as $key => $value) {
            $param[$key] = $value;
        }
        unset($param['secure_zone']);
    }

    public static function phoneSecurity($phone){
        return substr_replace($phone,'****',3,4);
    }

    public static function emailSecurity($email){

        $count = strcspn($email, "@");
        $count_star = "";
        if ($count > 4) {
            for ($x = 0; $x < $count - 4; $x++) {
                $count_star .= "*";
            }
            return substr_replace($email, $count_star, 2, $count - 4);
        }

        for ($x = 0; $x < $count - 1; $x++) {
            $count_star .= "*";
        }
        return substr_replace($email, $count_star, 1, $count - 1);

    }
}