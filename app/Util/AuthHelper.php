<?php
namespace App\Util;

use App\Library\Janitor\BaseJanitor;
use App\Library\Janitor\OneDayJanitor;
use App\Library\Janitor\RateWithFreezeJanitor;
use App\Library\Janitor\RateWithoutFreezeJanitor;

class AuthHelper
{
    const LOGIN_IP_LIMIT_KEY = 'login_ip_limit_key_%s';

    const REGISTER_IP_LIMIT_KEY = 'register_ip_limit_key_%s';
    const LOGIN_IP_LIMIT_CNT = 20;
    const LOGIN_IDENTIFIER_LIMIT_KEY = 'login_identifier_limit_key_%s';
    const LOGIN_IDENTIFIER_LIMIT_CNT = 3;
    const LOGIN_LIMIT_TIME = 7200;

    const REGISTER_LIMIT_TIME = 3600 * 24;

    // 因为底层判断是>cnt,业务逻辑是先判断后记录，所以实际限制为3的时候cnt应该为2
    const REGISTER_IP_LIMIT_CNT = 2;
    const LOGIN_PASSWORD_ERROR_LIMIT_KEY = 'password_error_limit_key_user_%s';
    const LOGIN_PASSWORD_ERROR_LIMIT_PERIOD = 600;
    const LOGIN_PASSWORD_ERROR_LIMIT_CNT = 5;
    const LOGIN_PASSWORD_ERROR_LIMIT_TIME = 900;

    public static function getSessionId()
    {
        return session()->getId();
    }

    public static function isLoginPicCaptchaRequired($identifier, $ip)
    {
        return static::hasExceedLimitWithSameIdentifier($identifier) || static::hasExceedLimitWithSameIp($ip);
    }

    private static function getIdentifierLimitKey($identifier)
    {
        return sprintf(static::LOGIN_IDENTIFIER_LIMIT_KEY, md5($identifier));
    }

    private static function getIpLimitKey($ip)
    {
        return sprintf(static::LOGIN_IP_LIMIT_KEY, md5($ip));
    }

    private static function getRegisterIpLimitKey($ip)
    {
        return sprintf(static::REGISTER_IP_LIMIT_KEY, md5($ip));
    }

    private static function getPasswordErrorLimitKey($identifier)
    {
        return sprintf(static::LOGIN_PASSWORD_ERROR_LIMIT_KEY, $identifier);
    }

    public static function recordLogin($identifier, $ip)
    {
        $identifier_janitor = static::getIdentifierJanitor($identifier);
        $identifier_janitor->hit();

        $ip_janitor = static::getIpJanitor($ip);
        $ip_janitor->hit();
    }

    public static function clearLoginRecord($identifier, $ip, $id)
    {
        $identifier_janitor = static::getIdentifierJanitor($identifier);
        $identifier_janitor->clear();

        $ip_janitor = static::getIpJanitor($ip);
        $ip_janitor->clear();

        $password_janitor = static::getPasswordErrorJanitor($id);
        $password_janitor->clear();
    }

    private static function hasExceedLimitWithSameIdentifier($identifier)
    {
        $janitor = static::getIdentifierJanitor($identifier);
        return $janitor->isFrozen();
    }

    private static function hasExceedLimitWithSameIp($ip)
    {
        $janitor = static::getIpJanitor($ip);
        return $janitor->isFrozen();
    }

    public static function hasExceedRegisterLimitWithSameIp($ip)
    {
        $janitor = static::getRegisterIpJanitor($ip);
        return $janitor->isFrozen();
    }

    private static function getIdentifierJanitor($identifier)
    {
        return (new RateWithoutFreezeJanitor(static::getIdentifierLimitKey($identifier)))
            ->setThresholdTime(static::LOGIN_LIMIT_TIME)
            ->setThresholdCount(static::LOGIN_IDENTIFIER_LIMIT_CNT);
    }

    private static function getIpJanitor($ip)
    {
        return (new RateWithoutFreezeJanitor(static::getIpLimitKey($ip)))
            ->setThresholdTime(static::LOGIN_LIMIT_TIME)
            ->setThresholdCount(static::LOGIN_IP_LIMIT_CNT);
    }

    private static function getRegisterIpJanitor($ip)
    {
        return (new OneDayJanitor(static::getRegisterIpLimitKey($ip)))
            ->setThresholdCount(static::REGISTER_IP_LIMIT_CNT);
    }

    public static function recordRegisterIp($ip)
    {
        $identifier_janitor = static::getRegisterIpJanitor($ip);
        return $identifier_janitor->hit();
    }

    private static function getPasswordErrorJanitor($identifier)
    {
        return (new RateWithFreezeJanitor())->setIdentifier(static::getPasswordErrorLimitKey($identifier))
            ->setThresholdTime(static::LOGIN_PASSWORD_ERROR_LIMIT_PERIOD)
            ->setThresholdCount(static::LOGIN_PASSWORD_ERROR_LIMIT_CNT)
            ->setFreezeTime(static::LOGIN_PASSWORD_ERROR_LIMIT_TIME);
    }

    public static function isLoginPasswordErrorLimit($identifier)
    {
        return static::hasExceedLimitWithLoginPasswordErrorIdentifier($identifier);
    }

    private static function hasExceedLimitWithLoginPasswordErrorIdentifier($identifier)
    {
        $janitor = static::getPasswordErrorJanitor($identifier);
        return $janitor->isFrozen();
    }

    public static function recordPasswordError($identifier)
    {
        $identifier_janitor = static::getPasswordErrorJanitor($identifier);
        return count($identifier_janitor->hit());
    }
}
