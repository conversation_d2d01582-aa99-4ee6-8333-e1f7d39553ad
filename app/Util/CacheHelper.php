<?php
namespace App\Util;

use App\Constants\CacheKey;
use App\Models\User;
use Illuminate\Cache\RateLimiter;
use Illuminate\Support\Facades\Auth;

class CacheHelper
{
    protected const SESSION_CACHE_KEY_PREFIX = 'cached_session_info_';

    public static function getCachedInfo($key)
    {
        return cache($key) ?: null;
    }

    public static function cacheInfo($key, $info, $expiration = 600)
    {
        cache([$key => $info], $expiration / 60);
    }

    public static function clearCachedInfo($key)
    {
        cache()->forget($key);
    }

    public static function increment($key, $value = 1)
    {
        return cache()->increment($key, $value);
    }

    public static function getCachedSessionInfo($type)
    {
        return cache(self::getCacheKey($type)) ?: null;
    }

    public static function cacheSessionInfo($type, $info, $expiration = 600)
    {
        cache([self::getCacheKey($type) => $info], $expiration / 60);
    }

    public static function clearCachedSessionInfo($type)
    {
        cache()->forget(self::getCacheKey($type));
    }

    private static function getCacheKey($type)
    {
        return self::SESSION_CACHE_KEY_PREFIX . AuthHelper::getSessionId() . '_' . $type;
    }

    public static function setThirdOAuthUserInfo($info)
    {
        static::cacheSessionInfo('ThirdOAuthUserInfo', $info);
    }

    public static function getThirdOAuthUserInfo()
    {
        return static::getCachedSessionInfo('ThirdOAuthUserInfo');
    }

    /**
     * OAuth State
     * @param $state
     * @param array $context
     */
    public static function setOAuthState($state, array $context)
    {
        static::cacheSessionInfo('OAuth_' . $state, $context);
    }

    public static function getOAuthState($state)
    {
        return static::getCachedSessionInfo('OAuth_' . $state);
    }

    public static function unsetOAuthState($state)
    {
        static::clearCachedSessionInfo('OAuth_' . $state);
    }

    public static function getGuardSecret($server_id, $client_id)
    {
        return static::getCachedInfo(CacheKey::GUARD_SECRET . $server_id . '_' . $client_id);
    }

    public static function setGuardSecret($server_id, $client_id, $secret)
    {
        $expiration = config('services.guard.secret_expiration');
        static::cacheInfo(CacheKey::GUARD_SECRET . $server_id . '_' . $client_id, $secret, $expiration);
    }

}
