<?php

namespace App\Util;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Session;
use Monolog\Formatter\LogstashFormatter;
use Monolog\Processor\MemoryPeakUsageProcessor;
use Monolog\Processor\MemoryUsageProcessor;
use Illuminate\Support\Facades\Request;

class LogHelper
{
    private static function getLogContext()
    {
//        $user_id = '';
//        $user = Context::user();
//        if ($user) {
//            $user_id = $user->user_id;
//        }

        $request = Request::instance();

        $context = [
            'ip' => CommonHelper::getClientIp(),
            'product_id' => CommonHelper::getProductId(),
            'session_id' => Session::getId(),
            'request_uri' => $request->server('REQUEST_URI', null),
            'request_id' => $request->attributes->get('X-Request-ID', uniqid()),
        ];

        return $context;
    }

    public static function serializeException(\Exception $exception)
    {
        $code = $exception->getCode();
        $type = get_class($exception);
        $msg = $exception->getMessage();
        $file = $exception->getFile();
        $line = $exception->getLine();
        $trace_string = $exception->getTraceAsString();
        $max = config('app.log_exception_trace_chars');

        return [
            'code' => $code,
            'type' => $type,
            'message' => $msg,
            'file' => $file,
            'line' => $line,
            'trace_string' => str_replace("\n", ' ', substr($trace_string, 0, $max)),
        ];
    }

    private static $init = false;

    private static function init()
    {
        if (!self::$init) {
            $monolog = Log::getMonolog();
            $handlers = $monolog->getHandlers();

            $format = "%datetime% %level_name% %message% %context% %extra%\n";
            $formatter = new \Monolog\Formatter\LineFormatter($format, 'Y-m-d H:i:s.u', true, true);

            foreach($handlers as $handler) {
                $handler->setFormatter($formatter);
            }

            $monolog->pushProcessor(new MemoryPeakUsageProcessor());
            $monolog->pushProcessor(new MemoryUsageProcessor());
        }
        self::$init = true;
    }

    public static function debug($type, $message, $context = [])
    {
        $context['context'] = static::getLogContext();
        $context['type'] = $type;
        self::init();
        try {
            Log::debug($message, $context);
        }
        catch (\Exception $e) {
        }
    }

    public static function info($type, $message, $context = [])
    {
        $context['context'] = static::getLogContext();
        $context['type'] = $type;
        self::init();
        try {
            Log::info($message, $context);
        }
        catch (\Exception $e) {
        }
    }

    public static function warning($type, $message, $context = [])
    {
        $context['context'] = static::getLogContext();
        $context['type'] = $type;
        self::init();
        try {
            Log::warning($message, $context);
        }
        catch (\Exception $e) {
        }
    }

    public static function error($type, $message, $context = [])
    {
        $context['context'] = static::getLogContext();
        $context['type'] = $type;
        self::init();
        try {
            Log::error($message, $context);
        }
        catch (\Exception $e) {
        }
    }

    public static function critical($type, $message, $context = [])
    {
        $context['context'] = static::getLogContext();
        $context['type'] = $type;
        self::init();
        try {
            Log::critical($message, $context);
        }
        catch (\Exception $e) {
        }
    }
}