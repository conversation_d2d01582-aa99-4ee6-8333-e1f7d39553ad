<?php
namespace App\Util;

class TwoStepLogHelper
{
    const FILE_PATH = '/var/log/application/two_step.log';

    public static function writeLog($msg)
    {
        $filePath = self::FILE_PATH;

        $message  = '';

        if ( ! $fp = @fopen($filePath, 'ab')) {
            return FALSE;
        }

        if(!$filePath OR !is_file($filePath) OR !is_writable($filePath))
        {
            return FALSE;
        }
        $server_ip = getenv('SERVER_ADDR');
        $request_id = self::get_request_id();
        $request_msg = "server_ip: $server_ip; request_id: $request_id";
        $message .= sprintf("[%s] [%s] [%s] [%s] [%s] [%s]\n", date('Y-m-d H:i:s'), 'two_step', 'INFO', __FUNCTION__, $msg, $request_msg);
        flock($fp, LOCK_EX);
        fwrite($fp, $message);
        flock($fp, LOCK_UN);
        fclose($fp);

        @chmod($filePath, 0666);
    }

    private static function get_request_id()
    {
        // 生成 REQUEST_ID 现在放在 PutEnvironmentConfig 了
        $request_id = getenv('REQUEST_ID');
        if ($request_id)
        {
            return $request_id;
        }
        else
        {
            // 没有就随机生成一个
            $random_string = sha1(openssl_random_pseudo_bytes(32));
            // 放进 env，便于下次提取
            putenv("REQUEST_ID=$random_string");
            return $random_string;
        }
    }
}