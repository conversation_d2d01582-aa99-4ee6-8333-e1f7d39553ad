<?php
namespace App\Util;
use App\Remote\AliyunCaptchaVerity;
use App\Remote\CheckCaptchaSwitch;
use Illuminate\Support\Facades\Cache;
use App\Remote\QiHooCaptchaVerity;

class Captcha
{
    const TYPE_REGISTER = 'register'; // 注册
    const TYPE_LOGIN = 'login'; // 登录
    const TYPE_INVITE_LINK_REGISTER = 'invite_link_register'; // 注册
    const TYPE_FORGOT_PASSWORD = 'forgot_password'; // 重置密码(输入账号时的图片验证码)
    const TYPE_FORGOT_PASSWORD_BY_PHONE = 'forgot_password_by_phone'; // 重置密码(用手机号忘记密码，短信验证码验证手机号)
    const TYPE_ACTIVATE = 'activate'; // 验证
    const TYPE_ACTIVATE_BY_PHONE = 'activate_by_phone'; // 验证
    const TYPE_MODIFY_PHONE = 'modify_phone'; // 修改手机号
    const TYPE_TWO_STEP = 'two_step'; //二次验证
    const TYPE_TWO_STEP_LOGIN = "two_step_login"; //二次验证登录
    const TYPE_BECOME_REFERRAL = "become_referral"; //成为推荐官

    const THROUGH_PIC = 'pic';
    const THROUGH_SMS = 'sms';
    const THROUGH_EMAIL = 'email';

    const GLUE = '_';
    const KEY_CAPTCHA_CODE = 'captcha_code';
    const KEY_CAPTCHA_PHONE_SENDING_COUNT = 'phone_sending_count';
    const KEY_CAPTCHA_PHONE_FREEZE = 'phone_freeze';
    const KEY_CAPTCHA_IP_SENDING_COUNT = 'ip_sending_count';
    const KEY_CAPTCHA_IP_FREEZE = 'ip_freeze';
    const KEY_PIC_CAPTCHA_IS_REQUIRED = 'pic_captcha_is_required';
    const KEY_ONE_DAY_SENDING_COUNT = 'one_day_sending_count';
    const KEY_ONE_DAY_SENDING_TOTAL_COUNT = 'sms_one_day_sending_total_count';

    const CAPTCHA_LEN_4 = 4;
    const CAPTCHA_LEN_6 = 6;
    const DEFAULT_CAPTCHA_LEN = self::CAPTCHA_LEN_4;

    const CAPTCHA_EXPIRATION_10 = 10;
    const CAPTCHA_EXPIRATION_5 = 5;
    const DEFAULT_CAPTCHA_EXPIRATION = self::CAPTCHA_EXPIRATION_10;

    const SAME_CAPTCHA_MAX_TRY_TIME = 5;

    protected static $captcha_type_length = [
        self::TYPE_TWO_STEP => self::CAPTCHA_LEN_4,
        self::TYPE_TWO_STEP_LOGIN => self::CAPTCHA_LEN_6,
    ];

    protected static $captcha_type_expiration = [
        self::TYPE_TWO_STEP => self::CAPTCHA_EXPIRATION_5,
        self::TYPE_TWO_STEP_LOGIN => self::CAPTCHA_EXPIRATION_5,
    ];

    public static function generateCaptcha($type, $through = self::THROUGH_PIC, $identifier = null)
    {
        $captcha = '';
        if ($identifier) {
            $captcha = self::getCachedValidateCaptchaCode($type, $through, $identifier);
        }

        if (!$captcha) {
            $length = self::getCaptchaLength($type, $through);
            if($through == self::THROUGH_SMS) {
                $captcha = self::generateNumericCode($length);
            }
            else {
                $captcha = self::generateAlphabetNumericCode($length);
            }
        }
        self::store($captcha, $type, $through, $identifier);
        return $captcha;
    }

    public static function getCachedCaptcha($type, $through, $identifier = null)
    {
        return cache(self::getCaptchaKey($type, $through, $identifier));
    }

    public static function getCachedValidateCaptchaCode($type, $through, $identifier = null)
    {
        $cached = self::getCachedCaptcha($type, $through, $identifier);
        if(!$cached) {
            return null;
        }
        if($cached['expires_at'] < time()) {
            self::clearCachedCaptcha($type, $through, $identifier);
            return null;
        }
        return $cached['captcha'];
    }

    public static function clearCachedCaptcha($type, $through, $identifier = null)
    {
        Cache::forget(self::getCaptchaKey($type, $through, $identifier));
    }

    protected static function getCaptchaKey($type, $through, $identifier = null)
    {
        return implode(self::GLUE, [$identifier ? md5($identifier) : AuthHelper::getSessionId(), $type, $through, self::KEY_CAPTCHA_CODE]);
    }

    public static function generateNumericCode($length)
    {
        $captcha_code = "";
        while ($length--)
        {
            $captcha_code .= random_int(0, 9);
        }
        return $captcha_code;
    }

    public static function generateAlphabetNumericCode($length)
    {
        $optional_chars = 'ABCDEFGHJKLMNPQRSTUVWXY3456789'; // 过滤了易混淆的字符：I O Z 0 1 2
        $optional_chars_len = strlen($optional_chars);
        $captcha_code = "";
        while ($length--)
        {
            $captcha_code .= $optional_chars[mt_rand(0, $optional_chars_len - 1)];
        }
        return $captcha_code;
    }

    public static function store($captcha, $type, $through, $identifier = null)
    {
        $expiration = static::getCaptchaExpiration($type, $through);
        cache([self::getCaptchaKey($type, $through, $identifier) => [
            'captcha' => $captcha,
            'try_time' => 0,
            'expires_at' => time() + $expiration * 60,
        ]], $expiration);
    }

    protected static function getCaptchaExpiration($type, $through)
    {
        return self::$captcha_type_expiration[$type] ?? self::DEFAULT_CAPTCHA_EXPIRATION;
    }

    protected static function getCaptchaLength($type, $through)
    {
        return self::$captcha_type_length[$type] ?? self::DEFAULT_CAPTCHA_LEN;
    }

    public static function check($captcha, $type, $through = self::THROUGH_PIC, $identifier = null)
    {
        if(config('app.skip_captcha_verify')) {
            return true;
        }
        $cached_captcha = self::getCachedCaptcha($type, $through, $identifier);
        if(!$cached_captcha) {
            return false;
        }
        if($cached_captcha['expires_at'] < time()) {
            self::clearCachedCaptcha($type, $through, $identifier);
            return false;
        }
        if(strtolower($captcha) == strtolower($cached_captcha['captcha'])) {
            self::clearCachedCaptcha($type, $through, $identifier);
            return true;
        }

        if($through == self::THROUGH_SMS) {
            $cached_captcha['try_time'] += 1;
            if($cached_captcha['try_time'] >= self::SAME_CAPTCHA_MAX_TRY_TIME){
                self::clearCachedCaptcha($type, $through, $identifier);
            }
            else {
                $expiration = static::getCaptchaExpiration($type, $through);
                cache([self::getCaptchaKey($type, $through, $identifier) => $cached_captcha], $expiration);
            }
        }
        return false;
    }

    public static function checkAliyunCaptcha($requestId): bool
    {
        return AliyunCaptchaVerity::checkCaptcha($requestId);
    }

    public static function increaseTryTime($captcha, $type, $through, $identifier = null)
    {
        $expiration = static::getCaptchaExpiration($type, $through);
        cache([self::getCaptchaKey($type, $through, $identifier) => [
            'captcha' => $captcha,
            'try_time' => 0,
        ]], $expiration);
    }

    public static function checkQiHooCaptcha($token, $vd): bool
    {
        return QiHooCaptchaVerity::checkCaptcha($token, $vd);
    }

    /**
     * 使用集团验证码校验方式，不使用之前SDK
     */
    public static function needQiHooCaptcha($type): bool
    {
        $allowType = [Captcha::TYPE_REGISTER];
        return in_array($type, $allowType);
    }

    /**
     * 检查滑动验证码开关，默认true、降级false
     */
    public static function checkCaptchaSwitch(): bool
    {
        return CheckCaptchaSwitch::checkCaptchaSwitch();
    }
}
