<?php

namespace App\Util;

use App\Constants\CacheKey;
use App\Constants\Common as Constants;
use App\Constants\Common;
use App\Constants\CookieKey;
use App\Constants\Http;
use App\Services\Security\ClientDeviceRegister;
use Fangcloud\IP\IP;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class CommonHelper
{
    public static $valid_country_codes = ['355', '213', '968', '93', '971', '297', '54', '994', '7', '593', '353', '20',
        '251', '372', '853', '61', '43', '376', '1268', '244', '1264', '1246', '675', '507', '595', '970', '973',
        '1242', '92', '55', '1787', '501', '591', '48', '267', '387', '32', '975', '257', '226', '375', '1441', '229',
        '359', '354', '680', '351', '960', '356', '261', '596', '265', '223', '60', '389', '373', '377', '212', '52',
        '258', '95', '51', '691', '1', '1340', '222', '230', '1664', '976', '880', '298', '33', '689', '594', '63',
        '679', '358', '49', '1767', '1809', '228', '45', '670', '992', '1868', '1649', '90', '216', '993', '66', '886',
        '255', '676', '264', '977', '505', '227', '234', '47', '27', '211', '371', '961', '231', '218', '370', '40',
        '352', '250', '266', '856', '262', '423', '1473', '995', '57', '299', '506', '53', '590', '502', '1671', '592',
        '220', '243', '242', '237', '269', '385', '965', '682', '238', '1345', '254', '7', '31', '599', '509', '382',
        '82', '504', '996', '263', '253', '855', '241', '233', '1', '224', '245', '686', '420', '974', '34', '30',
        '852', '687', '65', '64', '36', '225', '963', '260', '503', '685', '94', '421', '386', '268', '252', '249',
        '597', '677', '381', '357', '221', '232', '248', '235', '350', '56', '850', '236', '240', '966', '508', '378',
        '239', '1758', '1869', '1784', '81', '46', '41', '1876', '374', '967', '39', '91', '62', '964', '98', '972',
        '962', '84', '44', '1284', '678', '598', '256', '380', '998', '58', '673',];


    public static function isValidText($text)
    {
        $content_detection = new \Fangcloud_Composer\Content_Detection\Content_Detection(config('common.content_detection'));
        return $content_detection->is_valid_text($text);
    }

    public static function checkTextArray($array_of_texts)
    {
        $contet_detection = new \Fangcloud_Composer\Content_Detection\Content_Detection(config('common.content_detection'));
        return $contet_detection->get_suggestions_for_text_array($array_of_texts);
    }

    public static function isValidIdentifier($identifier)
    {
        return static::isValidPhoneNumber($identifier) || static::isValidEmail($identifier);
    }

    public static function isValidIdentifierIncludeThird($identifier)
    {
        return static::isValidPhoneNumber($identifier) || static::isValidEmail($identifier) || static::isValidWechat($identifier);
    }

    public static function isValidEmail($email)
    {
        return preg_match("/^([a-z0-9\+_\-]+)(\.[a-z0-9\+_\-]+)*@([a-z0-9\-]+\.)+[a-z]{2,6}$/ix", $email) && Str::length($email) < 100;
    }

    public static function isValidPhoneNumber($phone_number)
    {
        return static::isValidInternalPhone($phone_number) || static::isValidInternationalPhone($phone_number);
    }

    //不是手机也不是邮箱，再来验证是否为企业微信CorpId_UserId
    public static function isValidWechat($wechat_identifier)
    {
        $wechat_identifier = trim($wechat_identifier);
        $sub_wechat_identifier = substr($wechat_identifier, 0, 2);
        $wechat_identifier_explode = explode('_', $wechat_identifier);
        if ($sub_wechat_identifier == 'wx' || $sub_wechat_identifier == 'ww' || $sub_wechat_identifier == 'tj') {
            if(sizeof($wechat_identifier_explode) > 1)
            {
                return true;
            }
        }
        return false;
    }

    public static function isValidInternalPhone($phone_number)
    {
        return (bool)preg_match("/^(1[3456789])\d{9}$/", $phone_number);
    }

    public static function isValidInternationalPhone($phone_number)
    {
        if (!preg_match("/^\(\+(\d{1,5})\)(\d{1,20})$/", $phone_number)) {
            return false;
        }
        list($country_code, $phone) = self::extractCountryCodeAndPhone($phone_number);
        return in_array($country_code, self::$valid_country_codes);
    }

    public static function extractCountryCodeAndPhone($phone): array
    {
        if (self::isValidInternalPhone($phone)) {
            return ['', $phone];
        }

        $matched = preg_match("/^\(\+(\d{1,5})\)(\d{1,20})$/", $phone, $matches);
        if ($matched) {
            return [$matches[1] == Constants::CHINESE_COUNTRY_CODE ? '' : $matches[1], $matches[2]];
        }
        return [null, null];
    }

    public static function concatCountryCodeAndPhone($country_code, $phone, $format = '(+%s)%s')
    {
        return ($country_code == Constants::CHINESE_COUNTRY_CODE) ? $phone : sprintf($format, $country_code, $phone);
    }

    public static function hideIdentifier($identifier)
    {
        if (self::isValidEmail($identifier)) {
            $offset = strpos($identifier, '@');
            $len = ($offset > 4) ? 4 : $offset - 1;
            if (!function_exists('stars')) {
                function stars($num)
                {
                    $res = '';
                    while ($num--) {
                        $res .= '*';
                    }
                    return $res;
                }
            }
            return substr_replace($identifier, stars($len), $offset - $len, $len);
        } elseif (self::isValidInternalPhone($identifier)) {
            return preg_replace('/(\d{3})\d{4}(\d{4})/', '$1****$2', $identifier);
        } elseif (self::isValidInternationalPhone($identifier)) {
            return preg_replace("/^(\(\+\d{1,5}\))(\d{1})(\d{3})(\d*)$/", '$1$2***$4', $identifier);
        } else {
            return $identifier;
        }
    }

    public static function getClientIp()
    {
        // 主站请求过来的自定义header
        if (request()->header('X-Origin-Client-Ip')) {
            $ipaddress = request()->header('X-Origin-Client-Ip');
        } else if (getenv('HTTP_X_LOCAL_IP')) {
            $ipaddress = getenv('HTTP_X_LOCAL_IP');
        } else if (getenv('HTTP_X_REAL_IP')) {
            $ipaddress = getenv('HTTP_X_REAL_IP');
        } else if (getenv('HTTP_CLIENT_IP')) {
            $ipaddress = getenv('HTTP_CLIENT_IP');
        } else if (getenv('HTTP_X_FORWARDED_FOR')) {
            $ipaddress = getenv('HTTP_X_FORWARDED_FOR');
        } else if (getenv('HTTP_X_FORWARDED')) {
            $ipaddress = getenv('HTTP_X_FORWARDED');
        } else if (getenv('HTTP_FORWARDED_FOR')) {
            $ipaddress = getenv('HTTP_FORWARDED_FOR');
        } else if (getenv('HTTP_FORWARDED')) {
            $ipaddress = getenv('HTTP_FORWARDED');
        } else if (getenv('REMOTE_ADDR')) {
            $ipaddress = getenv('REMOTE_ADDR');
        } else {
            $ipaddress = '0.0.0.0';
        }

        return $ipaddress;
    }

    public static function isChineseIp($ip)
    {
        $location = IP::find($ip, 'zh-CN');
        return $location['region'] == '中国'
            || $location['region'] == '台湾'
            || $location['region'] == '香港'
            || $location['region'] == '澳门';
    }

    public static function isChineseIpExcludeSpecialRegion($ip): bool
    {
        $location = IP::find($ip, 'zh-CN');
        return $location['region'] == '中国';
    }

    public static function getFormattedIpLocation($ip, $lang = 'zh-CN')
    {
        if (self::isLanIp($ip)) {
            return trans('constants.LAN_IP', [], $lang);
        }
        $location = IP::find($ip, $lang);
        if ($location['region'] == trans('constants.china', [], $lang) && $location['province']) {
            $formatted_location = $location['province'] . ($location['city'] ? (' ' . $location['city']) : '');
        } else {
            $formatted_location = $location['region'];
            if ($location['province'] && $location['province'] != Common::LOCATION_UNKNOWN) {
                $formatted_location .= ' ' . $location['province'];
            }
            if ($location['city'] && $location['city'] != Common::LOCATION_UNKNOWN) {
                $formatted_location .= ' ' . $location['city'];
            }
        }
        return $formatted_location;
    }

    public static function getClientLocation($lang = 'zh-CN')
    {
        return IP::find(static::getClientIp(), $lang);
    }

    public static function getMaskedPhone($phone, $length = 4)
    {
        $str_length = strlen($phone);
        $length = $str_length > $length ? $length : $str_length;
        return substr($phone, $str_length - $length, $length);
    }

    public static function tryGetDeviceToken(Request $request)
    {
        $device_token = $request->header(Http::HEADER_DEVICE_TOKEN);
        if (!$device_token) {
            $device_token = $request->header(Http::HEADER_DEVICE_ID);
            if (!$device_token) {
                $client_info = $request->query(strtolower(Http::HEADER_EGEIO_CLIENT_INFO)) ?: $request->header(Http::HEADER_EGEIO_CLIENT_INFO);
                $client_info = json_decode(urldecode($client_info), true);
                if (json_last_error() === JSON_ERROR_NONE) {
                    $device_token = $client_info['Device-ID'] ?? null;
                }
            }
        }

        /**
         * 正常 case
         */
        if (!$device_token) {
            $device_token = $request->cookie(CookieKey::DEVICE_TOKEN);
        }
        return $device_token;
    }

    private static $product_id = null;

    public static function getProductId()
    {
        if (self::$product_id) {
            return self::$product_id;
        }
        $product_id = $_SERVER['HTTP_' . str_replace('-', '_', strtoupper(Http::HEADER_PRODUCT_ID))] ?? null;

        if (!$product_id) {
            $product_id = null;
            $file_name = '../chameleon/hosts/' . ($_SERVER['HTTP_HOST'] ?? '');
            if (file_exists($file_name)) {
                $product_id = trim(file_get_contents($file_name));
            }
        }

        // TODO 这个默认的无法写在配置文件里
        // 因为在 LoadEnvironmentVariables 中会用到
        $product_id = $product_id ?: 'fangcloud_v2';
        self::$product_id = $product_id;
        return $product_id;
    }

    /**
     * @warnging 这个函数除了 command 里不应该被调用
     * @param $product_id
     */
    public static function setProductId($product_id)
    {
        self::$product_id = $product_id;
    }

    /**
     * @param null $to
     * @param int $status
     * @param array $headers
     * @param null $secure
     * @return \Illuminate\Http\JsonResponse|\Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public static function ajaxRedirect($to = null, $status = 302, $headers = [], $secure = null)
    {
        $request = request();
        $response = redirect($to, $status, $headers, $secure);
        if ($request->ajax() && $request->acceptsJson()) {
            $redirect_uri = $response->getTargetUrl();
            return response()->json(['success' => false, 'redirect' => $redirect_uri]);
        } else {
            return $response;
        }
    }

    /**
     * @param \Illuminate\Foundation\Application|\Illuminate\Contracts\Foundation\Application $app
     * @return string
     */
    public static function getCachedConfigPath($app)
    {
        $product_id = self::getProductId();
        return $app->bootstrapPath() . '/cache/' . $product_id . '_config.php';
    }

    /**
     * @param \Illuminate\Foundation\Application|\Illuminate\Contracts\Foundation\Application $app
     * @return string
     */
    public static function getCachedProductIdsPath($app)
    {
        return $app->bootstrapPath() . '/cache/product_ids';
    }

    /**
     * @param \Illuminate\Foundation\Application|\Illuminate\Contracts\Foundation\Application $app
     * @return string
     */
    public static function getSSOEnvPath($app, $product_id)
    {
        return $app->bootstrapPath() . '/cache/.sso.' . $product_id . '.env';
    }

    public static function getLoginUrl($params = [])
    {
        return config('app.fangcloud_url') . 'sso/login' . ($params ? '?' . http_build_query($params) : '');
    }

    public static function translateMainSiteErrors($original_errors)
    {
        $original_errors = $original_errors['errors'];
        $errors = $original_errors['validation_errors'] ?? $original_errors['external_errors'];
        $original_error = array_shift($errors);
        $error = [
            'error_code' => $original_error['error_code'],
            'error_msg' => $original_error['error_tr_msg'],
        ];
        return ['errors' => [$error]];
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @param $fstate
     */
    public static function initFstate(&$request, $fstate)
    {
        $arr = CacheHelper::getCachedInfo(CacheKey::FSTATE . $fstate);
        if ($arr && isset($arr['query']) && $arr['query']) {
            $request->query->add($arr['query']);
        }

        // 部分参数可以放在header中
        if ($arr && isset($arr['headers']) && $arr['headers']) {
            foreach ($arr['headers'] as $key => $value) {
                $request->headers->add($arr['headers']);
            }
        }
    }

    /**
     * @throws \App\Exceptions\ValidationException
     * @throws \Exception
     * @throws \Throwable
     */
    public static function registerDevice()
    {
        (new ClientDeviceRegister())->handle([
            'user_agent' => (new UserAgent())->getUserAgent(),
            'device_token' => Context::getDeviceToken(),
        ]);
    }

    /**
     * 判断是否是局域网IP
     * @param $ip
     * @return boolean
     */
    public static function isLanIp($ip)
    {
        return preg_match('/^(127\.0\.0\.1)|(localhost)|(10\.\d{1,3}\.\d{1,3}\.\d{1,3})|(172\.((1[6-9])|(2\d)|(3[01]))\.\d{1,3}\.\d{1,3})|(192\.168\.\d{1,3}\.\d{1,3})$/', $ip) > 0;
    }
}
