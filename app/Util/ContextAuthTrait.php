<?php

namespace App\Util;

use App\Models\User;
use Illuminate\Support\Facades\Auth;

trait ContextAuthTrait
{
    public static function once(User $user)
    {
        Auth::guard('web')->onceUsingId($user->id);
    }

    public static function onceByUserId(int $user_id)
    {
        Auth::guard('web')->onceUsingId(User::findByMainUserId($user_id)->id);
    }

    public static function loginById(int $id)
    {
        Auth::guard('web')->loginUsingId($id);
    }

    public static function loginByUserId(int $user_id)
    {
        Auth::guard('web')->login(User::findByMainUserId($user_id));
    }

    public static function login(User $user)
    {
        Auth::guard('web')->login($user);
    }

    /**
     * 这两个方法所有 guard 都会实现
     */

    /**
     * @return \App\Models\User|null
     */
    public static function user(): ?User
    {
        /**
         * @var \App\Models\User $user
         */
        $user = Auth::guard()->user();
        return $user;
    }

    public static function logout()
    {
        Auth::guard()->logout();
    }
}