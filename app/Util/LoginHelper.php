<?php
namespace App\Util;

use App\Jobs\SendSystemMessage;

class LoginHelper
{
    public static function login_exception_reminder($enterprise, $current_user, $device_name, $ip)
    {
        if (!$enterprise->is_login_exception_reminder_system_enabled)
        {
            return;
        }

        $login_exception_reminder_users = $enterprise->login_exception_reminder_user_list;
        if (empty($login_exception_reminder_users))
        {
            return;
        }
        $base_api_url = config('app.api_url');
        $additional_info['receiver_ids'] = json_encode($login_exception_reminder_users);
        $additional_info['content_code'] = SendSystemMessage::SYSTEM_MESSAGE_LOGIN_EXCEPTION_REMINDER_CONTENT;
        $additional_info['title_code'] = SendSystemMessage::SYSTEM_MESSAGE_LOGIN_EXCEPTION_REMINDER_TITLE;
        $additional_info['url'] = config('app.fangcloud_url').'admin/departments/manage/member/';
        $content_args = [$current_user->name, date("H:i:s A"), $device_name, $ip];
        $additional_info['content_args'] = json_encode($content_args);
        $additional_info['type'] = SendSystemMessage::SYSTEM_MESSAGE_LOGIN_EXCEPTION_REMINDER;

        $job_specs['callback'] = $base_api_url . '/internal_api/jobs/message_system_job';
        $job_specs['callback_service_id'] = 1;
        $job_specs['additional_infos'] [] = $additional_info;
        $job_specs['jobType'] = 'workflow_message_job';
        $job_specs['timestamp'] = time();
        $send_system_message = new SendSystemMessage();
        $send_system_message->setBody($job_specs);
        $send_system_message->send();
    }

    public static function getDeviceName()
    {
        $request = request();
        $egeio_info = $request->header('Egeio-Client-Info');
        if ($egeio_info) {
            $info = json_decode($egeio_info, true);
            $device_name = (json_last_error() === JSON_ERROR_NONE
                ? urldecode($info['deviceName'])
                : null);
            if (!json_encode($device_name)) {
                $device_name = null;
            }
            return $device_name;
        } else {
            return self::getBrowser();
        }
    }

    public static function getBrowser() {
        $request = request();
        $agent = '';
        $x_device_info = $request->header('x-device-info');
        if ($x_device_info) {
            $agent = $x_device_info;
        } else {
            $user_agent = $request->header('user-agent');
            if ($user_agent) {
                $agent = $user_agent;
            }
        }
        $browser = '';
        $browser_ver = '';
        if ($agent) {
            if (preg_match('/Chrome\/([^\s]+)/i', $agent, $regs)) {
                $browser = 'Chrome';
                $browser_ver = $regs[1];
            } else if (preg_match('/OmniWeb\/(v*)([^\s|;]+)/i', $agent, $regs)) {
                $browser = 'OmniWeb';
                $browser_ver = $regs[2];
            } else if (preg_match('/Netscape([\d]*)\/([^\s]+)/i', $agent, $regs)) {
                $browser = 'Netscape';
                $browser_ver = $regs[2];
            } else if (preg_match('/safari\/([^\s]+)/i', $agent, $regs)) {
                $browser = 'Safari';
                $browser_ver = $regs[1];
            } else if (preg_match('/MSIE\s([^\s|;]+)/i', $agent, $regs)) {
                $browser = 'Internet Explorer';
                $browser_ver = $regs[1];
            } else if (preg_match('/Opera[\s|\/]([^\s]+)/i', $agent, $regs)) {
                $browser = 'Opera';
                $browser_ver = $regs[1];
            } else if (preg_match('/NetCaptor\s([^\s|;]+)/i', $agent, $regs)) {
                $browser = '(Internet Explorer ' . $browser_ver . ') NetCaptor';
                $browser_ver = $regs[1];
            } else if (preg_match('/Maxthon/i', $agent, $regs)) {
                $browser = '(Internet Explorer ' . $browser_ver . ') Maxthon';
                $browser_ver = '';
            } else if (preg_match('/360SE/i', $agent, $regs)) {
                $browser = '(Internet Explorer ' . $browser_ver . ') 360SE';
                $browser_ver = '';
            } else if (preg_match('/SE 2.x/i', $agent, $regs)) {
                $browser = '(Internet Explorer ' . $browser_ver . ') 搜狗';
                $browser_ver = '';
            } else if (preg_match('/FireFox\/([^\s]+)/i', $agent, $regs)) {
                $browser = 'FireFox';
                $browser_ver = $regs[1];
            } else if (preg_match('/Lynx\/([^\s]+)/i', $agent, $regs)) {
                $browser = 'Lynx';
                $browser_ver = $regs[1];
            } else if (preg_match('/MicroMessenger\/([^\s]+)/i', $agent, $regs)) {
                $browser = '微信浏览器';
                $browser_ver = $regs[1];
            } else {
                return 'Unknow browser';
            }
            if ($browser != '') {
                return $browser . '/' . $browser_ver;
            }
        } else {
            return $browser;
        }
    }
}
