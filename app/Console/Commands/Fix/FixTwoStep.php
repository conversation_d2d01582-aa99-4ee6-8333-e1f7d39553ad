<?php

namespace App\Console\Commands\Fix;

use App\Library\TwoStep;
use App\Models\User;
use App\Util\SecurityHelper;
use Illuminate\Console\Command;

class FixTwoStep extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'yfy:fix:two_step';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        User::where('settings', '>=', 2)
            ->chunk(10000, function($users) {
            foreach($users as $user) {
                /** @var User $user */
                if ($user->getIsTwoStepEnabledAttr() && $user->getTwoStepMethodAttr() == TwoStep::METHOD_SMS && !$user->getTwoStepSecretAttr()) {
                    $user->setTwoStepSecretAttr(SecurityHelper::base32());
                    $user->save();
                }
            }
        });
    }
}
