<?php

namespace App\Console\Commands;

use App\Util\CommonHelper;
use Illuminate\Console\Command;
use Illuminate\Filesystem\Filesystem;
use Illuminate\Contracts\Console\Kernel as ConsoleKernelContract;

class ConfigClearCommand extends Command
{
    /**
     * The console command name.
     *
     * @var string
     */
    protected $signature = 'yfy:config:clear
    {--d|dir= : chameleon path, default is ../chameleon}
    ';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Remove the configuration cache file';

    /**
     * The filesystem instance.
     *
     * @var \Illuminate\Filesystem\Filesystem
     */
    protected $files;

    /**
     * Create a new config clear command instance.
     *
     * @param  \Illuminate\Filesystem\Filesystem  $files
     * @return void
     */
    public function __construct(Filesystem $files)
    {
        parent::__construct();

        $this->files = $files;
    }

    /**
     * Execute the console command.
     *
     * @return void
     * @throws \Illuminate\Contracts\Filesystem\FileNotFoundException
     */
    public function handle()
    {
        if ($this->option('dir')) {
            $this->callSilent('yfy:chameleon', ['--dir' => $this->option('dir')]);
        } else {
            $this->callSilent('yfy:chameleon');
        }
        $product_ids = $this->files->get(CommonHelper::getCachedProductIdsPath($this->laravel));
        $product_ids = explode("\n", $product_ids);
        foreach($product_ids as $product_id) {
            if (!$product_id) {
                continue;
            }
            CommonHelper::setProductId($product_id);

            $app = require $this->laravel->bootstrapPath().'/app.php';

            $app->make(ConsoleKernelContract::class)->bootstrap();

            $this->files->delete(CommonHelper::getCachedConfigPath($app));
            $this->files->delete($this->laravel->basePath() . '/bootstrap/cache/.sso.' . $product_id . '.env');
        }

        $this->files->delete($this->laravel->getCachedConfigPath());
        $this->files->delete(CommonHelper::getCachedProductIdsPath($this->laravel));

        $this->info('Configuration cache cleared!');
    }
}
