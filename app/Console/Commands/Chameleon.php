<?php

namespace App\Console\Commands;

use App\Util\CommonHelper;
use Illuminate\Console\Command;
use Illuminate\Filesystem\Filesystem;

class <PERSON>meleon extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'yfy:chameleon
    {--d|dir= : chameleon path, default is ../chameleon}
    ';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'format chameleon config jsons to env files';

    protected $files;

    /**
     * Create a new command instance.
     *
     * @param Filesystem $files
     */
    public function __construct(Filesystem $files)
    {
        parent::__construct();

        $this->files = $files;
    }

    protected $available_keys = [
        'host',
        'product_id',
        'resource_id',
        'base_url',
        'account_url',
        'account_static_url',
        'host_url',
        'app_url',
        'ios_download_link',
        'android_download_link',
        'mac_download_link',
        'windows_download_link',
        'windows_xp_download_link',
        'referrer_url',
        'helper_center_url',
        'android_lowest_version',
        'ios_lowest_version',
        'mac_sync_lowest_version',
        'windows_sync_lowest_version',
        'windows_xp_lowest_version',
        'websocket_host',
        'user_community_url',
        'cookie_domain',
        'has_private_nginx_server',
        'special_user_guide_disabled',
        'share_with_colleagues_disallowed',
        'is_fangcloud',
        'login_disabled',
        'product_name_en',
        'product_name_zh_cn',
        'product_name_zh_tw',
        'product_name_zh_hk',
        'login_error_msg_en',
        'login_error_msg_zh_tw',
        'login_error_msg_zh_hk',
        'resource_logos',
    ];

    private $chameleon_dir = '';

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        if ($this->option('dir')) {
            $chameleon_dir = $this->option('dir');
        } else {
            $chameleon_dir = base_path() . '/../chameleon/';
        }
        $this->chameleon_dir = $chameleon_dir;

        $domain_configs_dir = $chameleon_dir . 'domain_configs/';

        $res = [];

        if (($dir_handler = opendir($domain_configs_dir)) !== false) {
            while (($file = readdir($dir_handler)) !== false) {
                if (!is_dir($file)) {
                    $path_info = pathinfo($file);
                    if (!(isset($path_info['extension']) && $path_info['extension'] == 'json')) {
                        continue;
                    }

                    $product_id = $path_info['filename'];
                    $sso_configs = [];

                    $file_content = file_get_contents($domain_configs_dir . $path_info['basename']);
                    $file_content = json_decode($file_content, true);
                    if (json_last_error() === JSON_ERROR_NONE) {
                        $domain_configs = $this->getDomainEnv($file_content);
                    } else {
                        $this->error("deserialize {$path_info['filename']} failed");
                        continue;
                    }

                    $resource_id = isset($domain_configs[$this->configKeyToEnvKey('resource_id')])
                        ? $domain_configs[$this->configKeyToEnvKey('resource_id')]['value']
                        : null;
                    if (!$resource_id) {
                        $this->error("for {$product_id}, resource id is needed");
                        continue;
                    }

//                    ignore sso configs
//                    $sso_config_file = $this->chameleon_dir . "sso_configs/{$product_id}.json";
//                    if (file_exists($sso_config_file)
//                        && ($file_content = file_get_contents($sso_config_file))
//                        && ($sso_configs = json_decode($file_content, true))
//                        && json_last_error() === JSON_ERROR_NONE
//                    ) {
//                        /**
//                         * @var array $sso_configs
//                         */
//                        $sso_configs = $this->getDomainEnv($sso_configs);
//                    } else {
//                        $this->error("{$sso_config_file} does not exist or invalid file content");
//                    }

                    $logos = $this->getLogoEnv($resource_id);
                    $product_names = $this->getProductNameEnv($resource_id);

                    $env_contents = '';
                    $configs = [$product_names, $domain_configs, $sso_configs, $logos];
                    foreach ($configs as $config) {
                        if ($config !== false) {
                            $this->addToEnv($env_contents, $config);
                            $env_contents .= "\n";
                        }
                    }

                    $res[$product_id] = $env_contents;
                }
            }
        }

        $header =
            <<<EOF
# sso_configs
# 该文件应该早于其他配置文件，如.env.app文件的加载
# 命名方式 .env.sso.{product_id}
# see App\Http\Bootstrap\LoadEnvironmentVariables.php for more help
# see chameleon for more help
# http://116.62.230.142:8090/pages/viewpage.action?pageId=1545443
# auto generate by php artisan yfy:chameleon  


EOF;

        $product_ids_path = CommonHelper::getCachedProductIdsPath($this->laravel);
        $content = '';
        foreach($res as $key => $value) {
            $content .= $key . "\n";
        }
        $this->files->put($product_ids_path, $content);

        foreach ($res as $key => $value) {
            $env_file_path = base_path() . "/bootstrap/cache/.sso.{$key}.env";

            $file_content = $header . $value;
            $this->files->put($env_file_path, $file_content);
            $this->info("generate {$env_file_path}");
        }
        return;
    }

    private function addToEnv(&$content, array $configs)
    {
        foreach ($configs as $key => $value) {
            if ($value['comment']) {
                $content .= '# ' . $value['comment'] . "\n";
            }
//            if (in_array(strtolower($key), $this->available_keys)) {
            $value_content = $value['value'];
            if(is_array($value_content)) {
                continue;
            }
            $content .= $key . '=' . $value_content . "\n";
//            }
        }
    }

    private function getProductNameEnv($resource_id)
    {
        $res = [];
        $languages = ['en', 'zh-CN', 'zh-TW', 'zh-HK'];
        foreach ($languages as $language) {
            $product_name_file = $this->chameleon_dir . "domain_configs/language/{$language}/{$resource_id}_custom_msgs_lang.php";
            $env_key = 'PRODUCT_NAME_' . str_replace('-', '_', strtoupper($language));
            if (!file_exists($product_name_file)) {
                $this->error($product_name_file . ' does not exists');
                $res[$env_key]['value'] = false;
                $res[$env_key]['comment'] = '';
                continue;
            }

            $msgs = file_get_contents($product_name_file);
            $matches = [];
            // $lang['{$resource_id}'] = '{$msg}';
            $pattern = sprintf('/\$lang\[[\'|\"|\s?]%s[\'|\"|\s?]\]\s?=\s?[\'|\"|\s?](?<name>.*?)[\'|\"|\s?]/', $resource_id);
            if (preg_match($pattern, $msgs, $matches)) {
                $product_name = $matches['name'];
                $res[$env_key]['value'] = $product_name;
                $res[$env_key]['comment'] = '';

            } else {
                $res[$env_key]['value'] = false;
                $res[$env_key]['comment'] = '';
            }
        }
        return $res;
    }

    private function getLogoEnv($resource_id)
    {
        $logo_file = $this->chameleon_dir . "domain_configs/logo/{$resource_id}.json";
        if (!file_exists($logo_file)) {
            $this->warn($logo_file . ' does not exist');
            return false;
        }

        if (($logos = json_decode(file_get_contents($logo_file), true))
            && json_last_error() === JSON_ERROR_NONE
        ) {
            $res = [];
            /**
             * @var array $logos
             */
            foreach ($logos as $type => $value_arr) {
                foreach ($value_arr as $logo_type => $uri) {
                    $env_key = $this->configKeyToEnvKey($type . '_' . $logo_type);
                    $res[$env_key]['comment'] = '';
                    $res[$env_key]['value'] = $uri;
                }
            }
            return $res;
        } else {
            $this->error("deserialize {$logo_file} failed");
            return false;
        }
    }

    private function getDomainEnv(array $configs)
    {
        $content = [];
        $content['IS_FANGCLOUD']['comment'] = '';
        $content['IS_FANGCLOUD']['value'] = 0;

        foreach ($configs as $key => $value) {
            $env_key = $this->configKeyToEnvKey($key);
            switch ($key) {
                case 'base_url':
                    $content['FANGCLOUD_URL']['comment'] = '';
                    $content['FANGCLOUD_URL']['value'] = $value;
                    $content['DEFAULT_PAGE']['comment'] = '';
                    $content['DEFAULT_PAGE']['value'] = $value . 'apps/files/desktop';
                    $content['DEFAULT_H5_PAGE']['comment'] = '';
                    $content['DEFAULT_H5_PAGE']['value'] = $value . 'h5';
                    break;
                case 'account_url':
                    $content['ACCOUNT_BASE_URL']['comment'] = '';
                    $content['ACCOUNT_BASE_URL']['value'] = $value;
                    break;
                case 'account_static_url':
                    $content['STATIC_URL']['comment'] = '';
                    $content['STATIC_URL']['value'] = $value;
                    break;
                case 'cookie_domain':
                    $content['SESSION_DOMAIN']['comment'] = '';
                    $content['SESSION_DOMAIN']['value'] = $value;
                    break;
                case 'resource_logos':
                case 'static_url':
                    break;
                case 'html_foot':
                    foreach ($value as $html_foot_key => $html_foot_value) {
                        $content[strtoupper($html_foot_key . "_NAME")]['comment'] = '';
                        $content[strtoupper($html_foot_key . "_NAME")]['value'] = $html_foot_value['name'];
                        $content[strtoupper($html_foot_key . "_LINK")]['comment'] = '';
                        $content[strtoupper($html_foot_key . "_LINK")]['value'] = $html_foot_value['link'];
                    }
                    break;
                default:
                    $content[$env_key]['comment'] = '';
                    $content[$env_key]['value'] = $value;
                    break;
            }
        }

        return $content;
    }

    private function configKeyToEnvKey($config_key): string
    {
        switch ($config_key) {
            default:
                $res = str_replace('-', '_', strtoupper($config_key));
                break;
        }
        return $res;
    }
}
