<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use ReflectionClass;
use ReflectionException;

class MakeFormatter extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'yfy:format
    {model?* : The model name to generate formatter, default for all models}
    {--i|ignore=* : ignore specific model name}
    {--u|update}
    {--d|debug : don\'t write to files}
    {--R|reset}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create a new formatter or update exist formatter for exist models';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        // get models to create formatter or update formatter
        $models = $this->getModels();

        $update = $this->option('update');

        foreach($models as $model) {
            $class = new ReflectionClass($this->namespace_root . $model);
            $studly_model = Str::studly($model);

            if (!$update) {
                $bitmask = $this->initBitmask($class);
                $value_box = $this->initValueBox($class);
                $property = $this->initProperty($class);

                $title = <<<EOF
<?php 

namespace App\Models\Formatter;

/**
 * Trait {$studly_model}Formatter
 * 
 * auto generate by php artisan yfy:formatter

EOF;

                $title_end = <<<EOF
 */
 trait {$studly_model}Formatter
 {
    use BaseFormatter;


EOF;

                $output = $title . $this->title_package . $property['class_doc'] . $title_end .
                        $this->begin_of_bitmask . $bitmask['method_doc'] .  $this->end_of_bitmask .
                        $this->begin_of_value_box . $value_box['method_doc'] . $this->end_of_value_box .
                        $this->begin_of_properties . $property['method_doc'] . $this->end_of_properties . <<<EOF
}

EOF;
;
                $formatter_dir = app_path('Models/Formatter/');
                $formatter_path = $formatter_dir . $model . 'Formatter.php';

                if (file_exists($formatter_path) && !$this->option('reset')) {
                    $this->warn("{$formatter_path} exists, skip");
                } else {
                    $this->info("generate {$formatter_path}");
                    if (!$this->option('debug')) {
                        file_put_contents($formatter_path, $output);
                    }
                }
            } else {
                $formatter_class = $this->getFormatterClass($class);
                if (!$formatter_class && $update) {
                    $this->warn($model . "'s formatter trait doesn't exist, ignore");
                    continue;
                }

                $file_str = file_get_contents($formatter_class->getFileName());
                $str = $file_str;

                $bitmask = $this->initBitmask($class, $this->getPropertyIgnores($formatter_class));
                if ($bitmask['method_doc']) {
                    if ($this->option('debug')) {
                        $this->info("new bitmasks:\n" . $bitmask['method_doc']);
                    }
                    if (!$this->insertIntoStr($str, $bitmask['method_doc'], $this->begin_of_bitmask)) {
                        $this->warn("lack of bitmask begin mark");
                        $this->warn("if you see this warning, try add these codes to your formatter:");
                        $this->warn($this->begin_of_bitmask);
                        $this->warn($this->end_of_bitmask);
                        $this->warn("attention! empty line is required!\n");
                    }
//                    else {
//                        if (!$this->insertIntoStr($str, $bitmask['class_doc'], $this->title_package)) {
//                            $this->warn("lack of class doc begin mark");
//                            $this->warn("if you see this warning, try add these codes to your formatter:");
//                            $this->warn($this->title_package);
//                        }
//                    }
                }
                $value_box = $this->initValueBox($class, $this->getPropertyIgnores($formatter_class));
                if ($value_box['method_doc']) {
                    if ($this->option('debug')) {
                        $this->info("new value boxes:\n" . $value_box['method_doc']);
                    }
                    if (!$this->insertIntoStr($str, $value_box['method_doc'], $this->begin_of_value_box)) {
                        $this->warn("lack of value box begin value box");
                        $this->warn("if you see this warning, try add these codes to your formatter:");
                        $this->warn($this->begin_of_value_box);
                        $this->warn($this->end_of_value_box);
                        $this->warn("attention! empty line is required!\n");
                    }
//                    else {
//                        if (!$this->insertIntoStr($str, $value_box['class_doc'], $this->title_package)) {
//                            $this->warn("lack of class doc begin mark");
//                            $this->warn("if you see this warning, try add these codes to your formatter:");
//                            $this->warn($this->title_package);
//                        }
//                    }
                }
                $property = $this->initProperty($class, $this->getPropertyIgnores($formatter_class));
                if ($property['class_doc']) {
                    if ($this->option('debug')) {
                        $this->info("new properties:\n" . $property['class_doc']);
                    }

                    if (!$this->insertIntoStr($str, $property['method_doc'], $this->begin_of_properties)) {
                        $this->warn("lock of method doc begin mark");
                        $this->warn("if you see this warning, try add these codes to your formatter:");
                        $this->warn($this->begin_of_properties);
                        $this->warn($this->end_of_properties);

                        $this->warn("attention! empty line is required!\n");
                    } else {
                        if (!$this->insertIntoStr($str, $property['class_doc'], $this->title_package)) {
                            $this->warn("lack of class doc begin mark");
                            $this->warn("if you see this warning, try add these codes to your formatter:");
                            $this->warn($this->title_package);
                        }
                    }
                }

                if ($str === $file_str) {
                    $this->warn($this->getSpaces() . "model {$model} nothing to update");
                } else {
                    if (!$this->option('debug')) {
                        file_put_contents($formatter_class->getFileName(), $str);
                    }
                }
            }
        }
    }

    private $title_package = <<<EOF
 * @package App\Models\Formatter

EOF;

    private function insertIntoStr(string &$original_str, string $content, string $delimiter_str)
    {
        $pos = strpos($original_str, $delimiter_str);

        if ($pos === false) {
            return false;
        }
        $pos += strlen($delimiter_str);

        $str_first = substr($original_str, 0, $pos);
        $str_second = substr($original_str, $pos);
        $original_str = $str_first . $content . $str_second;
        return true;
    }

    private $namespace_root = 'App\\Models\\';

    private function getModels()
    {
        $models = $this->argument('model');
        $models_dir  = app_path('Models/');

        $ignores = $this->option('ignore');

        $out = [];

        if (!$models) {
            if ($this->option('reset')) {
                if (!$this->confirm('Reset all formatters?')) {
                    $this->info('exist with no change');
                }
            }

            $handler = opendir($models_dir);

            while(($file = readdir($handler)) !== false) {
                if (!is_dir($models_dir . $file)) {
                    $path_info = pathinfo($file);
                    if ($path_info['extension'] !== 'php') {
                        continue;
                    }
                    $file_name = $path_info['filename'];
                    if (strpos($file_name, 'Trait') !== FALSE) {
                        continue;
                    }
                    if (in_array($file_name, $ignores)) {
                        continue;
                    }

                    $out[] = $file_name;
                }
            }
        } else {
            $models = array_diff($models, $ignores);
            $out = $models;
        }

        // output models dir
        $this->info("models dir: {$models_dir}");

        // output ignored models
        $this->info("Ignore these models...");
        foreach($ignores as $ignore) {
            $this->info($this->getSpaces() . $ignore);
        }

        // output find model files
        $this->info("Find these model files...");
        foreach($out as $o) {
            $this->info($this->getSpaces() . $o);
        }

        return $out;
    }

    private function initBitmask(ReflectionClass $reflection_class, $ignores = [])
    {
        $output = ['class_doc' => '', 'method_doc' => ''];

        $properties = $reflection_class->getStaticProperties();
        $bitmask_property = $properties['bitmask_fields'] ?? null;
        if (!$bitmask_property) {
            return $output;
        }

        foreach($bitmask_property as $key1 => $value1) {
            foreach($value1 as $key2 => $value2) {
                $property_name = $key2;
//                $matches = [];
//                if (preg_match("/is_(?<value>.*?)_enabled/", $key2, $matches)) {
//                    $property_name = $matches['value'];
//                }

                $studly_property = Str::studly($property_name);
                if (in_array($studly_property, $ignores)) {
                    continue;
                }

                $output['method_doc'] .= $this->getBitmaskFuncs($studly_property, $key2);
                $output['class_doc'] .= " * @property bool {$key2}\n";
            }
        }
        return $output;
    }

//    private function getBitmaskIgnores(ReflectionClass $reflection_trait)
//    {
//        $methods = $reflection_trait->getMethods();
//
//        $exist_property_names = [];
//        foreach($methods as $method) {
//            $matches = [];
//            if (preg_match("/get(?<name>.*?)Attr/", $method->getName(), $matches)) {
//                $exist_property_names[] = $matches['name'];
//            }
//        }
//
//        return $exist_property_names;
//    }

    private function getBitmaskFuncs($studly_name, $property_name)
    {
        return <<<EOF
    /**
     * get {$property_name} bitmask
     * @return bool
     */
    public function get{$studly_name}Attr(): bool
    {
        return (bool)\$this->getBitmaskField('{$property_name}');
    }
    
    /**
     * set {$property_name} bitmask
     */
    public function set{$studly_name}Attr()
    {
        \$this->setBitmaskField('{$property_name}', true);
    }
    
    /**
     * unset {$property_name} bitmask
     */
    public function unset{$studly_name}Attr()
    {
        \$this->setBitmaskField('{$property_name}', false);
    }


EOF;
    }

    private $begin_of_bitmask = <<<EOF
    /// ----------------------------------------------------
    ///  Begin of bitmask
    /// ----------------------------------------------------


EOF;

    private $end_of_bitmask = <<<EOF
    /// ----------------------------------------------------
    ///  End of bitmask
    /// ----------------------------------------------------


EOF;

    private function initValueBox(ReflectionClass $reflection_class, $ignores = [])
    {
        $output = ['class_doc' => '', 'method_doc' => ''];

        $properties = $reflection_class->getStaticProperties();
        $value_box_property = $properties['value_box_fields'] ?? null;

        if (!$value_box_property) {
            return $output;
        }

        foreach($value_box_property as $key1 => $value1) {
            foreach($value1 as $value2) {
                $studly_property = Str::studly($value2);
                if (in_array($studly_property, $ignores)) {
                    continue;
                }

                $output['method_doc'] .= $this->getValueBoxFuncs($studly_property, $value2);
                $output['class_doc'] .= " * @property mixed {$value2}\n";;;
            }
        }
        return $output;
    }

//    private function getValueBoxIgnores(ReflectionClass $reflection_trait)
//    {
//        $methods = $reflection_trait->getMethods();
//
//        $exist_property_names = [];
//        foreach($methods as $method) {
//            $matches = [];
//            if (preg_match("/get(?<name>.*?)Attr/", $method->getName(), $matches)) {
//                $exist_property_names[] = $matches['name'];
//            }
//        }
//
//        return $exist_property_names;
//    }

    private function getValueBoxFuncs($studly_name, $property_name)
    {
        return <<<EOF
    /**
     * get {$property_name} value box
     * @return mixed
     */
    public function get{$studly_name}Attr()
    {
        return \$this->getAdditionalValue('{$property_name}');
    }
    
    /**
     * set {$property_name} value box
     * @param mixed \$value
     */
    public function set{$studly_name}Attr(\$value)
    {
        \$this->setAdditionalValue('{$property_name}', \$value);
    }

    /**
     * unset {$property_name} value box
     */
    public function unset{$studly_name}Attr()
    {
        \$this->unsetAdditionalValue('{$property_name}');
    }


EOF;
    }

    private $begin_of_value_box = <<<EOF
    /// ----------------------------------------------------
    ///  Begin of value box
    /// ----------------------------------------------------


EOF;

    private $end_of_value_box = <<<EOF
    /// ----------------------------------------------------
    ///  End of value box
    /// ----------------------------------------------------


EOF;

    private function initProperty(ReflectionClass $reflection_class, $ignores = [])
    {
        $class_name = $reflection_class->getName();
        $date_func = $reflection_class->getMethod('getDates');
        $date_property = $date_func->getClosure(new $class_name)();

        $output = ['class_doc' => '', 'method_doc' => ''];
        $schemas = $this->getClassSchema($reflection_class);
        $class_doc = '';
        $method_doc = '';
        foreach($schemas as $schema) {
            $name = $schema->name;
            if (in_array($name, $date_property)) {
                $type = '\Illuminate\Support\Carbon';
            } else {
                $type = $this->convertType($schema->type);
            }
            if (in_array(Str::studly($name), $ignores)) {
                continue;
            }

            $method_doc .= $this->generateGetter($name, $type);
            $class_doc .= " * @property {$type} {$name}\n";
        }

        $output['class_doc'] .= $class_doc;
        $output['method_doc'] .= $method_doc;

        return $output;
    }

    private function generateGetter($property, $type): string
    {
        $studly_property = Str::studly($property);

        return
            <<<EOF
    /**
     * get {$property}
     * @return {$type}|null
     */
    public function get{$studly_property}Attr(): ?{$type}
    {
        return \$this->{$property};
    }


EOF;
    }

    private function getPropertyIgnores(ReflectionClass $reflection_trait)
    {
        $methods = $reflection_trait->getMethods();
        $exist_property_names = [];
        foreach($methods as $method) {
            if (!$method->isPublic()) {
                continue;
            }
            $matches = [];
            if (preg_match("/get(?<name>.*?)Attr/", $method->getName(), $matches)) {
                $exist_property_names[] = $matches['name'];
            }
        }

        return $exist_property_names;
    }

    private $begin_of_properties = <<<EOF
    /// ----------------------------------------------------
    ///  Begin of getters and setters
    /// ----------------------------------------------------


EOF;

    private $end_of_properties = <<<EOF
    /// ----------------------------------------------------
    ///  End of getters and setters
    /// ----------------------------------------------------


EOF;

    private $schema_sql = "SELECT COLUMN_NAME as name, DATA_TYPE as type FROM `information_schema`.COLUMNS WHERE TABLE_NAME = \"%s\" AND TABLE_SCHEMA= \"%s\"";

    private function getClassSchema(ReflectionClass $reflection_class)
    {
        if ($reflection_class->isTrait()) {
            return [];
        }
        $table = $reflection_class->getProperty('table');
        $table->setAccessible(true);
        $name = $reflection_class->getName();
        $table_name = $table->getValue(new $name);
        $database = config('database.connections.mysql.database');
        $schemas = DB::select(sprintf($this->schema_sql, $table_name, $database));

        return $schemas;
    }

    private function getFormatterClass(ReflectionClass $reflection_class)
    {
        $namespace = $reflection_class->getNamespaceName() . '\\Formatter\\';
        $class_name = $reflection_class->getShortName() . 'Formatter';
        $full_name = $namespace . $class_name;

        try
        {
            $class = new ReflectionClass($full_name);
        }
        catch(ReflectionException $e)
        {
//            $this->info($full_name . "doesn't exist, ignore");
        }
        return $class ?? null;
    }

    private function convertType($data_type): string
    {
        if (strpos($data_type, 'int') !== FALSE) {
            return 'int';
        } else {
            return 'string';
        }
    }

    private function getSpaces(int $num = 4)
    {
        $res = '';
        for ($i = 0; $i < $num; $i++) {
            $res .= ' ';
        }
        return $res;
    }
}
