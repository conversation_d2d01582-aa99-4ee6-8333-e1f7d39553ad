<?php

namespace App\Console\Commands;

use App\Util\CommonHelper;
use Dotenv\Dotenv;
use Illuminate\Console\Command;
use Illuminate\Filesystem\Filesystem;
use Illuminate\Contracts\Console\Kernel as ConsoleKernelContract;

class ConfigCacheCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'yfy:config:cache
    {--d|dir= : chameleon path, default is ../chameleon}
    ';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create a cache file for faster configuration loading';

    /**
     * The filesystem instance.
     *
     * @var \Illuminate\Filesystem\Filesystem
     */
    protected $files;

    protected $app;

    protected $default_product_id = 'fangcloud_v2';

    /**
     * Create a new config cache command instance.
     *
     * @param  \Illuminate\Filesystem\Filesystem  $files
     * @return void
     */
    public function __construct(Filesystem $files)
    {
        parent::__construct();

        $this->files = $files;
    }

    /**
     * Execute the console command.
     *
     * @return void
     * @throws \Illuminate\Contracts\Filesystem\FileNotFoundException
     */
    public function handle()
    {
        if ($this->option('dir')) {
            $this->call('yfy:config:clear', ['--dir' => $this->option('dir')]);
            $this->callSilent('yfy:chameleon', ['--dir' => $this->option('dir')]);
        } else {
            $this->call('yfy:config:clear');
            $this->callSilent('yfy:chameleon');
        }
        $product_ids = $this->files->get(CommonHelper::getCachedProductIdsPath($this->laravel));
        $product_ids = explode("\n", $product_ids);

        foreach($product_ids as $product_id) {
            if (!$product_id) {
                continue;
            }
            CommonHelper::setProductId($product_id);
            $config = $this->getFreshConfiguration();

            $this->files->put(
                CommonHelper::getCachedConfigPath($this->app), '<?php return '.var_export($config, true).';'.PHP_EOL
            );

            // fresh env to default config
            // 如果省略这步，会导致如果   product_id B  没有配置的项 读到 product_id A 配置的项
            $this->freshConfig();
        }

        // 因为 重载了 loadConfiguration 但是 app的默认方法无法改变
        $this->freshConfig();
        $this->files->copy(CommonHelper::getCachedConfigPath($this->app), $this->laravel->getCachedConfigPath());

        $this->info('Configuration cached successfully!');
    }

    private function freshConfig()
    {
        CommonHelper::setProductId($this->default_product_id);

        // 检查当前 env
        // load .env.app
        if (env('APP_ENV') !== 'production') {
            $this->tryLoadSpecificEnvironmentFile($this->app, '.app.test', true);
        } else {
            $this->tryLoadSpecificEnvironmentFile($this->app, '.app', true);
        }

        // load sso config if needed
        // 加载混合云相关的个性化配置
        $this->tryLoadSSOEnvironmentFile($this->app);

        // 优先加载 .env.custom 配置文件
        $this->tryLoadSpecificEnvironmentFile($this->app, '.custom', true);
    }

    /**
     * Boot a fresh copy of the application configuration.
     *
     * @return array
     */
    protected function getFreshConfiguration()
    {
        $app = require $this->laravel->bootstrapPath().'/app.php';

        $app->make(ConsoleKernelContract::class)->bootstrap();

        $this->app = $app;

        return $app['config']->all();
    }


    /**
     * load {$app->environmentPath().'/'.$app->environmentFile().'.'.$file_suffix} if exists
     *
     * @param \Illuminate\Foundation\Application $app
     * @param $file_prefix
     * @param bool $overload
     */
    private function tryLoadSpecificEnvironmentFile($app, $file_prefix, $overload = false)
    {
        if (file_exists($app->environmentPath().'/'. $file_prefix . $app->environmentFile())) {
            if ($overload) {
                (new Dotenv($app->environmentPath(),  $file_prefix . $app->environmentFile()))->overload();
            } else {
                (new Dotenv($app->environmentPath(),  $file_prefix . $app->environmentFile()))->load();
            }
        }
    }

    /**
     * @param \Illuminate\Foundation\Application $app
     */
    private function tryLoadSSOEnvironmentFile($app)
    {
        $product_id = CommonHelper::getProductId();

        if (file_exists(CommonHelper::getSSOEnvPath($app, $product_id))) {
            (new Dotenv($app->bootstrapPath('cache'), ".sso.{$product_id}.env"))->overload();
        }
    }
}
