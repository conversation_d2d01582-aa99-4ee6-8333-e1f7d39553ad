<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class <PERSON><PERSON>hecker extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'yfy:lang
    {--i|input: language files need to be checked in resources\lang\[language]}
    ';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'check if missing translation';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {

    }
}
