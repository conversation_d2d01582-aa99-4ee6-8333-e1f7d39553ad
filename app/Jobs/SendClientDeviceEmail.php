<?php

namespace App\Jobs;

use App\Models\ClientDevice;
use App\Remote\Email;
use App\Util\LogHelper;
use App\Util\LogType;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;

class SendClientDeviceEmail extends BaseJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $client_device;

    /**
     * Create a new job instance.
     *
     * @param ClientDevice $clientDevice
     */
    public function __construct(ClientDevice $clientDevice)
    {
        parent::__construct();

        $this->client_device = $clientDevice;

        $this->queue = 'account_client_device';
    }

    /**
     * Execute the job.
     *
     * @throws \App\Exceptions\RemoteCallException
     */
    public function handle()
    {
        $to = $this->client_device->user_id;
        $device_type = $this->client_device->device_name;

        (new Email(['product_id' => $this->product_id]))->sendNewDevice($to, $device_type);
    }
}
