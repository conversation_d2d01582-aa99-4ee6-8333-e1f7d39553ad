<?php

namespace App\Jobs;

use App\Library\TwoStep;
use App\Models\User;
use App\Remote\Email;
use App\Util\Context;
use App\Util\LogHelper;
use App\Util\LogType;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;

class SendTwoStepEmail extends BaseJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $user;

    protected $status;

    public const STATUS_CLOSE = 'close';
    public const STATUS_OPEN = 'open';
    public const STATUS_MODIFY = 'modify';

    /**
     * Create a new job instance.
     *
     * @param User $user
     * @param string $status
     */
    public function __construct(User $user, string $status)
    {
        parent::__construct();

        $this->user = $user;
        $this->status = $status;

        $this->queue = 'account_two_step';
    }

    /**
     * Execute the job.
     *
     * @return void
     * @throws \App\Exceptions\RemoteCallException
     */
    public function handle()
    {
        $to = $this->user->user_id;
        $status = $this->status;

        $lang = $this->user->getLang(false);
        if (!$lang) {
            $lang = Context::getLanguage();
        }

        (new Email(['product_id' => $this->product_id]))->sendTwoStep($to, $status, trans($this->user->getTwoStepMethodAttr()), $lang);
    }
}
