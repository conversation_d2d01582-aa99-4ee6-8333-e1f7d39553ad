<?php

namespace App\Jobs;

use Illuminate\Support\Facades\Queue;


/**
 * 日志发送任务
 * Class SendUA
 * @package App\Jobs
 */
class SendUA extends SimpleJob
{
    protected static $queue = 'ua_builder_queue';

    public function __construct($job_body)
    {
        $this->setBody($job_body);
    }

    /**
     * 发送日志任务
     */
    public function send()
    {
        Queue::connection('rabbitmq')->pushRaw(json_encode($this->getBody()), static::$queue);
    }
}