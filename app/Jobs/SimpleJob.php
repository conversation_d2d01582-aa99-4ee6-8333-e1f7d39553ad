<?php

namespace App\Jobs;

use Illuminate\Support\Facades\Queue;

abstract class SimpleJob
{
    protected static $queue = '';
    protected $body = [];

    public function setBody($body)
    {
        $this->body = $body;
    }

    public function getBody()
    {
        return $this->body;
    }

    public function send()
    {
        $body = $this->getBody();
        $body['timestamp'] = time();
        Queue::connection('rabbitmq')->pushRaw(json_encode($body), static::$queue);
    }
}
