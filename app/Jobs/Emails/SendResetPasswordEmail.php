<?php

namespace App\Jobs\Emails;

use App\Jobs\BaseJob;
use App\Models\User;
use App\Remote\Email;
use App\Util\LogHelper;
use App\Util\LogType;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;

class SendResetPasswordEmail extends BaseJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * @var User $user
     */
    protected $user;

    /**
     * @var string $url
     */
    protected $url;

    /**
     * Create a new job instance.
     *
     * @param User $to
     * @param string $url
     */
    public function __construct(User $to, string $url)
    {
        parent::__construct();

        $this->user = $to;

        $this->url = $url;

        $this->queue = 'account_reset_password';
    }

    /**
     * Execute the job.
     *
     * @return void
     * @throws \App\Exceptions\RemoteCallException
     */
    public function handle()
    {
        $to = $this->user->user_id;

        (new Email(['product_id' => $this->product_id]))->sendResetPassword($to, $this->url);
    }
}
