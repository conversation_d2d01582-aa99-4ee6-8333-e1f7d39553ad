<?php

namespace App\Http\Controllers;

use App\Util\CommonHelper;
use App\Util\Context;
use App\Util\UserAgent;
use Illuminate\Http\Request;

class UserSettingController extends Controller
{
    public function __construct(Request $request)
    {
        $this->middleware('auth:web');
        $this->middleware('identity')->only('showSetTwoStepForm');

        parent::__construct($request);
    }

    /**
     * @param UserAgent $user_agent
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     * @throws \App\Exceptions\ValidationException
     * @throws \Exception
     * @throws \Throwable
     */
    public function loginSettings(UserAgent $user_agent)
    {
        $platform = $user_agent->getBrowserPlatform();
        $user = Context::user();
        $enterprise = $user->getRemoteUser()->getEnterprise();
        $res = [
            'platform' => $platform,
            'js' => 'js/user_settings.js',
            'css' => 'css/web.css'
        ];
        $res['user'] = $user->format(['id', 'is_password_unset', 'credentials', 'is_two_step_enabled', 'created']);
        $res['user']['enterprise'] = $enterprise->toArray();
        $res['fangcloud_url'] = config('app.fangcloud_url');

        if($platform == UserAgent::BROWSER_PLATFORM_WEB) {
            CommonHelper::registerDevice();
        }

        return view('settings.login_settings', $res);
    }

    public function securitySettings(UserAgent $user_agent)
    {
        $platform = $user_agent->getBrowserPlatform();
        return view('settings.security_settings', [
            'platform' => $platform,
            'js' => 'js/user_settings.js',
            'css' => 'css/web.css'
        ]);
    }

    public function showSetTwoStepForm(UserAgent $user_agent)
    {
        $platform = $user_agent->getBrowserPlatform();
        return view('auth.force_two_step', [
            'platform' => $platform,
            'js' => 'js/force_two_step.js',
            'css' => 'css/web.css'
        ]);
    }
}
