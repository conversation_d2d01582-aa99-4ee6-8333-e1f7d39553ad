<?php

namespace App\Http\Controllers;

use App\Constants\CacheKey;
use App\Constants\ErrorCode;
use App\Constants\SessionCacheKey;
use App\Exceptions\ExternalException;
use App\Rules\ValidPhone;
use App\Services\Captcha\CaptchaEmailSend;
use App\Services\Captcha\CaptchaSmsSend;
use App\Util\CacheHelper;
use App\Util\Captcha;
use App\Util\CommonHelper;
use App\Util\LogHelper;
use App\Util\LogType;
use App\Util\Sms;
use Illuminate\Http\Request;
use Mews\Captcha\PicCaptcha;

class CaptchaController extends Controller
{
    public function __construct(Request $request)
    {
        parent::__construct($request);
    }

    public function getPicCaptcha(PicCaptcha $pic_captcha, $type)
    {
        $captcha = Captcha::generateCaptcha($type);
        return $pic_captcha->create($captcha);
    }

    /**
     * @param CaptchaSmsSend $captcha_send
     * @return array
     * @throws ExternalException
     * @throws \App\Exceptions\ValidationException
     * @throws \Exception
     * @throws \Throwable
     */
    public function sendSms(CaptchaSmsSend $captcha_send)
    {
        $input = $this->request->only('pic_captcha', 'phone', 'type', 'message_type', 'ts', 'register-sign', 'forgot-sign',
            'signature', 'referral-sign', 'token', 'vd', 'request_id');
        $pic_captcha = $input['pic_captcha'] ?? null;
        $phone = $input['phone'] ?? null;
        $type = $input['type'] ?? '';
        $message_type = $input['message_type'] ?? Sms::MESSAGE_TXT;
        if($type == Captcha::TYPE_FORGOT_PASSWORD_BY_PHONE) {
            $user_info = CacheHelper::getCachedSessionInfo(SessionCacheKey::FORGOT_PASSWORD_INFO) ?: [];
            if(!$user_info || !isset($user_info['phone'])) {
                throw new ExternalException(ErrorCode::FORGOT_PASSWORD_INFO_EXPIRED);
            }
            $phone = $user_info['phone'];
        }
        $signature = $input['register-sign'] ?? null;
        $ts = $input['ts'] ?? null;
        if ($type == Captcha::TYPE_REGISTER)
        {
            if (!$signature || !$phone)
            {
                throw new ExternalException(ErrorCode::EMAIL_CAPTCHA_INVALID);
            }
            $hit_cache_signature = CacheHelper::getCachedInfo(sprintf(CacheKey::REGISTER_SIGN_IDENTITY_FLAG, $signature));
            if ($hit_cache_signature)
            {
                throw new ExternalException(ErrorCode::EMAIL_CAPTCHA_INVALID);
            }
            $signKey = 'appId='.Sms::REGISTER_SMS_SIGN_APP_ID.'&ts='.$ts.'&phone='.$phone.'&type='.$type;
            $serverSign = md5($signKey);
            if ($serverSign != $signature)
            {
                throw new ExternalException(ErrorCode::EMAIL_CAPTCHA_INVALID);
            }
            CacheHelper::cacheInfo(sprintf(CacheKey::REGISTER_SIGN_IDENTITY_FLAG, $signature), true,300);
        }

        if ($type == Captcha::TYPE_FORGOT_PASSWORD_BY_PHONE)
        {
            $forgot_signature = $input['forgot-sign'] ?? null;
            if (!$forgot_signature)
            {
                throw new ExternalException(ErrorCode::FORGOT_PASSWORD_BY_PHONE_SIGN_ERROR);
            }
            $cache_signature = CacheHelper::getCachedInfo(sprintf(CacheKey::FORGOT_PASSWORD_BY_PHONE_IDENTITY_FLAG, $forgot_signature));
            if ($cache_signature)
            {
                throw new ExternalException(ErrorCode::FORGOT_PASSWORD_BY_PHONE_SIGN_ERROR);
            }
            $signKey = 'appId='.Sms::FORGOT_PASSWORD_BY_PHONE_SIGN_APP_ID.'&ts='.$ts.'&phone=f_phone'.'&type='.$type;
            $serverSign = md5($signKey);
            if ($serverSign != $forgot_signature)
            {
                throw new ExternalException(ErrorCode::FORGOT_PASSWORD_BY_PHONE_SIGN_ERROR);
            }
            CacheHelper::cacheInfo(sprintf(CacheKey::FORGOT_PASSWORD_BY_PHONE_IDENTITY_FLAG, $forgot_signature), true,300);
        }
        if ($type == Captcha::TYPE_BECOME_REFERRAL)
        {
            $referral_signature = $input['referral-sign'] ?? null;
            if (!$referral_signature || !$phone)
            {
                throw new ExternalException(ErrorCode::SIGNATURE_VERITY_ERROR);
            }
            $hit_cache_signature = CacheHelper::getCachedInfo(sprintf(CacheKey::BECOME_REFERRAL_IDENTITY_FLAG, $referral_signature));
            if ($hit_cache_signature)
            {
                throw new ExternalException(ErrorCode::SIGNATURE_VERITY_ERROR);
            }
            $signKey = 'appId='.Sms::BECOME_REFERRAL_SIGN_APP_ID.'&ts='.$ts.'&phone='.$phone.'&type='.$type;
            $serverSign = md5($signKey);
            if ($serverSign != $referral_signature)
            {
                throw new ExternalException(ErrorCode::SIGNATURE_VERITY_ERROR);
            }
            CacheHelper::cacheInfo(sprintf(CacheKey::BECOME_REFERRAL_IDENTITY_FLAG, $referral_signature), true,300);
        }

        $checkSignCaptchaType = [Captcha::TYPE_ACTIVATE_BY_PHONE, Captcha::TYPE_MODIFY_PHONE];
        $inputSignature = $input['signature'] ?? null;
        if (in_array($type, $checkSignCaptchaType))
        {
            $this->checkSignature($type, Sms::PUBLIC_SIGNATURE_VERITY_APP_ID, $ts, $phone, $inputSignature);
        }

        $skip_sdk_captcha_check = false;

        // 使用滑块的业务类型
        $typesForceUseQiHooCaptcha = [
            Captcha::TYPE_REGISTER,
            Captcha::TYPE_BECOME_REFERRAL,
            Captcha::TYPE_MODIFY_PHONE,
            Captcha::TYPE_FORGOT_PASSWORD_BY_PHONE,
            Captcha::TYPE_TWO_STEP,
            Captcha::TYPE_ACTIVATE_BY_PHONE,
        ];

        if (in_array($type, $typesForceUseQiHooCaptcha) && Captcha::checkCaptchaSwitch())
        {
            $skip_sdk_captcha_check = true;
            $requestId = $input['request_id'] ?? null;
            if (!Captcha::checkAliyunCaptcha($requestId))
            {
                throw new ExternalException(ErrorCode::PIC_CAPTCHA_INVALID, 'sliding_pic_captcha');
            }
        }

        $captcha_send->handle([
            'phone' => $phone,
            'pic_captcha' => $pic_captcha,
            'message_type' => $message_type,
            'captcha_type' => $type,
            'ip' => CommonHelper::getClientIp(),
            'skip_sdk_captcha_check' => $skip_sdk_captcha_check
        ]);
        return $this->success();
    }

    private function checkSignature($type, $appId, $ts, $phone, $inputSign)
    {
        $signKey = 'appId='.$appId.'&ts='.$ts.'&phone='.$phone.'&type='.$type;
        LogHelper::info(LogType::WebAndServerService, "checkSignature param: [ key = " .$signKey.']');
        if (!$inputSign || !$phone)
        {
            throw new ExternalException(ErrorCode::SIGNATURE_VERITY_ERROR);
        }
        $cache_signature = CacheHelper::getCachedInfo(sprintf($type.CacheKey::SIGN_VERITY_IDENTITY_FLAG, $inputSign));
        if ($cache_signature)
        {
            throw new ExternalException(ErrorCode::SIGNATURE_VERITY_ERROR);
        }

        $serverSign = md5($signKey);
        if ($serverSign != $inputSign)
        {
            throw new ExternalException(ErrorCode::SIGNATURE_VERITY_ERROR);
        }
        CacheHelper::cacheInfo(sprintf($type.CacheKey::SIGN_VERITY_IDENTITY_FLAG, $inputSign), true,300);
    }

    public function sendEmail(CaptchaEmailSend $captcha_send)
    {
        $captcha_send->handle([
            'email' => $this->request->json('email'),
            'captcha_type' => $this->request->json('type'),
            'ip' => $this->request->ip(),
        ]);
        return [
            'success' => true,
        ];
    }

    public function checkPicCaptchaNeeded($type)
    {
        $phone = $this->request->get('phone');
        $this->validate(['phone' => $phone], ['phone' => new ValidPhone]);
        $chineseIp = CommonHelper::isChineseIpExcludeSpecialRegion(CommonHelper::getClientIp());
        if ($type == Captcha::TYPE_REGISTER && !$chineseIp)
        {
            $is_pic_captcha_needed = true;
        } else{
            $is_pic_captcha_needed = Sms::isPicCaptchaRequired(null);
        }
        return [
            'success' => true,
            'is_pic_captcha_needed' => $is_pic_captcha_needed
        ];
    }


    /**
     * 卫瓴短信接入，发送短信接口
     * @param CaptchaSmsSend $captcha_send
     * @return array
     * @throws ValidationException
     * @throws \Throwable
     */
    public function sendWeilingSms(CaptchaSmsSend $captcha_send){

        $phone = intval($this->request->get('phoneNum'));
        $sign = $this->request->get('sign');
        $signature = $this->request->get('signature');
        $time = $this->request->get('time');

        $pic_captcha = null;
        $type = Captcha::TYPE_ACTIVATE;
        $message_type = $input['message_type'] ?? Sms::MESSAGE_TXT;


        $phone_json = [];
        $phone_json['phoneNum'] = strval($phone);
        $md5 = strtoupper(md5(json_encode($phone_json).$phone.$time));
        if($signature != $md5){
            // 验证失败
            $response['code'] = 500;
            $response['message'] = '签名验证失败';
            return $response;
        }

        $captcha_send->handle([
            'phone' => $phone,
            'pic_captcha' => $pic_captcha,
            'message_type' => $message_type,
            'captcha_type' => $type,
            'ip' => CommonHelper::getClientIp(),
        ]);

        // 卫瓴返回值
        $response['code'] = 200;
        $response['message'] = '发送短信成功';
        return $response;
    }

    /**
     * 发送验证短信验证码接⼝
     * @param CaptchaSmsSend $captcha_send
     * @return mixed
     */
    public function weilingCaptchaCheck(CaptchaSmsSend $captcha_send){

        $phone = intval($this->request->get('phoneNum'));
        $verificationCode = $this->request->get('verificationCode');
        $sign = $this->request->get('sign');
        $signature = $this->request->get('signature');
        $time = $this->request->get('time');

        $phone_json = [];
        $phone_json['phoneNum'] = strval($phone);
        $phone_json['verificationCode'] = strval($verificationCode);
        $md5 =  strtoupper(md5(json_encode($phone_json).$phone.$time));
        if($signature != $md5){
            // 验证失败
            $response['code'] = 500;
            $response['message'] = '签名验证失败';
            return $response;
        }

        $check = Captcha::check($verificationCode, Captcha::TYPE_ACTIVATE,Captcha::THROUGH_SMS,$phone);
        if(!$check){
            // 验证失败
            $response['code'] = 500;
            $response['message'] = '短信验证失败';
        }else{
            $response['code'] = 200;
            $response['message'] = '短信验证成功';
        }

        return $response;

    }



}
