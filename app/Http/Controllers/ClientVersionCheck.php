<?php

namespace App\Http\Controllers;

use App\Constants\Common;
use App\Constants\ErrorCode;
use App\Constants\Http;
use App\Exceptions\ExternalException;
use App\Exceptions\InternalException;
use App\Models\ClientDevice;
use App\Models\Service;
use App\Models\User;
use App\Remote\Chameleon;
use App\Remote\Enterprise;
use App\Services\Auth\AuthTokenCreate;
use App\Services\Auth\PreLogin;
use App\Services\Auth\UserLogin;
use App\Services\Security\ClientDeviceRegister;
use App\Util\Context;
use App\Util\UA\UAMessageTypes;
use App\Util\UA\UAClient;
use Closure;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cookie;
use Illuminate\Support\Str;

trait ClientVersionCheck
{
    /**
     * @param User $user
     * @param $current_version
     * @param $platform_id
     */
    public function checkEncryptionVersion($user, $current_version, $platform_id)
    {
        if($user->getRemoteUser()->getEnterprise()->is_encryption_enabled) {
            $encryption_version_limits = config('common.encryption_min_client_version_limit');
            $min_version = $encryption_version_limits[$platform_id] ?? null;
            if($min_version) {
                return version_compare($current_version, $min_version, '>=');
            }
        }
        return true;
    }

    /**
     * @param User $user
     * @param $current_version
     */
    public function checkDlpDevice($user, $current_version)
    {
        $enterprise = $user->getRemoteUser()->getEnterprise();
        if (!$enterprise->is_dlp_client_enabled) {
            return true;
        }
        $checkResult = Enterprise::checkDlpDevice($enterprise->id);
        return $checkResult['check_success'];
    }
}