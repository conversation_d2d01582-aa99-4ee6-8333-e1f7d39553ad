<?php

namespace App\Http\Controllers\Api;

use App\Constants\CacheKey;
use App\Constants\Common;
use App\Constants\ErrorCode;
use App\Constants\ExceptionFieldKey;
use App\Constants\Plan;
use App\Constants\Platform;
use App\Constants\SessionCacheKey;
use App\Exceptions\ExternalException;
use App\Http\Controllers\LoginTrait;
use App\Jobs\SendTwoStepEmail;
use App\Library\TwoStep;
use App\Models\AuthToken;
use App\Models\ClientDevice;
use App\Models\Service;
use App\Models\User;
use App\Remote\AuthToken as RemoteAuthToken;
use App\Remote\Sms;
use App\Remote\Wechat;
use App\Rules\ValidPhone;
use App\Services\Auth\AuthTokenCreate;
use App\Services\Auth\AuthTokenRefresh;
use App\Services\Auth\AuthTokenRevoke;
use App\Services\Auth\UserLogin;
use App\Services\Captcha\CaptchaSmsSend;
use App\Services\Password\ChangePassword;
use App\Services\Security\ClientDeviceRegister;
use App\Util\CacheHelper;
use App\Util\Captcha;
use App\Util\CommonHelper;
use App\Util\Context;
use App\Util\SecurityHelper;
use App\Util\UA\UAClient;
use App\Util\UA\UAMessageTypes;
use App\Util\UserAgent;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

/**
 * 与 App\Http\Controllers\SecurityController 中部分接口不同的是：
 * 这里的请求都是只需要通过 guard 的验证即可，以上帝视角来操作
 *
 * checkDevice 因为会将 检查的设备登出，所以需要再加上 api 的身份验证
 *
 * Class SecurityController
 * @package App\Http\Controllers\Api
 */
class SecurityController extends ApiController
{
    use LoginTrait;

    public function __construct(Request $request)
    {
        parent::__construct($request);

        $this->middleware('auth:api')->only(['checkDevice']);

        $this->middleware('guard')->except(['checkDevice', 'login', 'checkTwoStepLogin', 'getAvailableTwoStepMethod', 'getGoogleQRcode',
            'getGoogleSecret', 'setTwoStep', 'sendCode', 'getWechatQRcode', 'changePassword', 'showForgotPasswordForm', 'sendCaptchaSms']);

        $this->middleware('auth:api')->only('changePassword');
    }

    /**
     * api 检查设备
     * 1. 是否开启了强制二次验证
     * 2. auth 是否过期
     * 3. 注册设备
     *
     * @return array
     * @throws \App\Exceptions\ValidationException
     * @throws \Exception
     * @throws \Throwable
     */
    public function checkDevice()
    {
        $input = $this->request->only(['device_token', 'additional_info', 'auth_token']);
        $in['device_token'] = $input['device_token'] ?? null;
        $in['additional_info'] = $input['additional_info'] ?? null;
        $in['auth_token'] = $input['auth_token'] ?? null;
        $in['mark_credit'] = false;

        if (!$in['auth_token'] || !RemoteAuthToken::get($in['auth_token'])) {
            return $this->fail();
        }

        if ($in['device_token'] && !((new ClientDeviceRegister())->handle($in))) {
            return $this->fail();
        } else {
            return $this->success();
        }
    }

    /**
     * @throws \App\Exceptions\ValidationException
     * @throws \Exception
     * @throws \Throwable
     */
    public function createAuthToken()
    {
        $input = $this->request->only(['user_id', 'service_id']);
        $user_id = $input['user_id'] ?? null;
        $service_id = $input['service_id'] ?? null;
        $user = null;
        if ($user_id) {
            $user = User::findByMainUserId($user_id);
        }
        $service = null;
        if ($service_id) {
            $service = Service::findById($service_id);
        }

        $auth_token = (new AuthTokenCreate())->handle([
            'user' => $user,
            'service' => $service,
        ]);
        $auth_token = AuthToken::formatAsFrontend($auth_token);
        return $this->success(['auth_token' => array_shift($auth_token)]);
    }

    /**
     * @return array
     * @throws \App\Exceptions\ValidationException
     * @throws \Exception
     * @throws \Throwable
     */
    public function refreshAuthToken()
    {
        $input = $this->request->only(['refresh_token', 'rebuild']);

        $in['refresh_token'] = $input['refresh_token'] ?? null;
        $in['rebuild'] = $input['rebuild'] ?? null;

        $auth_token = (new AuthTokenRefresh())->handle($in);
        $auth_token = AuthToken::formatAsFrontend($auth_token);

        return $this->success(['auth_token' => array_shift($auth_token)]);
    }

    /**
     * revoke auth token
     *
     * @return array
     * @throws \App\Exceptions\ValidationException
     * @throws \Exception
     * @throws \Throwable
     */
    public function revokeAuthToken()
    {
        $input = $this->request->only(['auth_token']);
        $auth_token = $input['auth_token'] ?? null;
        if ($auth_token) {
            $auth_token = RemoteAuthToken::get($auth_token);
//            $auth_token = AuthToken::findByAuthTokenGently($auth_token);
            if ($auth_token) {
                (new AuthTokenRevoke())->handle(['auth_token' => $auth_token]);
            }
        }

        return $this->success();
    }

    public function verifyIdentity()
    {
        $input = $this->request->only(['password', 'user', 'secure_zone']);
        SecurityHelper::secureParamDecode($input);
        $password = $input['password'] ?? null;
        $user = $input['user'] ?? null;

        if (!$password || !$user) {
            return $this->fail();
        }

        $user = User::findByMainUserId($user);
        if (!$user || !$user->authenticate($password)) {
            return $this->fail();
        }

        return $this->success();
    }

    public function login()
    {
        try {
            $in = $this->request->json()->all();
            SecurityHelper::secureParamDecode($in);
            $in['only_throw'] = true;
            $in['skip_pic_captcha'] = true;
            return $this->doLogin("app", $in);
        } catch (ExternalException $e) {
            if ($e->getErrorCode() == ErrorCode::IDENTITY_NEED_TWO_STEP_VERIFY) {
                $user = Context::getUser();
                $two_step = $user->getTwoStep(false);
                $method = $two_step['method'];
                $identity = $two_step['identity'];
                return ['errors' => (new ExternalException(ErrorCode::IDENTITY_NEED_TWO_STEP_VERIFY))->getFormattedMessage(), 'method' => $method, 'identity' => $identity];
            }
            throw $e;
        }
    }

    public function checkTwoStepLogin()
    {
        $in = $this->request->json()->all();
        SecurityHelper::secureParamDecode($in);

        $user = (new UserLogin())->handle($in)['user'];

        $code = $in['code'] ?? '';
        $status = (new TwoStep(['user' => $user]))->verify($code);
        if ($status) {
            $in['user'] = $user;
            $in['skip_pic_captcha'] = true;
            return $this->doLogin("app", $in, static::$skip_check_password | static::$skip_check_two_step | static::$skip_check_security_restriction);
        } else {
            throw new ExternalException(ErrorCode::TWO_STEP_VERIFY_FAILED, ExceptionFieldKey::TWO_STEP_CAPTCHA);
        }
    }

    public function getAvailableTwoStepMethod()
    {
        $in = $this->request->json()->all();
        SecurityHelper::secureParamDecode($in);

        $user = (new UserLogin())->handle($in)['user'];
        $remote_user = $user->getRemoteUser();
        $enterprise = $remote_user->getEnterprise();

        $methods = [
            TwoStep::METHOD_SMS => TwoStep::METHOD_SMS,
            TwoStep::METHOD_GOOGLE => TwoStep::METHOD_GOOGLE,
            TwoStep::METHOD_WECHAT => TwoStep::METHOD_WECHAT
        ];

        if ($enterprise->is_resource_custom_enterprise || $enterprise->is_sso_enterprise) {
            unset($methods[TwoStep::METHOD_WECHAT]);
        } else if (in_array($enterprise->plan_id, PLan::$experience_plans)) {
            unset($methods[TwoStep::METHOD_SMS]);
        }

        if (($remote_user->country_code && $remote_user->country_code != Common::CHINESE_COUNTRY_CODE)
            || !(CommonHelper::isChineseIp(CommonHelper::getClientIp()))
            || $remote_user->lang != 'zh-CN'
        ) {
            unset($methods[TwoStep::METHOD_GOOGLE]);
            array_unshift($methods, TwoStep::METHOD_GOOGLE);
        }

        return $this->success(['methods' => array_values($methods)]);
    }

    public function getGoogleQRcode()
    {
        $in = $this->request->json()->all();
        SecurityHelper::secureParamDecode($in);

        $user = (new UserLogin())->handle($in)['user'];
        $two_step = new TwoStep(['user' => $user, 'method' => TwoStep::METHOD_GOOGLE, 'reset' => true]);
        $secret = $two_step->getSecret();
        CacheHelper::cacheInfo(SessionCacheKey::APP_TWO_STEP_INFO . $user->getUserId(), ['secret' => $secret, 'method' => TwoStep::METHOD_GOOGLE]);

        return $this->image($two_step->getGoogleQRcode());
    }

    public function getGoogleSecret()
    {
        $in = $this->request->json()->all();
        SecurityHelper::secureParamDecode($in);

        $user = (new UserLogin())->handle($in)['user'];
        $two_step = new TwoStep(['user' => $user, 'method' => TwoStep::METHOD_GOOGLE, 'reset' => true]);
        $secret = $two_step->getSecret();
        CacheHelper::cacheInfo(SessionCacheKey::APP_TWO_STEP_INFO . $user->getUserId(), ['secret' => $secret, 'method' => TwoStep::METHOD_GOOGLE]);

        return $this->success(['secret' => $secret]);
    }

    public function setTwoStep()
    {
        $in = $this->request->json()->all();
        SecurityHelper::secureParamDecode($in);

        $user = (new UserLogin())->handle($in)['user'];

        $code = $in['code'] ?? null;
        $method = $in['method'] ?? null;

        $identifier = null;
        $secret = null;
        /**
         * 1. 微信从 redis 中取缓存数据
         * 2. google 和 sms 都从 session 中取数据进行验证
         *    注意：两者都直接通过离线算法进行验证！
         */
        if ($method === TwoStep::METHOD_WECHAT) {
            $cache_code_info = CacheHelper::getCachedInfo(CacheKey::TWO_STEP_WECHAT_SUBSCRIBE . $code);
            if ($cache_code_info) {
                $identifier = $cache_code_info['wechat_id'];
                $secret = $cache_code_info['secret'];
            } else {
                throw new ExternalException(ErrorCode::TWO_STEP_VERIFY_FAILED, ExceptionFieldKey::TWO_STEP_CAPTCHA);
            }
        } else {
            // method == sms | google | none
            $info = CacheHelper::getCachedInfo(SessionCacheKey::APP_TWO_STEP_INFO . $user->getUserId());
            // 在取数组内容的时候尽量使用 isset 先去判断，否则会出 php notice
            if ($info && isset($info['method']) && $info['method'] === $method) {
                if ((new TwoStep(['secret' => $info['secret'], 'method' => $method]))->verify($code)) {
                    $secret = $info['secret'];
                    if ($method === TwoStep::METHOD_SMS) {
                        $identifier = $info['phone'];
                    }
                    CacheHelper::clearCachedInfo(SessionCacheKey::APP_TWO_STEP_INFO . $user->getUserId());
                } else {
                    throw new ExternalException(ErrorCode::TWO_STEP_VERIFY_FAILED, ExceptionFieldKey::TWO_STEP_CAPTCHA);
                }
            } else {
                // 没有缓存数据的情况下，暂时报这个错
                throw new ExternalException(ErrorCode::TWO_STEP_VERIFY_FAILED, ExceptionFieldKey::TWO_STEP_CAPTCHA);
            }
        }

        if ($secret !== null) {
            $user->setTwoStepSecretAttr($secret);

            // publish ua message
            if ($user->getIsTwoStepEnabledAttr()) {
                // 判断是否开启二次验证，如果开启了，那么就是修改
                UAClient::publish(UAMessageTypes::USER_MODIFY_TWO_STEP);
                SendTwoStepEmail::dispatch($user, SendTwoStepEmail::STATUS_MODIFY);
            } else {
                // 如果没有开启，那么就是设置
                UAClient::publish(UAMessageTypes::USER_OPEN_TWO_STEP);
                SendTwoStepEmail::dispatch($user, SendTwoStepEmail::STATUS_OPEN);
            }

            if ($identifier !== null) {
                $user->setTwoStepIdentifierAttr($identifier);
            } else {
                $user->unsetTwoStepIdentifierAttr();
            }
            $user->setTwoStepMethodAttr($method);

            $user->setIsTwoStepEnabledAttr();
            $user->setTwoStepSetTimeAttr(time());
            $user->save();

            // 删除之前的可信设备
            ClientDevice::setNotCreditableByUserId($user->user_id);

            $in['user'] = $user;
            $in['skip_pic_captcha'] = true;
            return $this->doLogin("app", $in, static::$skip_check_password | static::$skip_check_two_step | static::$skip_check_security_restriction);
        }

        return $this->success();
    }

    public function sendCode()
    {
        $in = $this->request->json()->all();
        SecurityHelper::secureParamDecode($in);

        $user = (new UserLogin())->handle($in)['user'];

        $method = $in['method'] ?? null;
        $phone = $in['phone'] ?? null;
        $sms_type = $in['message_type'] ?? Sms::TYPE_TXT;
        $signature = $in['signature'] ?? null;
        $ts = $in['ts'] ?? null;
        $aliyun_captcha = $in['aliyun_captcha'] ?? null;

        Validator::make([
            'method' => $method,
            'sms_type' => $sms_type,
            'phone' => $phone,
        ], [
            'method' => ['required', Rule::in([TwoStep::METHOD_SMS, TwoStep::METHOD_WECHAT, TwoStep::METHOD_GOOGLE])],
            'sms_type' => ['required', Rule::in(Sms::TYPE_TXT, Sms::TYPE_VOICE)]
        ])->sometimes('phone', new ValidPhone(), function ($in) {
            return $in['method'] === TwoStep::METHOD_SMS && $in['phone'] !== null;
        })->validate();

        $current_version = Context::getEgeioClientInfo('versionNumber');
        $platform_id = Context::getEgeioClientInfo('platform_id');
        $app_platform_id = [Platform::PLATFORM_ID_IOS, Platform::PLATFORM_ID_ANDROID];
        $required_check_signature_sliding = version_compare($current_version, '4.3.0', ">=");
        $checkVerity = in_array($platform_id, $app_platform_id) && $required_check_signature_sliding;
        if ($sms_type == Sms::TYPE_TXT && $checkVerity && $method === TwoStep::METHOD_SMS)
        {
            if (!$signature)
            {
                throw new ExternalException(ErrorCode::TWO_STEP_VERITY_SIGN_ERROR);
            }
            $hit_cache_signature = CacheHelper::getCachedInfo(sprintf(CacheKey::APP_TWO_STEP_VERITY_LOGIN_IDENTITY_FLAG, $signature));
            if ($hit_cache_signature)
            {
                throw new ExternalException(ErrorCode::TWO_STEP_VERITY_SIGN_ERROR);
            }
            $signKey = 'appId='.Sms::TWO_STEP_VERITY_LOGIN_SMS_SIGN_APP_ID.'&phone='.$phone.'&type='.Captcha::TYPE_TWO_STEP.'&ts='.$ts;
            $serverSign = md5($signKey);
            if ($serverSign != $signature)
            {
                throw new ExternalException(ErrorCode::TWO_STEP_VERITY_SIGN_ERROR);
            }
            CacheHelper::cacheInfo(sprintf(CacheKey::APP_TWO_STEP_VERITY_LOGIN_IDENTITY_FLAG, $signature), true,300);
        }

        //cache timestamp
        if ($method === TwoStep::METHOD_SMS && $phone) {
            if ($checkVerity && Captcha::checkCaptchaSwitch())
            {
                if ($aliyun_captcha)
                {
                    $this->checkAliyunCaptcha($in);
                } else {
                    $this->checkQiHooCaptcha($in);
                }
            }
            $secret = CacheHelper::getCachedInfo(SessionCacheKey::APP_TWO_STEP_INFO . $user->getUserId())['secret'];
            if (!$secret) {
                $two_step = new TwoStep(['user' => $user, 'method' => TwoStep::METHOD_SMS]);
                $secret = $two_step->getSecret();
                CacheHelper::cacheInfo(
                    SessionCacheKey::APP_TWO_STEP_INFO . $user->getUserId(),
                    [
                        'secret' => $secret, 'phone' => $phone, 'method' => $method
                    ]);
            } else {
                $two_step = new TwoStep(['user' => $user, 'method' => TwoStep::METHOD_SMS, 'secret' => $secret]);
            }
            (new Sms())->send($phone, $two_step->now(), $sms_type);
        } else {
            if ($user->getIsTwoStepEnabledAttr()) {
                $method = $user->getTwoStepMethodAttr();
                if ($method === TwoStep::METHOD_WECHAT) {
                    (new Wechat())->send(
                        $user->getTwoStepIdentifierAttr(),
                        $user->getName(),
                        (new TwoStep(['user' => $user]))->now()
                    );
                } else if ($method === TwoStep::METHOD_SMS) {
                    if ($checkVerity && Captcha::checkCaptchaSwitch())
                    {
                        if ($aliyun_captcha)
                        {
                            $this->checkAliyunCaptcha($in);
                        } else {
                            $this->checkQiHooCaptcha($in);
                        }
                    }
                    (new Sms())->send(
                        $user->getTwoStepIdentifierAttr(false),
                        (new TwoStep(['user' => $user]))->now(),
                        $sms_type
                    );
                }
            }
        }

        return $this->success();
    }

    private function checkQiHooCaptcha($input)
    {
        $captchaToken = $input['token'] ?? null;
        $captchaVd = $input['vd'] ?? null;
        if (!Captcha::checkQiHooCaptcha($captchaToken, $captchaVd))
        {
            throw new ExternalException(ErrorCode::PIC_CAPTCHA_INVALID, 'sliding_pic_captcha');
        }
    }

    private function checkAliyunCaptcha($input)
    {
        $requestId = $input['request_id'] ?? null;
        if (!Captcha::checkAliyunCaptcha($requestId))
        {
            throw new ExternalException(ErrorCode::PIC_CAPTCHA_INVALID, 'sliding_pic_captcha');
        }
    }

    public function getWechatQRcode()
    {
        return $this->image(TwoStep::getWechatQRcode());
    }

    public function changePassword(ChangePassword $change_password)
    {
        $in = $this->request->json()->all();
        SecurityHelper::secureParamDecode($in);
        try {
            $change_password->handle($in);
        } catch (ExternalException $e) {
            return ['errors' => $e->getFormattedMessage()];
        }

        return $this->success();
    }

    public function showForgotPasswordForm(UserAgent $user_agent)
    {
        $platform = $user_agent->getBrowserPlatform();
        return view('auth.forgot', [
            'js' => 'js/forgot.js',
            'css' => 'css/' . $platform . '.css',
            'platform' => $platform
        ]);
    }

    public function sendCaptchaSms(CaptchaSmsSend $captcha_send)
    {
        $input = $this->request->only('pic_captcha', 'phone', 'type', 'message_type', 'signature', 'vd', 'token', 'ts', 'request_id', 'aliyun_captcha');
        $pic_captcha = $input['pic_captcha'] ?? null;
        $phone = $input['phone'] ?? null;
        $type = $input['type'] ?? '';
        $message_type = $input['message_type'] ?? \App\Util\Sms::MESSAGE_TXT;
        if ($type == Captcha::TYPE_FORGOT_PASSWORD_BY_PHONE) {
            $user_info = CacheHelper::getCachedSessionInfo(SessionCacheKey::FORGOT_PASSWORD_INFO) ?: [];
            if (!$user_info || !isset($user_info['phone'])) {
                throw new ExternalException(ErrorCode::FORGOT_PASSWORD_INFO_EXPIRED);
            }
            $phone = $user_info['phone'];
        }

        $current_version = Context::getEgeioClientInfo('versionNumber');
        $required_check_signature_sliding = version_compare($current_version, '4.3.0', ">=");
        $platform_id = Context::getEgeioClientInfo('platform_id');
        $checkSignTypes = [Captcha::TYPE_REGISTER];
        $app_platform_id = [Platform::PLATFORM_ID_IOS, Platform::PLATFORM_ID_ANDROID];
        $skip_sdk_captcha_check = false;
        if (in_array($platform_id, $app_platform_id) && in_array($type, $checkSignTypes) && $required_check_signature_sliding)
        {
            $signature = $input['signature'] ?? null;
            if (!$signature || !$phone)
            {
                throw new ExternalException(ErrorCode::EMAIL_CAPTCHA_INVALID);
            }
            $hit_cache_signature = CacheHelper::getCachedInfo(sprintf(CacheKey::APP_REGISTER_SIGN_IDENTITY_FLAG, $signature));
            if ($hit_cache_signature)
            {
                throw new ExternalException(ErrorCode::EMAIL_CAPTCHA_INVALID);
            }
            $ts = $input['ts'] ?? null;
            $signKey = 'appId='.Sms::APP_REGISTER_SIGNATURE_APP_ID.'&phone='.$phone.'&type='.$type.'&ts='.$ts;
            $serverSign = md5($signKey);
            if ($serverSign != $signature)
            {
                throw new ExternalException(ErrorCode::EMAIL_CAPTCHA_INVALID);
            }
            CacheHelper::cacheInfo(sprintf(CacheKey::REGISTER_SIGN_IDENTITY_FLAG, $signature), true,300);
            if (in_array($type, $checkSignTypes) && Captcha::checkCaptchaSwitch())
            {
                $aliyun_captcha = $input['aliyun_captcha'] ?? null;
                if ($aliyun_captcha)
                {
                    $this->checkAliyunCaptcha($input);
                } else {
                    $this->checkQiHooCaptcha($input);
                }
            }
        }
        $captcha_send->handle([
            'phone' => $phone,
            'pic_captcha' => $pic_captcha,
            'message_type' => $message_type,
            'captcha_type' => $type,
            'ip' => CommonHelper::getClientIp()
        ]);
        return $this->success();
    }
}
