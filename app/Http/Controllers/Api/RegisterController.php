<?php

namespace App\Http\Controllers\Api;

use App\Exceptions\NeedRedirectException;
use App\Http\Controllers\LoginTrait;
use App\Services\Auth\QuickUserRegister;
use App\Util\SecurityHelper;
use Illuminate\Http\Request;

/**
 * 与 App\Http\Controllers\RegisterController 中部分接口不同的是：
 * 这里的请求都是只需要通过 guard 的验证即可，以上帝视角来操作
 *
 * Class RegisterController
 * @package App\Http\Controllers\Api
 */
class RegisterController extends ApiController
{
    use LoginTrait;

    public function __construct(Request $request)
    {
        parent::__construct($request);

        $this->middleware('guard')->except(['quickRegister']);
    }

    public function quickRegister(QuickUserRegister $userRegister)
    {
        try
        {
            $in = $this->request->json()->all();
            SecurityHelper::secureParamDecode($in);
            $res = $userRegister->handle($in);
            if (is_array($in) && array_key_exists('skip_result', $in) && $in['skip_result']) {
                return $this->success();
            }
            return $this->doLogin('app', ['user' => $res['user']], static::$skip_check_password | static::$skip_check_two_step | static::$skip_check_ent_status | static::$skip_check_security_restriction | static::$skip_init_password);
        }
        catch (NeedRedirectException $e)
        {
            return new RedirectResponse($e->getRedirect());
        }
    }
}
