<?php

namespace App\Http\Controllers;

use App\Exceptions\ValidationException;
use App\Constants\SessionCacheKey;
use App\Util\AuthHelper;
use App\Util\CacheHelper;
use Illuminate\Foundation\Bus\DispatchesJobs;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller as BaseController;
use Illuminate\Foundation\Validation\ValidatesRequests;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Validator;
use App\Util\UserAgent;

class Controller extends BaseController
{
    use AuthorizesRequests, DispatchesJobs, ValidatesRequests, CustomResponseTrait;

    /**
     * @var Request $request
     */
    protected $request;

    public function __construct(Request $request)
    {
        $this->request = $request;
    }

    protected function validate($params, $rules)
    {
        $validator = Validator::make($params, $rules);
        if($validator->fails()) {
            throw new ValidationException($validator->errors()->toArray());
        }
    }

    public function isIdentityVerified(): bool
    {
        return (bool)CacheHelper::getCachedSessionInfo(SessionCacheKey::IDENTITY_VERIFIED);
    }

    public function defaultPage()
    {
        $user_agent = new UserAgent();
        return ($user_agent->isMobile() && !$user_agent->isiPad()) ? config('app.default_h5_page') : config('app.default_page');
    }

    public function returnTo($default_page = '')
    {
        return $this->request->query('redirect') ?:
            ($default_page ?: $this->defaultPage());
    }
}