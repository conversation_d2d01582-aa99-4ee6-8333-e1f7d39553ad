<?php

namespace App\Http\Controllers;

use App\Constants\CacheKey;
use App\Constants\Common;
use App\Constants\ErrorCode;
use App\Constants\ExceptionFieldKey;
use App\Constants\Http;
use App\Constants\Plan;
use App\Constants\SessionCacheKey;
use App\Exceptions\ExternalException;
use App\Exceptions\VerifyIdentityException;
use App\Jobs\SendTwoStepEmail;
use App\Library\OAuth\Constants;
use App\Library\TwoStep;
use App\Models\ClientDevice;
use App\Models\Credential;
use App\Remote\Sms;
use App\Remote\Wechat;
use App\Rules\ValidPhone;
use App\Services\Security\ClientDeviceDelete;
use App\Services\Security\ClientDeviceGet;
use App\Services\Security\ClientDeviceRegister;
use App\Util\CacheHelper;
use App\Util\Captcha;
use App\Util\CommonHelper;
use App\Util\Context;
use App\Util\LogHelper;
use App\Util\LogType;
use App\Util\SecurityHelper;
use App\Util\UA\UAClient;
use App\Util\UA\UAMessageTypes;
use App\Util\UserAgent;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use Throwable;
use App\Services\Security\GetOpenApiTokenInfo;
use App\Services\Security\RefreshOpenApiTokenInfo;

class SecurityController extends Controller
{
    use LoginTrait;

    public function __construct(Request $request)
    {
        parent::__construct($request);

        $this->middleware('auth:web')->only(
            'checkDevice', 'getDevices', 'deleteDevice', 'unbindThird', 'getThirdAccounts', 'verifyIdentity'
        );

        $this->middleware(['auth:web,redis,false', 'fstate', 'lstate'])->only(
            'getWechatQRcode', 'getGoogleQRcode', 'getGoogleSecret', 'getTwoStepMethod',
            'getAvailableTwoStepMethod', 'sendCode', 'setTwoStep', 'closeTwoStep'
        );

        // TODO add identity middleware
//        $this->middleware('identity')->only('setTwoStep', 'sendCode', 'closeTwoStep');
    }

    /**
     * validate auth token
     * @return array|\Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     * @throws \App\Exceptions\ValidationException
     * @throws \Exception
     * @throws \Throwable
     */
    public function checkDevice()
    {
        $input = $this->request->only(['mark_credit', 'additional_info', 'auth_token']);
        $in['device_token'] = Context::getDeviceToken();
        $in['mark_credit'] = $input['mark_credit'] ?? null;
        $in['additional_info'] = $input['additional_info'] ?? null;
        $in['auth_token'] = $input['auth_token'] ?? null;

        $user = Context::user();
        // HTTP_USER_AGENT 为内部的客户端，真实的客户端放在 device_info 中
        $in['user_agent'] = $this->request->header(Http::HEADER_DEVICE_INFO);

        $status = (new ClientDeviceRegister())->handle($in);

        if (!$status) {
            // 检查失败 返回跳转地址，成功返回 success
            $user_agent = new UserAgent();
            if ($user_agent->isWebviewSync()) {
                $redirect = config('app.fangcloud_url') . 'auth/internal_logout';
            } else {
                if ($user->is_demo_user) {
                    $redirect = route('register');
                } else {
                    $redirect = CommonHelper::getLoginUrl();
                }
            }
            return $this->fail(['redirect' => $redirect]);
        } else {
            return $this->success();
        }
    }

    /**
     * @return array
     * @throws \App\Exceptions\ValidationException
     * @throws \Exception
     * @throws \Throwable
     */
    public function getDevices()
    {
        $client_devices = (new ClientDeviceGet())->handle();
        if ($client_devices) {
            $client_devices = ClientDevice::formatAsFrontend($client_devices);
        } else {
            $client_devices = [];
        }

        return $this->success(['devices' => $client_devices]);
    }

    /**
     * @return array
     * @throws \App\Exceptions\ValidationException
     * @throws \Exception
     * @throws \Throwable
     */
    public function deleteDevice()
    {
        $input = $this->request->only(['id']);
        $in['id'] = $input['id'] ?? null;

        (new ClientDeviceDelete())->handle($in);

        return $this->success();
    }

    /**
     * @return array
     * @throws \Exception
     */
    public function unbindThird()
    {
        $input = $this->request->only(['type']);
        $type = $input['type'] ?? null;
        $user = Context::user();

        switch ($type) {
            case Constants::TYPE_DINGTALK:
                $cred = Credential::getUserCredential($user->id, Credential::TYPE_DINGTALK);
                break;
            case Constants::TYPE_WECHAT:
                $cred = Credential::getUserCredential($user->id, Credential::TYPE_WECHAT);
                break;
            case Constants::TYPE_QIHOO360:
                $cred = Credential::getUserCredential($user->id, Credential::TYPE_QIHOO360);
                break;
            default:
                $cred = null;
                break;
        }
        if ($cred) {
            $cred->delete();
        }
        return $this->success();
    }

    public function getThirdAccounts()
    {
        $login_user = Context::user();
        $creds = Credential::getUserCredentials($login_user->id, Credential::$third_login_types);
        $creds = Credential::bulkFormat($creds, ['type', 'nick']);
        return $this->success(['accounts' => $creds]);
    }

    /**
     * @return mixed
     * @throws \App\Exceptions\ExternalException
     */
    public function getWechatQRcode()
    {
        return $this->image(TwoStep::getWechatQRcode());
    }

    public function getGoogleQRcode()
    {
        $user = Context::user();
        $two_step = new TwoStep(['user' => $user, 'method' => TwoStep::METHOD_GOOGLE, 'reset' => true]);
        $secret = $two_step->getSecret();
        CacheHelper::cacheSessionInfo(SessionCacheKey::TWO_STEP_INFO, ['secret' => $secret, 'method' => TwoStep::METHOD_GOOGLE]);

        return $this->image($two_step->getGoogleQRcode());
    }

    public function getGoogleSecret()
    {
        $user = Context::user();
        $two_step = new TwoStep(['user' => $user, 'method' => TwoStep::METHOD_GOOGLE, 'reset' => true]);
        $secret = $two_step->getSecret();
        CacheHelper::cacheSessionInfo(SessionCacheKey::TWO_STEP_INFO, ['secret' => $secret, 'method' => TwoStep::METHOD_GOOGLE]);

        return $this->success(['secret' => $secret]);
    }

    public function getTwoStepMethod()
    {
        $user = Context::user();

        $res = [];
        if ($user->getIsTwoStepEnabledAttr()) {
            $res['method'] = $user->getTwoStepMethodAttr();
            if ($res['method'] === TwoStep::METHOD_SMS) {
                $res['identity'] = $user->getTwoStepIdentifierAttr();
            }
            $res['created'] = $user->getTwoStepSetTimeAttr();
        } else {
            $res['method'] = 'none';
        }

        return $this->success($res);
    }


    /**
     * 获得一个用户可以使用的二次验证方式
     * 1. 混合云+个性化企业 去除微信
     * 2. 国外用户优先推荐谷歌
     * 3. 其余有短信优先推荐短信
     *
     * @return array
     */
    public function getAvailableTwoStepMethod()
    {
        $user = Context::user();
        $remote_user = $user->getRemoteUser();
        $enterprise = $remote_user->getEnterprise();

        $methods = [
            TwoStep::METHOD_SMS => TwoStep::METHOD_SMS,
            TwoStep::METHOD_GOOGLE => TwoStep::METHOD_GOOGLE,
            TwoStep::METHOD_WECHAT => TwoStep::METHOD_WECHAT
        ];

        if ($enterprise->is_resource_custom_enterprise || $enterprise->is_sso_enterprise) {
            unset($methods[TwoStep::METHOD_WECHAT]);
        } else if (in_array($enterprise->plan_id, PLan::$experience_plans)) {
            unset($methods[TwoStep::METHOD_SMS]);
        }

        if ( ($remote_user->country_code && $remote_user->country_code != Common::CHINESE_COUNTRY_CODE)
            || !(CommonHelper::isChineseIp(CommonHelper::getClientIp()))
            || $remote_user->lang != 'zh-CN'
        ) {
            unset($methods[TwoStep::METHOD_GOOGLE]);
            array_unshift($methods, TwoStep::METHOD_GOOGLE);
        }

        return $this->success(['methods' => array_values($methods)]);
    }

    /**
     * 发送二次验证验证码
     * 用于 设置和登录时
     *
     * @return array
     * @throws ExternalException
     * @throws \App\Exceptions\InternalException
     * @throws \Illuminate\Validation\ValidationException
     */
    public function sendCode()
    {
        $input = $this->request->only('method', 'phone', 'message_type', 'ts', 'login-sign', 'pic_captcha', 'token', 'vd', 'request_id');

        $method = $input['method'] ?? null;
        $phone = $input['phone'] ?? null;
        $sms_type = $input['message_type'] ?? Sms::TYPE_TXT;

        $signature = $input['login-sign'] ?? null;
        $ts = $input['ts'] ?? null;
        if ($sms_type == Sms::TYPE_TXT)
        {
            if (!$signature)
            {
                throw new ExternalException(ErrorCode::TWO_STEP_VERITY_SIGN_ERROR);
            }
            $hit_cache_signature = CacheHelper::getCachedInfo(sprintf(CacheKey::TWO_STEP_VERITY_LOGIN_IDENTITY_FLAG, $signature));
            if ($hit_cache_signature)
            {
                throw new ExternalException(ErrorCode::TWO_STEP_VERITY_SIGN_ERROR);
            }
            $signKey = 'appId='.Sms::TWO_STEP_VERITY_LOGIN_SMS_SIGN_APP_ID.'&ts='.$ts.'&phone=t_phone'.'&type='.Captcha::TYPE_TWO_STEP;
            $serverSign = md5($signKey);
            if ($serverSign != $signature)
            {
                throw new ExternalException(ErrorCode::TWO_STEP_VERITY_SIGN_ERROR);
            }
            CacheHelper::cacheInfo(sprintf(CacheKey::TWO_STEP_VERITY_LOGIN_IDENTITY_FLAG, $signature), true,300);
        }

        Validator::make([
            'method' => $method,
            'sms_type' => $sms_type,
            'phone' => $phone,
        ], [
            'method' => ['required', Rule::in([TwoStep::METHOD_SMS, TwoStep::METHOD_WECHAT, TwoStep::METHOD_GOOGLE])],
            'sms_type' => ['required', Rule::in(Sms::TYPE_TXT, Sms::TYPE_VOICE)]
        ])->sometimes('phone', new ValidPhone(), function ($in) {
            return $in['method'] === TwoStep::METHOD_SMS && $in['phone'] !== null;
        })->validate();

        $user = Context::user();

        //cache timestamp
        if ($method === TwoStep::METHOD_SMS && $phone) {

            // 二次验证需要图片验证码
            $this->checkPickCaptcha($input);

            $secret = CacheHelper::getCachedSessionInfo(SessionCacheKey::TWO_STEP_INFO)['secret'];
            if (!$secret) {
                $two_step = new TwoStep(['user' => $user, 'method' => TwoStep::METHOD_SMS]);
                $secret = $two_step->getSecret();
                CacheHelper::cacheSessionInfo(
                    SessionCacheKey::TWO_STEP_INFO,
                    [
                        'secret' => $secret, 'phone' => $phone, 'method' => $method
                    ]);
            } else {
                $two_step = new TwoStep(['user' => $user, 'method' => TwoStep::METHOD_SMS, 'secret' => $secret]);
            }
            (new Sms())->send($phone, $two_step->now(), $sms_type);
        } else {
            if ($user->getIsTwoStepEnabledAttr()) {
                $method = $user->getTwoStepMethodAttr();
                if ($method === TwoStep::METHOD_WECHAT) {
                    (new Wechat())->send(
                        $user->getTwoStepIdentifierAttr(),
                        $user->getName(),
                        (new TwoStep(['user' => $user]))->now()
                    );
                } else if ($method === TwoStep::METHOD_SMS) {

                    $identifier = $user->getTwoStepIdentifierAttr(false);

                    // 二次验证需要图片验证码
                    $this->checkPickCaptcha($input);

                    (new Sms())->send(
                        $identifier,
                        (new TwoStep(['user' => $user]))->now(),
                        $sms_type
                    );
                }
            }
        }

        return $this->success();
    }

    private function checkPickCaptcha($input)
    {
        if (\App\Util\Captcha::checkCaptchaSwitch()) {
            $this->checkAliCaptcha($input);
        } else {
            $this->checkPickCaptchaNaiveWay($input);
        }
    }

    private function checkQiHooCaptcha($input)
    {
        $captchaToken = $input['token'] ?? null;
        $captchaVd = $input['vd'] ?? null;

        if (empty($captchaToken) || empty($captchaVd)) {
            throw new ExternalException(ErrorCode::PIC_CAPTCHA_REQUIRED, 'sliding_pic_captcha');
        }

        if (!Captcha::checkQiHooCaptcha($captchaToken, $captchaVd))
        {
            throw new ExternalException(ErrorCode::PIC_CAPTCHA_INVALID, 'sliding_pic_captcha');
        }
    }
    private function checkAliCaptcha($input)
    {
        $requestId = $input['request_id'] ?? null;
        if (empty($requestId)) {
            throw new ExternalException(ErrorCode::PIC_CAPTCHA_REQUIRED, 'sliding_pic_captcha');
        }
        if (!Captcha::checkAliyunCaptcha($requestId))
        {
            throw new ExternalException(ErrorCode::PIC_CAPTCHA_INVALID, 'sliding_pic_captcha');
        }
    }
    private function checkPickCaptchaNaiveWay($input)
    {
        $pic_captcha = $input['pic_captcha'] ?? null;

        if (empty($pic_captcha)) {
            throw new ExternalException(ErrorCode::PIC_CAPTCHA_REQUIRED, 'pic_captcha');
        }

        if (!Captcha::check($pic_captcha, Captcha::TYPE_TWO_STEP)) {
            throw new ExternalException(ErrorCode::PIC_CAPTCHA_INVALID, 'pic_captcha');
        }
    }

    /**
     * 设置二次验证
     *
     * 如果是 redis guard 通过的验证，会将该用户进行 web guard 的登录。
     * 用于登录前页面的二次验证设置和登录后的二次验证的设置
     *
     * @return array|RedirectResponse|\Illuminate\Routing\Redirector
     * @throws ExternalException
     * @throws \App\Exceptions\ValidationException
     * @throws \Exception
     * @throws \Throwable
     */
    public function setTwoStep()
    {
        $input = $this->request->only('code', 'method');
        $code = $input['code'] ?? null;
        $method = $input['method'] ?? null;

        $identifier = null;
        $secret = null;
        /**
         * 1. 微信从 redis 中取缓存数据
         * 2. google 和 sms 都从 session 中取数据进行验证
         *    注意：两者都直接通过离线算法进行验证！
         */
        if ($method === TwoStep::METHOD_WECHAT) {
            $cache_code_info = CacheHelper::getCachedInfo(CacheKey::TWO_STEP_WECHAT_SUBSCRIBE . $code);
            if ($cache_code_info) {
                $identifier = $cache_code_info['wechat_id'];
                $secret = $cache_code_info['secret'];
            } else {
                throw new ExternalException(ErrorCode::TWO_STEP_VERIFY_FAILED, ExceptionFieldKey::TWO_STEP_CAPTCHA);
            }
        } else {
            // method == sms | google | none
            $info = CacheHelper::getCachedSessionInfo(SessionCacheKey::TWO_STEP_INFO);
            // 在取数组内容的时候尽量使用 isset 先去判断，否则会出 php notice
            if ($info && isset($info['method']) && $info['method'] === $method) {
                if (empty($code)) {
                    throw new ExternalException(ErrorCode::TWO_STEP_VERIFY_FAILED, ExceptionFieldKey::TWO_STEP_CAPTCHA);
                }

                if ((new TwoStep(['secret' => $info['secret'], 'method' => $method]))->verify($code)) {
                    $secret = $info['secret'];
                    if ($method === TwoStep::METHOD_SMS) {
                        $identifier = $info['phone'];
                    }
                    CacheHelper::clearCachedSessionInfo(SessionCacheKey::TWO_STEP_INFO);
                } else {
                    throw new ExternalException(ErrorCode::TWO_STEP_VERIFY_FAILED, ExceptionFieldKey::TWO_STEP_CAPTCHA);
                }
            } else {
                // 没有缓存数据的情况下，暂时报这个错
                throw new ExternalException(ErrorCode::TWO_STEP_VERIFY_FAILED, ExceptionFieldKey::TWO_STEP_CAPTCHA);
            }
        }

        if ($secret !== null) {
            $user = Context::user();
            $user->setTwoStepSecretAttr($secret);

            // publish ua message
            if ($user->getIsTwoStepEnabledAttr()) {
                // 判断是否开启二次验证，如果开启了，那么就是修改
                UAClient::publish(UAMessageTypes::USER_MODIFY_TWO_STEP);
                SendTwoStepEmail::dispatch($user, SendTwoStepEmail::STATUS_MODIFY);
            }
            else {
                // 如果没有开启，那么就是设置
                UAClient::publish(UAMessageTypes::USER_OPEN_TWO_STEP);
                SendTwoStepEmail::dispatch($user, SendTwoStepEmail::STATUS_OPEN);
            }

            if ($identifier !== null) {
                $user->setTwoStepIdentifierAttr($identifier);
            } else {
                $user->unsetTwoStepIdentifierAttr();
            }
            $user->setTwoStepMethodAttr($method);

            $user->setIsTwoStepEnabledAttr();
            $user->setTwoStepSetTimeAttr(time());
            $user->save();

            // 删除之前的可信设备
            ClientDevice::setNotCreditableByUserId($user->user_id);

            // 如果当前处于未登录状态
            // 如果是通过 redis guard 登录的，设置成功之后，需要将该用户登陆 web guard
            if (app('Illuminate\Auth\AuthManager')->getDefaultDriver() === 'redis') {
                return $this->doLogin();
            }
        }

        return $this->success();
    }

    /**
     * @return array
     * @throws ExternalException
     */
    public function closeTwoStep()
    {
        $user = Context::user();

        $enterprise = $user->getRemoteUser()->enterprise;
        if ($enterprise->is_two_step_enabled) {
            throw new ExternalException(ErrorCode::TWO_STEP_CANNOT_CLOSE);
        }
        $user->closeTwoStep();
        $user->save();

        SendTwoStepEmail::dispatch($user, SendTwoStepEmail::STATUS_CLOSE);
        UAClient::publish(UAMessageTypes::USER_CLOSE_TWO_STEP);
        return $this->success();
    }

    /**
     * @return array
     * @throws VerifyIdentityException
     * @throws \Exception
     * @throws \Throwable
     */
    public function verifyIdentity()
    {
        $user = Context::user();
        $enterprise = $user->getRemoteUser()->enterprise;
        //账号对接且非多凭据登录的,直接免密
        if ($enterprise && $enterprise->id != 0 && $enterprise->is_sso_enterprise && !$enterprise->is_public_login_available) {
            $expiration = config('cache.expiration.password_verified');
            CacheHelper::cacheSessionInfo(SessionCacheKey::IDENTITY_VERIFIED, time() + $expiration, $expiration);
            return $this->success();
        }
        $verify_expired = CacheHelper::getCachedSessionInfo(SessionCacheKey::IDENTITY_VERIFIED);
        if (!$verify_expired || $verify_expired < time()) {
            $input = $this->request->only(['password', 'secure_zone']);
            SecurityHelper::secureParamDecode($input);
            $passwd = $input['password'] ?? null;

            if (!$passwd) {
                $this->verify_identity_failed($user, true);
                throw new VerifyIdentityException;
            }

            $user = Context::user();
            if (!$user->authenticate($passwd)) {
                $this->verify_identity_failed($user, true);
                throw new ExternalException(ErrorCode::PASSWORD_INCORRECT, ExceptionFieldKey::PASSWORD);
            }

            $expiration = config('cache.expiration.password_verified');
            CacheHelper::cacheSessionInfo(SessionCacheKey::IDENTITY_VERIFIED, time() + $expiration, $expiration);
        }
        else {
            $expiration = config('cache.expiration.password_verified');
            CacheHelper::cacheSessionInfo(SessionCacheKey::IDENTITY_VERIFIED, time() + $expiration, $expiration);
        }
        $this->clear_verify_identity_cache($user);
        return $this->success();
    }

    private function verify_identity_failed($user, $pass = false)
    {
        try {
            CacheHelper::cacheInfo(sprintf(CacheKey::VERIFY_IDENTITY_FLAG,$user->user_id), $pass, 300);
        }catch (Throwable $ex) {
            LogHelper::info(LogType::Exception, $ex->getMessage(), ['exception' => LogHelper::serializeException($ex)]);
        }
    }

    private function clear_verify_identity_cache($user)
    {
        try {
            CacheHelper::clearCachedInfo(sprintf(CacheKey::VERIFY_IDENTITY_FLAG, $user->user_id));
        }catch (Throwable $ex) {
            LogHelper::info(LogType::Exception, $ex->getMessage(), ['exception' => LogHelper::serializeException($ex)]);
        }
    }


    /**
     * @return array
     * @throws \App\Exceptions\ValidationException
     * @throws \Exception
     * @throws \Throwable
     */
    public function getOpenApiTokenInfo()
    {
        $token_info = (new GetOpenApiTokenInfo())->handle();

        return $this->success($token_info);
    }

    /**
     * @return array
     * @throws \App\Exceptions\ValidationException
     * @throws \Exception
     * @throws \Throwable
     */
    public function refreshOpenApiTokenInfo()
    {
        $due_time_type = $this->request->json('due_time_type');
        $in['due_time_type'] = $due_time_type ?? null;

        $token_info = (new RefreshOpenApiTokenInfo())->handle($in);
        return $this->success($token_info);
    }
}
