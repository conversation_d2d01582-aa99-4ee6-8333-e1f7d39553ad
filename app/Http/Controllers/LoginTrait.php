<?php

namespace App\Http\Controllers;

use App\Constants\Common;
use App\Constants\ErrorCode;
use App\Constants\Http;
use App\Exceptions\ExternalException;
use App\Exceptions\InternalException;
use App\Exceptions\NeedRedirectException;
use App\Jobs\SendSystemMessage;
use App\Models\ClientDevice;
use App\Models\Service;
use App\Models\User;
use App\Remote\Chameleon;
use App\Remote\Enterprise;
use App\Remote\SecurityLoginRestriction;
use App\Remote\User as RemoteUser;
use App\Services\Auth\AuthTokenCreate;
use App\Services\Auth\PreLogin;
use App\Services\Auth\PreRefreshLogin;
use App\Services\Auth\UserLogin;
use App\Services\Security\ClientDeviceRegister;
use App\Util\CommonHelper;
use App\Util\Context;
use App\Util\LogHelper;
use App\Util\LoginHelper;
use App\Util\UA\UAMessageTypes;
use App\Util\UA\UAClient;
use Closure;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cookie;
use Illuminate\Support\Str;

use App\Constants\CacheKey;
use App\Util\AuthHelper;
use App\Util\CacheHelper;
use App\Util\UserAgent;
use App\Util\ViewHelper;
use App\Util\LogType;
use Throwable;


/**
 * Attention!
 * 该trait 使用的trait 原则上不应该在其他 controller 使用, 会出现重定义
 *
 * Trait LoginTrait
 * @package app\Http\Controllers
 * @property \Illuminate\Http\Request $request
 */
trait LoginTrait
{
    use ClientVersionCheck;

    public static $skip_check_password = 2;
    public static $skip_check_two_step = 4;
    public static $skip_check_ent_status = 8;
    public static $skip_check_security_restriction = 16;
    public static $skip_init_password = 32;

    /**
     * login 一共分为四步：
     * [step1]: 可选，密码等校验，在 Action UserLogin 中完成。第三方账号登录时会忽略
     * [step2]: 可选，二次验证相关的校验（如果有），在 doCheckTwoStep 中完成分发，在LoginController twoStepLogin 中完成校验
     * [step3]: 可选，企业等状态的校验
     * step4: 用户登录
     *
     * @param string $login_type  登录类型 正常为 web app sync。第三方登录为 bindweb
     * @param array $params params used for UserLogin and actions
     * @param int $login_flag 使用 static::$skip_check_two_step  static::$skip_check_ent_status 来跳过其中的若干步
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector|mixed
     * @throws InternalException
     * @throws NeedRedirectException
     * @throws \App\Exceptions\ValidationException
     * @throws \Exception
     * @throws \Throwable
     */
    public function doLogin($login_type = null, array $params = [], int $login_flag = 0)
    {
        // 注: 上下文切换强依赖于 FirstStateManager && LastStateManager 实现
        // 在login type都为空的情况下，说明这是上下文切换回来时的调用
        // 比如 \App\Controller\SecurityController function setTwoStep
        if ($login_type === null) {
            // 默认已经有 state
            $context = Context::getFStateContent()['_context'];

            // login type 原则上不需要再次指定
            $login_type = $context['login_type'];
            // 也可以继续传入新的参数
            $params = array_replace($context['params'] ?? [], $params);
            $login_flag = (int)$context['login_flag'] | $login_flag;
        }

        $this->validateLoginType($login_type);

        // step1  用户名密码等校验
        // 是否跳过步骤一
        if ($this->needCheckPassword($login_flag)) {
            if(!isset($params['v1_error'])) {
                $params['v1_error'] = $login_type !== 'web';
            }
            $user = (new UserLogin())->handle($params)['user'];
        } else {
            $user = $params['user'] ?? null;
            if ($user === null) {
                throw new InternalException("if skip step1, a specific user is required");
            }
            // 仍然需要检查是否冻结
            if(AuthHelper::isLoginPasswordErrorLimit($user->user_id)) {
                $current_user = $user->getRemoteUser()->getEnterprise();
                $enterprise = $current_user->getEnterprise();
                LoginHelper::login_exception_reminder($enterprise, $current_user, LoginHelper::getDeviceName(), CommonHelper::getClientIp());
                throw new ExternalException(ErrorCode::PASSWORD_ERROR_FROZEN, 'password');
            }
        }

        // 账号对应的企业信息检验
        if (!$this->checkUserWithDomainInfo($user)) {
            throw new ExternalException(ErrorCode::USER_NOT_IN_LOCAL_ENTERPRISE, 'password');
        }

        if($this->needCheckSecurityRestriction($login_flag)) {
            // 登录限制
            $sn_code = array_key_exists('sn_code', $params) ? $params['sn_code'] : null;
            $this->checkSecurityRestriction($user, $sn_code);
        }

        // step2 二次验证校验
        // 是否跳过步骤二
        // AjaxRedirect后Context被重置，为了不影响FStateContext这里重写缓存
        $qr_login_type = array_key_exists('qr_login_type', $params) && $params['qr_login_type'];
        if ($qr_login_type)
        {
            $user = $params['user'];
            if ($user)
            {
                try {
                    CacheHelper::cacheInfo(sprintf(CacheKey::QR_LOGIN_SKIP_TWO_STEP,$user->user_id), true);
                } catch (Throwable $ex) {
                    LogHelper::info(LogType::Exception, $ex->getMessage(), ['exception' => LogHelper::serializeException($ex)]);
                }
            }
        }
        if (!$qr_login_type && $this->needCheckTwoStep($login_flag)) {
            $func_ok = $this->getCallable($login_type, 'action_need_two_step', $params);
            $func_need = $this->getCallable($login_type, 'action_force_two_step', $params);

            $action_need_two_step = call_user_func_array($func_ok, [$user, $params]);
            $action_force_two_step = call_user_func_array($func_need, [$user, $params]);

            $only_throw = array_key_exists('only_throw', $params) ? $params['only_throw'] : false;
            if ($only_throw) {
                Context::setUser($user);
            }
            if (!(($status = $this->checkTwoStep($user, $action_need_two_step, $action_force_two_step, $only_throw)) === true)) {
                // 发生上下文切换
                // 需要保留现场
                // 再次回调的时候，理论上已经完成了密码验证和二次验证?
                Context::setFStateContent([
                    '_context' => [
                        'login_type' => $login_type,
                        // 当前的 user 会覆盖传入的 user
                        'params' => array_replace($params, ['user' => $user]),
                        'login_flag' => $login_flag | static::$skip_check_password | static::$skip_check_two_step
                    ]
                ]);

                return $status;
            }
        }
        //抖音渠道注册企业
        $tp_source_first_login = false;
        if ($this->fromDouYinRegisterUnActive($user)) {
            $enterprise = new Enterprise(['id' => $user->enterprise_id]);
            $res = $enterprise->activeDouYinRegisterEnterprise();
            if ($res) {
                LogHelper::info(LogType::WebAndServerService, "set tp_source_first_login true");
                $tp_source_first_login = true;
            }
        }
        // step3 企业状态校验
        // 是否跳过步骤三
        if (!$tp_source_first_login && $this->needCheckEntStatus($login_flag)) {
            (new PreLogin())->handle(['user' => $user, 'login_type' => $login_type]);
        }
        // step4 实际登录
        // do login
        $func_none = $this->getCallable($login_type, 'action_none', $params);
        $action_none = call_user_func_array($func_none, [$user, $params]);
        $return = $action_none();
        // 清除_fstate
        Context::clearFStateContent();

        $user = Context::user();
        $enterprise = $user->getRemoteUser()->getEnterprise();
        Context::setEnterprise($enterprise);


        // 记录登录日志
        UAClient::publish(UAMessageTypes::USER_LOGIN);
        if ($enterprise->is_login_reminder_system_enabled) {
            $this->login_reminder($user->user_id, $this->getDeviceName(), CommonHelper::getClientIp());
        }

        if ($login_type !== 'app' && $this->needCheckInitPassword($login_flag) && $user->getPasswordRequireInitAttr()) {
            Context::getFState();
            if ($return instanceof RedirectResponse) {
                /** @var RedirectResponse $return */
                Context::setFStateContent(['_login' => ['_redirect' => $return->getTargetUrl()]]);
            } else {
                Context::setFStateContent(['_login' => $return]);
            }
            return redirect(route('init_password'));
        }

        if ($return instanceof RedirectResponse) {
            $url = $return->getTargetUrl();
            if (CommonHelper::getProductId() == 'fangcloud_v2') {
                if (!stristr($url, env('FANG_HOST'))) {
                    $return->setTargetUrl(config('app.default_page'));
                }
            }
        }
        return $return;
    }

    private function getBrowser() {
        $agent = '';
        $x_device_info = $this->request->header('x-device-info');
        if ($x_device_info) {
            $agent = $x_device_info;
        } else {
            $user_agent = $this->request->header('user-agent');
            if ($user_agent) {
                $agent = $user_agent;
            }
        }
        $browser = '';
        $browser_ver = '';
        if ($agent) {
            if (preg_match('/Chrome\/([^\s]+)/i', $agent, $regs)) {
                $browser = 'Chrome';
                $browser_ver = $regs[1];
            } else if (preg_match('/OmniWeb\/(v*)([^\s|;]+)/i', $agent, $regs)) {
                $browser = 'OmniWeb';
                $browser_ver = $regs[2];
            } else if (preg_match('/Netscape([\d]*)\/([^\s]+)/i', $agent, $regs)) {
                $browser = 'Netscape';
                $browser_ver = $regs[2];
            } else if (preg_match('/safari\/([^\s]+)/i', $agent, $regs)) {
                $browser = 'Safari';
                $browser_ver = $regs[1];
            } else if (preg_match('/MSIE\s([^\s|;]+)/i', $agent, $regs)) {
                $browser = 'Internet Explorer';
                $browser_ver = $regs[1];
            } else if (preg_match('/Opera[\s|\/]([^\s]+)/i', $agent, $regs)) {
                $browser = 'Opera';
                $browser_ver = $regs[1];
            } else if (preg_match('/NetCaptor\s([^\s|;]+)/i', $agent, $regs)) {
                $browser = '(Internet Explorer ' . $browser_ver . ') NetCaptor';
                $browser_ver = $regs[1];
            } else if (preg_match('/Maxthon/i', $agent, $regs)) {
                $browser = '(Internet Explorer ' . $browser_ver . ') Maxthon';
                $browser_ver = '';
            } else if (preg_match('/360SE/i', $agent, $regs)) {
                $browser = '(Internet Explorer ' . $browser_ver . ') 360SE';
                $browser_ver = '';
            } else if (preg_match('/SE 2.x/i', $agent, $regs)) {
                $browser = '(Internet Explorer ' . $browser_ver . ') 搜狗';
                $browser_ver = '';
            } else if (preg_match('/FireFox\/([^\s]+)/i', $agent, $regs)) {
                $browser = 'FireFox';
                $browser_ver = $regs[1];
            } else if (preg_match('/Lynx\/([^\s]+)/i', $agent, $regs)) {
                $browser = 'Lynx';
                $browser_ver = $regs[1];
            } else if (preg_match('/MicroMessenger\/([^\s]+)/i', $agent, $regs)) {
                $browser = '微信浏览器';
                $browser_ver = $regs[1];
            } else {
                return 'Unknow browser';
            }
            if ($browser != '') {
                return $browser . '/' . $browser_ver;
            }
        } else {
            return $browser;
        }
    }

    private function getDeviceName()
    {
        $egeio_info = $this->request->header('Egeio-Client-Info');
        if ($egeio_info) {
            $info = json_decode($egeio_info, true);
            $device_name = (json_last_error() === JSON_ERROR_NONE
                ? urldecode($info['deviceName'])
                : null);
            if (!json_encode($device_name)) {
                $device_name = null;
            }
            return $device_name;
        } else {
            return $this->getBrowser();
        }
    }

    protected function login_reminder($user_id, $device_name, $ip)
    {
        $base_api_url = config('app.api_url');
        $receiver_ids[] = $user_id;
        $additional_info['receiver_ids'] = json_encode($receiver_ids);
        $additional_info['title_code'] = SendSystemMessage::SYSTEM_MESSAGE_LOGIN_REMINDER_TITLE;
        $additional_info['content_code'] = SendSystemMessage::SYSTEM_MESSAGE_LOGIN_REMINDER_CONTENT;
        $content_args = [date("H:i:s A"), $device_name, $ip];
        $additional_info['content_args'] = json_encode($content_args);
        $additional_info['type'] = SendSystemMessage::SYSTEM_MESSAGE_LOGIN_REMINDER;

        $job_specs['callback'] = $base_api_url . '/internal_api/jobs/message_system_job';
        $job_specs['callback_service_id'] = 1;
        $job_specs['additional_infos'] [] = $additional_info;
        $job_specs['jobType'] = 'workflow_message_job';
        $job_specs['jobType'] = 1;
        $job_specs['timestamp'] = time();
        $send_system_message = new SendSystemMessage();
        $send_system_message->setBody($job_specs);
        $send_system_message->send();
    }

    private function needCheckPassword($flag)
    {
        return !($flag >> 1 & 1);
    }

    private function needCheckTwoStep($flag)
    {
        return !($flag >> 2 & 1);
    }

    private function needCheckEntStatus($flag)
    {
        return !($flag >> 3 & 1);
    }

    private function needCheckSecurityRestriction($flag){
        return !($flag >> 4 & 1);
    }

    private function needCheckInitPassword($flag){
        return !($flag >> 5 & 1);
    }

    /**
     * @param $user
     * @return bool
     * 混合云企业使用account 登录时需要验证 当前环境中的 product_id 跟 变色龙中的配置是否一致
     */
    private function checkUserWithDomainInfo($user) {

        $product_id = CommonHelper::getProductId();
        if ($product_id == "fangcloud_v2") {
            return true;
        }

        $auth_type = '';
        $chameleon_sso_local_dir = base_path() . '/../chameleon/sso_configs/'. $product_id. '.json';
        if(file_exists($chameleon_sso_local_dir)) {
            $sso_config_local = json_decode(file_get_contents($chameleon_sso_local_dir), true);
            $auth_type = $sso_config_local['auth_type'];
        }

        if ($auth_type == "sso_main") {
            try {
                $all_sso_configs = Chameleon::get_all_sso_configs();
                $sso_config = $all_sso_configs[$product_id];
                if (isset($sso_config['auth_type']) && $sso_config['auth_type'] == "sso_main") {
                    $enterprise = $user->getRemoteUser()->getEnterprise();
                    if (isset($sso_config['platform_id']) && $enterprise->platform_id != $sso_config['platform_id']) {
                        return false;
                    }
                }
            } catch (\Exception $e) {
                // 变色龙异常时不影响登录
                return true;
            }
        }
        return true;
    }



    private function getCallable($login_type, $callable_type, $params)
    {
        // action_need_two_step => ActionNeedTwoStep
        // action_force_two_step => ActionForceTwoStep
        // action_none => ActionNone
        return isset($params[$callable_type])
            ? (is_callable($params[$callable_type]) ? $params[$callable_type] : [$this, $params[$callable_type]])
            : [$this, $login_type.'Login' . Str::studly($callable_type)];
    }

    /**
     * @param string $qrlogin_type
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector|mixed
     * @throws \App\Exceptions\InternalException
     * @throws \App\Exceptions\ValidationException
     * @throws \Exception
     * @throws \Throwable
     */

    public function qrloginCheckToken($qrlogin_type)
    {
        $result = [];
        $from = $this->request->query('from');

        if(!($token = $this->request->json('token'))) {
            $status = self::STATUS_NEW_TOKEN;
        }
        else {
            $cached_info = CacheHelper::getCachedInfo(sprintf(CacheKey::QR_LOGIN_TOKEN, $token));
            if(!$cached_info || !isset($cached_info['status']) || !$cached_info['session_id']) {
                $status = self::STATUS_NEW_TOKEN;
            }
            else {
                $status = $cached_info['status'];
                if($status == self::STATUS_LOGGED_IN && isset($cached_info['logged_in_user_id'])) {
                    $user = User::find($cached_info['logged_in_user_id']);
                    $in['user'] = $user;
                    $in['sn_code'] = $cached_info['sn_code'];
                    $in['qr_login_type'] = true;
                    $login_type = $from ?? 'web';

                    if($qrlogin_type === 'oauth') {
                        $url = config('app.fangcloud_url') . 'third_party/oauth/login?' . http_build_query($this->request->query->all());
                        $this->request->query->add(['redirect' => $url]);
                    }
                    $result = $this->doLogin($login_type, $in, static::$skip_check_password);

                    CacheHelper::clearCachedInfo(sprintf(CacheKey::QR_LOGIN_TOKEN, $token));
                    return $result;
                }
                else {
                    // TODO: 报错
                }
            }
        }
        if($status == self::STATUS_NEW_TOKEN) {
            $new_token = Str::random(32);
            $result['new_token'] = $new_token;
            CacheHelper::cacheInfo(sprintf(CacheKey::QR_LOGIN_TOKEN, $new_token), [
                'status' => self::STATUS_NOTHING,
                'session_id' => AuthHelper::getSessionId(),
                'from_platform' => (new UserAgent())->getBrowserPlatform(),
                'sn_code' => $this->request->header('X-Device-Serial-Number'),
            ], config('auth.qr_code_expire'));
        }
        $result['status'] = $status;
        return $this->success($result);
    }

    /**
     * validate login type.
     * typically get from UserAgent()->getBrowserPlatform
     *
     * @param $login_type
     * @throws InternalException
     */
    private function validateLoginType($login_type)
    {
        if (!in_array($login_type, ['web', 'sync', 'app'])) {
            throw new InternalException("{$login_type} is invalid login type");
        }
    }

    /**
     * step 2
     *
     * @param User $user
     * @param callable|null $action_need_two_step 当出现 需要二次验证需要发生的事。默认是重定向到登录页
     * @param callable|null $action_force_two_step 当出现 强制二次验证时需要发生的事。默认是重定向到二次验证设置页
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector|mixed
     */
    public function checkTwoStep(User $user, ?Callable $action_need_two_step = null, ?Callable $action_force_two_step = null, bool $only_throw = false)
    {
        // step 2
        $two_step_status = $user->getTwoStepStatus();

        if ($two_step_status === User::TWO_STEP_STATUS_OK) {
            $device_token = Context::getDeviceToken();
            $client_device = null;
            if ($device_token) {
                $client_device = ClientDevice::findByUserIdDeviceToken($user->user_id, $device_token);
            }

            if (!$client_device || !$client_device->isCredit()) {
                if ($only_throw) {
                    throw new ExternalException(ErrorCode::IDENTITY_NEED_TWO_STEP_VERIFY);
                }
                return $action_need_two_step === null ? $this->actionNothing()() : $action_need_two_step();
            }
        } else if ($two_step_status === User::TWO_STEP_STATUS_NEED) {
            if ($only_throw) {
                throw new ExternalException(ErrorCode::IDENTITY_NEED_FORCE_TWO_STEP);
            }
            return $action_force_two_step === null ? $this->actionNothing()() : $action_force_two_step();
        }

        return true;
    }

    /**
     * do nothing
     * @return Closure
     */
    private function actionNothing()
    {
        return function () {
            return ;
        };
    }

    protected function syncLoginActionNone(User $user, array $params = [])
    {
        return function () use ($user, $params) {
            $api_key = $params['api_key'] ?? $this->request->header(Http::HEADER_API_KEY);
            if (!$api_key) {
                throw new InternalException('sync login miss api key');
            }
            $service = Service::findByApiKey($api_key);

            $current_version = Context::getEgeioClientInfo('versionNumber');
            $platform_id = Context::getEgeioClientInfo('platform_id');

            if(!$this->checkEncryptionVersion($user, $current_version, $platform_id)) {
                throw new ExternalException(ErrorCode::CLIENT_VERSION_LOWER_THAN_MIN_ENCRYPTION_LIMIT);
            }
            if(!$this->checkDlpDevice($user, $current_version)) {
                throw new ExternalException(ErrorCode::DLP_CLIENT_COUNT_EXCEED);
            }

            /**
             * @var \App\Models\AuthToken $auth_token
             */
            $auth_token = (new AuthTokenCreate())->handle([
                'user' => $user,
                'service' => $service,
            ]);
            $results['auth_token'] = $auth_token->auth_token;
            $results['refresh_token'] = $auth_token->refresh_token;
            $results['expires_at'] = $auth_token->expires_at;

            if (version_compare($current_version, config('versions.user_sync_version'), '<')) {
                $results['user'] = $user->format(User::$app_keys);
            }

            if($user->getPasswordRequireInitAttr()) {
                Auth::guard('web')->login($user);
            } else {
                Auth::guard('web')->setUser($user);
            }

            Context::setAuthToken($auth_token->auth_token);

            $this->registerDevice(json_encode(Context::getEgeioClientInfo()));

            RemoteUser::dealWithUserLogin($user->getUserId());

            return $this->success($results);
        };
    }

    protected function syncLoginActionNeedTwoStep(User $user, array $params = [])
    {
        return function () use ($user, $params) {
            /**
             * @var \App\Auth\RedisGuard $guard
             */
            $guard = Auth::guard('redis');
            $guard->loginViaTwoStep($user);

            return redirect('two_step_login');
        };
    }

    protected function syncLoginActionForceTwoStep(User $user, array $params = [])
    {
        return function() use ($user, $params) {
            $current_version = Context::getEgeioClientInfo('versionNumber');
            if (empty($params['fromApp']) && version_compare($current_version, config('versions.new_login_sync_version'), '<')) {
                throw new ExternalException(ErrorCode::TWO_STEP_FORCE_TWO_STEP);
            } else {
                /**
                 * @var \App\Auth\RedisGuard $guard
                 */
                $guard = Auth::guard('redis');
                $guard->loginViaTwoStep($user);

                return redirect('force_set_two_step');
            }
        };
    }

    protected function appLoginActionNone(User $user, array $params = [])
    {
        return function () use ($user, $params) {
            $api_key = $params['api_key'] ?? $this->request->query('api_key');
            if (empty($api_key)) {
                $api_key = $this->request->header(Http::HEADER_API_KEY);
            }
            if (!$api_key) {
                throw new InternalException('app login miss api key');
            }
            $service = Service::findByApiKey($api_key);

            $current_version = Context::getEgeioClientInfo('versionNumber');

            /**
             * @var \App\Models\AuthToken $auth_token
             */
            $auth_token = (new AuthTokenCreate())->handle([
                'user' => $user,
                'service' => $service
            ]);
            $results['auth_token'] = $auth_token->auth_token;
            $results['refresh_token'] = $auth_token->refresh_token;
            $results['expires_at'] = $auth_token->expires_at;
            $results['env_config'] = config('app.env_config');

//            if (version_compare($current_version, config('versions.user_app_version'), '<')) {
                $results['user'] = $user->format(User::$app_keys);
//            }

            if($user->getPasswordRequireInitAttr()) {
                Auth::guard('web')->login($user);
            } else {
                Auth::guard('web')->setUser($user);
            }

            Context::setAuthToken($auth_token->auth_token);

            $this->registerDevice(json_encode(Context::getEgeioClientInfo()));

            $enterprise = $user->getRemoteUser()->getEnterprise();
            if($enterprise->is_external_login_enabled) {
                Cookie::queue(Cookie::forever(Common::PUBLIC_PRODUCT_ID_COOKIE_NAME, $enterprise->platform_id ));
            }
            else {
                Cookie::queue(Cookie::forget(Common::PUBLIC_PRODUCT_ID_COOKIE_NAME));
            }

            return $this->success($results);
        };
    }

    protected function appLoginActionNeedTwoStep(User $user, array $params = [])
    {
        return $this->syncLoginActionNeedTwoStep($user, $params);
    }

    protected function appLoginActionForceTwoStep(User $user, array $params = [])
    {
        return $this->syncLoginActionForceTwoStep($user, array_merge($params, ['fromApp' => true]));
    }

    protected function webLoginActionNeedTwoStep(User $user, array $params = [])
    {
        return function () use ($user, $params) {
            /**
             * @var \App\Auth\RedisGuard $guard
             */
            $guard = Auth::guard('redis');
            $guard->loginViaTwoStep($user);

            // 将 记住我 状态放入state
            $remember_login = (bool)($params['remember_login'] ?? false);
            if ($remember_login) {
                Context::setFStateContent(['remember_login' => $remember_login]);
            }

            return redirect('two_step_login');
        };
    }

    protected function webLoginActionForceTwoStep(User $user, array $params = [])
    {
        return function() use ($user, $params) {
            /**
             * @var \App\Auth\RedisGuard $guard
             */
            $guard = Auth::guard('redis');
            $guard->loginViaTwoStep($user);

            // 将 记住我 状态放入state
            $remember_login = (bool)($params['remember_login'] ?? false);
            if ($remember_login) {
                Context::setFStateContent(['remember_login' => $remember_login]);
            }

            return redirect('force_set_two_step');
        };
    }

    protected function webLoginActionNone(User $user, array $params = [])
    {
        return function() use ($user, $params) {
            //LogHelper::info(LogType::WebAndServerService, "webLoginActionNone start..");
            $remember_login = (bool)($params['remember_login'] ?? false);
            $mark_credit = $params['mark_credit'] ?? null;
            $redirect = $params['redirect'] ?? $this->returnTo();
            //LogHelper::info(LogType::WebAndServerService, "webLoginActionNone user_info".$user->updated.",value_box=".$user->value_box);
            Auth::guard('web')->login($user, $remember_login);
            Context::setEnterprise(Context::user()->getRemoteUser()->enterprise);

            $this->registerDevice(null, $mark_credit);
            $need_return_json = $params['need_return_json'] ?? false;
            if ($need_return_json) {
                $result['login_status'] = $params['to_register'];
                return $this->success($result);
            } else {
                return redirect($redirect);
            }
        };
    }

    /**
     * 直接请求 account 站点的设备注册
     * device_additional_info
     *
     * @param null $additional_info
     * @param bool $mark_credit
     * @throws \App\Exceptions\ValidationException
     * @throws \Exception
     * @throws \Throwable
     */
    public function registerDevice($additional_info = null, $mark_credit = null)
    {
        (new ClientDeviceRegister())->handle([
            'user_agent' => $_SERVER['HTTP_USER_AGENT'],
            'device_token' => Context::getDeviceToken(),
            'mark_credit' => $mark_credit,
            'additional_info' => $additional_info,
            'auth_token' => Context::getAuthToken(),
            'scene' => 'login',
        ]);
    }
    /**
     * 验证登录限制
     *
     * @param User $user
     * @param string $sn_code
     *
     * @throws ExternalException
    */
    public function checkSecurityRestriction($user, $sn_code = null)
    {
        $agent = new UserAgent();

        $check_param = array(
            'ip' => CommonHelper::getClientIp(), // ip登录限制参数
            'platform' => $agent->getBrowserPlatform(),
            'sn_code' => $sn_code ?? $this->request->header('X-Device-Serial-Number'),
        );
        if ($agent->getBrowserPlatform() == UserAgent::BROWSER_PLATFORM_MOBILE) {
            $device_id = trim($this->request->header('Device-Id'));
            if(!$device_id)
            {
                $egeio_client_info = $this->request->header(Http::HEADER_EGEIO_CLIENT_INFO);
                $egeio_client_info = json_decode(trim($egeio_client_info), true);
                $device_id = $egeio_client_info['Device-ID'];
            }
            if ($device_id) {
                $check_param['sn_code'] = $device_id;
            }
        }

        $check_client = new SecurityLoginRestriction();
        $pass = $check_client->is_pass_check($user->user_id, $check_param);
        if (isset($pass['is_pass']) && !$pass['is_pass']) {
            if (isset($pass['error_code']) && $pass['error_code'] === 'err_empty_sn_code') {
                throw new ExternalException(ErrorCode::SECURITY_LOGIN_EMPTY_SN_CODE, null, ['download_link' =>
                    $agent->isMacDesktop() ? ViewHelper::getMacDownloadLink() : ViewHelper::getWindowsDownloadLink()]);
            }
            if (isset($pass['restriction_type'])) {
                switch ($pass['restriction_type']) {
                    case 'ip':
                        throw new ExternalException(ErrorCode::SECURITY_LOGIN_FORBIDDEN_IP);
                        break;
                    case UserAgent::BROWSER_PLATFORM_WEB:
                        throw new ExternalException(ErrorCode::SECURITY_LOGIN_FORBIDDEN_WEB);
                        break;
                    case UserAgent::BROWSER_PLATFORM_SYNC:
                        throw new ExternalException(ErrorCode::SECURITY_LOGIN_FORBIDDEN_SYNC);
                        break;
                    case UserAgent::BROWSER_PLATFORM_MOBILE:
                        throw new ExternalException(ErrorCode::SECURITY_LOGIN_FORBIDDEN_MOBILE);
                        break;
                    default:
                        break;
                }
            }
        }
    }
    private function fromDouYinRegisterUnActive($user):bool
    {
        return ($user->settings & 4) > 0;
    }
}
