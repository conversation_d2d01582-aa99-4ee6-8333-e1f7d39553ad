<?php

namespace App\Http\Controllers\Auth;

use App\Constants\ErrorCode;
use App\Exceptions\ExternalException;
use App\Http\Controllers\Controller;
use App\Http\Controllers\LoginTrait;
use App\Models\Credential;
use App\Services\Activation\ConfirmActivateAccount;
use App\Services\Activation\UserActivate;
use App\Services\Activation\VerifyActivationPhone;
use App\Util\HttpHelper;
use App\Util\SecurityHelper;
use Illuminate\Http\Request;
use App\Util\UserAgent;

class ActivationController extends Controller
{
    use LoginTrait;

    public function __construct(Request $request)
    {
        $this->middleware(['guest', 'fstate', 'lstate']);
        parent::__construct($request);
    }

    public function activate(UserActivate $user_active)
    {
        $credential = Credential::getCredentialByActivationCode($this->request->json('code'));
        if(!$credential || $credential->status != Credential::STATUS_TO_BE_VALIDATED) {
            return ['errors' => [(new ExternalException(ErrorCode::ACTIVATION_CODE_INVALID))->getFormattedMessage()]];
        }
        $user = $credential->getUser();
        if(!$user || $user->isActive()) {
            return ['errors' => [(new ExternalException(ErrorCode::ACTIVATION_CODE_INVALID))->getFormattedMessage()]];
        }
        if($credential->activation_expires_at < time()) {
            return ['errors' => [(new ExternalException(ErrorCode::ACTIVATION_CODE_INVALID))->getFormattedMessage()]];
        }
        $params = $this->request->json()->all();
        SecurityHelper::secureParamDecode($params);
        $login_type = $params['login_type'] ?? 'web';
        $is_mobile = $params['is_mobile'] ?? false;

        $params['credential'] = $credential;
        $result = $user_active->handle($params);

        $remote_user = $result['user']->getRemoteUser();
        $info = $remote_user->getInfo(['space_total']);
        $enterprise = $remote_user->getEnterprise();
        return $this->doLogin($login_type, ['user' => $result['user'], 'redirect' => route('validate_user_success', ['space' => $info['space_total'], 'name' => $enterprise->name])], static::$skip_check_password | static::$skip_check_two_step);
    }

    public function showActivationForm(UserAgent $user_agent)
    {
        HttpHelper::disableCache();

        $platform = $user_agent->getBrowserPlatform();
        return view('auth.phone_activation', [
            'css' => 'css/' . $platform . '.css',
            'js' => 'js/activation.js',
            'platform' => $platform
        ]);
    }

    public function confirmAccount(ConfirmActivateAccount $confirm_activate_account, UserAgent $user_agent)
    {
        $params = $this->request->json()->all();
        $platform = $user_agent->getBrowserPlatform();
        $params['allow_v1'] = $platform === 'web';
        $confirm_activate_account->handle($params);
        return $this->success();
    }

    public function verifyPhone(VerifyActivationPhone $verify_activation_phone)
    {
        $result = $verify_activation_phone->handle($this->request->json()->all());
        return $this->success($result);
    }

    public function activation($code, UserAgent $user_agent)
    {
        $platform = $user_agent->getBrowserPlatform();

        $credential = Credential::getCredentialByActivationCode($code);
        if(!$credential) {
            $result = ['errors' => (new ExternalException(ErrorCode::ACTIVATION_CODE_INVALID))->getFormattedMessage()];
        } elseif($credential->status != Credential::STATUS_TO_BE_VALIDATED) {
            if ($credential->status == Credential::STATUS_VALID) {
                $result = ['errors' => (new ExternalException(ErrorCode::CREDENTIAL_ALREADY_ACTIVATED))->getFormattedMessage()];
            } else {
                $result = ['errors' => (new ExternalException(ErrorCode::ACTIVATION_CODE_INVALID))->getFormattedMessage()];
            }
        } else {
            $user = $credential->getUser();
            if(!$user || $user->isActive()) {
                return redirect($credential->getValidationUrl());
            }
            $remote_user = $user->getRemoteUser();
            $enterprise = $remote_user->getEnterprise();
            $admin_user = $enterprise->getAdminUser();

            if($credential->activation_expires_at < time()) {
                $result = ['errors' => (new ExternalException(ErrorCode::PHONE_ALREADY_OCCUPIED_ACTIVATION_EXPIRED, $enterprise->name))->getFormattedMessage()];
            }
            else {
                $result['user'] = $remote_user->getInfo(['username', 'departments', 'company_name', 'full_name', 'login_type', 'phone', 'email']);
                $result['user']['enterprise'] = $enterprise->toArray();
                $result['admin'] = $admin_user->getInfo(['username', 'company_name', 'full_name', 'login_type', 'phone', 'email']);
                $result['success'] = true;
            }
        }
        // var_dump($result['errors']);
        // echo $result['errors']['error_msg'];
        $result['css'] = 'css/' . $platform . '.css';
        if(!isset($result['errors'])) {
            $result['js'] = 'js/activation.js';
        }
        $result['platform'] = $platform;

        return view('auth.activation', $result);
    }
}
