<?php
/**
 * Created by PhpStorm.
 * User: luther
 * Date: 03/11/2017
 * Time: 15:53
 */

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Remote\User as RemoteUser;
use App\Services\Password\ChangePassword;
use App\Services\Password\InitPassword;
use App\Services\Password\SetPasswordForSsoUser;
use App\Util\CommonHelper;
use App\Util\Context;
use App\Util\SecurityHelper;
use App\Util\UserAgent;
use Illuminate\Http\Request;

class PasswordController extends Controller
{
    public function __construct(Request $request)
    {
        $this->middleware('auth');
        $this->middleware(['fstate', 'lstate'])->only('showInitForm', 'initPassword');

        parent::__construct($request);
    }

    /**
     * @param ChangePassword $change_password
     * @return array
     * @throws \App\Exceptions\ValidationException
     * @throws \Exception
     * @throws \Throwable
     */
    public function change(ChangePassword $change_password)
    {
        $in = $this->request->json()->all();
        SecurityHelper::secureParamDecode($in);
        $change_password->handle($in);
        return $this->success();
    }

    /**
     * @param SetPasswordForSsoUser $set_password
     * @return array
     * @throws \App\Exceptions\ValidationException
     * @throws \Exception
     * @throws \Throwable
     */
    public function setPasswordForSsoUser(SetPasswordForSsoUser $set_password)
    {
        $input = $this->request->json()->all();
        SecurityHelper::secureParamDecode($input);
        $set_password->handle($input);
        return $this->success();
    }

    public function showInitForm(UserAgent $user_agent)
    {
        $user = Context::user();
        if (!$user->getPasswordRequireInitAttr()) {
            return redirect(CommonHelper::getLoginUrl());
        }

        $platform = $user_agent->getBrowserPlatform();
        $enterprise = $user->getRemoteUser()->enterprise;
        $res['user'] = [
            'enterprise' => [
                'password_length_min' => $enterprise->password_length_min,
                'password_strength_require_special_symbol' => $enterprise->password_strength_require_special_symbol,
                'password_strength_require_capital_letter' => $enterprise->password_strength_require_capital_letter
            ]
        ];

        $res['platform'] = $platform;
        $res['register'] = false;
        $res['js'] = 'js/password_init.js';
        $res['css'] = 'css/' . $platform . '.css';

        return view('auth.password_init')->with($res);
    }

    /**
     * @return array
     *
     * @throws \App\Exceptions\ValidationException
     * @throws \Exception
     * @throws \Throwable
     */
    public function initPassword()
    {
        $user = Context::user();
        if (!$user->getPasswordRequireInitAttr()) {
            return redirect(CommonHelper::getLoginUrl());
        }

        $input = $this->request->only(['password', 'secure_zone']);
        SecurityHelper::secureParamDecode($input);
        $in['password'] = $input['password'] ?? null;
        (new InitPassword())->handle($in);
        RemoteUser::dealWithUserInitPassword($user->getUserId());
        $return = Context::getFStateContent('_login');
        if (isset($return['_redirect'])) {
            return redirect($return['_redirect']);
        } else {
            return $return;
        }
    }
}