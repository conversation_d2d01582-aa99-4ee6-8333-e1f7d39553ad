<?php

namespace App\Http\Controllers\Auth;

use App\Constants\CookieKey;
use App\Services\Auth\UserRegister;
use App\Http\Controllers\Controller;
use App\Util\CacheHelper;
use App\Util\Context;
use App\Util\HttpHelper;
use App\Util\SecurityHelper;
use App\Util\UserAgent;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;

class AliMarketRegisterController extends Controller
{
    protected $fangcloudUrl = null;

    /**
     * Create a new controller instance.
     *
     * @param Request $request
     */
    public function __construct(Request $request)
    {
        $this->middleware('guest');
        $this->middleware(['fstate', 'lstate']);
        $this->fangcloudUrl = config('app.fangcloud_url');
        parent::__construct($request);
    }

    private function getRegisterInfoOrRedirect() {
        $regKey = $this->request->cookie(CookieKey::ALI_MARKET_REG);
        if ($regKey) {
            $regInfo = CacheHelper::getCachedInfo($regKey);
            if ($regInfo && isset($regInfo['user_info']) && isset($regInfo['plan_info'])) {
                return $regInfo;
            }
        }
        return redirect($this->fangcloudUrl . 'ali_biz_auth/show_error_page');
    }

    private function updateRegisterInfo($newInfo) {
        $regKey = $this->request->cookie(CookieKey::ALI_MARKET_REG);
        if ($regKey) {
            CacheHelper::cacheInfo($regKey, $newInfo, config('auth.ali_market_registry_expire'));
        }
    }

    public function showRegistrationForm(UserAgent $user_agent)
    {
        HttpHelper::disableCache();
        $regInfo = $this->getRegisterInfoOrRedirect();
        if ($regInfo instanceof RedirectResponse) {
            return $regInfo;
        }

        return view('auth.register', [
            'css' => 'css/web.css',
            'js' => 'js/register.js',
            'platform' => 'web',
            'register' => 1,
            'personal_register' => 0,
            'aliyun_register' => 1,
            'plan_id' => '', // 阿里云不需要plan_id
            'redirect' => '/',
            'regInfo' => $regInfo,
            'source' => Context::getSource(),
        ]);
    }

    /**
     * Handle a registration request for the application.
     *
     * @param UserRegister $user_register
     * @return \Illuminate\Http\RedirectResponse
     * @throws \App\Exceptions\ValidationException
     * @throws \Exception
     * @throws \Throwable
     */
    public function register(UserRegister $user_register)
    {
        $regInfo = $this->getRegisterInfoOrRedirect();
        if ($regInfo instanceof RedirectResponse) {
            return $regInfo;
        }
        $planInfo = $regInfo['plan_info'];
        $userInfo = $regInfo['user_info'];

        $params = $this->request->json()->all();
        SecurityHelper::secureParamDecode($params);
        $params['plan_id'] = $planInfo['id'];
        $params['skip_bind_storage'] = $planInfo['use_external_storage'];
        $params['seat_limit'] = $userInfo['account_quality'];
        $params[$userInfo['is_trial'] ? 'trial_expires_at' : 'expires_at'] = $userInfo['expires_at'];
        $params['ali_biz_id'] = $userInfo['instance_id'];
        $params['ali_order_id'] = $userInfo['ali_order_id'];
        $res = $user_register->handle($params);
        $successRedirect = $this->fangcloudUrl . "ali_biz_auth/register_success?ali_biz_id={$userInfo['instance_id']}";
        $regInfo['user_info']['enterprise_id'] = $res['user']->getRemoteUser()->enterprise_id;
        $this->updateRegisterInfo($regInfo);
        return new RedirectResponse($successRedirect);
    }
}
