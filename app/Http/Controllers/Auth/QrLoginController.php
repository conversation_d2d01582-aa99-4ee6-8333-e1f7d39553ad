<?php

namespace App\Http\Controllers\Auth;

use App\Constants\CacheKey;
use App\Constants\ErrorCode;
use App\Constants\Http;
use App\Exceptions\ExternalException;
use App\Http\Controllers\LoginTrait;
use App\Models\User;
use App\Util\AuthHelper;
use App\Util\CacheHelper;
use App\Util\Context;
use App\Util\UserAgent;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Str;
use SimpleSoftwareIO\QrCode\Facades\QrCode;

class QrLoginController extends Controller
{
    use LoginTrait;

    const STATUS_NOTHING = 0;
    const STATUS_NEW_TOKEN = 1;
    const STATUS_SCANNED = 2;
    const STATUS_LOGGED_IN = 3;

    /**
     * Create a new controller instance.
     *
     * @param Request $request
     */
    public function __construct(Request $request)
    {
        parent::__construct($request);
        $this->middleware(['fstate', 'lstate']);
        $this->middleware('guest')->except(['showMobileConfirmForm', 'confirmLogin']);
        $this->middleware('auth:web,false')->only('showMobileConfirmForm', 'confirmLogin');
    }

    /**
     * @return array
     * @throws \App\Exceptions\ValidationException
     * @throws \Exception
     * @throws \Throwable
     */

    public function checkToken() {
        return $this->qrloginCheckToken('official');
    }


    public function getImage($token)
    {
        $url = route('qr_login', ['token' => $token]);
        if (env('APP_ENV', 'local') === 'profession' && strpos(env('EXTERNAL_ACCOUNT_URL'), 'https://') !== false && strpos($url, 'http://') !== false) {
            $url = str_replace('http://', 'https://', $url);
        }
        return $this->image(QrCode::format('png')->size(200)->margin(0)->generate($url));
    }

    public function showMobileConfirmForm($token)
    {
        $result = [];
        $egeio_client_info = $this->request->header(Http::HEADER_EGEIO_CLIENT_INFO);
        if ($egeio_client_info) {
            Context::setFStateContent(['headers' => [Http::HEADER_EGEIO_CLIENT_INFO => $egeio_client_info]]);
        }
        $cached_info = CacheHelper::getCachedInfo(sprintf(CacheKey::QR_LOGIN_TOKEN, $token));
        if(!$cached_info || !isset($cached_info['status']) || $cached_info['status'] != self::STATUS_NOTHING || !$cached_info['session_id']) {
            $e = new ExternalException(ErrorCode::QR_TOKEN_INVALID);
            $result = $e->getError();
        }
        else {
            $cached_info['status']  = self::STATUS_SCANNED;
            CacheHelper::cacheInfo(sprintf(CacheKey::QR_LOGIN_TOKEN, $token), $cached_info, config('auth.qr_code_expire'));
            $result['success'] = true;
        }

        // return $result;
        $result['css'] = 'css/mobile.css';
        $result['js'] = 'js/mobileConfirm.js';
        $result['platform'] = 'mobile';
        $result['from'] = $cached_info['from_platform'] ?? 'web';
        $result['token'] = $token;
        return view('auth.mobile_confirm', $result);
    }

    public function confirmLogin()
    {
        $token = $this->request->json('token');
        $cached_info = CacheHelper::getCachedInfo(sprintf(CacheKey::QR_LOGIN_TOKEN, $token));
        if(!$cached_info || !isset($cached_info['status']) || $cached_info['status'] != self::STATUS_SCANNED || !$cached_info['session_id']) {
            $e = new ExternalException(ErrorCode::QR_TOKEN_INVALID);
            return $e->getError();
        }
        $cached_info['status'] = self::STATUS_LOGGED_IN;
        $cached_info['logged_in_user_id'] = Context::user()->id;
        CacheHelper::cacheInfo(sprintf(CacheKey::QR_LOGIN_TOKEN, $token), $cached_info, config('auth.qr_code_expire'));
        return $this->success();
    }
}
