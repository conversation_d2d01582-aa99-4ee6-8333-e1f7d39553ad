<?php

namespace App\Http\Controllers\Auth;

use App\Constants\CacheKey;
use App\Constants\ErrorCode;
use App\Constants\Plan;
use App\Exceptions\ExternalException;
use App\Http\Controllers\LoginTrait;
use App\Models\Credential;
use App\Remote\Referral;
use App\Services\Auth\ChannelCoupons;
use App\Services\Auth\RegisterEnterpriseEdition;
use App\Services\Auth\PersonalUserRegister;
use App\Services\Auth\UserRegister;
use App\Http\Controllers\Controller;
use App\Remote\ReportWhyLeave;
use App\Remote\YunPan360;
use App\Util\CacheHelper;
use App\Util\Captcha;
use App\Util\Context;
use App\Util\HttpHelper;
use App\Util\LogHelper;
use App\Util\LogType;
use App\Util\SecurityHelper;
use App\Util\Sms;
use App\Util\UserAgent;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Services\Auth\ReferralRegister;
use Error;

class RegisterController extends Controller
{
    use LoginTrait;

    /**
     * Create a new controller instance.
     *
     * @param Request $request
     */
    public function __construct(Request $request)
    {
        $this->middleware('guest');
        $this->middleware(['fstate', 'lstate']);
        $this->middleware('set_source');
        $this->middleware('sem')->only(['showRegistrationForm']);
        parent::__construct($request);
    }

    /**
     * Get a validator for an incoming registration request.
     *
     * @param array $data
     * @return \Illuminate\Contracts\Validation\Validator
     */
    protected function validator(array $data)
    {
        return Validator::make($data, [
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:6|confirmed',
        ]);
    }

    public function showRegistrationForm(UserAgent $user_agent)
    {
        HttpHelper::disableCache();

        $platform = $user_agent->getBrowserPlatform();

        $redirect = $this->request->query('redirect');
        $origin = $this->request->query('origin');
        //区分注册来源
        if ($origin) {
            HttpHelper::setRegisterFrom($origin);
        }
        // 202003 套餐项目 默认注册到高级版
        $plan_id = $this->request->query('plan_id') ?? 24;
        $register_position = $this->request->query('register_position') ?? 1;

        if (!in_array($plan_id, array(24, 26, 27, 28, 29, 30, 31, 32, 33, 'custom', 'cooperate'))) {
            $plan_id = 24;
        }

        $invite_code = $this->request->query('invite_code') ?? '';

        $redirect = str_ireplace('/collab_email/', '/external_collab/', $redirect);


        if ($this->request->path() == 'personal_register' || $plan_id == 'custom' || $plan_id == 'cooperate') {

            return view('auth.register', [
                'css' => 'css/' . $platform . '.css',
                'js' => 'js/register.js',
                'platform' => $platform,
                'register' => 1,
                'plan_id' => $plan_id,
                'register_position' => $register_position,
                'invite_code' => $invite_code,
                'personal_register' => $this->request->path() == 'personal_register',
                'redirect' => $redirect,
                'source' => Context::getSource(),
            ]);
        }


        if ($platform == 'mobile') {
            return view('auth.mobile_register', [
                'css' => 'css/' . $platform . '.css',
                'js' => 'js/mobile_register.js',
                'platform' => $platform,
                'register' => 1,
                'plan_id' => $plan_id,
                'register_position' => $register_position,
                'invite_code' => $invite_code,
                'mobile_register' => 1,
                'personal_register' => $this->request->path() == 'personal_register',
                'redirect' => $redirect,
                'source' => Context::getSource(),
            ]);
        }

        return view('auth.quick_register', [
            'css' => 'css/quick-register.css',
            'js' => 'js/quick_register.js',
            'platform' => $platform,
            'register' => 1,
            'quick_register' => 1,
            'plan_id' => $plan_id,
            'register_position' => $register_position,
            'invite_code' => $invite_code,
            'redirect' => $redirect,
            'source' => Context::getSource(),
        ]);
    }

    public function showAliyunBindForm(UserAgent $user_agent)
    {
        HttpHelper::disableCache();

        $platform = $user_agent->getBrowserPlatform();

        return view('auth.bind_aliyun', [
            'css' => 'css/web.css',
            'js' => 'js/aliyun_bind.js',
            'aliyun_register' => 1,
            'platform' => 'web',
        ]);
    }

    public function showRegisterSuccessForm()
    {
        return view('auth.register_success', [
            'css' => 'css/web.css',
            'platform' => 'web'
        ]);
    }

    public function showEnterpriseRegisterSuccessForm()
    {
        return view('auth.enterprise_register_success', [
            'css' => 'css/web.css',
            'platform' => 'web',
            'register' => 1
        ]);
    }

    /**
     * Handle a registration request for the application.
     *
     * @param UserRegister $user_register
     * @return \Illuminate\Http\Response
     * @throws \App\Exceptions\InternalException
     * @throws \App\Exceptions\ValidationException
     * @throws \Exception
     * @throws \Throwable
     */
    public function register(UserRegister $user_register)
    {
        $params = $this->request->json()->all();
        SecurityHelper::secureParamDecode($params);
        $res = $user_register->handle($params);
        if (isset($res) && isset($params['referral_id'])) {
            $enterprise_id = null;
            if (isset($res['user']))
            {
                $enterprise_id = $res['user']->enterprise_id;
            }
            Referral::add_record($enterprise_id, $params['referral_id'], $params['phone']);
        }
        $login_type = $params['login_type'] ?? 'web';
        $redirect_url = $this->generateRedirectUrl($params['redirect'] ?? '');

        return $this->doLogin($login_type, ['user' => $res['user'], 'redirect' => $redirect_url], static::$skip_check_password | static::$skip_check_two_step | static::$skip_check_ent_status | static::$skip_check_security_restriction);
    }

    /**
     * Handle a quickLogin request for the application.
     *
     * @param UserRegister $user_register
     * @return \Illuminate\Http\Response
     * @throws \App\Exceptions\InternalException
     * @throws \App\Exceptions\ValidationException
     * @throws \Exception
     * @throws \Throwable
     */
    public function quickLogin(UserRegister $user_register)
    {
        $params = $this->request->json()->all();
        SecurityHelper::secureParamDecode($params);
        LogHelper::info(LogType::WebAndServerService, "quickLogin phone:".$params['phone'].',sms_captcha'.$params['sms_captcha']);
        if (strpos($params['phone'], '(+') === 0 && strpos($params['phone'], '(+86)') !== 0) {
            throw new ExternalException(ErrorCode::QUICK_LOGIN_INVALID);
        }
        $signature = $params['register-sign'];
        $ts = $params['ts'];
        if (!$signature || !$params['phone'])
        {
            throw new ExternalException(ErrorCode::QUICK_LOGIN_INVALID);
        }
        $hit_cache_signature = CacheHelper::getCachedInfo(sprintf(CacheKey::QUICK_LOGIN_SIGN_IDENTITY_FLAG, $signature));
        if ($hit_cache_signature)
        {
            throw new ExternalException(ErrorCode::QUICK_LOGIN_INVALID);
        }
        $signKey = 'appId='.Sms::QUICK_LOGIN_SIGN_APP_ID.'&ts='.$ts.'&phone='.$params['phone'].'&type='.'quick_login';
        $serverSign = md5($signKey);
        if ($serverSign != $signature)
        {
            throw new ExternalException(ErrorCode::QUICK_LOGIN_INVALID);
        }
        CacheHelper::cacheInfo(sprintf(CacheKey::QUICK_LOGIN_SIGN_IDENTITY_FLAG, $signature), true,300);
        $credential = Credential::checkCredentialExistence($params['phone'], Credential::TYPE_PHONE);

        // 登录前创建安全云盘关联账号
        try {
            if (isset($params['phone']) && !empty($params['phone'])) {
                $accessToken = YunPan360::createAccountByMobile($params['phone']);
                LogHelper::info(LogType::WebAndServerService, " yunpna 360 account created ". $accessToken);
            } else {
                LogHelper::warning(LogType::RemoteCallException, "Phone number is not set or empty for YunPan360 account creation.");
            }
        } catch (\Exception $e) {
            LogHelper::error(LogType::RemoteCallException, "Error creating account with YunPan360: " . $e->getMessage());
        }

        if ($credential) {
            if (!Captcha::check($params['sms_captcha'], Captcha::TYPE_REGISTER, Captcha::THROUGH_SMS, $params['phone'])) {
                throw new ExternalException(ErrorCode::SMS_CAPTCHA_INVALID, 'sms_captcha');
            }
            $user = $credential->getUser();
            $to_register = 0;
        } else {
            $params['plan_id'] = Plan::PLAN_TYPE_PREMIUM_2020;
            $pwd = $this->generatePassword();
            $params['password'] = $pwd;
            $res = $user_register->handle($params);
            $user = $res['user'];
            $to_register = 1;
        }
        
        $login_type = $params['login_type'] ?? 'web';
        return $this->doLogin($login_type, ['user' => $user,'need_return_json' => true, 'to_register' => $to_register], static::$skip_check_password | static::$skip_check_two_step | static::$skip_check_ent_status | static::$skip_check_security_restriction | static::$skip_init_password);
    }

    public function showCheckChannelCouponForm()
    {
        return view('auth.check_channel_coupon', [
            'css' => 'css/quick-register.css',
            'js' => 'js/check_channel_coupon.js',
            'platform' => 'mobile',
            'register' => 0
        ]);
    }

    // 推荐官活动登录页
    public function introductionRegister()
    {
        return view('auth.introduction_register', [
            'css' => 'css/quick-register.css',
            'js' => 'js/introduction_register.js',
            'platform' => 'mobile',
            'register' => 0
        ]);
    }

    public function checkChannelCoupon(ChannelCoupons $channelCoupons)
    {
        $params = $this->request->json()->all();
        $channelCoupons->handle($params);
        return $this->success();
    }

    public function newEnterpriseRegister()
    {
        $plan_id = $this->request->query('plan_id') ?? 24;
        if (!in_array($plan_id, array(24, 26, 27, 28, 29, 30, 31, 32, 33, 'custom', 'cooperate'))) {
            $plan_id = 24;
        }
        $register_position = $this->request->query('register_position') ?? 1;
        return view('auth.new_enterprise_register', [
            'css' => 'css/quick-register.css',
            'js' => 'js/new_enterprise_register.js',
            'platform' => 'web',
            'register' => 0,
            'new_register' => 1,
            'plan_id' => $plan_id,
            'register_position' => $register_position,
            'source' => Context::getSource()
        ]);
    }

    public function clearRegisterInfo()
    {
        CacheHelper::clearCachedSessionInfo('register_info');
        return $this->success();
    }

    public function registerEnterpriseEdition(RegisterEnterpriseEdition $register)
    {
        $input = $this->request->json()->all();
        SecurityHelper::secureParamDecode($input);
        $register->handle($input);
        return $this->success(['redirect' => route('enterprise_register_success')]);
    }

    public function personalRegister(PersonalUserRegister $personal_user_register)
    {
        $input = $this->request->json()->all();
        SecurityHelper::secureParamDecode($input);
        $res = $personal_user_register->handle($input);
        // TODO login type
        $login_type = 'web';
        return $this->doLogin($login_type, ['user' => $res['user']], static::$skip_check_password | static::$skip_check_two_step | static::$skip_check_ent_status | static::$skip_check_security_restriction);
    }

    public function reportWhyLeave(ReportWhyLeave $reportWhyLeave)
    {
        $reportWhyLeave->report($this->request->json()->all());
        return $this->success();
    }

    public function becomeReferral(ReferralRegister $referral_register)
    {
        $params = $this->request->json()->all();
        $res = $referral_register->handle($params);
        if (!is_null($res)) {
            return $this->success($res);
        } else {
            return $this->success();
        }
    }

    private function generateRedirectUrl($redirect_url)
    {
        if (stristr($redirect_url, env('FANG_HOST'))) {
            $redirect = $redirect_url;
        } else {
            $redirect = $this->defaultPage() . '?from_register=1';
        }
        return $redirect;
    }

    function generatePassword() {
        $length = 8;
        $lowercase = 'abcdefghijklmnopqrstuvwxyz';
        $uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $numbers = '1234567890';
        $specialChars = '@#$%';
        $passwordRegex = '/^(?=.*[0-9])(?=.*[A-Z])(?=.*[a-z])(?=.*[\W_]).{8,20}$/';
        do {
            $password = '';
            for ($i = 0; $i < $length; $i++) {
                $rule = rand(0, 3);
                switch ($rule) {
                    case 0:
                        $password .= $lowercase[rand(0, strlen($lowercase) - 1)];
                        break;
                    case 1:
                        $password .= $uppercase[rand(0, strlen($uppercase) - 1)];
                        break;
                    case 2:
                        $password .= $numbers[rand(0, strlen($numbers) - 1)];
                        break;
                    case 3:
                        $password .= $specialChars[rand(0, strlen($specialChars) - 1)];
                        break;
                }
            }
        } while (!preg_match($passwordRegex, $password));
        return $password;
    }
}
