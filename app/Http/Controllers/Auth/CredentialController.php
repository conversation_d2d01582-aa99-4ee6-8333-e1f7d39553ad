<?php
/**
 * Created by PhpStorm.
 * User: luther
 * Date: 03/11/2017
 * Time: 15:53
 */

namespace App\Http\Controllers\Auth;

use App\Constants\ErrorCode;
use App\Exceptions\ExternalException;
use App\Http\Controllers\Controller;
use App\Models\Credential;
use App\Notifications\EmailToBeVerified;
use App\Services\Credential\CredentialValidate;
use App\Services\Credential\PreVerifyEmail;
use App\Services\Credential\PreVerifyPhone;
use App\Services\Credential\UnbindCredential;
use App\Services\Credential\UpdateEmail;
use App\Services\Credential\UpdateEmailWithPassword;
use App\Services\Credential\UpdatePhone;
use App\Services\Credential\UpdatePhoneWithPassword;
use App\Services\Credential\VerifyEmail;
use App\Services\Credential\VerifyPhone;
use App\Util\SecurityHelper;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class CredentialController extends Controller
{
    public function __construct(Request $request)
    {
        $this->middleware('auth')->except('validation', 'activation', 'appShowUpdateEmailForm', 'appShowUpdatePhoneForm');
        $this->middleware('auth:web,false')->only('appShowUpdateEmailForm', 'appShowUpdatePhoneForm');
        $this->middleware('identity')->only('unbind', 'updateEmail');
        parent::__construct($request);
    }

    public function validation($code)
    {
        $credential = Credential::getCredentialByActivationCode($code);
        if(!$credential || $credential->status != Credential::STATUS_TO_BE_VALIDATED || !$credential->isTypeEmail()) {
            $res = ['errors' => (new ExternalException(ErrorCode::CREDENTIAL_VERIFICATION_CODE_INVALID))->getFormattedMessage()];
        }
        else {
            $user = $credential->getUser();
            // TODO: 兼容迁移过来的邮箱注册老用户，后面可以删掉
            if(!$user->isActive() && strlen($code) !== 40) {
                return redirect($credential->getActivationUrl());
            }
            if($credential->activation_expires_at < time()) {
                $credential->regenerateActivationCode();
                $user->notify(new EmailToBeVerified($credential));
                $res = ['errors' => (new ExternalException(ErrorCode::CREDENTIAL_VERIFICATION_CODE_INVALID))->getFormattedMessage()];
            }
            else {
                $credential_validate = app(CredentialValidate::class);
                $credential_validate->handle(['credential' => $credential]);
                $res = [
                    'success' => true,
                    'redirect' => config('app.default_page')
                ];
            }
        }

        $res['css'] = 'css/web.css';
        $res['platform'] = 'web';

        return view('auth.validate_success', $res);
    }

    public function updateEmail(Request $request, UpdateEmail $update_email)
    {
        $update_email->handle(['email' => $request->json('email')]);
        return ['success' => true];
    }

    public function verifyEmail(VerifyEmail $verify_email)
    {
        $verify_email->handle();
        return ['success' => true];
    }

    public function verifyPhone(VerifyPhone $verify_phone)
    {
        $verify_phone->handle(['sms_captcha' => $this->request->json('sms_captcha')]);
        return ['success' => true];
    }

    public function preVerifyPhone(PreVerifyPhone $pre_verify_phone)
    {
        $credential = $pre_verify_phone->handle()['credential'];
        $result['credential'] = $credential->format(Credential::$frontend_keys);
        return $this->success($result);
    }

    public function preVerifyEmail(PreVerifyEmail $pre_verify_email)
    {
        $credential = $pre_verify_email->handle()['credential'];
        $result['credential'] = $credential->format(Credential::$frontend_keys);
        return $this->success($result);
    }

    public function checkExistence()
    {
        $request_data = $this->request->json();
        $type = $request_data['type'];
        $identifier = $request_data['identifier'];
        $credential = Credential::checkCredentialExistence($identifier, $type);
        if($credential && $credential->login_user_id != Auth::id()) {
            throw new ExternalException($credential->isTypePhone() ? ErrorCode::PHONE_ALREADY_OCCUPIED_WHEN_VERIFY
                : ErrorCode::EMAIL_ALREADY_OCCUPIED_WHEN_VERIFY, 'identifier');
        }
        return ['success' => true];
    }

    public function updatePhone(Request $request, UpdatePhone $update_phone)
    {
        $update_phone->handle(['phone' => $request->json('phone'), 'sms_captcha' => $request->json('sms_captcha')]);
        return ['success' => true];
    }
//
//    public function unbind(Request $request, UnbindCredential $unbind_credential)
//    {
//        $unbind_credential->handle(['type' => $request->json('type')]);
//        return ['success' => true];
//    }

    public function getBasicLogin()
    {
        $current_user = Auth::user();
        $credentials = Credential::getUserCredentials($current_user->id, Credential::$public_login_types)->all();
        $virtual_credentials = $current_user->getUnverifiedFakeCredentials();
        if($virtual_credentials) {
            $credentials = array_merge($credentials, $virtual_credentials);
        }
        $formatted_credentials = Credential::bulkFormat($credentials, Credential::$frontend_keys);
        return [
            'success' => true,
            'credentials' => $formatted_credentials,
        ];
    }

    public function appShowUpdateEmailForm()
    {
        $res = [
            'email' => $this->request->get('email'),
            'platform' => 'mobile',
            'css' => 'css/mobile.css',
            'edit_email' => 1
        ];
        return view('auth.not_completed', $res);
    }

    public function appShowUpdatePhoneForm()
    {
        $res = [
            'phone' => $this->request->get('phone'),
            'platform' => 'mobile',
            'css' => 'css/mobile.css',
            'edit_phone' => 1

        ];
        return view('auth.not_completed', $res);

    }

    public function appUpdateEmail(UpdateEmailWithPassword $update_email)
    {
        $update_email->handle([
            'email' => $this->request->json('email'),
            'password' => $this->request->json('password')
        ]);
        return ['success' => true];
    }

    public function appUpdatePhone(UpdatePhoneWithPassword $update_phone)
    {
        $update_phone->handle([
            'phone' => $this->request->json('phone'),
            'sms_captcha' => $this->request->json('sms_captcha'),
            'password' => $this->request->json('password')
        ]);
        return ['success' => true];
    }
}