<?php

namespace App\Http\Controllers\Auth;

use App\Constants\Http;
use App\Http\Controllers\Controller;
use App\Http\Controllers\LoginTrait;
use App\Util\Context;
use App\Util\SecurityHelper;
use App\Util\UserAgent;
use Illuminate\Support\Str;
use App\Util\CacheHelper;
use Illuminate\Http\Request;
use App\Constants\CacheKey;
use App\Models\User;
use App\Util\AuthHelper;

class OAuthController extends Controller
{
    use LoginTrait;

    const STATUS_NOTHING = 0;
    const STATUS_NEW_TOKEN = 1;
    const STATUS_SCANNED = 2;
    const STATUS_LOGGED_IN = 3;

    public function __construct(Request $request)
    {
        parent::__construct($request);
        $this->middleware(['fstate', 'lstate']);
    }

    public function showLoginForm(UserAgent $user_agent)
    {
        $platform = $user_agent->getBrowserPlatform();
        if ($platform === UserAgent::BROWSER_PLATFORM_SYNC) {
            $device_token = $this->request->header(Http::HEADER_DEVICE_TOKEN);
            Context::setFStateContent(['headers' => [Http::HEADER_DEVICE_TOKEN => $device_token]]);
        } elseif ($platform === UserAgent::BROWSER_PLATFORM_MOBILE) {
            $egeio_client_info = $this->request->header(Http::HEADER_EGEIO_CLIENT_INFO);
            if ($egeio_client_info) {
                Context::setFStateContent(['headers' => [Http::HEADER_EGEIO_CLIENT_INFO => $egeio_client_info]]);
            }
        }

        return view('auth.login', [
            'css' => 'css/' . $platform . '.css',
            'js' => 'js/login.js',
            'oauth' => 1,
            'platform' => $platform,
        ]);
    }

    /**
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector|mixed
     * @throws \App\Exceptions\InternalException
     * @throws \App\Exceptions\ValidationException
     * @throws \Exception
     * @throws \Throwable
     */
    public function oauthLogin()
    {
        $input = $this->request->only(['login', 'password', 'remember_login', 'pic_captcha', 'login_type', 'secure_zone']);
        SecurityHelper::secureParamDecode($input);
        $in['login'] = $input['login'] ?? null;
        $in['password'] = $input['password'] ?? null;
        $in['remember_login'] = $input['remember_login'] ?? null;
        $in['pic_captcha'] = $input['pic_captcha'] ?? null;
        $login_type = $input['login_type'] ?? 'web';

        // 之后会做一些事，因为主站已经做了，所以直接302回主站，然后让主站做剩下的事情
        $url = config('app.fangcloud_url') . 'third_party/oauth/login?' . http_build_query($this->request->query->all());
        $this->request->query->add(['redirect' => $url]);

        return $this->doLogin($login_type, $in, static::$skip_check_security_restriction);
    }

    /**
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector|mixed
     * @throws \App\Exceptions\InternalException
     * @throws \App\Exceptions\ValidationException
     * @throws \Exception
     * @throws \Throwable
     */
    public function oauthQrLogin()
    {
        return $this->qrloginCheckToken('oauth');
    }
}
