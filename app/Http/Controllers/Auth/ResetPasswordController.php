<?php

namespace App\Http\Controllers\Auth;

use App\Constants\ErrorCode;
use App\Exceptions\ExternalException;
use App\Http\Controllers\Controller;
use App\Models\PasswordReset;
use App\Services\Password\ResetPassword;
use App\Util\SecurityHelper;
use Illuminate\Foundation\Auth\ResetsPasswords;
use Illuminate\Http\Request;
use App\Util\UserAgent;

class ResetPasswordController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @param Request $request
     */
    public function __construct(Request $request)
    {
        $this->middleware('guest');
        parent::__construct($request);
    }

    public function reset(Request $request, ResetPassword $reset_password)
    {
        $input = $this->request->json()->all();
        SecurityHelper::secureParamDecode($input);

        $user = $this->getResetUser($input['code']);
        $result = $reset_password->handle([
            'user' =>$user,
            'password' => $input['password'],
            'password_confirmation' => $input['password_confirmation'],
        ]);

        return $this->success();
    }

    public function showResetForm($code, UserAgent $user_agent)
    {
        try{
            $user = $this->getResetUser($code);
            $enterprise = $user->getRemoteUser()->getEnterprise();
            $res = ['code' => $code];
            $res['user'] = $user->format(['id']);
            $res['user']['enterprise'] = $enterprise->toArray();
        }
        catch (ExternalException $e){
            $res = ['errors' => $e->getFormattedMessage()];
        }

        $platform = $user_agent->getBrowserPlatform();
        $res['platform'] = $platform;
        $res['register'] = false;
        $res['js'] = 'js/forgot.js';
        $res['css'] = 'css/' . $platform . '.css';

        return view('auth.reset')->with($res);
    }

    private function getResetUser($code)
    {
        $password_reset = PasswordReset::getByUniqueCode($code);
        if(!$password_reset || $password_reset->expires_at < time()) {
            throw new ExternalException(ErrorCode::FORGOT_PASSWORD_CODE_INVALID);
        }
        $user = $password_reset->getUser();
        if(!$user) {
            throw new ExternalException(ErrorCode::FORGOT_PASSWORD_CODE_INVALID);
        }
        return $user;
    }
}
