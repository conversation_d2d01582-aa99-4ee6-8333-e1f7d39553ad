<?php

namespace App\Http\Controllers\Auth;

use App\Constants\ErrorCode;
use App\Constants\ExceptionFieldKey;
use App\Constants\Http;
use App\Exceptions\ExternalException;
use App\Exceptions\NeedRedirectException;
use App\Http\Controllers\LoginTrait;
use App\Library\TwoStep;
use App\Services\Auth\UserLogout;
use App\Util\AuthHelper;
use App\Util\CommonHelper;
use App\Util\Context;
use App\Util\HttpHelper;
use App\Util\SecurityHelper;
use App\Util\UserAgent;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

class LoginController extends Controller
{
    use LoginTrait, ThirdBindTrait;

    /**
     * Create a new controller instance.
     *
     * @param Request $request
     */
    public function __construct(Request $request)
    {
        parent::__construct($request);
        $this->middleware(['fstate', 'lstate']);
        $this->middleware('guest')->except(['logout', 'twoStepLogin', 'showLoginVerifyForm', 'showForceStepForm']);
        $this->middleware('auth:redis,false')->only(['twoStepLogin', 'showLoginVerifyForm', 'showForceStepForm']);
        $this->middleware('auth:web')->only(['logout']);
        $this->middleware('set_source');
    }

    public function showLoginForm(UserAgent $user_agent)
    {
        HttpHelper::disableCache();

        $platform = $user_agent->getBrowserPlatform();
        if ($platform === UserAgent::BROWSER_PLATFORM_SYNC) {
            $device_token = $this->request->header(Http::HEADER_DEVICE_TOKEN);
            $api_key = $this->request->header(Http::HEADER_API_KEY);
            Context::setFStateContent(['headers' => [Http::HEADER_DEVICE_TOKEN => $device_token, Http::HEADER_API_KEY => $api_key]]);
        } elseif ($platform === UserAgent::BROWSER_PLATFORM_MOBILE) {
            $egeio_client_info = $this->request->header(Http::HEADER_EGEIO_CLIENT_INFO);
            if ($egeio_client_info) {
                Context::setFStateContent(['headers' => [Http::HEADER_EGEIO_CLIENT_INFO => $egeio_client_info]]);
            }
        }
        // $platform = 'sync';
        return view('auth.login', [
            'css' => 'css/' . $platform . '.css',
            'js' => 'js/login.js',
            'platform' => $platform,
            'source' => Context::getSource(),
        ]);
    }

    public function showLoginFormV3(UserAgent $user_agent)
    {
        HttpHelper::disableCache();

        $platform = $user_agent->getBrowserPlatform();
        if ($platform === UserAgent::BROWSER_PLATFORM_SYNC) {
            $device_token = $this->request->header(Http::HEADER_DEVICE_TOKEN);
            $api_key = $this->request->header(Http::HEADER_API_KEY);
            Context::setFStateContent(['headers' => [Http::HEADER_DEVICE_TOKEN => $device_token, Http::HEADER_API_KEY => $api_key]]);
        } elseif ($platform === UserAgent::BROWSER_PLATFORM_MOBILE) {
            $egeio_client_info = $this->request->header(Http::HEADER_EGEIO_CLIENT_INFO);
            if ($egeio_client_info) {
                Context::setFStateContent(['headers' => [Http::HEADER_EGEIO_CLIENT_INFO => $egeio_client_info]]);
            }
        }
        $isReg = request()->has('mode') && in_array(request('mode'), ['ent_reg', 'person_reg']);
        // $platform = 'sync';
        return view('auth.login_v3', [
            'css' => 'css/' . $platform . '_v3.css',
            'js' => 'js/login_v3.js',
            'platform' => $platform,
            'isReg' => $isReg,
            'source' => Context::getSource(),
            'login_v3' => 1
        ]);
    }

    public function showOtherLoginForm(UserAgent $user_agent)
    {
        $platform = $user_agent->getBrowserPlatform();

        return view('auth.other_login', [
            'css' => 'css/' . $platform . '.css',
            'js' => 'js/other_login.js',
            'platform' => $platform,
            'other' => true
        ]);
    }

    public function showHybridLoginForm(UserAgent $user_agent)
    {
        $platform = $user_agent->getBrowserPlatform();

        return view('auth.hybrid_login', [
            'css' => 'css/' . $platform . '.css',
            'js' => 'js/hybrid_login.js',
            'platform' => $platform,
            'hybrid' => true
        ]);
    }

    public function showInternationalLoginForm(UserAgent $user_agent)
    {
        $platform = $user_agent->getBrowserPlatform();

        return view('auth.login', [
            'css' => 'css/' . $platform . '.css',
            'js' => 'js/login.js',
            'platform' => $platform,
            'international' => true
        ]);
    }

    public function showLoginVerifyForm(UserAgent $user_agent)
    {
        $platform = $user_agent->getBrowserPlatform();

        $two_step = Context::user()->getTwoStep();
        $method = $two_step['method'];
        $identity = $two_step['identity'];

        return view('auth.login_verify', [
            'css' => 'css/' . $platform . '.css',
            'js' => 'js/login_verify.js',
            'platform' => $platform,
            'type' => $method,
            'identity' => $identity,
        ]);
    }

    public function showForceStepForm(UserAgent $user_agent)
    {
        $platform = $user_agent->getBrowserPlatform();

        if($platform === 'mobile') {
            $two_step = Context::user()->getTwoStep();
            $method = $two_step['method'];
            $identity = $two_step['identity'];

            return view('auth.set_two_step_verify', [
                'css' => 'css/' . $platform . '.css',
                'js' => 'js/set_two_step_verify.js',
                'platform' => $platform,
                'type' => $method,
                'identity' => $identity,
            ]);
        }

        return view('auth.force_two_step', [
            'css' => 'css/' . $platform . '.css',
            'js' => 'js/force_two_step.js',
            'force_login' => '1',
            'platform' => $platform,
        ]);
    }

    /**
     * web 端登录请求
     *
     * @return RedirectResponse|\Illuminate\Routing\Redirector|mixed
     * @throws \App\Exceptions\ValidationException
     * @throws \Exception
     * @throws \Throwable
     */
    public function login()
    {
        return $this->loginFor('web');
    }

    /**
     * app 端 登录请求
     * TODO only
     *
     * @return RedirectResponse|\Illuminate\Routing\Redirector|mixed
     * @throws \Exception
     * @throws \Throwable
     */
    public function appLogin()
    {
        return $this->loginFor('app');
    }

    /**
     * sync 端 登录请求
     *
     * @return RedirectResponse|\Illuminate\Routing\Redirector|mixed
     * @throws \App\Exceptions\ValidationException
     * @throws \Exception
     * @throws \Throwable
     */
    public function syncLogin()
    {
        return $this->loginFor('sync');
    }


    /**
     * @param $login_type
     * @return RedirectResponse|\Illuminate\Routing\Redirector|mixed
     * @throws \App\Exceptions\InternalException
     * @throws \App\Exceptions\ValidationException
     * @throws \Throwable
     */
    private function loginFor($login_type) {
        try
        {
            $in = $this->request->json()->all();
            SecurityHelper::secureParamDecode($in);
            return $this->doLogin($login_type, $in);
        }
        catch (NeedRedirectException $e)
        {
            return new RedirectResponse($e->getRedirect());
        }
    }

    /**
     * 处理三端的二次验证登录请求
     *
     * @return RedirectResponse|\Illuminate\Routing\Redirector|int
     * @throws ExternalException
     * @throws \App\Exceptions\ValidationException
     * @throws \Exception
     * @throws \Throwable
     */
    public function twoStepLogin()
    {
        $input = $this->request->only('code', 'mark_credit', 'login_type');
        $code = $input['code'] ?? '';
        $mark_credit = $input['mark_credit'] ?? false;
        $login_type = $input['login_type'] ?? null;
        $in['mark_credit'] = $mark_credit;
        if ($login_type) {
            $in['login_type'] = $login_type;
        }

        $user = Context::user();
        $status = (new TwoStep(['user' => $user]))->verify($code);
        if ($status) {
            return $this->doLogin(null, $in, static::$skip_check_security_restriction);
        } else {
            throw new ExternalException(ErrorCode::TWO_STEP_VERIFY_FAILED, ExceptionFieldKey::TWO_STEP_CAPTCHA);
        }
    }

    /**
     * 登出请求
     *
     * @param UserLogout $user_logout
     * @return RedirectResponse|\Illuminate\Routing\Redirector
     * @throws \App\Exceptions\ValidationException
     * @throws \Exception
     * @throws \Throwable
     */
    public function logout(UserLogout $user_logout)
    {
        $user_logout->handle();
        return redirect(CommonHelper::getLoginUrl());
    }

    /**
     * 登出请求v2
     *
     * @param UserLogout $user_logout
     * @return RedirectResponse|\Illuminate\Routing\Redirector
     * @throws \App\Exceptions\ValidationException
     * @throws \Exception
     * @throws \Throwable
     */
    public function logoutV2(UserLogout $user_logout)
    {
        $user_logout->handle();
        return $this->success();
    }

    public function checkPicCaptchaNeeded()
    {
        return [
            'success' => true,
            'is_pic_captcha_needed' => AuthHelper::isLoginPicCaptchaRequired($this->request->get('login'), CommonHelper::getClientIp())
        ];
    }

    public function h5DownloadNotice()
    {
        HttpHelper::disableCache();
        // $platform = $user_agent->getBrowserPlatform();

        // if($platform === 'mobile') {

            // $is_custom_product = $this->input->get('is_custom_product');
            // $resource_id = $this->input->get('resource_id');
            // $product_id = $this->input->get('product_id');
            // $scheme_url_prefix = $is_custom_product ? 'open' . $product_id . '://' : 'openfangcloudv2://';

            $scheme_url_prefix = 'openfangcloudv2://';

            // $result['product_name'] = get_product_name($lang, $resource_id);
            // // $result['is_custom_product'] = $is_custom_product;
            // $result['host'] = rawurldecode($this->input->get('base_url'));
            // $result['scheme_url'] = $scheme_url_prefix . 'v2.fangcloud.com/';
            $scheme_url = $scheme_url_prefix . 'v2.fangcloud.com/';
        // }

        return view('auth.h5_download_notice', [
            'css' => 'css/mobile.css',
            'platform' => 'mobile',
            'scheme_url' => $scheme_url
        ]);
    }
}
