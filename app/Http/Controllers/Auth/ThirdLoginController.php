<?php

namespace App\Http\Controllers\Auth;

use App\Constants\Common;
use App\Constants\ErrorCode;
use App\Constants\Http;
use App\Exceptions\ExternalException;
use App\Http\Controllers\Controller;
use App\Http\Controllers\LoginTrait;
use App\Library\OAuth\Constants;
use App\Library\OAuth\Dingtalk\DingtalkProvider;
use App\Library\OAuth\Qihoo360\Qihoo360Provider;
use App\Library\OAuth\Wechat\WechatProvider;
use App\Models\Credential;
use App\Util\CacheHelper;
use App\Util\Context;
use App\Util\SecurityHelper;
use App\Util\UserAgent;
use Illuminate\Http\Request;

class ThirdLoginController extends Controller
{
    use LoginTrait, ThirdBindTrait;

    public function __construct(Request $request)
    {
        parent::__construct($request);
        $this->middleware(['fstate', 'lstate']);
        $this->middleware('guest')->only('showBindLoginForm', 'bindLogin');
        $this->middleware('oauth_state')->only('dingtalkCallback', 'wechatCallback');
    }

    public function showBindLoginForm(UserAgent $user_agent)
    {
        $platform = $user_agent->getBrowserPlatform();

        return view('auth.bind_account', [
            'css' => 'css/' . $platform . '.css',
            'js' => 'js/login.js',
            'platform' => $platform,
            'bind' => true
        ]);
    }

    /**
     * 第三方账号绑定登录
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector|mixed
     * @throws \App\Exceptions\InternalException
     * @throws \App\Exceptions\ValidationException
     * @throws \Exception
     * @throws \Throwable
     */
    public function bindLogin()
    {
        $third_user = CacheHelper::getThirdOAuthUserInfo();

        $in = $this->request->json()->all();
        SecurityHelper::secureParamDecode($in);
        $in['third_user'] = $third_user;
        $login_type = Context::getFStateContent('login_type', 'web');
        switch ($login_type) {
            case 'sync':
                $in['action_none'] = 'bindsyncLoginActionNone';
                break;
            case 'app':
                $in['action_none'] = 'bindappLoginActionNone';
                break;
            default:
                $in['action_none'] = 'bindwebLoginActionNone';
                break;
        }

        $in['v1_error'] = ErrorCode::V1_LOGIN_NOT_AllOWED_FOR_THIRD;
        return $this->doLogin($login_type, $in);
    }

    public function dingtalkLogin()
    {
        $qrcode = $this->request->get('qrcode');
        $scene = $this->request->get('scene');
        $login_type = $this->request->get('login_type');

        Context::setFStateContent([
            'headers' => [
                'device_token' => Context::getDeviceToken(),
                Http::HEADER_API_KEY => $this->request->header(Http::HEADER_API_KEY),
                Http::HEADER_EGEIO_CLIENT_INFO => $this->request->header(Http::HEADER_EGEIO_CLIENT_INFO),
            ]
        ]);

        $provider = new DingtalkProvider($qrcode ? 'qrcode' : '');
        $state = $provider->getRandomState();
        CacheHelper::setOAuthState($state, ['scene' => $scene, 'login_type' => $login_type, '_fstate' => Context::getFState()]);

        return redirect($provider->getAuthorizationUrl([
            'redirect' => $this->request->get('redirect'),
            'state' => $state
        ]));
    }

    public function wechatLogin()
    {
        $scene = $this->request->get('scene');
        $login_type = $this->request->get('login_type');

        Context::setFStateContent([
            'headers' => [
                'device_token' => Context::getDeviceToken(),
                Http::HEADER_API_KEY => $this->request->header(Http::HEADER_API_KEY),
                Http::HEADER_EGEIO_CLIENT_INFO => $this->request->header(Http::HEADER_EGEIO_CLIENT_INFO),
            ]
        ]);

        $provider = new WechatProvider();
        $state = $provider->getRandomState();
        CacheHelper::setOAuthState($state, ['scene' => $scene, 'login_type' => $login_type, '_fstate' => Context::getFState()]);

        return redirect($provider->getAuthorizationUrl([
            'redirect' => $this->request->get('redirect'),
            'usertype' => 'member',
            'state' => $state,
        ]));
    }

    public function qihoo360Login(UserAgent $user_agent)
    {
        $scene = $this->request->get('scene');
        $login_type = $this->request->get('login_type');
        $api_key = $this->request->get('api_key');

        Context::setFStateContent([
            'headers' => [
                'device_token' => Context::getDeviceToken(),
                Http::HEADER_API_KEY => $this->request->header(Http::HEADER_API_KEY),
                Http::HEADER_EGEIO_CLIENT_INFO => $this->request->header(Http::HEADER_EGEIO_CLIENT_INFO),
            ]
        ]);

        $provider = new Qihoo360Provider();
        $state = $provider->getRandomState();
        CacheHelper::setOAuthState($state,
            [
                'scene' => $scene,
                'login_type' => $login_type,
                '_fstate' => Context::getFState(),
                'api_key' => $api_key,
            ]
        );

        $platform = $user_agent->getBrowserPlatform();
        $display = 'default';
        if ($platform == UserAgent::BROWSER_PLATFORM_MOBILE) {
            $display = 'mobile.new';
        }

        return redirect($provider->getAuthorizationUrl([
            'redirect' => $this->request->get('redirect'),
            'state' => $state,
            'display' => $display,
            'user_center' => 'quc',
            'scope' => 'basic',
            'src' => ''
        ]));
    }

    /**
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector|mixed
     * @throws ExternalException
     * @throws \App\Exceptions\InternalException
     * @throws \App\Exceptions\ValidationException
     * @throws \Exception
     * @throws \Throwable
     */
    public function dingtalkCallback()
    {
        return $this->thirdOAuthCallback(Constants::TYPE_DINGTALK);
    }

    /**
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector|mixed
     * @throws ExternalException
     * @throws \App\Exceptions\InternalException
     * @throws \App\Exceptions\ValidationException
     * @throws \Exception
     * @throws \Throwable
     */
    public function wechatCallback()
    {
        return $this->thirdOAuthCallback(Constants::TYPE_WECHAT);
    }

    /**
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector|mixed
     * @throws ExternalException
     * @throws \App\Exceptions\InternalException
     * @throws \App\Exceptions\ValidationException
     * @throws \Throwable
     */
    public function qihoo360Callback()
    {
        return $this->thirdOAuthCallback(Constants::TYPE_QIHOO360);
    }

    /**
     * 第三方账号回调。
     *
     * 绑定 or 登录
     *
     * @param $type
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector|mixed
     * @throws ExternalException
     * @throws \App\Exceptions\InternalException
     * @throws \App\Exceptions\ValidationException
     * @throws \Exception
     * @throws \Throwable
     */
    private function thirdOAuthCallback($type)
    {
        // Verify state. Another way of csrf protection
        $state = $this->request->get('state');
        $context = CacheHelper::getOAuthState($state);
        $scene = $context['scene'] ?? null;
        $login_type = $context['login_type'] ?? 'web';

        if (!$state || !$context) {
            // throw new ExternalException(ErrorCode::OAUTH_INVALID_STATE);
            $result = ['errors' => (new ExternalException(ErrorCode::OAUTH_INVALID_STATE))->getFormattedMessage()];
            return $this->thirdMiddlePage($result, $login_type);
        }

        if ($scene != Constants::SCENE_LOGIN && $scene != Constants::SCENE_BIND) {
            // throw new ExternalException(ErrorCode::OAUTH_INVALID_STATE);
            $result = ['errors' => (new ExternalException(ErrorCode::OAUTH_INVALID_STATE))->getFormattedMessage()];
            return $this->thirdMiddlePage($result, $login_type);
        }

        if ($type === Constants::TYPE_WECHAT) {
            $provider = new WechatProvider();
            $credential_type = Credential::TYPE_WECHAT;
            $common = Common::WECHAT;
        } elseif ($type === Constants::TYPE_DINGTALK) {
            $provider = new DingtalkProvider();
            $credential_type = Credential::TYPE_DINGTALK;
            $common = Common::DINGTALK;
        } elseif ($type === Constants::TYPE_QIHOO360) {
            $authCode = $this->request->get('code');
            $provider = new Qihoo360Provider($authCode);
            $credential_type = Credential::TYPE_QIHOO360;
            $common = Common::QIHOO360;
        }

        $access_token = $provider->getAccessToken();
        $third_user = $provider->getResourceOwner($access_token);
        CacheHelper::unsetOAuthState($state);

        $credential = Credential::getUniqueCredentialByIdentifier($third_user->getId(), $credential_type);
        // 找不到表示需要绑定
        if (!$credential) {
            $login_user = Context::user();
            if ($login_user && $scene == Constants::SCENE_BIND) {
                $this->bindUser($login_user, $third_user, $credential_type);
                $result = ['success' => true, 'message' => trans('message.third_success', ['third' => $common, 'nick' => $third_user->getNick()])];
                return $this->thirdMiddlePage($result, $login_type);
            } else {
                CacheHelper::setThirdOAuthUserInfo(['type' => $credential_type, 'user' => $third_user->toArray()]);
                Context::setFStateContent(['scene' => $scene, 'login_type' => $login_type]);
                if (!empty($context['api_key'])) {
                    // 把api_key放到fstate的query里，这样可以一直带下去
                    $exist_query = Context::getFStateContent('query');
                    $exist_query['api_key'] = $context['api_key'];
                    Context::setFStateContent(['query' => $exist_query]);
                    // 在request的query里也存一个api_key，用来跳转的时候带上
                    $this->request->query->add(['api_key' => $context['api_key']]);
                }
                $redirect_url = "/bind_login?scene={$scene}&bind_type={$type}";
                return redirect($redirect_url);
            }
        } else {
            if ($scene == Constants::SCENE_LOGIN) {
                $in['user'] = $credential->getUser();
                if ($login_type == 'sync') {
                    $in['action_none'] = 'bindsyncviewLoginActionNone';
                }
                if ($login_type == 'app') {
                    $in['api_key'] = $context['api_key'];
                }
                try {
                    $return = $this->doLogin($login_type, $in, static::$skip_check_password | static::$skip_check_two_step | static::$skip_check_security_restriction);
                }
                catch(ExternalException $e) {
                    return view('auth.login_error', [
                        'platform' => 'web',
                        'css'=> 'css/web.css',
                        'errors' => $e->getFormattedMessage()
                    ]);
                }
                // app第三方登录回调，需要返回页面，在页面中调app中的方法实现登录
                if ($login_type == 'app') {
                    return view('auth.app_finish_bind', [
                        'login_info' => $return,
                        'platform' => 'mobile',
                    ]);
                } else {
                    return $return;
                }
            } else {
                $result = ['errors' => (new ExternalException(ErrorCode::BINDED_THIRD_ACCOUNT))->getFormattedMessage()];
                return $this->thirdMiddlePage($result, $login_type);
            }
        }


    }

    public function thirdMiddlePage($result, $login_type) {

        return view('auth.middleware', [
            'json' => $result,
            'platform' => $login_type,
            'post' => 1
        ]);
    }

}