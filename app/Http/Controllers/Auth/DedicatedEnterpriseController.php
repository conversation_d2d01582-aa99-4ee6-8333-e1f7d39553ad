<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Remote\DedicatedEnterprise;
use App\Util\CommonHelper;
use Illuminate\Http\Request;

class DedicatedEnterpriseController extends Controller
{
    public function __construct(Request $request)
    {
        parent::__construct($request);
        $this->middleware('guest');
    }

    public function getSsoProductId()
    {
        $alias = $this->request->get('alias');
        $res = (new DedicatedEnterprise())->getSsoProductId($alias);
        if(isset($res['errors'])) {
            return CommonHelper::translateMainSiteErrors($res);
        }
        return $this->success($res);
    }
}
