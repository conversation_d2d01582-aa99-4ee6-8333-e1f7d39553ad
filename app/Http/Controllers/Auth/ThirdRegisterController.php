<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Http\Controllers\LoginTrait;
use App\Services\Auth\UserRegister;
use App\Util\CacheHelper;
use App\Util\SecurityHelper;

class ThirdRegisterController extends Controller
{
    use ThirdBindTrait, LoginTrait;

    /**
     * @param UserRegister $user_register
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     * @throws \App\Exceptions\InternalException
     * @throws \App\Exceptions\ValidationException
     * @throws \Exception
     * @throws \Throwable
     */
    public function bindRegister(UserRegister $user_register)
    {
        $request = $this->request;

        $third_user = CacheHelper::getThirdOAuthUserInfo();

        $input = $request->json()->all();
        SecurityHelper::secureParamDecode($input);
        $res = $user_register->handle($input);

        $login_type = 'web';
        $in['third_user'] = $third_user;
        $in['action_none'] = 'bindwebLoginActionNone';
        $in['user'] = $res['user'];
        return $this->doLogin($login_type, $in, self::$skip_check_password | self::$skip_check_two_step | self::$skip_check_ent_status | self::$skip_check_security_restriction);
    }
}