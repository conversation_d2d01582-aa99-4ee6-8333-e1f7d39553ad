<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Services\Password\ConfirmForgotAccount;
use App\Services\Password\ForgotPassword;
use Illuminate\Foundation\Auth\SendsPasswordResetEmails;
use Illuminate\Http\Request;
use App\Util\UserAgent;

class ForgotPasswordController extends Controller
{
    public function __construct(Request $request)
    {
        $this->middleware('guest');
        $this->middleware(['fstate', 'lstate']);
        parent::__construct($request);
    }

    public function showForgotPasswordForm(UserAgent $user_agent)
    {
        $platform = $user_agent->getBrowserPlatform();
        return view('auth.forgot', [
            'js' => 'js/forgot.js',
            'css' => 'css/' . $platform . '.css',
            'platform' => $platform
        ]);
    }

    public function confirmAccount(ConfirmForgotAccount $comfirm_forgot_account)
    {
        $result = $comfirm_forgot_account->handle($this->request->json()->all());
        return $this->success($result);
    }

    public function confirmForgot(ForgotPassword $forgot_password)
    {
        $result = $forgot_password->handle($this->request->json()->all());
        return $this->success($result);
    }
}
