<?php

namespace App\Http\Controllers\Auth;

use App\Constants\CookieKey;
use App\Remote\Chameleon;
use App\Http\Controllers\Controller;
use App\Util\CacheHelper;
use App\Util\CommonHelper;
use App\Util\Context;
use App\Util\HttpHelper;
use App\Util\UserAgent;
use App\Util\ViewHelper;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;

class SsoEntryController extends Controller
{

    /**
     * Create a new controller instance.
     *
     * @param Request $request
     */
    public function __construct(Request $request)
    {
        $this->middleware('guest');
        parent::__construct($request);
    }

    public function showSsoEntryForm(UserAgent $user_agent)
    {
        HttpHelper::disableCache();

        $platform = $user_agent->getBrowserPlatform();
        $_q = $this->request->query("_q");
        $loginUrlsWithName = [];
        $loginUrl = null;
        if ($_q)
        {
            $ssoQueries = json_decode(base64_decode(strtr($_q, '-_~', '+/=')), true);
            if ($ssoQueries)
            {
                $queries = $ssoQueries['queries'] ?? [];
                $loginUrl = $ssoQueries['login_url'] ?? null;
                if (isset($ssoQueries['product_ids']))
                {
                    $domainProductId = CommonHelper::getProductId();
                    foreach ($ssoQueries['product_ids'] as $productId)
                    {
                        $queries['product_id'] = $productId;
                        $loginUrlsWithName[$loginUrl . '?' .  http_build_query($queries)] = ViewHelper::getDomainCustomResource($domainProductId, $productId);
                    }
                }
            }
        }

        return view('auth.sso_entry', [
            'css' => 'css/' . $platform . '.css',
            'platform' => $platform,
            'loginUrl' => $loginUrl,
            'loginUrlsWithName' => $loginUrlsWithName
        ]);
    }
}
