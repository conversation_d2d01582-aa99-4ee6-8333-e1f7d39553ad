<?php

namespace App\Http\Controllers\Auth;
use App\Constants\ErrorCode;
use App\Constants\ExceptionFieldKey;
use App\Exceptions\ExternalException;
use App\Models\Credential;
use App\Models\User;
use App\Util\Context;
use League\OAuth2\Client\Provider\ResourceOwnerInterface;

/**
 * 第三方账号绑定是用到的共享代码
 * Trait ThirdBindTrait
 * @package App\Http\Controllers\Auth
 */
trait ThirdBindTrait
{
    /**
     * @param $third_user
     * @throws ExternalException
     */
    public function thirdBind($third_user)
    {
        if ($third_user) {
            $login_user = Context::user();
            $type = $third_user['type'];
            if ($type == Credential::TYPE_WECHAT) {
                $cred = Credential::getUserCredential($login_user->id, Credential::TYPE_WECHAT);
                if ($cred) {
                    throw new ExternalException(ErrorCode::ALREADY_BIND_WECHAT_ACCOUNT, ExceptionFieldKey::LOGIN);
                }
                $third_user = new \App\Library\OAuth\Wechat\User($third_user['user']);
            } elseif ($type == Credential::TYPE_DINGTALK) {
                $cred = Credential::getUserCredential($login_user->id, Credential::TYPE_DINGTALK);
                if ($cred) {
                    throw new ExternalException(ErrorCode::ALREADY_BIND_DINGTALK_ACCOUNT, ExceptionFieldKey::LOGIN);
                }
                $third_user = new \App\Library\OAuth\Dingtalk\User($third_user['user']);
            } elseif ($type == Credential::TYPE_QIHOO360) {
                $cred = Credential::getUserCredential($login_user->id, Credential::TYPE_QIHOO360);
                if ($cred) {
                    throw new ExternalException(ErrorCode::ALREADY_BIND_360_ACCOUNT, ExceptionFieldKey::LOGIN);
                }
                $third_user = new \App\Library\OAuth\Qihoo360\User($third_user['user']);
            } else {
                throw new ExternalException(ErrorCode::CREDENTIAL_NOT_FOUND, ExceptionFieldKey::LOGIN);
            }
            $this->bindUser($login_user, $third_user, $type);
        }
    }

    private function bindUser(User $user, ResourceOwnerInterface $third_user, $type)
    {
        $login_user = $user;
        $credential = Credential::generate($login_user->id, $third_user->getId(), $type);

        if ($type == Credential::TYPE_WECHAT) {
            $credential->setAdditionalValue(Credential::VALUE_WECHAT, $third_user->toArray());
        } elseif ($type == Credential::TYPE_DINGTALK) {
            $credential->setAdditionalValue(Credential::VALUE_DINGTALK, $third_user->toArray());
        } elseif ($type == Credential::TYPE_QIHOO360) {
            $credential->setAdditionalValue(Credential::VALUE_QIHOO360, $third_user->toArray());
        }

        $credential->setAdditionalValue(Credential::VALUE_VERIFIED_AT, time());
        $credential->save();
    }

    protected function bindwebLoginActionNone(User $user, array $params = [])
    {
        return function() use ($user, $params) {
            $return = $this->webLoginActionNone($user, $params)();
            $third_user = $params['third_user'] ?? null;
            try {
                $this->thirdBind($third_user);
            } catch(\Exception $e) {
                Context::logout();
                throw $e;
            }
            return $return;
        };
    }

    protected function bindappLoginActionNone(User $user, array $params = [])
    {
        return function() use ($user, $params) {
            $return = $this->appLoginActionNone($user, $params)();
            $third_user = $params['third_user'] ?? null;
            try {
                $this->thirdBind($third_user);
            } catch(\Exception $e) {
                Context::logout();
                throw $e;
            }
            return $return;
        };
    }

    protected function bindsyncLoginActionNone(User $user, array $params = [])
    {
        return function() use ($user, $params) {
            $result = $this->syncLoginActionNone($user, $params)();
            $third_user = $params['third_user'] ?? null;
            try {
                $this->thirdBind($third_user);
            } catch(\Exception $e) {
                Context::logout();
                throw $e;
            }
            return $result;
        };
    }

    protected function bindsyncviewLoginActionNone(User $user, array $params = [])
    {
        return function() use ($user, $params) {
            $return = $this->syncLoginActionNone($user, $params)();

            return view('auth.middleware', [
                'js' => 'js/login.js',
                'json' => $return,
                'platform' => 'sync',
            ]);
        };
    }
}
