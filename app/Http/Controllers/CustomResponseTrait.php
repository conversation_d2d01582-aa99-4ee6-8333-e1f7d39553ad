<?php

namespace App\Http\Controllers;

use Illuminate\Support\Facades\Response;

trait CustomResponseTrait
{
    protected function success(array $response = [])
    {
        $response['success'] = true;
        return $response;
    }

    protected function fail(array $response = [])
    {
        $response['success'] = false;
        return $response;
    }

    protected function image($img, $extension = 'png')
    {
        if (is_resource($img)) {
            $func = 'image'.$extension;
            $func($img);
            return Response::make('', 200, [
                "Content-type" => "image/$extension",
            ]);
        } else {
            return Response::make($img, 200, [
                "Content-type" => "image/$extension",
            ]);
        }
    }
}