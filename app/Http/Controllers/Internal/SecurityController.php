<?php

namespace App\Http\Controllers\Internal;

use App\Constants\CacheKey;
use App\Http\Controllers\Controller;
use App\Library\TwoStep;
use App\Util\CacheHelper;
use App\Util\LogHelper;
use App\Util\LogType;
use Illuminate\Http\Request;

class SecurityController extends Controller
{
    public function __construct(Request $request)
    {
        parent::__construct($request);
        $this->middleware('guard:web');
    }

    public function getCode()
    {
        $input = $this->request->only(['wechat_id']);

        $two_step = new TwoStep(['method' => TwoStep::METHOD_WECHAT]);
        $secret = $two_step->getSecret();
        $code = $two_step->now();
        $id = $input['wechat_id'];

        CacheHelper::cacheInfo(CacheKey::TWO_STEP_WECHAT_SUBSCRIBE . $code, [
            'wechat_id' => $id,
            'secret' => $secret
        ], 90);

        return $this->success([
            'code' => $code
        ]);
    }
}