<?php

namespace App\Http\Controllers\Internal;

use App\Constants\ErrorCode;
use App\Exceptions\ExternalException;
use App\Http\Controllers\Controller;
use App\Http\Controllers\LoginTrait;
use App\Models\ClientDevice;
use App\Models\Credential;
use App\Models\User;
use App\Remote\AuthToken as RemoteAuthToken;
use App\Services\Auth\UserLogout;
use App\Services\Internal\ChangePassword;
use App\Services\Internal\CreatePersonalUser;
use App\Services\Internal\CreateUser;
use App\Services\Internal\CreateCredential;
use App\Services\Internal\CreateUsers;
use App\Services\Internal\DeleteUser;
use App\Services\Internal\RenewCredentials;
use App\Services\Security\ClientDeviceDeleteByAdmin;
use App\Util\ArrayHelper;
use App\Util\Context;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use App\Util\HttpHelper;

class AccountController extends Controller
{
    use LoginTrait;

    public function __construct(Request $request)
    {
        parent::__construct($request);
        $this->middleware('auth:api')->only('getLoginInfo');
    }

    public function login()
    {
        $main_user_id = $this->request->get('user_id');
        $api_key = $this->request->get('api_key');
        $login_type = $this->request->get('login_type');
        $skip_check_two_step = $this->request->get('skip_check_two_step', true);
        $user = User::findByMainUserId($main_user_id);
        if(!$user) {
            throw new ExternalException(ErrorCode::ACCOUNT_NOT_FOUND);
        }
        $ret = $this->doLogin($login_type, ['user' => $user, 'api_key' => $api_key],
            static::$skip_check_password | ($skip_check_two_step ? static::$skip_check_two_step : 0) | static::$skip_check_ent_status | 0);
        if($ret instanceof RedirectResponse) {
            //搬LastStateManager代码,暂时没有好的处理方式,先这样
            if ($skip_check_two_step || !in_array($ret->getTargetUrl(), [route('login_verify'), route('force_set_two_step')])) {
                $ret = [];
            } else {
                $url = $ret->getTargetUrl();
                $new_url = HttpHelper::addQuery($url, '_fstate', Context::getFState());
                foreach ($ret->getRequest()->query() as $key => $value)
                {
                    if($key === '_fstate')
                    {
                        continue;
                    }
                    $new_url = HttpHelper::addQuery($new_url, $key, $value);
                }
                $ret = ['redirect' => $new_url];
            }
        }
        return $this->success($ret);
    }

    public function logout(UserLogout $user_logout)
    {
        $main_user_id = $this->request->get('user_id');
        $user = User::findByMainUserId($main_user_id);
        if(!$user) {
            if(!Context::user()) {
                throw new ExternalException(ErrorCode::ACCOUNT_NOT_FOUND);
            }
        }
        else {
            Context::once($user);
        }
        $user_logout->handle();
        return $this->success();
    }

    public function checkCredentialExistence(Request $request)
    {
        $identifiers = $request->json('identifiers');
//        $identifiers = explode(',', $request->get('identifiers'));
        $credentials = Credential::getCredentialsByIdentifiers($identifiers);
        $user_ids = array_map(function($e){return $e->login_user_id;}, $credentials);
        $users = User::findByIds($user_ids);
        $users = ArrayHelper::indexObjectsByField('id', $users);
        $identifiers_user_id_mapping = [];
        foreach ($credentials as $identifier => $credential) {
            if(isset($users[$credential->login_user_id])) {
                $identifiers_user_id_mapping[$identifier] = $users[$credential->login_user_id]->user_id;
            }
        }
        return [
            'success' => true,
            'identifier_user_id_mapping' => $identifiers_user_id_mapping,
        ];
    }

    public function checkThirdCredentialExistence(Request $request)
    {
        $identifiers = $request->json('identifiers');
//        $identifiers = explode(',', $request->get('identifiers'));
        $credentials = Credential::getThirdCredentialsByIdentifiers($identifiers);
        $user_ids = array_map(function($e){return $e->login_user_id;}, $credentials);
        $users = User::findByIds($user_ids);
        $users = ArrayHelper::indexObjectsByField('id', $users);
        $identifiers_user_id_mapping = [];
        foreach ($credentials as $identifier => $credential) {
            if(isset($users[$credential->login_user_id])) {
                $identifiers_user_id_mapping[$identifier] = $users[$credential->login_user_id]->user_id;
            }
        }
        return [
            'success' => true,
            'identifier_user_id_mapping' => $identifiers_user_id_mapping,
        ];
    }

    public function updateThirdUsersCredential(Request $request)
    {
        $old_identifier = $request->json('old_identifier');
        $new_identifier = $request->json('new_identifier');
        $types = $request->json('types');
        $credential = Credential::updateThirdUsersCredential($old_identifier, $new_identifier, $types);
        if(!$credential)
        {
            throw new ExternalException(ErrorCode::CREDENTIAL_NOT_FOUND);
        }
        $user_id = $credential->login_user_id;
        $user = User::findById($user_id);
        $identifier = $credential->getIdentifier();
        $identifiers_user_id_mapping = [];
        $identifiers_user_id_mapping[$identifier] = $user->user_id;

        return [
            'success' => true,
            'identifier_user_id_mapping' => $identifiers_user_id_mapping,
        ];
    }

    public function createUser(Request $request, CreateUser $create_user)
    {
        $res = $create_user->handle($request->all());
        $user = $res['user']->format(User::$internal_keys);
        if($res['credential']) {
            $user['credential'] = $res['credential']->format(Credential::$internal_keys);
        }
        return [
            'success' => true,
            'created_user' => $user
        ];
    }

    public function createCredential(Request $request, CreateCredential $create_user)
    {
        $res = $create_user->handle($request->all());
        $user = $res['user']->format(User::$internal_keys);
        if($res['credential']) {
            $user['credential'] = $res['credential']->format(Credential::$internal_keys);
        }
        return [
            'success' => true,
            'created_user' => $user
        ];
    }

    public function createPersonalUser(Request $request, CreatePersonalUser $create_user)
    {
        $res = $create_user->handle($request->all());
        $user = $res['user']->format(User::$internal_keys);
        if($res['credential']) {
            $user['credential'] = $res['credential']->format(Credential::$internal_keys);
        }
        return [
            'success' => true,
            'created_user' => $user
        ];
    }

    public function changePassword(Request $request, ChangePassword $changePassword)
    {
        $res = $changePassword->handle($request->all());
        $user = $res['user']->format(User::$internal_keys);
        return [
            'success' => true,
            'created_user' => $user
        ];
    }

    public function createUsers(Request $request, CreateUsers $create_users)
    {
        $res = $create_users->handle($request->all());
        $created_users = [];
        foreach ($res['created_users'] as $user_id => $created_user) {
            $user = $created_user['user']->format(User::$internal_keys);
            if($created_user['credential']) {
                $user['credential'] = $created_user['credential']->format(Credential::$internal_keys);
            }
            $created_users[$user_id] = $user;
        }
        return [
            'success' => true,
            'created_users' => $created_users,
        ];
    }

    public function renewUsersCredentials(RenewCredentials $renew_credentials)
    {
        $main_user_ids = $this->request->json('user_ids');
        $users = User::findByMainUserIds($main_user_ids);
        $user_ids = ArrayHelper::fetchPropertyFromObject('id', $users);

        $credentials = Credential::getUsersCredentials($user_ids);
        $credentials = $renew_credentials->handle(['credentials' => $credentials])['credentials'];
        $credentials_arr = Credential::bulkFormat($credentials, Credential::$internal_keys);
        $credentials_arr = ArrayHelper::indexArraysByField('login_user_id', $credentials_arr);
        $users = ArrayHelper::indexObjectsByField('id', $users);
        $credentials_indexed_by_main_user_id = [];
        foreach ($credentials_arr as $user_id => $credential_arr) {
            $credentials_indexed_by_main_user_id[$users[$user_id]->user_id] = $credential_arr;
        }
        return $this->success(['credentials' => $credentials_indexed_by_main_user_id]);
    }

    public function deleteUser(Request $request, DeleteUser $delete_user)
    {
        $delete_user->handle($request->all());
        return $this->success();
    }

    public function getUserActiveStatus()
    {
        $main_user_ids = $this->request->json('user_ids');
        $users = User::findByMainUserIds($main_user_ids);
        $user_ids = ArrayHelper::fetchPropertyFromObject('id', $users);

        $credentials = Credential::getUsersCredentials($user_ids);
        $credentials = ArrayHelper::indexObjectsByField('login_user_id', $credentials);
        $users = ArrayHelper::indexObjectsByField('id', $users);
        $now = time();
        $active_statuses = [];
        foreach ($credentials as $login_user_id => $credential) {
            $active_status = [];
            $user= $users[$login_user_id];
            if(!$user->isActive() && !$credential->isValid()) {
                if($credential->activation_expires_at < $now) {
                    $active_status['activation_code_expired'] = true;
                }
                else {
                    $active_status['activation_url'] = $credential->getActivationUrl();
                }
            }
            $active_statuses[$user->user_id] = $active_status;
        }
        $res['active_status'] = $active_statuses;
        return $this->success($res);
    }

    public function getUserTwoStepStatus()
    {
        $main_user_ids = $this->request->json('user_ids');
        $users = User::findByMainUserIds($main_user_ids);

        $two_step_statuses = [];
        foreach ($users as $user) {
            $two_step_statuses[$user->user_id] = $user->getTwoStepStatus();
        }
        $res['two_step_status'] = $two_step_statuses;
        return $this->success($res);
    }

    public function closeTwoStep()
    {
        $main_user_id = $this->request->get('user_id');
        $user = User::findByMainUserId($main_user_id);
        if($user) {
            $user->closeTwoStep();
            $user->save();
        }

        return $this->success();
    }

    public function deleteClientDevice()
    {
        $user_id = $this->request->get('user_id');
        $device_id = $this->request->get('id');
        $in['id'] = $device_id;
        $in['user_id'] = $user_id;

        (new ClientDeviceDeleteByAdmin())->handle($in);
        return $this->success();
    }

    public function getLoginInfo()
    {
        $user = Context::user();
        if(!$user) {
            return $this->fail();
        }

        $auth_token = Context::getAuthToken();
        if(!$auth_token) {
            return $this->fail();
        }
        $auth_token_obj = RemoteAuthToken::get($auth_token);
//        $auth_token_obj = AuthToken::findByAuthTokenGently($auth_token);
        if(!$auth_token_obj) {
            return $this->fail();
        }

        $client_device = ClientDevice::findByAuthToken($auth_token_obj->id);

        $login_info = [
            'login_user_id' => $user->id,
            'client_device_id' => $client_device ? $client_device->id : null,
        ];
        return $this->success(['login_info' => $login_info]);
    }
}
