<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Util\CommonHelper;
use App\Util\UserAgent;
use Illuminate\Http\Request;

class HomeController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @param Request $request
     */
    public function __construct(Request $request)
    {
        parent::__construct($request);
    }

    /**
     * Show the application dashboard.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        return redirect(CommonHelper::getLoginUrl());
    }

    public function showRegisterSuccessForm()
    {
        return view('auth.register_success', [
            'css' => 'css/web.css',
            'platform' => 'web',
        ]);
    }

    public function showValidateUserSuccessForm($name, $space, UserAgent $user_agent)
    {   
        $platform = $user_agent->getBrowserPlatform();
        return view('auth.validate_success', [
            'css' => 'css/' . $platform . '.css',
            'platform' => $platform,
            'space' => $space,
            'name' => $name,
            'js' => 'js/validate.js'
        ]);
    }

    public function showEnterpriseRegisterSuccessForm()
    {
        return view('auth.enterprise_register_success', [
            'css' => 'css/web.css',
            'platform' => 'web',
            'register' => 1
        ]);
    }

    public function showValidateSuccessForm(UserAgent $user_agent) {
        $platform = $user_agent->getBrowserPlatform();
        return view('auth.validate_success', [
            'css' => 'css/' . $platform . '.css',
            'platform' => $platform,
            'js' => 'js/validate.js'
        ]);
    }

    public function showRefreshTokenPage()
    {
        return view('auth.refresh_token', [
            'redirect' => $this->request->getUri(),
            'platform' => 'web',
            'js' => 'js/refresh_token.js'
        ]);
    }
}


