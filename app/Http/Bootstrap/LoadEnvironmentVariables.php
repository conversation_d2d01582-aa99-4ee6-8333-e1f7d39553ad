<?php

namespace App\Http\Bootstrap;

use App\Util\CommonHelper;
use Dotenv\Dotenv;
use Dotenv\Exception\InvalidPathException;
use Illuminate\Foundation\Application;

class LoadEnvironmentVariables
{
    /**
     * Bootstrap the given application.
     *
     * @param  \Illuminate\Foundation\Application  $app
     * @return void
     */
    public function bootstrap(Application $app)
    {
        if ($app->configurationIsCached()) {
            return;
        }

        try {
            // 优先加载 .env.custom 配置文件
            $this->tryLoadSpecificEnvironmentFile($app, '.custom');

            // load .env file
            // .env 中的配置允许被 .env.custom 文件覆盖，但是不允许被其他配置文件覆盖
            (new Dotenv($app->environmentPath(), $app->environmentFile()))->load();

            // load sso config if needed
            // 加载混合云相关的个性化配置
            $this->tryLoadSSOEnvironmentFile($app);

            // 检查当前 env
            // load .env.app
            if (getenv('APP_ENV') !== 'production') {
                $this->tryLoadSpecificEnvironmentFile($app, '.app.test');
            } else {
                $this->tryLoadSpecificEnvironmentFile($app, '.app');
            }

            $app->useStoragePath(env('PATH_STORAGE', base_path()) . '/storage');

        } catch (InvalidPathException $e) {
            // TODO
        }
    }

    /**
     * load {$app->environmentPath().'/'.$app->environmentFile().'.'.$file_suffix} if exists
     *
     * @param Application $app
     * @param $file_prefix
     * @param bool $overload
     */
    private function tryLoadSpecificEnvironmentFile($app, $file_prefix, $overload = false)
    {
        if (file_exists($app->environmentPath().'/'. $file_prefix . $app->environmentFile())) {
            if ($overload) {
                (new Dotenv($app->environmentPath(),  $file_prefix . $app->environmentFile()))->overload();
            } else {
                (new Dotenv($app->environmentPath(),  $file_prefix . $app->environmentFile()))->load();
            }
        }
    }

    /**
     * @param Application $app
     */
    private function tryLoadSSOEnvironmentFile($app)
    {
        $product_id = CommonHelper::getProductId();

        if (file_exists(CommonHelper::getSSOEnvPath($app, $product_id))) {
            (new Dotenv($app->bootstrapPath('cache'), ".sso.{$product_id}.env"))->load();
        }
    }
}