<?php

namespace App\Http;

use Illuminate\Foundation\Http\Kernel as HttpKernel;

class <PERSON>el extends HttpKernel
{
    /**
     * The application's global HTTP middleware stack.
     *
     * These middleware are run during every request to your application.
     *
     * @var array
     */
    protected $middleware = [
        \Illuminate\Foundation\Http\Middleware\CheckForMaintenanceMode::class,
        \Illuminate\Foundation\Http\Middleware\ValidatePostSize::class,
        \App\Http\Middleware\TrimStrings::class,
        \Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull::class,
        \App\Http\Middleware\TrustProxies::class,
        \App\Http\Middleware\CaptureRequestId::class,
    ];

    /**
     * The application's route middleware groups.
     *
     * @var array
     */
    protected $middlewareGroups = [
        'web' => [
            \App\Http\Middleware\EncryptCookies::class,
            \Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse::class,
            \Illuminate\Session\Middleware\StartSession::class,
//            \Illuminate\View\Middleware\ShareErrorsFromSession::class,
//            \App\Http\Middleware\VerifyCsrfToken::class,
            \Illuminate\Routing\Middleware\SubstituteBindings::class,
            \App\Http\Middleware\AjaxRedirect::class,
            \App\Http\Middleware\DeviceToken::class,
            \App\Http\Middleware\InitPassword::class,
            \App\Http\Middleware\SetLocale::class,
        ],

        'api' => [
            \App\Http\Middleware\DeviceToken::class,
            'throttle:500,1',
            'bindings',
        ],
        'internal' => [
            \App\Http\Middleware\DeviceToken::class,
            'guard',
        ],
    ];

    /**
     * The application's route middleware.
     *
     * These middleware may be assigned to groups or used individually.
     *
     * @var array
     */
    protected $routeMiddleware = [
        'auth' => \App\Http\Middleware\Authenticate::class,
        'bindings' => \Illuminate\Routing\Middleware\SubstituteBindings::class,
        'can' => \Illuminate\Auth\Middleware\Authorize::class,
        'guest' => \App\Http\Middleware\RedirectIfAuthenticated::class,
        'throttle' => \Illuminate\Routing\Middleware\ThrottleRequests::class,
        // 内部鉴权 middleware
        'guard' => \App\Http\Middleware\BaseGuard::class,
        // 这两个中间件需要打包使用
        'fstate' => \App\Http\Middleware\FirstStateManager::class,
        'lstate' => \App\Http\Middleware\LastStateManager::class,

        // 需要额外身份验证的请求可以用这个中间件。
        'identity' => \App\Http\Middleware\VerifyIdentity::class,
        // 所有 ajax 请求的 302 请求会被 构造成 ['success' => true, 'redirect' => 'uri'] 的形式
        'ajax_redirect' => \App\Http\Middleware\AjaxRedirect::class,
        'oauth_state' => \App\Http\Middleware\OAuthState::class,
        'set_source' => \App\Http\Middleware\SetSource::class,
        'sem' => \App\Http\Middleware\SetSem::class,
    ];

    /**
     * The priority-sorted list of middleware.
     *
     * Forces the listed middleware to always be in the given order.
     *
     * @var array
     */
    protected $middlewarePriority = [
        // 处理所有返回的redirect，将如果不是get请求，把重定向转成json返回
        \App\Http\Middleware\AjaxRedirect::class,

        \Illuminate\Session\Middleware\StartSession::class,
        \Illuminate\View\Middleware\ShareErrorsFromSession::class,
        \Illuminate\Auth\Middleware\Authenticate::class,
        \Illuminate\Session\Middleware\AuthenticateSession::class,
        \Illuminate\Routing\Middleware\SubstituteBindings::class,
        \Illuminate\Auth\Middleware\Authorize::class,

        // user defined middlewares's order
        // 前置中间件
        // 绝大多数情况下，FirstState 应该最先加载
        // 该中间件会往 request 中添加 query headers 等参数
        \App\Http\Middleware\OAuthState::class,
        \App\Http\Middleware\FirstStateManager::class,
        \App\Http\Middleware\Authenticate::class,
        \App\Http\Middleware\InitPassword::class,
        \Illuminate\Routing\Middleware\SubstituteBindings::class,
        \Illuminate\Auth\Middleware\Authorize::class,
        \App\Http\Middleware\RedirectIfAuthenticated::class,
        \App\Http\Middleware\SetLocale::class,
        \App\Http\Middleware\DeviceToken::class,
        \Illuminate\Routing\Middleware\ThrottleRequests::class,
        \App\Http\Middleware\BaseGuard::class,
        \App\Http\Middleware\VerifyIdentity::class,
        \App\Http\Middleware\SetSem::class,

        // 后置中间件
        // 后置中间件的顺序和前置完全相反。
        // 以下中间件，先定义的会后执行
        // 绝大多数情况下，LastState 应该最后被加载
        // 该中间件会往 302 请求中添加 query 参数
        \App\Http\Middleware\LastStateManager::class,
    ];

    /**
     * 重写父类中的该数组。
     * 主要是重写了配置文件的加载
     * 同 \App\Console\Kernel.php
     *
     * @var array
     */
    protected $bootstrappers = [
        \App\Http\Bootstrap\LoadEnvironmentVariables::class,
        \App\Http\Bootstrap\LoadConfiguration::class,
        \Illuminate\Foundation\Bootstrap\HandleExceptions::class,
        \Illuminate\Foundation\Bootstrap\RegisterFacades::class,
        \Illuminate\Foundation\Bootstrap\RegisterProviders::class,
        \Illuminate\Foundation\Bootstrap\BootProviders::class,
    ];
}
