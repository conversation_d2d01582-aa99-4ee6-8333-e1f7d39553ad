<?php

namespace App\Http\Middleware;

use App\Util\LogHelper;
use App\Util\LogType;
use Closure;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Response;

class AjaxRedirect
{
    /**
     * 1. 如果是 post 请求返回了 302。统一处理成 json形式的 redirect 让前端重定向
     *
     * @param  \Illuminate\Http\Request $request
     * @param  \Closure $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        /**
         * @var Response $response
         */
        $response = $next($request);

        if ($response->isRedirection() && $request->ajax() && $request->acceptsJson()) {
            /**
             * @var RedirectResponse $response
             */
            $redirect_uri = $response->getTargetUrl();
            $response->headers->remove('Location');
            return response()->json(['success' => true, 'redirect' => $redirect_uri], 200, $response->headers->all());
        }

        return $response;
    }
}
