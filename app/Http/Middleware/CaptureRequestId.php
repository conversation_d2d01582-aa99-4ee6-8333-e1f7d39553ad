<?php

namespace App\Http\Middleware;

use App\Util\CommonHelper;
use Closure;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Log;

class CaptureRequestId
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        // Capture the HTTP_X_REQUEST_ID (passed by NGINX), or generate one if it's missing
        $requestId = $request->server('HTTP_X_REQUEST_ID', uniqid());

        // Store the X-Request-ID in the request attributes
        $request->attributes->set('X-Request-ID', $requestId);

        // Register a custom processor to add this ID to the logging context
        $monolog = Log::getMonolog();

        $format = "%datetime% %level_name% %message% %context% %extra%\n";
        $formatter = new \Monolog\Formatter\LineFormatter($format, 'Y-m-d H:i:s.u', true, true);

        $handlers = $monolog->getHandlers();
        foreach($handlers as $handler) {
            $handler->setFormatter($formatter);
        }

        $context = $this->getLogContext($request, $requestId);
        Log::info("RequestReceived:", $context);

        $response = $next($request);

        // Check if the request URI is "/login" and exclude response body if so
        $logResponseBody = ($response->headers->get('Content-Type') === 'application/json');

        // Log the response details, omit body if the URI is "/login"
        Log::info('ResponseSent', [
            'request_id' => $requestId,
            'status' => $response->status(),
            'response' => $logResponseBody ? $response->getContent() : 'ResponseBodyHidden',
        ]);

        return $response;
    }

    /**
     * Get detailed log context for the request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $requestId
     * @return array
     */
    private function getLogContext($request, $requestId)
    {
        return [
            'ip' => CommonHelper::getClientIp(),
            'product_id' => CommonHelper::getProductId(),
            'session_id' => Session::getId(),
            'cookies' => $request->cookies->all(),
            'referer' => $request->server('HTTP_REFERER', null),
            'request_uri' => $request->server('REQUEST_URI', null),
            'user_agent' => $request->server('HTTP_USER_AGENT', null),
            'request_parameters' => $request->query(),
            'post_body' => $request->isMethod('post') ? $request->all() : null,
            'request_id' => $requestId,
        ];
    }

}