<?php

namespace App\Http\Middleware;

use App\Util\CommonHelper;
use App\Util\Context;
use App\Util\LogHelper;
use App\Util\LogType;
use App\Util\UserAgent;
use Closure;
use Illuminate\Support\Facades\Auth;

class RedirectIfAuthenticated
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @param  string|null  $guard
     * @return mixed
     */
    public function handle($request, Closure $next, $guard = null)
    {
        if (Auth::guard($guard)->check()) {
            $user_agent = new UserAgent();
            if($user_agent->isWebviewSync()) {
                // TODO: 临时方案，如果是在sync里就登出
                Auth::guard()->logout();
                session([config('session.user_id_session_name') => null]);
            } else {
                if (CommonHelper::getProductId() == 'fangcloud_v2' && stristr($request->getRequestUri(), '/introduction_register')) {
                    $parsedUrl = parse_url($request->fullUrl());
                    if ($parsedUrl['path']  == '/introduction_register') {
                        return response()->view('auth.introduction_register', [
                            'css' => 'css/quick-register.css',
                            'js' => 'js/introduction_register.js',
                            'platform' => 'mobile',
                            'register' => 0
                        ]);
                    }
                }

                if (CommonHelper::getProductId() == 'fangcloud_v2' && stristr($request->getRequestUri(), '/become_referral')) {
                    $parsedUrl = parse_url($request->fullUrl());
                    if ($parsedUrl['path']  == '/become_referral') {
                        return $next($request);
                    }
                }
                if (CommonHelper::getProductId() == 'fangcloud_v2' && stristr($request->getRequestUri(), '/quick_login/v1/register')) {
                    $parsedUrl = parse_url($request->fullUrl());
                    if ($parsedUrl['path']  == '/quick_login/v1/register') {
                        return $next($request);
                    }
                }
                if (CommonHelper::getProductId() == 'fangcloud_v2' && stristr($request->getRequestUri(), '/quick_login/v1/logout')) {
                    $parsedUrl = parse_url($request->fullUrl());
                    if ($parsedUrl['path']  == '/quick_login/v1/logout') {
                        return $next($request);
                    }
                }
                if ($request->get('redirect')) {
                    if (CommonHelper::getProductId() == 'fangcloud_v2') {
                        if (stristr($request->get('redirect'), env('FANG_HOST'))) {
                            return redirect($request->get('redirect'));
                        } else {
                            return redirect(config('app.default_page'));
                        }
                    }
                    return redirect($request->get('redirect'));
                } elseif ($user_agent->isMobile() && !$user_agent->isiPad()) {
                    return redirect(config('app.default_h5_page'));
                } else {
                    return redirect(config('app.default_page'));
                }
            }
        }

        Context::setWebContext();

        return $next($request);
    }
}
