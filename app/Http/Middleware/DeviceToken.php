<?php

namespace App\Http\Middleware;

use App\Util\CommonHelper;
use App\Util\Context;
use App\Util\LogHelper;
use App\Util\LogType;
use Closure;

class DeviceToken
{
    /**
     * 获取 device token
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        // auth middleware 会处理一次 device token
        // 比如从 auth_token 中取
        // 这个middleware 保证最后一定会有device token
        if (!Context::getDeviceToken()) {
            $device_token = CommonHelper::tryGetDeviceToken($request);
            Context::setDeviceToken($device_token);
        }

        return $next($request);
    }
}
