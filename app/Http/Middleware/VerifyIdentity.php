<?php

namespace App\Http\Middleware;

use App\Constants\ErrorCode;
use App\Constants\SessionCacheKey;
use App\Exceptions\ExternalException;
use App\Exceptions\VerifyIdentityException;
use App\Http\Controllers\LoginTrait;
use App\Util\CacheHelper;
use App\Util\LogHelper;
use App\Util\LogType;
use Closure;

class VerifyIdentity
{
    use LoginTrait;

    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request $request
     * @param  \Closure $next
     * @return mixed
     * @throws ExternalException
     */
    public function handle($request, Closure $next)
    {
        $status = CacheHelper::getCachedSessionInfo(SessionCacheKey::IDENTITY_VERIFIED);
        if (!$status) {
            throw new VerifyIdentityException;
        }

        return $next($request);
    }
}
