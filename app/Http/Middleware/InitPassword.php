<?php

namespace App\Http\Middleware;

use App\Util\Context;
use Closure;
use Illuminate\Support\Facades\Route;

class InitPassword
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        if (!Route::is(['init_password', 'do_login_verify', 'login_verify', 'force_set_two_step', 'security'])) {
            $user = Context::user();
            if ($user && $user->getPasswordRequireInitAttr()) {
                return redirect(route('init_password'));
            }
        }

        return $next($request);
    }
}
