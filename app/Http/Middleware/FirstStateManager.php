<?php

namespace App\Http\Middleware;

use App\Util\Context;
use App\Util\LogHelper;
use App\Util\LogType;
use App\Util\SecurityHelper;
use Closure;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Route;

class FirstStateManager
{
    /**
     * this routers will generate a new _fstate
     * @var array $fresh_routers
     */
    public $fresh_routers = [
        'login',
    ];

    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        // 登录页面应该每次都生成一个新的state, 放弃旧的state
        if ($request->method() === 'GET' && (in_array(Route::currentRouteName(), $this->fresh_routers) || !$request->query('_fstate'))) {
            $query_str = $request->getQueryString();
            $query_arr = [];
            parse_str($query_str, $query_arr);
            $fstate = Context::getFState();
            Context::setFState($fstate);
            if ($query_arr) {
                Context::setFStateContent(['query' => $query_arr]);
            }
        } else {
            // Attention! 这里新增的逻辑需要在  CommonHelper::initFstate 中最好再实现一下

            $fstate = $request->query('_fstate');
            Context::setFState($fstate);
            $arr = Context::getFStateContent();
            if ($arr && isset($arr['query']) && $arr['query']) {
                $request->query->add($arr['query']);
            }

            // 部分参数可以放在header中
            if ($arr && isset($arr['headers']) && $arr['headers']) {
                foreach($arr['headers'] as $key => $value) {
                    $request->headers->add($arr['headers']);
                }
            }
        }

        return $next($request);
    }
}
