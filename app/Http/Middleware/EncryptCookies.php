<?php

namespace App\Http\Middleware;

use App\Constants\CookieKey;
use Illuminate\Cookie\Middleware\EncryptCookies as Middleware;

class EncryptCookies extends Middleware
{
    /**
     * The names of the cookies that should not be encrypted.
     *
     * @var array
     */
    protected $except = [
        'device_token',
        'lang',
        'client_lang',
        'egeio-client-info',
        'public_product_id',
        'source',
        'sem',
        'marketing_referrer',
        Cookie<PERSON>ey::ALI_MARKET_REG,
        'gray_test',
        'qhclick_msg',
        'jlclick_msg',
        'bdclick_msg'
    ];
}
