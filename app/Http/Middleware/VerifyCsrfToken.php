<?php

namespace App\Http\Middleware;

use App\Exceptions\CsrfTokenInvalidException;
use App\Util\UserAgent;
use Closure;
use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken as Middleware;

class VerifyCsrfToken extends Middleware
{
    /**
     * The URIs that should be excluded from CSRF verification.
     *
     * @var array
     */
    protected $except = [
        //
        //'*',
        '/captcha/send_weiling_sms',
        '/captcha/weiling_captcha_check'
    ];

    /**
     * @param \Illuminate\Http\Request $request
     * @param Closure $next
     * @return mixed|\Symfony\Component\HttpFoundation\Response
     * @throws CsrfTokenInvalidException
     */
    public function handle($request, Closure $next)
    {
        if (
            $this->isReading($request) ||
            $this->runningUnitTests() ||
            $this->inExceptArray($request) ||
            $this->tokensMatch($request)
        ) {
            $ua = new UserAgent();
            if ($ua->isSameSiteNoneIncompatible()) {
                config(['session.same_site' => null]);
            }
            return $this->addCookieToResponse($request, $next($request));
        }

        throw new CsrfTokenInvalidException();
    }
}
