<?php

namespace App\Http\Middleware;

use App\Constants\CacheKey;
use App\Exceptions\EmptyDeviceTokenException;
use App\Exceptions\WebAuthenticationException;
use App\Util\CacheHelper;
use App\Util\LogHelper;
use App\Util\LogType;
use Closure;
use App\Auth\AuthTokenGuard;
use App\Auth\SessionGuard;
use App\Constants\Http;
use App\Exceptions\TwoStepForcedException;
use App\Exceptions\TwoStepNeededException;
use App\Models\ClientDevice;
use App\Models\User;
use App\Util\CommonHelper;
use App\Util\Context;
use App\Exceptions\AuthenticationException as RedisAuthenticationException;
use Illuminate\Auth\AuthenticationException;
use Illuminate\Auth\AuthManager as Auth;
use Throwable;

class Authenticate
{
    /**
     * The authentication factory instance.
     *
     * @var \Illuminate\Contracts\Auth\Factory
     */
    protected $auth;

    /**
     * Create a new middleware instance.
     *
     * @param  \Illuminate\Auth\AuthManager  $auth
     * @return void
     */
    public function __construct(Auth $auth)
    {
        $this->auth = $auth;
    }

    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request $request
     * @param  \Closure $next
     * @param array $args
     * @return mixed
     * @throws AuthenticationException
     * @throws TwoStepForcedException  企业开启强制二次验证时的异常
     * @throws TwoStepNeededException  用户通过记住我登录，同时设备不可信时抛出的异常
     * @throws RedisAuthenticationException
     * @throws \Exception
     */
    public function handle($request, Closure $next, ...$args)
    {
        $force_two_step_redirect = true;
        if (!empty($args)) {
            $last = array_pop($args);
            if (strtolower($last) == 'false') {
                $force_two_step_redirect = false;
            } else if (strtolower($last) != 'true'){
                $args[] = $last;
            }
        }
        $guards = $args;
        $this->authenticate($guards);
        $user = Context::user();
        $enterprise = $user->getRemoteUser()->enterprise;
        Context::setEnterprise($enterprise);

        try{
            $this->verifyClientDevice($request, $force_two_step_redirect);
        } catch(AuthenticationException | TwoStepForcedException $e) {
            Context::logout();
            throw $e;
        }

        return $next($request);
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @param $force_two_step_redirect
     * @throws AuthenticationException
     * @throws TwoStepForcedException
     * @throws TwoStepNeededException
     * @throws \Exception
     */
    private function verifyClientDevice($request, $force_two_step_redirect)
    {
        $user = Context::user();
        $client_device = null;
        $device_token = null;

        $guard = $this->auth->getDefaultDriver();

        if ($guard == 'web' || $guard == 'redis') {
            Context::setWebContext();

            $device_token = CommonHelper::tryGetDeviceToken($request);
        } else {
            Context::setApiContext();

            /**
             * @var AuthTokenGuard $guard_obj
             */
            $guard_obj = $this->auth->guard($guard);
            $auth_token = $guard_obj->getAuthToken();

            Context::setAuthToken($auth_token->auth_token);

            $client_device = ClientDevice::findByAuthToken($auth_token->id);

            if (!$client_device) {
                $device_token = $request->header(Http::HEADER_DEVICE_TOKEN);
            } else {
                $device_token = $client_device->device_token;
            }
        }
        Context::setDeviceToken($device_token);

        // redis guard 不需要检查设备
        if ($guard === 'redis') {
            return ;
        }

        /**
         * 设备不存在
         */
        if (!$device_token) {
            LogHelper::warning(LogType::MISS_DEVICE_TOKEN, 'miss device token ' . $user->user_id);
            return ;
            throw new EmptyDeviceTokenException;
        }

        if ($client_device === null) {
            $client_device = ClientDevice::findByUserIdDeviceToken($user->user_id, $device_token);
        }

        /**
         * 设备被删除
         * 理论上新的删除设备逻辑走不到这里，兼容老逻辑
         */
        if ($client_device && $client_device->isDeleted()) {
            if ($client_device->isOnline()) {
                $client_device->logout_time = time();
                $client_device->save();
                throw new AuthenticationException;
            }
        }

        $skip_twp_step = false;
        try {
            $skip_twp_step = CacheHelper::getCachedInfo(sprintf(CacheKey::QR_LOGIN_SKIP_TWO_STEP, $user->user_id));
        } catch (Throwable $ex) {
            LogHelper::info(LogType::Exception, $ex->getMessage(), ['exception' => LogHelper::serializeException($ex)]);
        }
        if ($skip_twp_step)
        {
            return;
        }
        $two_step_status = $user->getTwoStepStatus();
        /**
         * 通过记住我登录，需要进行二次验证，并且设备不可信
         */
        if ($two_step_status === User::TWO_STEP_STATUS_OK && $guard == 'web') {
            /**
             * @var SessionGuard $session_guard
             */
            $session_guard = $this->auth->guard();
            if ($session_guard->viaRemember() && (!$client_device || !$client_device->isCredit())) {
                throw new TwoStepNeededException;
            }
        } else if ($two_step_status === User::TWO_STEP_STATUS_NEED && $force_two_step_redirect) {
            /**
             * 强制二次验证，需要做统一跳转
             * login_settings 等页面的 $force_two_step_redirect 参数为 false
             */
            if ($client_device) {
                $client_device->logout();
            }
            throw new TwoStepForcedException;
        }
    }

    /**
     * Determine if the user is logged in to any of the given guards.
     *
     * @param  array $guards
     * @return void
     *6
     * @throws \Illuminate\Auth\AuthenticationException
     * @throws RedisAuthenticationException
     * @throws WebAuthenticationException
     */
    protected function authenticate(array $guards)
    {
        if (empty($guards)) {
            return $this->auth->authenticate();
        }

        foreach ($guards as $guard) {
            if ($this->auth->guard($guard)->check()) {
                return $this->auth->shouldUse($guard);
            }
        }

        if (in_array('redis', $guards)) {
            throw new RedisAuthenticationException('Unauthenticated.', $guards);
        }

        if (in_array('web', $guards)) {
            throw new WebAuthenticationException('Unauthenticated' , $guards);
        }

        throw new AuthenticationException('Unauthenticated.', $guards);
    }
}
