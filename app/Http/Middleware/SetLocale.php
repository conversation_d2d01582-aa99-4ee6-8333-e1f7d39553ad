<?php

namespace App\Http\Middleware;

use App\Util\CommonHelper;
use App\Util\Context;
use App\Util\HttpHelper;
use Closure;
use Fangcloud\IP\IP;
use Illuminate\Support\Facades\Auth;

class SetLocale
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @param  string|null  $guard
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        $client_info = $request->header('egeio-client-info') ?: $request->get('egeio-client-info') ?: $request->cookie('egeio-client-info');

        if($request->cookie('api_key')) {
            HttpHelper::setClientLanguageEnabled();
        }

        if($client_info)
        {
            // 主要负责客户端web view中的多语言。mobile上首先使用cookie中的egeio-client-info,
            $client_info_str = $client_info;
            $client_info = json_decode($client_info, true);

            HttpHelper::setClientInfo($client_info_str);

            if(isset($client_info['language']) && in_array($client_info['language'], Context::$supported_languages))
            {
                Context::setLanguage($client_info['language']);
                HttpHelper::setLanguage($client_info['language']);
                HttpHelper::setClientLanguageEnabled();

                return $next($request);
            }
        }

        if(in_array(($lang = $request->get('lang')), Context::$supported_languages))
        {
            Context::setLanguage($lang);
            HttpHelper::setLanguage($lang);

            return $next($request);
        }

        $client_lang_first = Httphelper::isClientLanguageEnabled();
        $user_lang = null;
        if($user = Auth::user()) {
            $user_lang = $user->getLang(false);
        }
        $client_lang = HttpHelper::getLanguage();
        if($client_lang_first) {
            $lang = $client_lang ?: $user_lang;
        }
        else {
            $lang = $user_lang ?: $client_lang;
        }
        if(in_array($lang, Context::$supported_languages))
        {
            Context::setLanguage($lang);
            HttpHelper::setLanguage($lang);

            return $next($request);
        }

        $ip_location = IP::find(CommonHelper::getClientIp());

        if($ip_location['region'] == '中国' || $ip_location['region'] == 'unknown')
        {
            Context::setLanguage('zh-CN');
        }
        elseif ($ip_location['region'] == '台湾' || $ip_location['region'] == '香港' || $ip_location['region'] == '澳门')
        {
            Context::setLanguage('zh-TW');
        }
        else
        {
            Context::setLanguage('en');
        }

        return $next($request);
    }
}
