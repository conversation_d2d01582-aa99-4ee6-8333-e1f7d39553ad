<?php

namespace App\Http\Middleware;

use App\Constants\Http;
use App\Library\HttpClient\Guard;
use App\Library\HttpClient\GuardException;
use App\Util\LogHelper;
use App\Util\LogType;
use Closure;

class BaseGuard
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request $request
     * @param  \Closure $next
     * @param null $arg
     * @return mixed
     * @throws GuardException
     */
    public function handle($request, Closure $next, $arg=null)
    {
        $server_id = config('services.internal_service_id');
        $client_id = $request->header(Http::HEADER_INTERNAL_SERVICEID);

        $guard = new Guard($server_id);

        if ($arg != null) {
            if ($client_id && $client_id != config('services.internal_services.' . $arg)) {
                throw new GuardException('invalid internal_services_id with ' . $arg);
            }
        }

        $guard->verifyClient($client_id, [
            'timestamp' => $request->header(Http::HEADER_INTERNAL_TIMESTAMP),
            'version' => $request->header(Http::HEADER_INTERNAL_VERSION),
            'token' => $request->header(Http::HEADER_INTERNAL_TOKEN),
        ]);

        return $next($request);
    }
}
