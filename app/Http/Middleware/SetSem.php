<?php

namespace App\Http\Middleware;

use App\Util\HttpHelper;
use Closure;
use Illuminate\Support\Facades\Auth;

class SetSem
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @param  string|null  $guard
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        if(!Auth::user()) {
            $from = $request->get('from');
            $keyword = $request->get('keyword');
            $medium = $request->get('medium');
            $campaign = $request->get('campaign');
            $content = $request->get('content');

            if (isset($from, $keyword))
            {
                $cookie_value = json_encode(
                    [
                        'from' => $from,
                        'keyword' => $keyword,
                        'medium' => $medium,
                        'campaign' => $campaign,
                        'content' => $content,
                    ]
                );
                HttpHelper::setSem($cookie_value);
            }
        }

        return $next($request);
    }
}
