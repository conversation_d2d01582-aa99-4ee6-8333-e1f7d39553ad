<?php

namespace App\Http\Middleware;

use App\Util\CacheHelper;
use Closure;

class OAuthState
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        $state = $request->get('state');
        if ($state) {
            $context = CacheHelper::getOAuthState($state);
            $fstate = $context['_fstate'] ?: null;
            if ($fstate) {
                $request->query->add(['_fstate' => $fstate]);
            }
        }
        return $next($request);
    }
}
