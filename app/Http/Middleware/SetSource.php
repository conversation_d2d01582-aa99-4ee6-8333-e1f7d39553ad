<?php

namespace App\Http\Middleware;

use App\Util\Context;
use App\Util\HttpHelper;
use Closure;

class SetSource
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @param  string|null  $guard
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        $source = $request->get('source')?: $request->cookie('source');
        if($source) {
            HttpHelper::setSource($source);
            Context::setSource($source);
        }

        return $next($request);
    }
}
