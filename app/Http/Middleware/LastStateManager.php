<?php

namespace App\Http\Middleware;

use App\Util\Context;
use App\Util\HttpHelper;
use App\Util\LogHelper;
use App\Util\LogType;
use Closure;
use Illuminate\Http\RedirectResponse;

class LastStateManager
{
    /**
     * 1. 自动将get参数放入state
     * 2. 如果有新的值传入，更新 缓存的值
     * 3. 生成一个state
     *
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        $response = $next($request);

        // 正常response 不需要返回 state
        // view 返回的页面使用 ViewHelper::fstate_field()
        // 302 请求带在 返回的 location 上
        if ($response instanceof RedirectResponse) {
            $url = $response->getTargetUrl();
            $new_url = HttpHelper::addQuery($url, '_fstate', Context::getFState());
            foreach ($response->getRequest()->query() as $key => $value)
            {
                if($key === '_fstate')
                {
                    continue;
                }
                $new_url = HttpHelper::addQuery($new_url, $key, $value);
            }
            $response->setTargetUrl($new_url);
        }

        return $response;
    }
}
