<?php

namespace App\Listeners;
use App\Constants\Plan;
use App\Events\EnterpriseCreated;
use App\Jobs\CrmImport;
use App\Remote\Enterprise;
use App\Util\CommonHelper;
use App\Util\UserAgent;

class EnterpriseCreatedListener
{
    public function handle(EnterpriseCreated $enterprise_created)
    {
        $enterprise = $enterprise_created->getEnterprise();
        $additional_create_info = $enterprise_created->getAdditionalCreateInfo();
        //台塑网的就不管了
        if($enterprise_created->getSource() && $enterprise_created->getSource() != 'efpg') {
            try{
                Enterprise::registerEnterpriseSource($enterprise->id, $enterprise_created->getSource());
            }
            catch (\Exception $e) {
            }
        } else {
            $user = $enterprise_created->getAdminUser();
            $sem_value = request()->cookie('sem');
            $parsed_sem_value = @json_decode($sem_value, true);
            $from = $parsed_sem_value['from'] ?? '';
            $keyword = $parsed_sem_value['keyword'] ?? '';
            $medium = $parsed_sem_value['medium'] ?? '';
            $campaign = $parsed_sem_value['campaign'] ?? '';
            $content = $parsed_sem_value['content'] ?? '';
            $register_origin = request()->cookie(config('app.register_origin_cookie_name'))?? '';
            if($from || $keyword){
                try{
                    Enterprise::recordSem($user->id, $from, $keyword, $medium, $campaign, $content, CommonHelper::getClientIp());
                }
                catch (\Exception $e) {
                }
            }
            if(!config('app.skip_crm_import')) {
                $user->getInfo(['name', 'email', 'phone', 'ip_address']);
                $enterprise->getInfo();
                $user_agent = new UserAgent();

                $user_data['enterprise_id'] = (string)$enterprise->id;
                $user_data['enterprise_name'] = $enterprise->name;
                $user_data['admin_user_ip_address'] = CommonHelper::getClientIp();
                $user_data['admin_user_name'] = $user->name;
                $user_data['admin_user_phone'] = $user->phone;
                $user_data['admin_user_email'] = $additional_create_info['email'] ?? '';
                $user_data['plan_type'] = Plan::$plan_descs[$enterprise->plan_id];
                $user_data['plan_id'] = (string) $enterprise->plan_id;
                $user_data['register_date'] = (string) $enterprise->created_at;
                $user_data['expire_date'] = (string) $enterprise->expires_at;
                $user_data['service_id'] = $user_agent->getServiceId();
                $user_data['sem'] = [
                    'from' => $from,
                    'keyword' => $keyword,
                    'medium' => $medium,
                    'campaign' => $campaign,
                    'content' => $content,
                    'register_origin' => $register_origin
                ];
                //台塑网
                if ($enterprise_created->getSource() == 'efpg') {
                    $user_data['sem'] = [
                        'from' => '台塑网',
                        'keyword' => '',
                    ];
                }

                $user_data['is_from_wap'] = !$user_agent->getServiceId() && $user_agent->isMobile();
                if (isset($additional_create_info['ali_biz_id'])) {
                    $user_data['ali_biz_id'] = $additional_create_info['ali_biz_id'];
                }
                if (isset($additional_create_info['ali_order_id'])) {
                    $user_data['ali_order_id'] = $additional_create_info['ali_order_id'];
                }
                if (isset($additional_create_info['motivation'])) {
                    $user_data['motivation'] = $additional_create_info['motivation'];
                }
                if (isset($additional_create_info['gray_test']) && $additional_create_info['gray_test']) {
                    $user_data['external_source'] = ['gray_test' => true];
                }
                if (isset($additional_create_info['invite_code'])) {
                    $user_data['invite_code'] = $additional_create_info['invite_code'];
                }
                if (isset($additional_create_info['activity_code'])) {
                    $user_data['activity_code'] = $additional_create_info['activity_code'];
                }
                if (isset($additional_create_info['from_page'])) {
                    $user_data['from_page'] = $additional_create_info['from_page'];
                }
                if (isset($additional_create_info['qhclick_msg'])) {
                    $user_data['qhclick_msg'] = $additional_create_info['qhclick_msg'];
                }
                if (isset($additional_create_info['jlclick_msg'])) {
                    $user_data['jlclick_msg'] = $additional_create_info['jlclick_msg'];
                }
                if (isset($additional_create_info['bdclick_msg'])) {
                    $user_data['bdclick_msg'] = $additional_create_info['bdclick_msg'];
                }

                //来源,目前就分享页面来源
                $market_value = request()->cookie('marketing_referrer');
                $parsed_market_value = @json_decode($market_value, true);
                if (!empty($parsed_market_value['from'])) {
                    $user_data['marketing'] = [
                        'referrer' => $parsed_market_value['from']
                    ];
                }

                // 需要存储的信息都放在additional_info中
                $additional_info = [
                    'user_data' => json_encode($user_data), // user_data以json的格式存储
                    'sales_crm_type' => 1,
                ];
                // 回调地址
                $job_specs['callback'] = '/internal_api/jobs/sales_crm_import_job';
                $job_specs['callback_service_id'] = 1;
                // 新的xman回调需要将所有的参数放在additional_infos中，支持批量job
                $job_specs['additional_infos'][] = $additional_info;
                $job_specs['job_count'] = count($job_specs['additional_infos']);

                $crm_import_job = new CrmImport();
                $crm_import_job->setBody($job_specs);
                $crm_import_job->send();
            }
        }
        if (isset($additional_create_info['motivation'])) {
            $enterprise->getInfo();
            Enterprise::motivationCollect($enterprise->id, $enterprise->plan_id, $additional_create_info['motivation']);
        }
    }
}