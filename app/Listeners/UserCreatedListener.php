<?php

namespace App\Listeners;
use App\Events\UserCreated;
use App\Jobs\CrmImport;
use App\Remote\User as RemoteUser;

class UserCreatedListener
{
    public function handle(UserCreated $user_created)
    {
        $additional_info = [
            'user_id' => $user_created->getUser()->user_id
        ];
        // 回调地址
        $job_specs['callback'] = '/internal_api/jobs/deal_with_user_created_job';
        $job_specs['callback_service_id'] = 1;
        // 新的xman回调需要将所有的参数放在additional_infos中，支持批量job
        $job_specs['additional_infos'][] = $additional_info;
        $job_specs['job_count'] = count($job_specs['additional_infos']);

        $crm_import_job = new CrmImport();
        $crm_import_job->setBody($job_specs);
        $crm_import_job->send();
//        RemoteUser::dealWithUserCreated($user_created->getUser()->user_id);
    }
}