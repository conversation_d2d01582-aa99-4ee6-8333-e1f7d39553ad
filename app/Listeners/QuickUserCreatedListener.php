<?php

namespace App\Listeners;
use App\Events\QuickUserRegistered;
use App\Remote\User as RemoteUser;
use App\Util\Sms;

class QuickUserCreatedListener
{
    public function handle(QuickUserRegistered $userCreated)
    {
        $user = $userCreated->getUser();
        if ($user) {
//            $user->setPasswordRequireInitAttr();
//            $user->save();
            RemoteUser::dealWithUserCreated($user->user_id);
            Sms::sendRegister($userCreated->getPhone(), $userCreated->getPass(), config('app.locale'));
        }
    }
}