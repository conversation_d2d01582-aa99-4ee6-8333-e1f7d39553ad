<?php

namespace App\Constants;
class SessionCacheKey
{
    const FORGOT_PASSWORD_INFO = 'forgot_password_info';
    const ACTIVATION_INFO = 'activation_info';
    const TWO_STEP_INFO = 'two_step_info';
    const APP_TWO_STEP_INFO = 'app_two_step_info_user_id_';

    /**
     * 这个状态存在10min。表示用户最近通过了身份验证
     *
     * == verified
     */
    const IDENTITY_VERIFIED = 'identity_verified';

    const FORCE_TWO_STEP = 'force_two_step';

    /**
     * 在登录之后会将该值设置为 true。之后的 auth middleware 不需要再次进行二次验证的相关判断
     */
    const TWO_STEP_AUTHED = 'two_step_authed';


}