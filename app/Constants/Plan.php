<?php

namespace App\Constants;

class Plan
{
    const PLAN_TYPE_PERSONAL = 0; // not a real plan type
    const PLAN_TYPE_EXPERIENCE = 1;
    const PLAN_TYPE_TEAM = 2;
    const PLAN_TYPE_ENTERPRISE = 3;
    const PLAN_TYPE_TITA = 4;
    const PLAN_TYPE_HYBRID_STORAGE = 8;
    const PLAN_TYPE_STARTER = 9;
    const PLAN_TYPE_ALI_MARKET_TEAM = 10;
    const PLAN_TYPE_CER = 11;
    const PLAN_TYPE_CHANNEL_TEST = 12;
    const PLAN_TYPE_FREE = 13;
    const PLAN_TYPE_STARTER_2018 = 14;
    const PLAN_TYPE_BASIC = 15;
    const PLAN_TYPE_PROFESSIONAL = 16;
    const PLAN_TYPE_INTERNATIONAL_PROFESSIONAL = 17;
    const PLAN_TYPE_SAAS_BASIC = 18;
    const PLAN_TYPE_SAAS_PROFESSIONAL = 19;
    const PLAN_TYPE_SAAS_INTERNATIONAL_PROFESSIONAL = 20;
    const PLAN_TYPE_OSS_BASIC = 21;
    const PLAN_TYPE_OSS_PROFESSIONAL = 22;
    const PLAN_TYPE_FREE_2017 = 23;
    const PLAN_TYPE_PREMIUM_2020 = 24;
    const PLAN_TYPE_FREE_2020 = 25;
    const PLAN_TYPE_ULTIMATE_2020 = 26;
    const PLAN_TYPE_STARTER_2020 = 27;
    const PLAN_TYPE_FREE_2023 = 28;
    const PLAN_TYPE_LITE = 29;

    const PLAN_TYPE_5T_INTELLIGENT_EDITION = 30;

    const PLAN_TYPE_20T_INTELLIGENT_EDITION = 31;

    const PLAN_TYPE_AI_5T_INTELLIGENT_EDITION = 32;

    const PLAN_TYPE_AI_20T_INTELLIGENT_EDITION = 33;

    const PLAN_TYPE_STARTER_2024 = 34;

    const PLAN_TYPE_CUSTOM_2020 = "custom";

    const PLAN_TYPE_PROFESSIONAL_INI_SEAT_LIMIT = 100;//专业版本注册的初始化人数

    public static $plan_descs = [
        self::PLAN_TYPE_PERSONAL => '个人版',
        self::PLAN_TYPE_EXPERIENCE => '体验版',
        self::PLAN_TYPE_TEAM => '团队版',
        self::PLAN_TYPE_ENTERPRISE => '企业版',
        self::PLAN_TYPE_TITA => 'TITA版',
        self::PLAN_TYPE_HYBRID_STORAGE => '混合云版',
        self::PLAN_TYPE_STARTER => '入门版',
        self::PLAN_TYPE_ALI_MARKET_TEAM => '阿里云市场版',
        self::PLAN_TYPE_CER => '赛尔合作版',
        self::PLAN_TYPE_CHANNEL_TEST => '渠道测试版',
        self::PLAN_TYPE_FREE => '免费版',
        self::PLAN_TYPE_STARTER_2018 => '入门版',
        self::PLAN_TYPE_BASIC => '基础版',
        self::PLAN_TYPE_PROFESSIONAL => '专业版',
        self::PLAN_TYPE_INTERNATIONAL_PROFESSIONAL => '国际专业版',
        self::PLAN_TYPE_SAAS_BASIC => 'SaaS基础版',
        self::PLAN_TYPE_SAAS_PROFESSIONAL => 'SaaS专业版',
        self::PLAN_TYPE_SAAS_INTERNATIONAL_PROFESSIONAL => 'SaaS国际专业版',
        self::PLAN_TYPE_OSS_BASIC => 'OSS基础版',
        self::PLAN_TYPE_OSS_PROFESSIONAL => 'OSS专业版',
        self::PLAN_TYPE_FREE_2017 => '免费版',
        self::PLAN_TYPE_PREMIUM_2020 => '高级版',
        self::PLAN_TYPE_FREE_2020 => '免费版',
        self::PLAN_TYPE_ULTIMATE_2020 => '旗舰版',
        self::PLAN_TYPE_STARTER_2020 => '入门版',
        self::PLAN_TYPE_CUSTOM_2020 => '定制版',
        self::PLAN_TYPE_FREE_2023 => '免费版',
        self::PLAN_TYPE_LITE => '小微版',
        self::PLAN_TYPE_5T_INTELLIGENT_EDITION => '5T智能版',
        self::PLAN_TYPE_20T_INTELLIGENT_EDITION => '20T智能版',
        self::PLAN_TYPE_AI_5T_INTELLIGENT_EDITION => '360AI网盘5T',
        self::PLAN_TYPE_AI_20T_INTELLIGENT_EDITION => '360AI网盘20T',
        self::PLAN_TYPE_STARTER_2024 => '入门版（10人）',
    ];

    public static $enterprise_plans = [
        self::PLAN_TYPE_ENTERPRISE,
        self::PLAN_TYPE_PROFESSIONAL,
        self::PLAN_TYPE_INTERNATIONAL_PROFESSIONAL,
        self::PLAN_TYPE_SAAS_PROFESSIONAL,
        self::PLAN_TYPE_SAAS_INTERNATIONAL_PROFESSIONAL,
        self::PLAN_TYPE_OSS_PROFESSIONAL,
        self::PLAN_TYPE_ULTIMATE_2020,
        self::PLAN_TYPE_5T_INTELLIGENT_EDITION,
        self::PLAN_TYPE_20T_INTELLIGENT_EDITION,
        self::PLAN_TYPE_AI_5T_INTELLIGENT_EDITION,
        self::PLAN_TYPE_AI_20T_INTELLIGENT_EDITION,
    ];

    public static $experience_plans = [
        self::PLAN_TYPE_EXPERIENCE,
        self::PLAN_TYPE_FREE,
        self::PLAN_TYPE_FREE_2017,
        self::PLAN_TYPE_FREE_2020
    ];

    public static $ali_market_plans = [
        self::PLAN_TYPE_ALI_MARKET_TEAM,
        self::PLAN_TYPE_SAAS_BASIC,
        self::PLAN_TYPE_SAAS_PROFESSIONAL,
        self::PLAN_TYPE_SAAS_INTERNATIONAL_PROFESSIONAL,
        self::PLAN_TYPE_OSS_PROFESSIONAL,
        self::PLAN_TYPE_OSS_BASIC
    ];

    public static $team_plans = [
        self::PLAN_TYPE_TEAM,
        self::PLAN_TYPE_ALI_MARKET_TEAM,
        self::PLAN_TYPE_BASIC,
        self::PLAN_TYPE_SAAS_BASIC,
        self::PLAN_TYPE_OSS_BASIC,
        self::PLAN_TYPE_PREMIUM_2020
    ];

    public static $starter_plans = [
        self::PLAN_TYPE_STARTER,
        self::PLAN_TYPE_STARTER_2018,
        self::PLAN_TYPE_STARTER_2020,
        self::PLAN_TYPE_STARTER_2024,
    ];
}