<?php

namespace App\Constants;
class ErrorCode
{
    const CSRF_TOKEN_INVALID = 9000001;
    const PERMISSION_DENIED = 9000002;
    const AUTHENTICATE_EXCEPTION = 9000003;
    const REMOTE_SERVICE_EXCEPTION = 9000004;
    const THIRD_EXCEPTION = 9000005;
    const API_EXCEPTION = 9000006;
    const INTERNAL_EXCEPTION = 9000007;
    const GUARD_EXCEPTION = 9000008;
    const DEFAULT_EXCEPTION = 9000009;

    const ENTERPRISE_NAME_TOO_LONG = 9100001;
    const USER_NAME_TOO_LONG = 9100002;
    const INVALID_EMAIL = 9100011;
    const INVALID_PHONE = 9100012;
    const INVALID_IDENTIFIER = 9100013;
    const INVALID_PASSWORD = 9100014;
    const PASSWORD_NOT_MATCH_ENTERPRISE_SETTING = 9100015;
    const INVALID_TEXT = 9100019;
    const INVALID_ENTERPRISE_NAME = 9100020;
    const INVALID_USER_NAME = 9100021;

    const CLIENT_VERSION_LOWER_THAN_MIN_ENCRYPTION_LIMIT = 9200001;
    const ENCRYPTION_USER_LOGIN_INVALID = 9200002;
    const DLP_CLIENT_COUNT_EXCEED = 9200003;

    const ACCOUNT_NOT_FOUND = 9001001;
    const V1_LOGIN_NOT_AllOWED = 9001002;
    const V1_ACTIVATE_NOT_AllOWED = 9001003;
    const V1_LOGIN_NOT_AllOWED_FOR_THIRD = 9001004;

    const LOGIN_PASSWORD_INVALID = 1000000;
    const SMS_CAPTCHA_INVALID = 1000001;
    const PIC_CAPTCHA_INVALID = 1000002;
    const EMAIL_CAPTCHA_INVALID = 1000003;
    const BINDED_THIRD_ACCOUNT = 1000004;
    const EMAIL_EXCEED_LIMIT = 1000005;
    const PASSWORD_ERROR_FROZEN = 1000006;
    const LOGIN_PASSWORD_INVALID_BY_TIMES = 1000007;
    const YIDUN_CHECK_INVALID = 1000008;
    const QUICK_LOGIN_TYPE_INVALID = 1000009;
    const UPDATE_EMAIL_EXCEED_LIMIT = 1000010;
    const REGISTER_EXCEED_LIMIT = 1000011;
    const USER_NOT_IN_LOCAL_ENTERPRISE = 1000012;
    const PIC_CAPTCHA_REQUIRED = 1000013;
    const QUICK_LOGIN_INVALID = 1000014;

    const IDENTIFIER_ALREADY_OCCUPIED = 1100000;
    const PHONE_ALREADY_OCCUPIED = 1100001;
    const EMAIL_ALREADY_OCCUPIED = 1100002;
    const PHONE_ALREADY_OCCUPIED_WHEN_VERIFY = 1100003;
    const EMAIL_ALREADY_OCCUPIED_WHEN_VERIFY = 1100004;
    const PHONE_UNBIND_WHEN_UPDATE_EMAIL = 1100005;
    const PHONE_UNBIND_WHEN_UNBIND_EMAIL = 1100006;
    const PHONE_ALREADY_OCCUPIED_WHEN_REGISTER = 1100007;
    const PHONE_ALREADY_OCCUPIED_BY_PERSONAL_USER_WHEN_REGISTER = 1100008;
    const PHONE_NOT_CHANGED = 1100010;
    const EMAIL_NOT_CHANGED = 1100011;
    const PHONE_ALREADY_OCCUPIED_BUT_NOT_ACTIVATED = 1100012;
    const PHONE_ALREADY_OCCUPIED_ACTIVATION_EXPIRED = 1100013;
    const PLAN_REGISTER_COUNT_EXCEED_LIMIT = 1100014;

    const CHANNEL_COUPON_TICKET_ALREADY_USED = 1100015;
    const CHANNEL_COUPON_TICKET_ALREADY_EXPIRED = 1100016;
    const CHANNEL_COUPON_TICKET_ID_NOT_EXIST = 1100017;

    const LOGIN_UNVERIFIED = 1200000;
    const CREDENTIAL_EMAIL_UNVERIFIED = 1200001;
    const CREDENTIAL_PHONE_UNVERIFIED = 1200002;

    const CREDENTIAL_VERIFICATION_CODE_INVALID = 1300000;
    const CREDENTIAL_NOT_FOUND = 1300001;
    const CREDENTIAL_NOT_NEED_VALIDATED = 1300002;

    const FORGOT_PASSWORD_INFO_EXPIRED = 1400000;
    const FORGOT_PASSWORD_CODE_INVALID = 1400003;

    const PASSWORD_NOT_CONFIRMED = 1500000;
    const PASSWORD_INCORRECT = 1500001;
    const PASSWORD_VERIFY_STATUS_EXPIRED = 1500002;
    const OLD_PASSWORD_INCORRECT = 1500003;
    const PASSWORD_ALREADY_SET = 1500004;
    const PASSWORD_CHECK_FAILED = 1500005;

    const ACTIVATION_CODE_INVALID = 1600000;
    const ACTIVATION_INFO_EXPIRED = 1600001;
    const CREDENTIAL_TO_BE_ACTIVATED_NOT_FOUND = 1600002;
    const CREDENTIAL_ALREADY_ACTIVATED = 1600003;

    const QR_TOKEN_INVALID = 1700000;

    const USER_HAS_BEEN_FROZEN = 1800000;
    const ENTERPRISE_HAS_BEEN_FROZEN = 1800001;
    const ENTERPRISE_HAS_BEEN_EXPIRED = 1800002;
    const PUBLIC_LOGIN_ENABLED_SSO_ENTERPRISES_ONLY = 1800003;
    const ENTERPRISE_HAS_BEEN_EXPIRED_ADMIN = 1800004;
    const ENTERPRISE_HAS_BEEN_FORBIDDEN_LOGIN = 1800005;
    const USER_LOGIN_HAS_BEEN_DISABLED = 1800006;
    const USER_LOGIN_HAS_BEEN_WRITTEN_OFF = 1800007;
    const ENTERPRISE_HAS_BEEN_EXPIRED_NORMAL_USER = 1800008;

    const OAUTH_INVALID_STATE = 2000000;

    const TWO_STEP_WECHAT_FREEZE = 3000000;
    const TWO_STEP_SMS_LIMIT = 3000001;
    const TWO_STEP_VERIFY_FAILED = 3000002;
    const EMPTY_DEVICE_TOKEN = 3000003;
    const TWO_STEP_FORCE_TWO_STEP = 3000004;
    const IDENTITY_NEED_TWO_STEP_VERIFY = 3000005;
    const TWO_STEP_CANNOT_CLOSE = 300006;
    const ALREADY_BIND_DINGTALK_ACCOUNT = 3000007;
    const ALREADY_BIND_WECHAT_ACCOUNT = 3000008;
    const LOGIN_DENIED_FOR_EMPTY_ENTERPRISE = 3000009;
    const ALREADY_BIND_360_ACCOUNT = 3000010;
    const IDENTITY_NEED_FORCE_TWO_STEP = 3000011;

    const SECURITY_LOGIN_EMPTY_SN_CODE = 2001001;
    const SECURITY_LOGIN_FORBIDDEN_IP = 2001002;
    const SECURITY_LOGIN_FORBIDDEN_WEB = 2001003;
    const SECURITY_LOGIN_FORBIDDEN_SYNC = 2001004;
    const SECURITY_LOGIN_FORBIDDEN_MOBILE = 2001005;

    const FORGOT_PASSWORD_BY_PHONE_SIGN_ERROR = 4000000;

    const TWO_STEP_VERITY_SIGN_ERROR = 4000001;

    const SIGNATURE_VERITY_ERROR = 4000003;

    const UPDATE_PHONE_EXCEED_LIMIT = 4000004;



}