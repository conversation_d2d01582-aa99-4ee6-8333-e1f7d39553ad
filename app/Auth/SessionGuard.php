<?php

namespace App\Auth;

use App\Constants\Http;
use App\Remote\AuthToken as RemoteAuthToken;
use App\Util\UserAgent;
use Illuminate\Contracts\Auth\Authenticatable as AuthenticatableContract;
use Illuminate\Auth\SessionGuard as BaseSessionGuard;

class SessionGuard extends BaseSessionGuard
{

    /**
     * Get the currently authenticated user.
     *
     * @return \Illuminate\Contracts\Auth\Authenticatable|null
     */
    public function user()
    {
        $ua = new UserAgent();
        if ($ua->isSameSiteNoneIncompatible()) {
            config(['session.same_site' => null]);
        }
        if ($this->loggedOut) {
            return;
        }

        // If we've already retrieved the user for the current request we can just
        // return it back immediately. We do not want to fetch the user data on
        // every call to this method because that would be tremendously slow.
        if (! is_null($this->user)) {
            return $this->user;
        }

        $user_id = $this->session->get(config('session.user_id_session_name'));

        // First we will try to load the user using the identifier in the session if
        // one exists. Otherwise we will check for a "remember me" cookie in this
        // request, and if one exists, attempt to retrieve the user using that.
        if (! is_null($user_id)) {
            if ($this->user = $this->provider->retrieveByUserId($user_id)) {
                $this->fireAuthenticatedEvent($this->user);
            }
        }

        // If the user is null, but we decrypt a "recaller" cookie we can attempt to
        // pull the user data on that cookie which serves as a remember cookie on
        // the application. Once we have a user we can return it to the caller.
        $recaller = $this->recaller();

        if (is_null($this->user) && ! is_null($recaller)) {
            $this->user = $this->userFromRecaller($recaller);

            if ($this->user) {
                $this->updateSession($this->user->getAuthIdentifier());
                $this->session->put(config('session.user_id_session_name'), $this->user->user_id);

                $this->fireLoginEvent($this->user, true);
            }
        }

        /**
         * AuthToken 换 cookie 的情况
         */
        if (is_null($this->user)) {
            /**
             * @var \Illuminate\Http\Request $request
             */
            $request = $this->request;
            $auth_token = $request->header(trim(Http::HEADER_AUTH_TOKEN));
            if($auth_token) {
                $auth_token_obj = RemoteAuthToken::get($auth_token);
//                $auth_token_obj = AuthToken::findByAuthTokenGently($auth_token);
                if($auth_token_obj && $auth_token_obj->isValid()) {
                    $this->user = $auth_token_obj->user();
                    $this->fireAuthenticatedEvent($this->user);
                    $this->updateSession($this->user->getAuthIdentifier());
                    $this->session->put(config('session.user_id_session_name'), $this->user->user_id);
                }
            }
        }

        return $this->user;
    }

    /**
     * Log a user into the application.
     *
     * @param  \Illuminate\Contracts\Auth\Authenticatable  $user
     * @param  bool  $remember
     * @return void
     */
    public function login(AuthenticatableContract $user, $remember = false)
    {
        $ua = new UserAgent();
        if ($ua->isSameSiteNoneIncompatible()) {
            config(['session.same_site' => null]);
        }
        $this->updateSession($user->getAuthIdentifier());
        $this->session->put(config('session.user_id_session_name'), $user->user_id);

        // If the user should be permanently "remembered" by the application we will
        // queue a permanent cookie that contains the encrypted copy of the user
        // identifier. We will then decrypt this later to retrieve the users.
        if ($remember) {
            $this->ensureRememberTokenIsSet($user);

            $this->queueRecallerCookie($user);
        }

        // If we have an event dispatcher instance set we will fire an event so that
        // any listeners will hook into the authentication events and run actions
        // based on the login and logout events fired from the guard instances.
        $this->fireLoginEvent($user, $remember);

        $this->setUser($user);
    }

    protected function updateSession($id)
    {
        $this->session->migrate(true);
    }

    protected function clearUserDataFromStorage()
    {
        $this->session->remove($this->getName());
        $this->session->remove(config('session.user_id_session_name'));

        if (! is_null($this->recaller())) {
            $this->getCookieJar()->queue($this->getCookieJar()
                ->forget($this->getRecallerName()));
        }
    }
}
