<?php

namespace App\Auth;

use App\Http\Controllers\LoginTrait;
use App\Models\User;
use App\Util\Context;
use Illuminate\Auth\GuardHelpers;
use Illuminate\Contracts\Auth\Guard;
use Illuminate\Contracts\Auth\UserProvider;
use Illuminate\Http\Request;
use Illuminate\Support\Traits\Macroable;

class RedisGuard implements Guard
{
    use GuardHelpers, Macroable;

    use LoginTrait;

    protected $name;

    protected $provider;

    /**
     * @var \Illuminate\Http\Request $request
     */
    protected $request;

    protected $via_two_step = false;

    public function __construct($name, UserProvider $provider)
    {
        $this->name = $name;
        $this->provider = $provider;
    }

    public function user()
    {
        if ($this->user !== null) {
            return $this->user;
        }

        // 如果有其他通过缓存拿到登录信息的可以使用这个 guard

        // 二次验证 case
        $state = Context::getFStateContent();
        if ($state
            && isset($state['two_step']['status'])
            && $state['two_step']['status']
            && $user_id = $state['two_step']['user_id']
        ) {
            $this->user = User::findByMainUserId($user_id);
        }

        return $this->user;
    }

    public function loginViaTwoStep(User $user)
    {
        $state = Context::getFState();
        Context::setFState($state);
        Context::setFStateContent([
            'two_step' => [
                'status' => 1,
                'user_id' => $user->user_id,
            ]
        ], true);
        $this->user = $user;
        $this->via_two_step = true;
    }

    public function logout()
    {
        $this->user = null;
    }

    /**
     * Validate a user's credentials.
     *
     * @param  array $credentials
     * @return bool
     */
    public function validate(array $credentials = [])
    {
        return $this->user() !== null;
    }

    /**
     * Set the current request instance.
     *
     * @param \Illuminate\Http\Request  $request
     * @return $this
     */
    public function setRequest(Request $request)
    {
        $this->request = $request;

        return $this;
    }
}