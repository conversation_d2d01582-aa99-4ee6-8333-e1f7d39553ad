<?php

namespace App\Auth;

use App\Remote\AuthToken as RemoteAuthToken;
use Illuminate\Auth\GuardHelpers;
use Illuminate\Contracts\Auth\Guard;
use Illuminate\Contracts\Auth\UserProvider;
use Illuminate\Http\Request;
use Illuminate\Support\Traits\Macroable;

class AuthTokenGuard implements Guard
{
    use GuardHelpers, Macroable;

    protected $name;

    protected $provider;

    /**
     * @var \Illuminate\Http\Request $request
     */
    protected $request;

    protected $auth_token;

    public function __construct($name, UserProvider $provider)
    {
        $this->name = $name;
        $this->provider = $provider;
    }

    /**
     * Get the currently authenticated user.
     * @return \Illuminate\Contracts\Auth\Authenticatable|null
     */
    public function user()
    {
        if ($this->user !== null) {
            return $this->user;
        }

        $auth_token = $this->request->header('auth_token');
        $auth_token = RemoteAuthToken::get($auth_token);
//        $auth_token = AuthToken::findByAuthTokenGently($auth_token);

        if ($auth_token && $auth_token->isValid()) {
            $this->auth_token = $auth_token;
            $this->user = $auth_token->user();
        }

        return $this->user;
    }

    public function logout()
    {
        $this->user = null;
    }

    public function getAuthToken()
    {
        return $this->auth_token;
    }

    /**
     * Validate a user's credentials.
     *
     * @param  array $credentials
     * @return bool
     */
    public function validate(array $credentials = [])
    {
        return $this->user() !== null;
    }

    /**
     * Set the current request instance.
     *
     * @param \Illuminate\Http\Request  $request
     * @return $this
     */
    public function setRequest(Request $request)
    {
        $this->request = $request;

        return $this;
    }
}