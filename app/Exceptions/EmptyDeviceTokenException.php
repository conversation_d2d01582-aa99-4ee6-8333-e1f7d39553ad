<?php

namespace App\Exceptions;

use App\Constants\ErrorCode;
use Throwable;

class EmptyDeviceTokenException extends SimpleException
{
    public function __construct($message = '', Throwable $previous = null)
    {
        parent::__construct(ErrorCode::EMPTY_DEVICE_TOKEN, $message, $previous);
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function render($request)
    {
        if ($request->ajax() && $request->acceptsJson()) {
            return parent::render($request);
        } else {
            return view('auth.refresh_token', [
                'redirect' => $request->getUri(),
                'platform' => 'web',
                'js' => 'js/refresh_token.js'
            ]);
        }
    }
}