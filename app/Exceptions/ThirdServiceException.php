<?php

namespace App\Exceptions;

use App\Constants\ErrorCode;
use Throwable;

class ThirdServiceException extends SimpleException
{
    public function __construct(string $message = '', Throwable $previous = null)
    {
        parent::__construct(ErrorCode::THIRD_EXCEPTION, $message, $previous);
    }

    public static function createFrom(\Exception $exception)
    {
        return new self($exception->getMessage(), $exception->getPrevious());
    }
}