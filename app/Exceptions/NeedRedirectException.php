<?php

namespace App\Exceptions;

use Throwable;

/**
 * Class NeedRedirectException
 * @package App\Exceptions
 */
class NeedRedirectException extends ExternalException
{
    protected $redirect = null;
    public function __construct($redirect, Throwable $previous = null)
    {
        parent::__construct(0, '', [], '', $previous);
        $this->redirect = $redirect;
    }

    /**
     * @return null
     */
    public function getRedirect()
    {
        return $this->redirect;
    }
}