<?php

namespace App\Exceptions;

use App\Util\CommonHelper;

class V1AccountForgotPasswordException extends SimpleException
{
    private $token;

    public function __construct($token)
    {
        parent::__construct();
        $this->token = $token;
    }

    /**
     * @param $request
     * @return \Illuminate\Http\JsonResponse|\Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function render($request)
    {
        return CommonHelper::ajaxRedirect(config('app.v1_url') . 'auth/forgot_password_by_account_token?token=' . $this->token);
    }
}