<?php

namespace App\Exceptions;

use App\Constants\ErrorCode;
use App\Util\LogHelper;
use App\Util\LogType;
use Throwable;

/**
 * 用于 laravel 框架 report / render 的最基本的 exception
 * Class BaseException
 * @package App\Exceptions
 */
class BaseException extends \Exception
{
    public function __construct($error_code = 0, $message = '', Throwable $previous = null)
    {
        parent::__construct($message, $error_code, $previous);
    }

    public function render($request)
    {
        return response()->json($this->getError());
    }

    public function getErrorCode()
    {
        return $this->getCode();
    }

    public function getError()
    {
        return [
            'errors' => [
                'error_msg' => $this->getMessage(),
                'error_code' => ErrorCode::DEFAULT_EXCEPTION,
            ],
        ];
    }

    public function report()
    {
        LogHelper::info(LogType::Exception, $this->getMessage(), ['exception' => LogHelper::serializeException($this)]);
    }
}