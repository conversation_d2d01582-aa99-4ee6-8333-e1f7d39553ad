<?php

namespace App\Exceptions;

use Throwable;

class ExternalException extends BaseException
{
    protected $additional_info = [];
    protected $field = [];

    public function __construct($error_code = 0, $field = '', $additional_info = [], $message = '', Throwable $previous = null)
    {
        parent::__construct($error_code, $message, $previous);
        $this->additional_info = $additional_info;
        $this->field = $field;
    }

    public function getFormattedMessage()
    {
        $replace = isset($this->additional_info['replace']) ? $this->additional_info['replace'] : [];
        $locale = isset($this->additional_info['locale']) ? $this->additional_info['locale'] : null;

        $ret = [
            'error_code' => $this->getErrorCode(),
            'error_msg' => trans('errors.'.$this->getErrorCode(), $replace, $locale)
        ];
        if($this->field) {
            $ret['field'] = $this->field;
        }
        //临时处理
        if (isset($this->additional_info['download_link'])) {
            $ret['download_link'] = $this->additional_info['download_link'];
        }
        return $ret;
    }

    public function getError()
    {
        $e = $this;
        $errors = [];
        do {
            $errors[] = $this->getFormattedMessage();
        } while ($e = $e->getPrevious());
        return ['errors' => $errors];
    }

}