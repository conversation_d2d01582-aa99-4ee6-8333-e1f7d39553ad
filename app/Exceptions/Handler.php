<?php

namespace App\Exceptions;

use App\Library\HttpClient\GuardException;
use App\Library\OAuth\Dingtalk\DingtalkException;
use App\Library\OAuth\Wechat\WechatException;
use App\Util\CommonHelper;
use App\Util\Context;
use App\Util\LogHelper;
use App\Util\LogType;
use Exception;
use Throwable;
use Illuminate\Auth\AuthenticationException;
use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;

class Handler extends ExceptionHandler
{
    /**
     * A list of the exception types that are not reported.
     *
     * @var array
     */
    protected $dontReport = [
        //
    ];

    /**
     * A list of the inputs that are never flashed for validation exceptions.
     *
     * @var array
     */
    protected $dontFlash = [
        'password',
        'password_confirmation',
    ];

    /**
     * Report or log an exception.
     *
     * This is a great spot to send exceptions to Sentry, Bugsnag, etc.
     *
     * @param  \Exception $exception
     * @return mixed
     * @throws Exception
     */
    public function report(Exception $exception)
    {
        if (Context::isApiContext()
            || $exception instanceof GuardException
        ) {
            $this->doReport(ApiException::createFrom($exception));
        } else if ($exception instanceof WechatException
            || $exception instanceof DingtalkException) {
            $this->doReport(ThirdServiceException::createFrom($exception));
        } else {
            $this->doReport($exception);
        }
    }

    public function doReport(Exception $exception)
    {
        if ($this->shouldntReport($exception)) {
            return;
        }

        if (method_exists($exception, 'report')) {
            return $exception->report();
        }
        try{
            LogHelper::info(LogType::Exception, $exception->getMessage(), ['exception' => LogHelper::serializeException($exception)]);
        } catch (Exception $ex) {
            throw $exception; // throw the original exception
        } catch (Throwable $ex) {
            throw $exception; // throw the original exception
        }
    }

    /**
     * Render an exception into an HTTP response.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Exception  $exception
     * @return \Illuminate\Http\Response|\Symfony\Component\HttpFoundation\Response
     */
    public function render($request, Exception $exception)
    {
        if (Context::isApiContext()
            || $exception instanceof GuardException
        ) {
            return parent::render($request, ApiException::createFrom($exception));
        } else if ($exception instanceof WechatException
        || $exception instanceof DingtalkException) {
            return parent::render($request, ThirdServiceException::createFrom($exception));
        } else if ($exception instanceof \Illuminate\Validation\ValidationException) {
            return parent::render($request, new ValidationException($exception->errors()));
        } else {
            return parent::render($request, $exception);
        }
    }

    protected function unauthenticated($request, AuthenticationException $exception)
    {
        /** @var \Illuminate\Http\Request $request */
        return $request->ajax() && $request->expectsJson()
            ? response()->json(['redirect' => CommonHelper::getLoginUrl(), 'success' => false, '_status' => 'login_expired'])
            : redirect()->guest(CommonHelper::getLoginUrl( ['redirect' => $request->fullUrl()]));
    }
}
