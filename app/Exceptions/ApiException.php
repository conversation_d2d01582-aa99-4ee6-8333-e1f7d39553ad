<?php

namespace App\Exceptions;

use App\Util\LogHelper;
use App\Util\LogType;

/**
 * 一般不单独 new 该 exception
 *
 * 用于 construct from 其他 exception，然后使用同一的 api render 转成 api 所需要的 exception 格式
 *
 * Class ApiException
 * @package App\Exceptions
 */
class ApiException extends ExternalException
{
    public function __construct(\Exception $exception)
    {
        parent::__construct($exception->getCode(), $exception->getMessage(), $exception->getPrevious());
    }

    public static function createFrom(\Exception $exception)
    {
        LogHelper::error(LogType::RemoteCallException, $exception->getMessage(), ['exception' => LogHelper::serializeException($exception)]);
        return new self($exception);
    }

    public function render($request)
    {
        return response()->json($this->getError())->setStatusCode(400);
    }
}