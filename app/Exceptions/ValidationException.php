<?php

namespace App\Exceptions;

class ValidationException extends BaseException
{
    protected $additional_info = [];
    protected $errors = [];

    public function __construct(array $errors, $additional_info = [])
    {
        parent::__construct();
        $this->additional_info = $additional_info;
        $this->errors = $errors;
    }

    public function getError()
    {
        $errors = [];
        foreach ($this->errors as $field => $error_msgs)
        {
            $errors[] = [
                'field' => $field,
                'error_msg' => $error_msgs[0]
            ];
        }
        return ['errors' => $errors];
    }
}