<?php

namespace App\Exceptions;

use Throwable;

/**
 * 一般 error code 固定的 exception
 * 比如 permission denied / csrf token expired
 * Class SimpleException
 * @package App\Exceptions
 */
class SimpleException extends ExternalException
{
    public function __construct($error_code = 0, $message = '', Throwable $previous = null)
    {
        parent::__construct($error_code, '', [], $message, $previous);
    }
}