<?php

namespace App\Exceptions;

use App\Constants\ErrorCode;
use App\Util\CommonHelper;
use Illuminate\Support\Facades\Route;

class WebAuthenticationException extends BaseException
{
    /**
     * All of the guards that were checked.
     *
     * @var array
     */
    protected $guards;

    /**
     * Create a new authentication exception.
     *
     * @param  string  $message
     * @param  array  $guards
     * @return void
     */
    public function __construct($message = 'Unauthenticated.', array $guards = [])
    {
        parent::__construct(ErrorCode::AUTHENTICATE_EXCEPTION, $message);

        $this->guards = $guards;
    }

    /**
     * Get the guards that were checked.
     *
     * @return array
     */
    public function guards()
    {
        return $this->guards;
    }

    /**
     * redis guard 这种产生的异常渲染需要和默认的 app\Exception\Handler.php 中的 function unauthenticated 区分开
     *
     * 校验失败的时候不应该加上 redirect 地址
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse|\Illuminate\Http\RedirectResponse
     */
    public function render($request)
    {
        return $request->ajax() && $request->expectsJson()
            ? response()->json(['redirect' => CommonHelper::getLoginUrl(), 'success' => false, '_status' => 'login_expired'])
            : (Route::is('qr_login') ? redirect(config('app.h5_download_notice')) : redirect()->guest(CommonHelper::getLoginUrl(['redirect' => $request->fullUrl()])));
    }
}