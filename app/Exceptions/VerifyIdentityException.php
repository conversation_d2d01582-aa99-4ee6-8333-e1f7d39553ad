<?php

namespace App\Exceptions;

use App\Constants\ErrorCode;
use App\Library\TwoStep;
use App\Util\Context;
use Throwable;

class VerifyIdentityException extends ExternalException
{
    public function __construct()
    {
        parent::__construct(ErrorCode::PASSWORD_VERIFY_STATUS_EXPIRED);
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function render($request)
    {
        if ($request->ajax() && $request->acceptsJson()) {
            return parent::render($request);
        } else {
            return redirect(route('login_settings'));
        }
    }
}