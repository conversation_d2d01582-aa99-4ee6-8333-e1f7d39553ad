<?php

namespace App\Exceptions;

use App\Constants\ErrorCode;
use App\Util\LogHelper;
use App\Util\LogType;
use Throwable;

class RemoteCallException extends SimpleException
{
    public function __construct($message = '', Throwable $previous = null)
    {
        parent::__construct(ErrorCode::REMOTE_SERVICE_EXCEPTION, $message, $previous);
    }

    public function report()
    {
        LogHelper::error(LogType::RemoteCallException, $this->getMessage(), ['exception' => LogHelper::serializeException($this)]);
    }

    public static function createFrom(\Exception $exception)
    {
        return new self($exception->getMessage(), $exception->getPrevious());
    }
}