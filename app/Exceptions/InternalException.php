<?php

namespace App\Exceptions;

use App\Constants\ErrorCode;
use App\Util\LogHelper;
use App\Util\LogType;
use Throwable;

class InternalException extends SimpleException
{
    public function __construct($message = '', Throwable $previous = null)
    {
        parent::__construct(ErrorCode::INTERNAL_EXCEPTION, $message, $previous);
    }

    public function render($request)
    {
        return response($this->getMessage(), 500);
    }

    public function report()
    {
        LogHelper::critical(LogType::InternalException, $this->getMessage(), ['exception' => LogHelper::serializeException($this)]);
    }
}