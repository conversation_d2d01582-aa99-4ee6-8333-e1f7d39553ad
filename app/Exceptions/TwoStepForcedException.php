<?php

namespace App\Exceptions;

use App\Util\CommonHelper;
use App\Util\UserAgent;

class TwoStepForcedException extends SimpleException
{
    public function __construct($message = 'two step forced')
    {
        parent::__construct(0, $message);
    }

    /**
     * @param $request
     * @return \Illuminate\Http\JsonResponse|\Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function render($request)
    {
        $user_agent = new UserAgent();
        return CommonHelper::ajaxRedirect($user_agent->isWebviewSync() ?
            config('app.fangcloud_url') . 'auth/internal_logout' : CommonHelper::getLoginUrl());
    }
}