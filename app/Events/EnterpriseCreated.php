<?php

namespace App\Events;

use App\Remote\Enterprise;
use App\Remote\User;

class EnterpriseCreated
{
    public $enterprise;
    public $admin_user;
    public $source;
    protected $additional_create_info;

    public function __construct(Enterprise $enterprise, User $admin_user, $source = null, $additional_info = [])
    {
        $this->enterprise = $enterprise;
        $this->admin_user = $admin_user;
        $this->source = $source;
        $this->additional_create_info = $additional_info;
    }

    public function getEnterprise()
    {
        return $this->enterprise;
    }

    public function getAdminUser()
    {
        return $this->admin_user;
    }

    public function getSource()
    {
        return $this->source;
    }

    /**
     * @return array
     */
    public function getAdditionalCreateInfo(): array
    {
        return $this->additional_create_info;
    }
}