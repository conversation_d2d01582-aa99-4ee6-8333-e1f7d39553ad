<?php

namespace App\Events;

use App\Models\User;
use App\Remote\Enterprise;
use Illuminate\Queue\SerializesModels;

class UserRegistered implements UserCreated
{
    use SerializesModels;

    public $user;
    public $enterprise;

    public function __construct(User $user, Enterprise $enterprise = null)
    {
        $this->user = $user;
        $this->enterprise = $enterprise;
    }

    public function getUser()
    {
        return $this->user;
    }
}