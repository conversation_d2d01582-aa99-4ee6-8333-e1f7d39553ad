<?php

namespace App\Events;

use App\Models\User;
use App\Remote\Enterprise;
use Illuminate\Queue\SerializesModels;

class QuickUserRegistered implements UserCreated
{
    use SerializesModels;

    public $user;
    public $enterprise;
    public $phone;
    public $pass;

    public function __construct(User $user, string $phone, string $pass, Enterprise $enterprise = null)
    {
        $this->user = $user;
        $this->enterprise = $enterprise;
        $this->phone = $phone;
        $this->pass = $pass;
    }

    public function getUser()
    {
        return $this->user;
    }

    public function getPhone()
    {
        return $this->phone;
    }

    public function getPass()
    {
        return $this->pass;
    }
}