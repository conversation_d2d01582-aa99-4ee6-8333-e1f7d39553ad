<?php
/**
 * Created by PhpStorm.
 * User: luther
 * Date: 24/08/2017
 * Time: 17:29
 */

namespace App\Events;

use App\Models\User;
use Illuminate\Queue\SerializesModels;

class UserActivated implements UserCreated
{
    use SerializesModels;

    public $user;

    public function __construct(User $user)
    {
        $this->user = $user;
    }

    public function getUser()
    {
        return $this->user;
    }

}