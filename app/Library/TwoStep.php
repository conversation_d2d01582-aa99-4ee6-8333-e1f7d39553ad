<?php

namespace App\Library;

use App\Constants\Common;
use App\Exceptions\ExternalException;
use App\Exceptions\ThirdServiceException;
use App\Models\User;
use App\Util\SecurityHelper;
use App\Util\TwoStepLogHelper;
use GuzzleHttp\Client;
use OTPHP\TOTP;
use SimpleSoftwareIO\QrCode\Facades\QrCode;

/**
 * TODO 这个类不应该存在于 Library 命名空间下
 *
 * Class TwoStep
 * @package App\Library
 */
class TwoStep
{
    public const METHOD_SMS = Common::TWO_STEP_METHOD_SMS;
    public const METHOD_WECHAT = Common::TWO_STEP_METHOD_WECHAT;
    public const METHOD_GOOGLE = Common::TWO_STEP_METHOD_GOOGLE;
    public const METHOD_NONE = Common::TWO_STEP_METHOD_NONE;

    protected $secret;

    protected $method;

    /**
     * wechat: wechat_id
     * sms: phone
     * google: null
     *
     * @var string $identifier
     */
    protected $identifier;

    /**
     * @var TOTP $otp
     */
    protected $otp;

    public function __construct(array $options = [])
    {
        /**
         * @var User $user
         */
        $user = $options['user'] ?? null;
        $secret = $options['secret'] ?? null;
        $method = $options['method'] ?? null;
        $reset = $options['reset'] ?? null;

        if (!$secret) {
            if ($user && !$reset) {
                $secret = $user->getTwoStepSecretAttr();
            }
            if (!$secret) {
                $secret = SecurityHelper::base32();
            }
        }
        $this->secret = $secret;
        $this->method = $method ?: ($user !== null ? $user->getTwoStepMethodAttr() : null);

        if ($this->method === self::METHOD_SMS) {
            $otp = TOTP::create($secret, 300, 'sha1', 6);
        } else {
            $otp = TOTP::create($secret, 30, 'sha1', 6);
        }

        $otp->setIssuer(config('sso.product_name_' . config('app.locale')));
        if ($user !== null) {
            $otp->setLabel($user->getName());
        }
        $this->otp = $otp;
    }

    public function getSecret()
    {
        return $this->secret;
    }

    public function getMethod()
    {
        return $this->method;
    }

    public function now()
    {
        return $this->otp->now();
    }

    public function verify(string $code)
    {
        if(config('app.skip_captcha_verify')) {
            return true;
        }
        if ($this->method === self::METHOD_WECHAT || $this->method === self::METHOD_SMS) {
            return $this->otp->verify($code, null, 2);
        } else {
            $time = time();
            $logMsg = "time : $time;";
            $logMsg .= "send code : $code;";
            if ($this->otp->verify($code, $time)) {
                return true;
            }
            $logMsg .= "calc code : ".$this->otp->at($time).";";
            TwoStepLogHelper::writeLog($logMsg);
            return false;
        }
    }

    public function getGoogleUri()
    {
        return $this->otp->getProvisioningUri();
    }

    /**
     * @return string
     */
    public function getGoogleQRcode()
    {
        $png = QrCode::encoding('utf-8')
            ->format('png')
            ->size(154)
            ->margin(0)
            ->errorCorrection('L')
            ->generate($this->otp->getProvisioningUri());

        return $png;
    }

    /**
     * @return resource
     * @throws ExternalException
     */
    public static function getWechatQRcode()
    {
        $http_client = new Client();
        $secret = config('common.two_step.wechat_permanent_qrcode_ticket');
        $response = $http_client->request('GET', "https://mp.weixin.qq.com/cgi-bin/showqrcode", [
            'query' => [
                'ticket' => $secret,
            ]
        ]);

        if ($response->getStatusCode() == 200) {
            $body = $response->getBody();

            $imgdata = getimagesizefromstring($body);
            $img = imagecreatefromstring($body);

            $small_img = imagecreatetruecolor(154, 154);
            imagecopyresized($small_img, $img, 0, 0, 0, 0, 154, 154, $imgdata[0], $imgdata[1]);
            return $small_img;
        } else {
            throw new ThirdServiceException('get wechat qr code failed');
        }

    }
}