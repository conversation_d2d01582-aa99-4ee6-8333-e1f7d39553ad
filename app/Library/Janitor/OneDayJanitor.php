<?php

namespace App\Library\Janitor;

use App\Util\CacheHelper;

class OneDayJanitor extends BaseJanitor
{
    static protected $janitor_type = 'one_day';

    public function hit()
    {
        $threshold_key = $this->getThresholdKey();
        $record = CacheHelper::getCachedInfo($threshold_key);
        $date = date('Y-m-d');
        if(!$record || !isset($record['cnt'], $record['date']) || $record['date'] != $date) {
            $record = [
                'cnt' => 1,
                'date' => $date,
            ];
        }
        else {
            $record['cnt'] += 1;
        }
        CacheHelper::cacheInfo($threshold_key, $record, 86400);
        return $record;
    }

    protected function hasExceedLimit($record = null)
    {
        if(!$record) {
            $threshold_key = $this->getThresholdKey();
            $record = CacheHelper::getCachedInfo($threshold_key);
        }
        $date = date('Y-m-d');
        if(!$record || !isset($record['cnt'], $record['date']) || $record['date'] != $date) {
            return false;
        }
        return $record['cnt'] > $this->threshold_count;
    }
}