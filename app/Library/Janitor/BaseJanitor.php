<?php

namespace App\Library\Janitor;

use App\Constants\CacheKey;
use App\Exceptions\InternalException;
use App\Util\CacheHelper;

/**
 * Class BaseJanitor
 * @package App\Library\Janitor
 *
 * 用来限制频率，有三个子类，其中
 * RateWithFreezeJanitor用来处理$threshold_time 时间内限制$threshold_count次，超出后冻结$freeze_time时间
 * RateWithoutFreezeJanitor用来处理$threshold_time 时间内限制$threshold_count次，超出后等到频率降低到阈值内自然解冻
 * OneDayJanitor用来处理一天内限制$threshold_count次，超出后第二天自然解冻
 *
 */

class BaseJanitor
{
    static protected $janitor_type = '';

    protected const STATUS_OK = 'ok';
    protected const STATUS_FREEZE = 'freeze';

    // 在$threshold_time的时间内
    protected $threshold_time = 600;

    // 限制$threshold_count次
    protected $threshold_count = 20;

    // 超出后冻结$freeze_time时间
    protected $freeze_time = 600;

    protected $identifier;

    protected const THRESHOLD_SUFFIX = '_threshold_suffix';
    protected const STATUS_SUFFIX = '_status_suffix';

    public function __construct($identifier = '', array $options = [])
    {
        $this->identifier = $identifier;
    }

    public function setIdentifier(string $identifier): BaseJanitor
    {
        $this->identifier = $identifier;
        return $this;
    }

    public function getJanitorType()
    {
        return static::$janitor_type;
    }

    public function setThresholdCount(int $threshold_count): BaseJanitor
    {
        $this->threshold_count = $threshold_count;
        return $this;
    }

    public function setThresholdTime(int $threshold_time): BaseJanitor
    {
        $this->threshold_time = $threshold_time;
        return $this;
    }

    public function setFreezeTime(int $freeze_time): BaseJanitor
    {
        $this->freeze_time = $freeze_time;
        return $this;
    }

    protected function getThresholdKey()
    {
        if (!$this->identifier) {
            throw new InternalException('identifier not set');
        }
        return CacheKey::JANITOR_PREFIX . $this->getJanitorType() . $this->identifier . self::THRESHOLD_SUFFIX;
    }

    protected function getStatusKey()
    {
        if (!$this->identifier) {
            throw new InternalException('identifier not set');
        }
        return CacheKey::JANITOR_PREFIX . $this->getJanitorType() . $this->identifier . self::STATUS_SUFFIX;
    }

    public function isFrozen()
    {
        return $this->hasExceedLimit();
    }

    public function fire(\Closure $func)
    {
        // how to set key?
        // 1. construct with identifier
        // 2. fire before set identifier

        if (!$this->identifier) {
            throw new InternalException('identifier not set');
        }

        $this->hit();

        if ($this->isFrozen()) {
            return false;
        }

        // fire
        $func();

        return true;
    }

    public function hit()
    {
    }

    protected function hasExceedLimit($record = null)
    {
        return false;
    }

    public function clear()
    {
        $this->clearRecords();
        $this->clearStatus();
    }

    protected function clearStatus()
    {
    }

    protected function clearRecords()
    {
        CacheHelper::clearCachedInfo($this->getThresholdKey());
    }

    protected function freeze()
    {
    }
}