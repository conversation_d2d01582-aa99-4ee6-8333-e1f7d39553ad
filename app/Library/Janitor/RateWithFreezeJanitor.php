<?php

namespace App\Library\Janitor;

use App\Util\CacheHelper;

/**
 * Class Janitor
 * @package App\Library
 * @description 用于处理，在 X 时间内，操作 Y 次，冻结 Z 时间
 *
 * identifier is required to generate cache key
 *
 * default:
 *
 * X = 600 => 10 min
 * Y = 20
 * Z = 600 => 10 min
 *
 * eg.
 *
 * app\Remote\Sms.php
 *
 * $func = function() {
 * echo 'Hello World!';
 * };
 *
 * 1. (new Janitor())->setIdentifier('demo')
 * ->setFreezeTime(600)
 * ->setThresholdCount(20)
 * ->setThresholdTime(600)
 * ->fire($func);
 *
 * 2. (new Janitor('demo', [
 * 'threshold_time' => 600,
 * 'threshold_count' => 20,
 * 'freeze_time' => 600,
 * ]))->fire($func);
 *
 * 3. (new Janitor())->setIdentifier('demo')->fire(function(){
 * echo 'Hello World';
 * });
 *
 * 4. freeze or unfreeze
 * (new Janitor('demo'))->freeze();
 * (new Janitor('demo'))->unfreeze();
 *
 *
 *
 */
class RateWithFreezeJanitor extends BaseJanitor
{
    static protected $janitor_type = 'rate_with_freeze';

    public function isFrozen()
    {
        $freeze_status = CacheHelper::getCachedInfo($this->getStatusKey());

        return ($freeze_status === self::STATUS_FREEZE);
    }

    public function hit()
    {
        $threshold_key = $this->getThresholdKey();
        $val = CacheHelper::getCachedInfo($threshold_key);
        if (!$val) {
            $val = [time()];
        } else {
            $val[] = time();
            if (count($val) > $this->threshold_count) {
                array_shift($val);
            }
        }
        CacheHelper::cacheInfo($threshold_key, $val, $this->threshold_time);
        if($this->hasExceedLimit($val)) {
            $this->freeze();
        }
        return $val;
    }

    protected function hasExceedLimit($record = null)
    {
        if(!$record) {
            $threshold_key = $this->getThresholdKey();
            $record = CacheHelper::getCachedInfo($threshold_key);
        }
        if (!$record || count($record) < $this->threshold_count) {
            return false;
        }
        return $record[0] + $this->threshold_time > time();
    }

    protected function clearStatus()
    {
        CacheHelper::clearCachedInfo($this->getStatusKey());
    }

    public function freeze()
    {
        $this->clearRecords();
        CacheHelper::cacheInfo($this->getStatusKey(), static::STATUS_FREEZE, $this->freeze_time);
    }
}