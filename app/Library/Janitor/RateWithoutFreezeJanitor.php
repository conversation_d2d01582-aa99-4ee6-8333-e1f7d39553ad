<?php

namespace App\Library\Janitor;

use App\Util\CacheHelper;

class RateWithoutFreezeJanitor extends BaseJanitor
{
    static protected $janitor_type = 'rate_without_freeze';

    public function hit()
    {
        $threshold_key = $this->getThresholdKey();
        $val = CacheHelper::getCachedInfo($threshold_key);
        if (!$val) {
            $val = [time()];
        } else {
            $val[] = time();
            if (count($val) > $this->threshold_count) {
                array_shift($val);
            }
        }
        CacheHelper::cacheInfo($threshold_key, $val, $this->threshold_time);
        if($this->hasExceedLimit($val)) {
            $this->freeze();
        }
        return $val;
    }

    protected function hasExceedLimit($record = null)
    {
        if(!$record) {
            $threshold_key = $this->getThresholdKey();
            $record = CacheHelper::getCachedInfo($threshold_key);
        }
        if (!$record || count($record) < $this->threshold_count) {
            return false;
        }
        return $record[0] + $this->threshold_time > time();
    }
}