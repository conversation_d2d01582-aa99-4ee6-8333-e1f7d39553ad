<?php

namespace App\Library\HttpClient;

use App\Constants\Http;
use App\Util\CacheHelper;
use GuzzleHttp\Client;

/**
 * php Guard 客户端, 自动添加\校验内部鉴权信息
 *
 * Class Guard
 * @package App\Library\Phoenix
 */
class Guard extends Client
{
    use PhoenixTrait, AbstractClientTrait;

    /**
     * 使用内部鉴权时，当前服务的service id
     * @var int
     */
    protected $service_id;

    public function __construct(int $service_id)
    {
        $this->service_id = $service_id;
        parent::__construct(['http_errors' => false]);
    }

    /**
     * Guard 服务端认证客户端的基本方法
     * 进行内部鉴权
     *
     * @param $client_id
     * @param array $options version timestamp token
     * @throws GuardException options 中缺少内部鉴权所需的必要信息
     */
    public function verifyClient($client_id, array $options)
    {
        if (!isset($options['timestamp']) || !isset($options['version']) || !isset($options['token']) || !$client_id) {
            throw new GuardException('invalid token!');
        }
        $timestamp = $options['timestamp'];

        $current_time = time();
        $period_time = $current_time - $timestamp;
        $open_token_expire = 600;
        if (abs($period_time) >= $open_token_expire)
        {
            throw new GuardException('invalid token!');
        }

        $secret = $this->getClient($client_id);
        if ($secret === null) {
            throw new GuardException('invalid token!');
        }

        $token = $this->getToken($client_id, $timestamp, $secret['secret'], $options['version']);
        if ($token != $options['token'])
        {
            throw new GuardException('invalid token!');
        }
    }

    /**
     * Guard 请求服务端的基本方法
     * 添加内部鉴权相关信息
     *
     * @param int $server_id
     * @param $method
     * @param string $url
     * @param array $options
     * @return mixed|\Psr\Http\Message\ResponseInterface
     */
    public function requestServer(int $server_id, $method, $url = '', array $options = [])
    {
        if (isset($options['headers'])) {
            $options['headers'] = array_merge($this->getAuthHeaders($server_id), $options['headers']);
        } else {
            $options['headers'] = $this->getAuthHeaders($server_id);
        }

        $response = $this->request($method, $url, $options);
        return $response;
    }

    public function getServer(int $server_id)
    {
        if ($secret = CacheHelper::getGuardSecret($server_id, $this->service_id)) {
            return $secret;
        }

        $url = 'service/simba/guard/get_internal_client_token';

        $res = $this->requestWithPhoenix( 'GET', $url, [
            'query' => [
                'client_service_id' => $this->service_id,
                'server_service_id' => $server_id,
            ],
        ]);

        $this->checkResponse($res);
        $data = $this->getResponse($res);

        $secret = [
            'service_id' => $data['serverServiceId'],
            'secret' => $data['secret'],
            'version' => $data['version'],
        ];
        CacheHelper::setGuardSecret($server_id, $this->service_id, $secret);
        return $secret;
    }

    public function getClient(int $client_id)
    {
        if ($secret = CacheHelper::getGuardSecret($this->service_id, $client_id)) {
            return $secret;
        }

        $url = 'service/simba/guard/get_internal_server_token';

        $res = $this->requestWithPhoenix( 'GET', $url, [
            'query' => [
                'client_service_id' => $client_id,
                'server_service_id' => $this->service_id,
            ],
        ]);

        $this->checkResponse($res);
        $data = $this->getResponse($res);

        $secret = [
            'service_id' => $data['clientServiceId'],
            'secret' => $data['secret'],
        ];
        CacheHelper::setGuardSecret($this->service_id, $client_id, $secret);
        return $secret;
    }

    private function getAuthHeaders(int $server_id)
    {
        $server = $this->getServer($server_id);
        $timestamp = time();
        $token = $this->getToken($this->service_id, $timestamp, $server['secret'], $server['version']);

        return [
            Http::HEADER_INTERNAL_TOKEN => $token,
            Http::HEADER_INTERNAL_VERSION => $server['version'],
            Http::HEADER_INTERNAL_TIMESTAMP => $timestamp,
            Http::HEADER_INTERNAL_SERVICEID => $this->service_id,
        ];
    }

    private function getToken($client_id, $timestamp, $secret, $version)
    {
        $token_array = [
            'platform_id' => $client_id,
            'timestamp' => $timestamp,
            'secret' => $secret,
            'version' => $version,
        ];
        ksort($token_array);
        $token = md5(implode(array_values($token_array)));
        return $token;
    }

    /**
     * 返回远端服务的 remote interface
     * @return string
     */
    protected function getRemoteInterface(): string
    {
        return 'com.fangcloud.service.simba.common.api.service.GuardService';
    }
}