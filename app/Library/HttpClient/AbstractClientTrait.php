<?php

namespace App\Library\HttpClient;

use Psr\Http\Message\ResponseInterface;

trait AbstractClientTrait
{
    /**
     * 自定义校验
     *
     * @param ResponseInterface $response
     * @throws HttpClientException
     */
    protected function checkResponse(ResponseInterface $response)
    {
        if ($response->getStatusCode() !== 200) {
            throw new HttpClientException($response->getBody()->getContents());
        }
    }

    /**
     * @param ResponseInterface $response
     * @return mixed
     * @throws HttpClientException
     */
    protected function getResponse(ResponseInterface $response)
    {
        $content = $response->getBody()->getContents();
        $body = json_decode($content, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new HttpClientException('invalid body! ' . $content);
        }
        return $body;
    }
}
