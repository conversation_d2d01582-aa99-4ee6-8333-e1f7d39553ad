<?php

namespace App\Library\HttpClient;

use App\Constants\Http;
use GuzzleHttp\Client;
use GuzzleHttp\Handler\CurlHandler;
use GuzzleHttp\HandlerStack;
use Psr\Http\Message\RequestInterface;

/**
 * Base Client For Phoenix PHP
 *
 * 每个请求都会带上 X-Dubbo-Service http header
 *
 * 也可以使用 \App\Library\Phoenix\PhoenixTrait
 *
 * Class BaseClient
 * @package App\Library\Phoenix
 */
class PhoenixClient extends Client
{
    protected $remote_interface;

    public function __construct(string $remote_interface, string $base_uri = '')
    {
        $stack = new HandlerStack();
        $stack->setHandler(new CurlHandler());
        $stack->push(self::add_header(Http::HEADER_DUBBO_SERVICE, $remote_interface));
        $config = [
            'handler' => $stack,
        ];
        if ($base_uri) {
            $config['base_uri'] = $base_uri;
        }

        parent::__construct($config);

        $this->remote_interface = $remote_interface;
    }

    /**
     * http://guzzle-cn.readthedocs.io/zh_CN/latest/handlers-and-middleware.html#middleware
     *
     * @param $header
     * @param $value
     * @return \Closure
     */
    function add_header($header, $value)
    {
        return function (callable $handler) use ($header, $value) {
            return function (
                RequestInterface $request,
                array $options
            ) use ($handler, $header, $value) {
                $request = $request->withHeader($header, $value);
                return $handler($request, $options);
            };
        };
    }
}