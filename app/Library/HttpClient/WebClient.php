<?php

namespace App\Library\HttpClient;

/**
 * Class WebClient
 * @package App\Library\HttpClient
 */
class WebClient extends Guard
{
    use AbstractClientTrait;

    protected $base_url;

    protected $remote_service_id;

    public function __construct($service_id, $remote_service_id, $remote_service_base_url)
    {
        $this->remote_service_id = $remote_service_id;
        $this->base_url =$remote_service_base_url;

        parent::__construct($service_id);
    }

    /**
     * @param $method
     * @param string $uri
     * @param array $options
     * @return mixed
     * @throws HttpClientException
     */
    public function requestRemote($method, $uri = '', array $options = [])
    {
        if (isset($options['body'])) {
            if (is_array($options['body'])) {
                $options['body'] = json_encode($options['body']);
            }

            if (!isset($options['headers']['Content-Type'])) {
                $options['headers']['Content-Type'] = 'application/json; charset=utf-8';
            }
        }

        try {
            $response =  parent::requestServer($this->remote_service_id, $method, $this->base_url . $uri, $options);
        } catch(\GuzzleHttp\Exception\RequestException | \GuzzleHttp\Exception\ConnectException $e) {
            throw new HttpClientException($e->getMessage(), 0, $e);
        }
        $this->checkResponse($response);
        return $this->getResponse($response);
    }
}