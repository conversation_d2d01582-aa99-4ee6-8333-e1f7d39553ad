<?php

namespace app\Library\HttpClient;

/**
 * 用于和 java 服务通信的trait。
 *
 * 各 guzzle http client 可以使用这个trait，并且使用提供的 requestWithPhoenix 方法自动添加 phoenix 相关 header
 *
 * Trait PhoenixTrait
 * @package app\Library\Phoenix
 */
trait PhoenixTrait
{
    /**
     * 返回 phoenix base url
     *
     * @return \Illuminate\Config\Repository|mixed
     */
    protected function getPhoenixUri()
    {
        return config('services.phoenix');
    }

    /**
     * 请求 phoenix 转发请求
     *
     * @param $method
     * @param string $uri
     * @param array $options
     * @return mixed|\Psr\Http\Message\ResponseInterface
     */
    public function requestWithPhoenix($method, $uri = '', array $options = [])
    {
        if (isset($options['headers'])) {
            $options['headers'] = array_merge($options['headers'], $this->getPhoenixHeader($options));
        } else {
            $options['headers'] = $this->getPhoenixHeader($options);
        }

        return $this->request($method, $this->getPhoenixUri() . $uri, $options);
    }

    private function getPhoenixHeader($options)
    {
        if (isset($options['remote_interface'])) {
            $remote_interface = $options['remote_interface'];
        } else {
            $remote_interface = $this->getRemoteInterface();
        }
        return ['X-Dubbo-Service' => $remote_interface];

    }

    /**
     * 返回远端服务的 remote interface
     * @return string
     */
    abstract protected function getRemoteInterface(): string;
}