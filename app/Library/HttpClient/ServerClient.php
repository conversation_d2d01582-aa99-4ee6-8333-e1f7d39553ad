<?php

namespace App\Library\HttpClient;

use GuzzleHttp\Client;

class ServerClient extends Client
{
    use PhoenixTrait, AbstractClientTrait;

    protected $remote_interface;

    public function __construct($remote_interface)
    {
        $this->remote_interface = $remote_interface;
        parent::__construct(['http_errors' => false]);
    }

    /**
     * @param $method
     * @param string $uri
     * @param array $options
     * @return mixed|\Psr\Http\Message\ResponseInterface
     * @throws HttpClientException
     */
    public function requestRemote($method, $uri = '', array $options = [])
    {
        if (isset($options['body'])) {
            if (is_array($options['body'])) {
                $options['body'] = json_encode($options['body']);
            }

            if (!isset($options['headers']['Content-Type'])) {
                $options['headers']['Content-Type'] = 'application/json; charset=utf-8';
            }
        }

        try {
            $response = $this->requestWithPhoenix($method, $uri, $options);
        } catch(\GuzzleHttp\Exception\RequestException | \GuzzleHttp\Exception\ConnectException $e) {
            throw new HttpClientException($e->getMessage(), 0, $e);
        }
        $this->checkResponse($response);
        return $this->getResponse($response);
    }

    /**
     * 返回远端服务的 remote interface
     * @return string
     */
    protected function getRemoteInterface(): string
    {
        return $this->remote_interface;
    }
}