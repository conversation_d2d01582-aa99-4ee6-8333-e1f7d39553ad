<?php

namespace App\Library\OAuth\Qihoo360;

use League\OAuth2\Client\Provider\ResourceOwnerInterface;

/**
 * Class User
 * @package App\Library\OAuth\Qihoo360
 * @property string id
 * @property string name
 * @property string avatar
 */
class User implements ResourceOwnerInterface
{
    protected $raw_data;

    protected $id;

    protected $name;

    protected $avatar;

    /**
     * User constructor.
     *
     * http://open.app.360.cn/dev/docapi#1.1.E3.80.81.E8.8E.B7.E5.8F.96.E5.BD.93.E5.89.8D.E7.99.BB.E5.BD.95.E7.94.A8.E6.88.B7.E7.9A.84.E8.B5.84.E6.96.99.28user.2Fme.29
     *
     * @param array $info
     * {
           id: "123456"
           name: "test_user_name"
           avatar:"http://u1.qhimg.com/qhimg/quc/48_48/20/01/e5/2001e5q43.3eb2e3.jpg"
       }
     *
     *
     *
     * 参数名	介绍
     * id	    用户ID
     * name	    用户名
     * avatar	用户头像
     */
    public function __construct(array $info)
    {
        $this->id = $info['id'];
        $this->name = $info['name'];
        $this->avatar = $info['avatar'];

        $this->raw_data = $info;
    }


    /**
     * Returns the identifier of the authorized resource owner.
     *
     * @return mixed
     */
    public function getId()
    {
        return $this->id;
    }

    public function getNick()
    {
        return $this->name;
    }

    /**
     * Return all of the owner details available as an array.
     *
     * @return array
     */
    public function toArray()
    {
        return $this->raw_data;
    }
}