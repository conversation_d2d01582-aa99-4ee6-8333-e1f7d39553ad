<?php

namespace App\Library\OAuth\Qihoo360;

use League\OAuth2\Client\Provider\AbstractProvider;
use League\OAuth2\Client\Provider\ResourceOwnerInterface;
use League\OAuth2\Client\Token\AccessToken;
use Psr\Http\Message\ResponseInterface;

class Qihoo360<PERSON>rovider extends AbstractProvider
{
    private $authCode;

    /**
     * http://open.app.360.cn/dev/doc#2.1.2.E3.80.81.E8.8E.B7.E5.8F.96Authorization_Code
     *
     * @return string
     */
    public function getBaseAuthorizationUrl()
    {
        return 'https://oauth2.openapi.360.cn/authorize/';
    }

    /**
     * http://open.app.360.cn/dev/doc#2.1.3.E3.80.81.E9.80.9A.E8.BF.87Authorization_Code.E8.8E.B7.E5.8F.96Access_Token
     *
     * @param array $params
     * @return string
     */
    public function getBaseAccessTokenUrl(array $params)
    {
        return 'https://oauth2-api.openapi.360.cn/token';
    }

    /**
     * http://open.app.360.cn/dev/docapi#1.1.E3.80.81.E8.8E.B7.E5.8F.96.E5.BD.93.E5.89.8D.E7.99.BB.E5.BD.95.E7.94.A8.E6.88.B7.E7.9A.84.E8.B5.84.E6.96.99.28user.2Fme.29
     *
     * @param AccessToken $token
     * @return string
     */
    public function getResourceOwnerDetailsUrl(AccessToken $token)
    {
        return 'https://oauth2-api.openapi.360.cn/oauth2/getinfo';
    }

    /**
     * Returns the default scopes used by this provider.
     *
     * This should only be the scopes that are required to request the details
     * of the resource owner, rather than all the available scopes.
     *
     * @return array
     */
    protected function getDefaultScopes()
    {
        return [];
    }

    /**
     * Checks a provider response for errors.
     *
     * @param  ResponseInterface $response
     * @param  array|string $data Parsed response data
     * @throws Qihoo360Exception
     */
    protected function checkResponse(ResponseInterface $response, $data)
    {
        if (isset($data['error']) && $data['error'] != 0) {
            $error_code = $data['error'];
            $error_msg = $data['error_description'];
            throw new Qihoo360Exception($error_code, $error_msg);
        }
    }

    /**
     * Generates a resource owner object from a successful resource owner
     * details request.
     *
     * @param  array $response
     * @param  AccessToken $token
     * @return User
     */
    protected function createResourceOwner(array $response, AccessToken $token)
    {
        $info['id'] = $response['data']['user']['qid'];
        $info['name'] = $response['data']['user']['nickname'];
        $info['avatar'] = $response['data']['user']['imageurl'];
        return new User($info);
    }

    /**
     * Requests and returns the resource owner of given access token.
     *
     * @param  AccessToken $token
     * @return User
     */
    public function getResourceOwner(AccessToken $token)
    {
        $response = $this->fetchResourceOwnerDetails($token);

        return $this->createResourceOwner($response, $token);
    }

    /**
     * http://open.app.360.cn/dev/doc#2.1.3.E3.80.81.E9.80.9A.E8.BF.87Authorization_Code.E8.8E.B7.E5.8F.96Access_Token
     *
     * @param  mixed $grant
     * @param  array $options
     * @return AccessToken
     */
    public function getAccessToken($grant = null, array $options = [])
    {
        $params = [
            'grant_type' => 'authorization_code',
            'code'       => $this->authCode,
            'client_id'  => $this->clientId,
            'client_secret' => $this->clientSecret,
            'redirect_uri' => $this->redirectUri,
        ];

        $request  = $this->getAccessTokenRequest($params);
        $response = $this->getParsedResponse($request);
        $prepared = $this->prepareAccessTokenResponse($response);
        $token    = new AccessToken([
            'access_token' => $prepared['access_token'],
            'expires_in' => $prepared['expires_in'],
            'refresh_token' => $prepared['refresh_token'],
        ]);

        return $token;
    }

    /**
     * Requests resource owner details.
     *
     * @param  AccessToken $token
     * @return mixed
     */
    protected function fetchResourceOwnerDetails(AccessToken $token)
    {
        $url = $this->getResourceOwnerDetailsUrl($token);
        $params = ['access_token' => $token->getToken()];
        $factory = $this->getRequestFactory();
        $options = ['headers' => ['content-type' => 'application/x-www-form-urlencoded']];

        $options['body'] = http_build_query($params, null, '&', \PHP_QUERY_RFC3986);;

        $request =  $factory->getRequestWithOptions(self::METHOD_POST, $url, $options);
        return $this->getParsedResponse($request);
    }

    public function getRandomState($length = 32)
    {
        return parent::getRandomState($length);
    }

    public function __construct(string $authCode = null, array $options = [], array $collaborators = [])
    {
        $this->authCode = $authCode;

        $options = [
            'clientId'                => config('qihoo360.appid'),
            'clientSecret'            => config('qihoo360.appsecret'),
            'redirectUri'             => config('qihoo360.redirecturi'),
        ];

        parent::__construct($options, $collaborators);
    }
}