<?php

namespace App\Library\OAuth\Dingtalk;

use League\OAuth2\Client\Grant\AbstractGrant;
use League\OAuth2\Client\Provider\AbstractProvider;
use League\OAuth2\Client\Token\AccessToken;

class DingtalkProvider extends AbstractProvider
{
    /**
     * https://open-doc.dingtalk.com/docs/doc.htm?spm=a219a.7629140.0.0.i2GuS1&treeId=168&articleId=104881&docType=1
     *
     * @return string
     */
    public function getBaseAuthorizationUrl()
    {
        if ($this->type == 'qrcode') {
            return 'https://oapi.dingtalk.com/connect/qrconnect';
        } else {
            return 'https://oapi.dingtalk.com/connect/oauth2/sns_authorize';
        }
    }

    /**
     * https://open-doc.dingtalk.com/docs/doc.htm?spm=a219a.7629140.0.0.pYWYKv&treeId=385&articleId=104968&docType=1#s0
     *
     * @param array $params
     * @return string
     */
    public function getBaseAccessTokenUrl(array $params)
    {
        return 'https://oapi.dingtalk.com/sns/gettoken';
    }

    /**
     * https://open-doc.dingtalk.com/docs/doc.htm?spm=a219a.7629140.0.0.E7m5El&treeId=385&articleId=104968&docType=1#s3
     *
     * 其实是拿持久授权码时候的url
     *
     * @param \League\OAuth2\Client\Token\AccessToken $token
     * @return string
     */
    public function getResourceOwnerDetailsUrl(\League\OAuth2\Client\Token\AccessToken $token)
    {
        return 'https://oapi.dingtalk.com/sns/get_persistent_code?access_token=' . $token->getToken();
    }

    /**
     * Returns the default scopes used by this provider.
     *
     * This should only be the scopes that are required to request the details
     * of the resource owner, rather than all the available scopes.
     *
     * @return array
     */
    protected function getDefaultScopes()
    {
        return ['snsapi_login'];
    }

    /**
     * Checks a provider response for errors.
     *
     * @param  \Psr\Http\Message\ResponseInterface $response
     * @param  array|string $data Parsed response data
     * @throws DingtalkException
     */
    protected function checkResponse(\Psr\Http\Message\ResponseInterface $response, $data)
    {
        if (isset($data['errcode']) && $data['errcode'] != 0) {
            $error_code = $data['errcode'];
            $error_msg = $data['errmsg'];
            throw new DingtalkException($error_code, $error_msg);
        }
    }

    /**
     * Generates a resource owner object from a successful resource owner
     * details request.
     *
     * @param  array $response
     * @param  \League\OAuth2\Client\Token\AccessToken $token
     * @return \App\Library\OAuth\Dingtalk\User
     */
    protected function createResourceOwner(array $response, \League\OAuth2\Client\Token\AccessToken $token)
    {
        $info = $response['user_info'];
        return new User($info);
    }

    /**
     * https://open-doc.dingtalk.com/docs/doc.htm?spm=a219a.7629140.0.0.Ix98Cw&treeId=385&articleId=104968&docType=1#s0
     *
     * @param  mixed $grant
     * @param  array $options
     * @return AccessToken
     */
    public function getAccessToken($grant = null, array $options = [])
    {
        $params = [
            'appid'     => $this->clientId,
            'appsecret' => $this->clientSecret,
        ];

        $request  = $this->getAccessTokenRequest($params);
        $response = $this->getParsedResponse($request);
        $prepared = $this->prepareAccessTokenResponse($response);
        $token    = $this->createAccessToken($prepared, $grant);

        return $token;
    }

    /**
     * Creates an access token from a response.
     *
     * The grant that was used to fetch the response can be used to provide
     * additional context.
     *
     * @param  array $response
     * @param  AbstractGrant|null $grant
     * @return AccessToken
     */
    protected function createAccessToken(array $response, AbstractGrant $grant=null)
    {
        return new AccessToken($response);
    }

    protected function getAccessTokenMethod()
    {
        return self::METHOD_GET;
    }

    /**
     * Requests and returns the resource owner of given access token.
     *
     * 1. 拿到 持久码
     * 2. 拿到 sns_token
     * 3. 拿到 用户信息
     *
     * @param  AccessToken $token
     * @return \App\Library\OAuth\Dingtalk\User
     */
    public function getResourceOwner(AccessToken $token)
    {
        $url = $this->getResourceOwnerDetailsUrl($token);

        $request_persistent_code = $this->createRequest(
            self::METHOD_POST,
            $url,
            $token,
            [
                'body' => json_encode([
                    'tmp_auth_code' => $_GET['code'],
                ]),
            ]
        );

        $response_persistent_code = $this->getParsedResponse($request_persistent_code);
        $persistent_code = $response_persistent_code['persistent_code'];
        $openid = $response_persistent_code['openid'];

        $request_sns_token = $this->createRequest(
            self::METHOD_POST,
            'https://oapi.dingtalk.com/sns/get_sns_token?access_token=' . $token->getToken(),
            $token,
            [
                'body' => json_encode([
                    'openid' => $openid,
                    'persistent_code' => $persistent_code,
                ]),
            ]
        );

        $response_sns_token = $this->getParsedResponse($request_sns_token);
        $sns_token = $response_sns_token['sns_token'];

        $request_user_info = $this->createRequest(
            self::METHOD_GET,
            'https://oapi.dingtalk.com/sns/getuserinfo?sns_token=' . $sns_token,
            $token,
            []
        );

        $response_user_info = $this->getParsedResponse($request_user_info);

        return $this->createResourceOwner($response_user_info, $token);
    }

    /**
     * Returns authorization parameters based on provided options.
     *
     * @param  array $options
     * @return array Authorization parameters
     */
    protected function getAuthorizationParameters(array $options)
    {
        if (empty($options['state'])) {
            $options['state'] = $this->getRandomState();
        }

        if (empty($options['scope'])) {
            $options['scope'] = $this->getDefaultScopes();
        }

        $options += [
            'response_type'   => 'code',
            'approval_prompt' => 'auto'
        ];

        if (is_array($options['scope'])) {
            $separator = $this->getScopeSeparator();
            $options['scope'] = implode($separator, $options['scope']);
        }

        // Store the state as it may need to be accessed later on.
        $this->state = $options['state'];

        // Business code layer might set a different redirect_uri parameter
        // depending on the context, leave it as-is
        if (!isset($options['redirect_uri'])) {
            $options['redirect_uri'] = $this->redirectUri;
        }

        $options['appid'] = $this->clientId;

        return $options;
    }

    public function getRandomState($length = 32)
    {
        return parent::getRandomState($length);
    }

    protected $type;

    public function __construct($type='')
    {
        $this->type = $type;

        $options = [
            'clientId'                => config('dingtalk.appid'),
            'clientSecret'            => config('dingtalk.appsecret'),
            'redirectUri'             => config('dingtalk.redirecturi'),
        ];

        parent::__construct($options);
    }
}