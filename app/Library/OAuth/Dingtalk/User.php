<?php

namespace App\Library\OAuth\Dingtalk;
use League\OAuth2\Client\Provider\ResourceOwnerInterface;

/**
 * Class User
 * @package App\Library\OAuth\Dingtalk
 *
 * @property string $nick
 * @property string $openid
 * @property string $unionid
 * @property string $dingId
 */
class User implements ResourceOwnerInterface
{
    protected $nick, $openid, $unionid, $dingId;

    public function __construct($info)
    {
        $this->nick = $info['nick'];
        $this->openid = $info['openid'];
        $this->unionid = $info['unionid'];
        $this->dingId = $info['dingId'];
    }

    /**
     * Return dingId
     *
     * @return mixed
     */
    public function getId()
    {
        return $this->dingId;
    }

    public function getNick()
    {
        return $this->nick;
    }

    /**
     * Return all of the owner details available as an array.
     *
     * @return array
     */
    public function toArray()
    {
        return [
            'nick' => $this->nick,
            'openid' => $this->openid,
            'unionid' => $this->unionid,
            'dingId' => $this->dingId,
        ];
    }
}