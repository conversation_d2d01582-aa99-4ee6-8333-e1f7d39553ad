<?php

namespace App\Library\OAuth\Wechat;

use League\OAuth2\Client\Provider\AbstractProvider;
use League\OAuth2\Client\Provider\ResourceOwnerInterface;
use League\OAuth2\Client\Token\AccessToken;
use Psr\Http\Message\ResponseInterface;

class WechatProvider extends AbstractProvider
{

    /**
     * https://work.weixin.qq.com/api/doc#10991
     *
     * @return string
     */
    public function getBaseAuthorizationUrl()
    {
        return 'https://open.work.weixin.qq.com/wwopen/sso/3rd_qrConnect';
    }

    /**
     * https://work.weixin.qq.com/api/doc#11791/服务商的token
     *
     * @param array $params
     * @return string
     */
    public function getBaseAccessTokenUrl(array $params)
    {
        return 'https://qyapi.weixin.qq.com/cgi-bin/service/get_provider_token';
    }

    /**
     * https://work.weixin.qq.com/api/doc#10991
     *
     * @param AccessToken $token
     * @return string
     */
    public function getResourceOwnerDetailsUrl(AccessToken $token)
    {
        return 'https://qyapi.weixin.qq.com/cgi-bin/service/get_login_info?access_token=' . $token->getToken();
    }

    /**
     * Returns the default scopes used by this provider.
     *
     * This should only be the scopes that are required to request the details
     * of the resource owner, rather than all the available scopes.
     *
     * @return array
     */
    protected function getDefaultScopes()
    {
        return [];
    }

    /**
     * Checks a provider response for errors.
     *
     * @param  ResponseInterface $response
     * @param  array|string $data Parsed response data
     * @throws WechatException
     */
    protected function checkResponse(ResponseInterface $response, $data)
    {
        if (isset($data['errcode']) && $data['errcode'] != 0) {
            $error_code = $data['errcode'];
            $error_msg = $data['errmsg'];
            throw new WechatException($error_code, $error_msg);
        }
    }

    /**
     * Generates a resource owner object from a successful resource owner
     * details request.
     *
     * @param  array $response
     * @param  AccessToken $token
     * @return User
     */
    protected function createResourceOwner(array $response, AccessToken $token)
    {
        return new User($response);
    }

    /**
     * Requests and returns the resource owner of given access token.
     *
     * @param  AccessToken $token
     * @return User
     */
    public function getResourceOwner(AccessToken $token)
    {
        $response = $this->fetchResourceOwnerDetails($token);

        return $this->createResourceOwner($response, $token);
    }

    /**
     * https://work.weixin.qq.com/api/doc#11791/服务商的token
     *
     * @param  mixed $grant
     * @param  array $options
     * @return AccessToken
     */
    public function getAccessToken($grant = null, array $options = [])
    {
        $params = [
            'corpid'     => $this->clientId,
            'provider_secret' => $this->clientSecret,
        ];

        $request = $this->createRequest(
            self::METHOD_POST,
            $this->getBaseAccessTokenUrl([]),
            null,
            [
                'headers' => [
                    'Content-Type' => 'application/json',
                ],
                "body" => json_encode($params)
            ]
        );

        $response = $this->getParsedResponse($request);
        $prepared = $this->prepareAccessTokenResponse($response);
        $token    = new AccessToken([
            'access_token' => $prepared['provider_access_token'],
            'expires_in' => $prepared['expires_in'],
        ]);

        return $token;
    }

    /**
     * Requests resource owner details.
     *
     * @param  AccessToken $token
     * @return mixed
     */
    protected function fetchResourceOwnerDetails(AccessToken $token)
    {
        $url = $this->getResourceOwnerDetailsUrl($token);

        $request = $this->getAuthenticatedRequest(
            self::METHOD_POST,
            $url,
            $token,
            [
                'body' => json_encode([
                    'auth_code' => $_GET['auth_code'],
                ]),
            ]
        );

        return $this->getParsedResponse($request);
    }

    /**
     * Returns authorization parameters based on provided options.
     *
     * @param  array $options
     * @return array Authorization parameters
     */
    protected function getAuthorizationParameters(array $options)
    {
        if (empty($options['state'])) {
            $options['state'] = $this->getRandomState();
        }

        if (empty($options['scope'])) {
            $options['scope'] = $this->getDefaultScopes();
        }

        $options += [
            'response_type'   => 'code',
            'approval_prompt' => 'auto'
        ];

        if (is_array($options['scope'])) {
            $separator = $this->getScopeSeparator();
            $options['scope'] = implode($separator, $options['scope']);
        }

        // Store the state as it may need to be accessed later on.
        $this->state = $options['state'];

        // Business code layer might set a different redirect_uri parameter
        // depending on the context, leave it as-is
        if (!isset($options['redirect_uri'])) {
            $options['redirect_uri'] = $this->redirectUri;
        }

        $options['appid'] = $this->clientId;

        return $options;
    }

    public function getRandomState($length = 32)
    {
        return parent::getRandomState($length);
    }

    public function __construct(array $options = [], array $collaborators = [])
    {
        $options = [
            'clientId'                => config('wechat.appid'),
            'clientSecret'            => config('wechat.appsecret'),
            'redirectUri'             => config('wechat.redirecturi'),
        ];

        parent::__construct($options, $collaborators);
    }
}