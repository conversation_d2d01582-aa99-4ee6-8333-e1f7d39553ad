<?php

namespace App\Library\OAuth\Wechat;

use League\OAuth2\Client\Provider\ResourceOwnerInterface;

class User implements ResourceOwnerInterface
{
    protected $raw_data;

    protected $usertype;

    protected $user_info;

    protected $corp_info;

    protected $agent;

    protected $auth_info;

    /**
     * User constructor.
     *
     * https://work.weixin.qq.com/api/doc#10991/获取登录用户信息
     *
     * @param array $info
     * {
            "usertype": 1,
            "user_info":{
                "userid":"xxxx",
                "name":"xxxx",
                "avatar":"xxxx"
                "email":"xxxx"
            },
            "corp_info":{
                "corpid":"wx6c698d13f7a409a4",
            },
            "agent":[
                {"agentid":0,"auth_type":1},
                {"agentid":1,"auth_type":1},
                {"agentid":2,"auth_type":1}
            ],
            "auth_info":{
                "department":[
                    {
                        "id":"2",
                        "writable":"true"
                    }
                ]
            }
     * }
     *
     *  参数	        说明
        usertype	登录用户的类型：1.创建者 2.内部系统管理员 3.外部系统管理员 4.分级管理员 5.成员
        user_info	登录用户的信息
        userid	    登录用户的userid，登录用户在通讯录中时返回
        name	    登录用户的名字，登录用户在通讯录中时返回
        avatar	    登录用户的头像，登录用户在通讯录中时返回
        email	    登录用户邮箱
        corp_info	授权方企业信息
        corpid	    授权方企业id
        agent	    该管理员在该提供商中能使用的应用列表，当登录用户为管理员时返回
        agentid	    应用id
        auth_type	该管理员对应用的权限：1.管理权限，0.使用权限
        auth_info	该管理员拥有的通讯录权限，当登录用户为管理员时返回
     */
    public function __construct(array $info)
    {
        $this->usertype = $info['usertype'];
        $this->user_info = $info['user_info'];
        $this->corp_info = $info['corp_info'];
        $this->agent = $info['agent'];
        if (isset($info['auth_info'])) {
            $this->auth_info = $info['auth_info'];
        }

        $this->raw_data = $info;
    }


    /**
     * Returns the identifier of the authorized resource owner.
     *
     * @return mixed
     */
    public function getId()
    {
        return $this->corp_info['corpid'] . '_' . $this->user_info['userid'];
    }

    public function getNick()
    {
        return $this->user_info['name'];
    }

    /**
     * Return all of the owner details available as an array.
     *
     * @return array
     */
    public function toArray()
    {
        return $this->raw_data;
    }
}