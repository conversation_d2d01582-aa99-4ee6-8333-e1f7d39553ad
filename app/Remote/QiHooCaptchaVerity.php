<?php

namespace App\Remote;

use App\Constants\Http;
use App\Util\LogHelper;
use App\Util\LogType;

class QiHooCaptchaVerity extends CommonService
{
    const CHECK_CAPTCHA_VERITY = "internal_api/account/check_captcha";

    protected $web_service_uris = [
        self::CHECK_CAPTCHA_VERITY
    ];

    public static function checkCaptcha($token, $vd): bool
    {
        if (!$token || !$vd)
        {
            return false;
        }
        try {
            $handler = new self();
            $result = $handler->request(
                Http::METHOD_POST,
                self::CHECK_CAPTCHA_VERITY,
                [
                    'body' => [
                        'token' => $token,
                        'vd' => $vd,
                    ]
                ]);
            if (!$result || !$result['success'] || $result['success'] = false)
            {
                return false;
            }
            return $result['flag'];
        } catch (\Exception $exception) {
            LogHelper::error(LogType::RemoteCallException, $exception->getMessage(),
                ['checkCaptcha exception' => LogHelper::serializeException($exception)]);
            return false;
        }
    }
}