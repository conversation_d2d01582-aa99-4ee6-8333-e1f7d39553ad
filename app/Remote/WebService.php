<?php

namespace App\Remote;

use App\Constants\Http;
use App\Exceptions\RemoteCallException;
use App\Library\HttpClient\HttpClientException;
use App\Library\HttpClient\WebClient;
use App\Util\CommonHelper;
use App\Util\LogHelper;
use App\Util\LogType;

class WebService
{
    protected static $client = null;
    protected $product_id = null;

    public function __construct($params = [])
    {
        $this->product_id = $params['product_id'] ?? null;

        if (self::$client === null) {
            $service_id = config('services.internal_service_id');
            $web_service_id = config('services.web_service_id');
            $base_url = config('app.api_url');
            self::$client = new WebClient($service_id, $web_service_id, $base_url);
        }
    }

    /**
     * @param $method
     * @param string $uri
     * @param array $options
     * @return mixed
     * @throws RemoteCallException
     */
    public function request($method, $uri = '', array $options = [])
    {
        LogHelper::info(LogType::WebAndServerService, 'WebService\\request\\request', ['method' => $method, 'uri' => $uri, 'options' => $options]);
        try {
            $response = self::$client->requestRemote($method, $uri, $options);
        } catch (HttpClientException $exception) {
            throw RemoteCallException::createFrom($exception);
        }
        LogHelper::info(LogType::WebAndServerService, 'WebService\\request\\response', ['response' => $response]);
        return $response;
    }

    /**
     * @param $method
     * @param string $uri
     * @param array $options
     * @return mixed
     * @throws RemoteCallException
     */
    public function requestWithProductid($method, $uri = '', array $options = [])
    {
        if (!isset($options['headers'][Http::HEADER_PRODUCT_ID])) {
            $options['headers'][Http::HEADER_PRODUCT_ID] = $this->product_id ?: CommonHelper::getProductId();
        }
        return $this->request($method, $uri, $options);
    }
}