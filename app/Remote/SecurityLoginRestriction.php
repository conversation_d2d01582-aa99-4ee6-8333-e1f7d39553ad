<?php
/**
 * Created by IntelliJ IDEA.
 * User: ltq
 * Date: 2019/7/10
 * Time: 3:18 PM
 */

namespace App\Remote;

use App\Constants\Http;

class SecurityLoginRestriction extends WebService
{
    private const URL_SECURITY_LOGIN_CHECK = 'internal_api/security_login_check/is_pass_security_login';

    public function is_pass_check($user_id, $check_params)
    {

        $res = $this->request(
            Http::METHOD_POST,
            self::URL_SECURITY_LOGIN_CHECK,
            [
                'body' => [
                    'user_id' => $user_id,
                    'check_param' => $check_params
                ],
            ]
        );
        return $res;
    }
}