<?php

namespace App\Remote;

use App\Constants\Http;
use App\Exceptions\InternalException;
use App\Util\CommonHelper;

/**
 * Class Chameleon
 * @package App\Remote
 *
 */
class Chameleon extends CommonService
{
    protected static $remote_interface = 'com.fangcloud.service.simba.common.api.service.ChameleonService';

    const URI_GET_SSO_PRODUCT_ID = "service/simba/chameleon/sso_config/get_product_id";
    const URI_GET_DOMAIN_CONFIG = "service/simba/chameleon/domain_config/get/%s";
    const URI_GET_SSO_CONFIGS = "service/simba/chameleon/sso_config/get_all";

    public static function get_all_sso_configs(){
        $handler = new static();
        $res = $handler->request(Http::METHOD_GET, self::URI_GET_SSO_CONFIGS, []);
        if (!empty($res)) {
            $new_sso_configs = [];
            foreach ($res['sso_configs'] as $sso_config) {
                $new_sso_configs[$sso_config['product_id']] = $sso_config;
            }
            return $new_sso_configs;
        }
        return [];
    }

    public static function get_product_id($platform_id)
    {
        $handler = new static();
        $res = $handler->request(Http::METHOD_GET,self::URI_GET_SSO_PRODUCT_ID, [
            'query' => [
                'platform_id' => $platform_id,
            ]
        ]);

        return $res;
    }

    public static function get_domain_config($product_id)
    {
        $handler = new static();
        $res = $handler->request(Http::METHOD_GET, sprintf(self::URI_GET_DOMAIN_CONFIG, $product_id), []);
        return $res;
    }
}