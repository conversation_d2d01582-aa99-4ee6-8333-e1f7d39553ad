<?php

namespace App\Remote;

use App\Exceptions\RemoteCallException;
use App\Constants\CacheKey;
use App\Util\CacheHelper;


class YunPan360 extends CommonService
{

    public function __construct(array $params = [])
    {
        $params = [
            'host'                => config('yunpan360.host'),
            'client_id'           => config('yunpan360.client_id'),
            'client_secret'       => config('yunpan360.client_secret'),
        ];
        parent::__construct($params);
    }

    public static function createAccountByMobile($mobile)
    {
        $accessToken = CacheHelper::getCachedInfo(sprintf(CacheKey::GROUP_PAN_ACCESS_TOKEN, $mobile));
        if ($accessToken) {
            return $accessToken;
        }
        $handle = new self();
        $url = sprintf(
            "%s/intf.php?client_id=%s&client_secret=%s&grant_type=authorization_code&mobile=%s&method=Oauth.getAccessTokenByMobile",
            $handle->getAttribute('host'),
            $handle->getAttribute('client_id'),
            $handle->getAttribute('client_secret'),
            $mobile
        );
        $response = json_decode(file_get_contents($url), true);

        if ($response['errno'] > 0) {
            throw new RemoteCallException('Failed to retrieve access token: ' . $response['errmsg']);
        }

        if (isset($response['data'])) {
            $access_token_expire = $response['data']['access_token_expire'] - (time() + 20);
            CacheHelper::cacheInfo(sprintf(CacheKey::GROUP_PAN_ACCESS_TOKEN, $mobile), $response['data']['access_token'], $access_token_expire);
        }
        

        return $response['data']['access_token'];
    }

    public static function getAccountNameByToken($mobile, $accessToken) {
        $handle = new self();
        $url = sprintf(
            "%s/intf.php?mobile=%s&method=User.getUserDetailByMobile",
            $handle->getAttribute('host'),
            $mobile
        );
        $headers = [
            "Access-Token: " . $accessToken,
        ];
        
        $options = [
            "http" => [
                "header" => $headers,
                "method" => "GET",
            ],
        ];
        $context = stream_context_create($options);
        $response = file_get_contents($url, false, $context);
        
        $responseData = json_decode($response, true);
        
        if (isset($responseData['data']['nickName']) && !empty($responseData['data']['nickName'])) {
            return $responseData['data']['nickName'];
        }
        
        return  "";

    }
}
