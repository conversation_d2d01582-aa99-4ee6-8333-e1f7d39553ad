<?php

namespace App\Remote;

use App\Constants\Http;
use App\Util\LogHelper;
use App\Util\LogType;

class CheckCaptchaSwitch extends CommonService
{
    const CHECK_CAPTCHA_SWITCH = "internal_api/account/check_captcha_switch";

    protected $web_service_uris = [
        self::CHECK_CAPTCHA_SWITCH
    ];

    public static function checkCaptchaSwitch(): bool
    {
        try {
            $handler = new self();
            $result = $handler->request(
                Http::METHOD_POST,
                self::CHECK_CAPTCHA_SWITCH,
                [
                    'body' => [

                    ]
                ]);
            if (!$result || !$result['success'] || $result['success'] = false)
            {
                return false;
            }
            return $result['sliding_verify_enable'];
        } catch (\Exception $exception) {
            LogHelper::error(LogType::RemoteCallException, $exception->getMessage(),
                ['checkCaptchaSwitch exception' => LogHelper::serializeException($exception)]);
            return false;
        }
    }
}