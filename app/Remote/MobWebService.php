<?php

namespace App\Remote;

use App\Constants\Http;
use App\Exceptions\RemoteCallException;
use App\Library\HttpClient\HttpClientException;
use App\Library\HttpClient\WebClient;
use App\Util\CommonHelper;
use App\Util\LogHelper;
use App\Util\LogType;

class MobWebService
{
    protected static $client = null;

    public function __construct()
    {
        if (self::$client === null) {
            $service_id = config('services.internal_service_id');
            $web_service_id = config('services.web_service_id');
            $base_url = config('app.mob_url');
            self::$client = new WebClient($service_id, $web_service_id, $base_url);
        }
    }

    /**
     * @param $method
     * @param string $uri
     * @param array $options
     * @return mixed
     * @throws RemoteCallException
     */
    public function request($method, $uri = '', array $options = [])
    {
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $uri);
        curl_setopt($curl, CURLOPT_HEADER, 0);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($curl, CURLOPT_POST, 0);
        $post_data = array(
            "appkey" => $appkey,
            "token" => $token,
            "opToken" => $opToken,
            'operator'=> $operator,
            'timestamp'=> 1564046825531
        );
        if ($md5 != '') {
            $post_data['md5'] = $md5;
        }
        $post_data['sign'] = getSign($post_data, $appSecret);
        $jsonStr = json_encode($post_data);
        curl_setopt($curl, CURLOPT_POSTFIELDS, $jsonStr);
        curl_setopt($curl, CURLOPT_HTTPHEADER, array(
                'Content-Type: application/json; charset=utf-8',
                'Content-Length: ' . strlen($jsonStr)
            )
        );
//执行命令
        $data = curl_exec($curl);
//关闭URL请求
        curl_close($curl);
        $json = json_decode($data);

        if ($json->status == 200) {
            $d = openssl_decrypt($json->res, "DES-CBC", $appSecret, 0, "00000000");
//    $des = new CryptDes($appSecret);
//    $json->res = $des->decrypt($json->res);
            $json->res = json_decode($d, true);
        }
//显示获得的数据
        print_r($json);
    }
}