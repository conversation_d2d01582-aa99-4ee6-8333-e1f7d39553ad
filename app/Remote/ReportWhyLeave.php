<?php
namespace App\Remote;

use App\Constants\Http;
use App\Util\Captcha;

/**
 * <AUTHOR> <PERSON><PERSON> <<EMAIL>>
 */
class ReportWhyLeave extends WebService
{
    private const URL_POST_WHY_LEAVE = 'internal_api/account/report_why_leave';

    public function report($data)
    {
        if (!Captcha::check($data['captcha'] ?? '', Captcha::TYPE_REGISTER, Captcha::THROUGH_SMS, $data['phone'] ?? '')) {
            return;
        }
        $password = $data['password'] ?? '';
        return $this->request(Http::METHOD_POST,
            self::URL_POST_WHY_LEAVE,
            [
                'body' => [
                    'phone' => $data['phone'] ?? '',//手机号
                    'captcha' => $data['captcha'] ?? '',//验证码
                    'password' => str_repeat('*', strlen($password)),//密码
                    'user_name' => $data['user_name'] ?? '',//用户名
                    'company_name' => $data['company_name'] ?? '',//企业名
                    'motivation' => $data['motivation'] ?? '',//动机
                ]
            ]
        );
    }
}