<?php

namespace App\Remote;

use App\Constants\Common;
use App\Constants\Http;
use App\Constants\Plan;
use App\Util\CommonHelper;
use App\Util\LogHelper;
use App\Util\LogType;

/**
 * Class AuthToken
 * @package App\Remote
 *
 */
class AuthToken extends CommonService
{
    protected static $remote_interface = 'com.fangcloud.corps.api.service.AuthTokenService';

    const URL_CREATE_AUTH_TOKEN = "service/corps/auth_token/create";
    const URL_GET_AUTH_TOKEN = "service/corps/auth_token/get";
    const URL_DELETE_AUTH_TOKEN = "service/corps/auth_token/delete";
    const URL_GET_AUTH_TOKEN_BY_REFRESH = "service/corps/auth_token/get_by_refresh";
    const URL_REFRESH_AUTH_TOKEN = "service/corps/auth_token/refresh";

    protected $web_service_uris = [];

    public static $base_attributes = [
        'id' => 'id',
        'auth_token' => 'authToken',
        'user_id' => 'userId',
        'login_user_id' => 'loginUserId',
        'expires_at' => 'expiresAt',
        'refresh_token' => 'refreshToken',
        'service_id' => 'serviceId',
        'created' => 'created',
        'updated' => 'updated'
    ];

    private static function query($url, $body) {
        $ent = new self();
        return $ent->request(
            Http::METHOD_POST,
            $url,
            [
                'body' => $body,
                'skip_add_context' => true
            ]);
    }

    public static function get($authToken)
    {
        $body = [
            'authToken' => $authToken
        ];
        $res = static::query(self::URL_GET_AUTH_TOKEN, $body);
        LogHelper::info(LogType::WebAndServerService, "auth_token:".json_encode($res));
        $remote = $res['data']['authToken'];
        if ($remote) {
            $auth_token = new \App\Models\AuthToken();
            foreach (self::$base_attributes as $key => $value) {
                $auth_token->$key = $remote[$value];
            }
            return $auth_token;
        }
        return null;
    }

    public static function delete($id)
    {
        $body = [
            'id' => $id
        ];
        static::query(self::URL_DELETE_AUTH_TOKEN, $body);
    }

    public static function create($userId, $loginUserId, $serviceId)
    {
        $body = [
            'userId' => $userId,
            'loginUserId' => $loginUserId,
            'serviceId' => $serviceId,
        ];
        $res = static::query(self::URL_CREATE_AUTH_TOKEN, $body);
        $remote = $res['data']['authToken'];
        $auth_token = new \App\Models\AuthToken();
        foreach (self::$base_attributes as $key => $value) {
            $auth_token->$key = $remote[$value];
        }
        return $auth_token;
    }

    public static function refresh($refresh_token)
    {
        $body = [
            'refreshToken' => $refresh_token,
        ];
        $res = static::query(self::URL_REFRESH_AUTH_TOKEN, $body);
        $remote = $res['data']['authToken'];
        if ($remote) {
            $auth_token = new \App\Models\AuthToken();
            foreach (self::$base_attributes as $key => $value) {
                $auth_token->$key = $remote[$value];
            }
            return $auth_token;
        }
        return null;
    }

    public static function getByRefreshToken($refresh_token)
    {
        $body = [
            'refreshToken' => $refresh_token,
        ];
        $res = static::query(self::URL_GET_AUTH_TOKEN_BY_REFRESH, $body);
        $remote = $res['data']['authToken'];
        if ($remote) {
            $auth_token = new \App\Models\AuthToken();
            foreach (self::$base_attributes as $key => $value) {
                $auth_token->$key = $remote[$value];
            }
            return $auth_token;
        }
        return null;
    }
}
