<?php

namespace App\Remote;

use App\Constants\Http;
use App\Exceptions\ExternalException;

class OpenApiTokenInfo extends WebService
{
    private const URL_GET_OPEN_API_TOKEN_INFO = '/internal_api/account/get_open_api_token_info';
    private const URL_REFRESH_OPEN_API_TOKEN_INFO = '/internal_api/account/refresh_open_api_token_info';


    public function __construct()
    {
        parent::__construct();

    }

    /**
     * @param $phone
     * @param $code
     * @param string $type
     * @throws ExternalException
     * @throws \App\Exceptions\InternalException
     */


    /**
     * @param $userId
     * @return \Closure
     */
    public function getOpenApiTokenInfo($userId)
    {
        $body = [
            'body' => [
                'user_id' => $userId,
            ],
        ];
        return $this->_send(self::URL_GET_OPEN_API_TOKEN_INFO, $body);
    }


    /**
     * @param $userId
     * @return \Closure
     */
    public function refreshOpenApiTokenInfo($userId, $dueTimeType)
    {
        $body = [
            'body' => [
                'user_id' => $userId,
                'due_time_type' => $dueTimeType,
            ],
        ];
        return $this->_send(self::URL_REFRESH_OPEN_API_TOKEN_INFO, $body);
    }

    private function _send($url, $body)
    {

         $result = $this->request(
             Http::METHOD_POST,
             $url,
             $body
         );

        return (array) $result;
    }


}