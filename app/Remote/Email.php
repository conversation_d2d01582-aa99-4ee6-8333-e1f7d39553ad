<?php

namespace App\Remote;

use App\Constants\Http;

class Email extends WebService
{
    private const URL_NEW_DEVICE = 'internal_api/account/send_new_device_email';
    private const URL_TWO_STEP = 'internal_api/account/send_two_step_email';
    private const URL_RESET_PASSWORD = 'internal_api/account/send_reset_password_email';


    /**
     * @param $to
     * @param $device_type
     * @throws \App\Exceptions\RemoteCallException
     */
    public function sendNewDevice($to, $device_type)
    {
        $this->requestWithProductid(Http::METHOD_POST, self::URL_NEW_DEVICE, [
            'body' => [
                'to' => $to,
                'device_type' => $device_type
            ],
        ]);
    }

    /**
     * @param $to
     * @param $status
     * @param string $method
     * @param string $lang
     * @throws \App\Exceptions\RemoteCallException
     */
    public function sendTwoStep($to, $status, $method = '', $lang = '')
    {
        $this->requestWithProductid(Http::METHOD_POST, self::URL_TWO_STEP, [
            'body' => [
                'to' => $to,
                'status' => $status,
                'method' => $method,
                'lang' => $lang,
            ],
        ]);
    }

    /**
     * @param $to
     * @param $url
     * @throws \App\Exceptions\RemoteCallException
     */
    public function sendResetPassword($to, $url)
    {
        $this->requestWithProductid(Http::METHOD_POST, self::URL_RESET_PASSWORD, [
            'body' => [
                'to' => $to,
                'url' => $url,
            ],
        ]);
    }
}