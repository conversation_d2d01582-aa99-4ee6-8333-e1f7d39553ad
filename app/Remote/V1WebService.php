<?php

namespace App\Remote;

use App\Constants\Http;
use App\Exceptions\RemoteCallException;
use App\Library\HttpClient\HttpClientException;
use App\Library\HttpClient\WebClient;
use App\Util\CommonHelper;
use App\Util\LogHelper;
use App\Util\LogType;

class V1WebService
{
    protected static $client = null;

    public function __construct()
    {
        if (self::$client === null) {
            $service_id = config('services.internal_service_id');
            $web_service_id = config('services.web_service_id');
            $base_url = config('app.is_profession') ? config('app.api_url') : config('app.v1_url');
            self::$client = new WebClient($service_id, $web_service_id, $base_url);
        }
    }

    /**
     * @param $method
     * @param string $uri
     * @param array $options
     * @return mixed
     * @throws RemoteCallException
     */
    public function request($method, $uri = '', array $options = [])
    {
        LogHelper::info(LogType::WebAndServerService, 'V1WebService\\request\\request', ['method' => $method, 'uri' => $uri, 'options' => $options]);
        try {
            $response = self::$client->requestRemote($method, $uri, $options);
        } catch (HttpClientException $exception) {
            throw RemoteCallException::createFrom($exception);
        }
        LogHelper::info(LogType::WebAndServerService, 'V1WebService\\request\\response', ['response' => $response]);
        return $response;
    }

    /**
     * @param $method
     * @param string $uri
     * @param array $options
     * @return mixed
     * @throws RemoteCallException
     */
    public function requestWithProductid($method, $uri = '', array $options = [])
    {
        if (!isset($options['headers'][Http::HEADER_PRODUCT_ID])) {
            $options['headers'][Http::HEADER_PRODUCT_ID] = CommonHelper::getProductId();
        }
        return $this->request($method, $uri, $options);
    }
}