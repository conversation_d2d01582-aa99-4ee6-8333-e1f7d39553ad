<?php

namespace App\Remote;

class CommonService extends ServerService
{
    /**
     * @var WebService $web_service
     */
    protected $web_service;

    protected $attributes = [];

    /**
     * 使用 web service 的请求
     * @var array
     */
    protected $web_service_uris = [];

    protected static $remote_interface;

    public function __construct(array $params = [])
    {
        if ($this->web_service_uris) {
            $this->web_service = new WebService();
        }

        parent::__construct();

        $this->init($params);
    }

    /**
     * @param string $method
     * @param string $uri
     * @param array $options
     * @return mixed
     * @throws \App\Exceptions\RemoteCallException
     */
    public function request($method = 'GET', $uri = '', array $options = [])
    {
        if (in_array($uri, $this->web_service_uris)) {
            return $this->web_service->request($method, $uri, $options);
        } else {
            // 默认都去请求服务
            return parent::request($method, $uri, $options);
        }
    }

    public function getAttribute($key)
    {
        return $this->attributes[$key] ?? null;
    }

    public function toArray()
    {
        return $this->attributes;
    }

    protected function init(array $params = [])
    {
        foreach($params as $key => $value) {
            $this->attributes[$key] = $value;
        }
    }
}