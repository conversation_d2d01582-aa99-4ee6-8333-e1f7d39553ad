<?php

namespace App\Remote;
/**
 * Class User
 * @package App\Remote
 *
 * @property int id
 * @property string name
 */
trait GetInfo
{
    public function getInfo($fields = [])
    {
        $single_field = '';
        if(!$fields) {
            $fields = static::$basic_attributes;
        }
        elseif(!is_array($fields)) {
            $single_field = $fields;
            $fields = [$fields];
        }
        $fields_lost = [];
        $result = [];
        foreach ($fields as $field) {
            if(!isset($this->attributes[$field])) {
                $fields_lost[] = $field;
            }
            else {
                $result[$field] = $this->attributes[$field];
            }
        }
        if($fields_lost) {
            $result = array_merge($result, $this->fetchInfo($fields_lost));
        }
        return $single_field ? ($this->attributes[$single_field] ?? null) : $result;
    }
}