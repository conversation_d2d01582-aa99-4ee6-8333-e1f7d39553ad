<?php

namespace App\Remote;

use App\Constants\Http;
use App\Library\Janitor\RateWithFreezeJanitor;
use App\Library\TwoStep;

class V1Account extends V1WebService
{
    private const URL_LOGIN = 'internal_api/account/v1_login';
    private const URL_FORGOT_PASSWORD = 'internal_api/account/v1_forgot_password';
    private const URL_ACTIVATE = 'internal_api/account/v1_activate_by_phone';

    public function __construct()
    {
        parent::__construct();
    }

    public function login($params)
    {
        return $this->request(
            Http::METHOD_POST,
            self::URL_LOGIN,
            [
                'body' => $params,
            ]);
    }

    public function forgot_password($params)
    {
        return $this->request(
            Http::METHOD_POST,
            self::URL_FORGOT_PASSWORD,
            [
                'body' => $params,
            ]);
    }

    public function activate($params)
    {
        return $this->request(
            Http::METHOD_POST,
            self::URL_ACTIVATE,
            [
                'body' => $params,
            ]);
    }
}