<?php

namespace App\Remote;

use App\Constants\Http;
use App\Util\LogHelper;
use App\Util\LogType;

class AliyunCaptchaVerity extends CommonService
{
    const CHECK_CAPTCHA_VERITY = "internal_api/account/check_aliyun_captcha";

    protected $web_service_uris = [
        self::CHECK_CAPTCHA_VERITY
    ];

    public static function checkCaptcha($requestId): bool
    {
        if (is_null($requestId))
        {
            return false;
        }
        try {
            $handler = new self();
            $result = $handler->request(
                Http::METHOD_POST,
                self::CHECK_CAPTCHA_VERITY,
                [
                    'body' => [
                        'request_id' => $requestId
                    ]
                ]);
            if (!$result || !$result['success'] || $result['success'] = false)
            {
                return false;
            }
            return $result['flag'];
        } catch (\Exception $exception) {
            LogHelper::error(LogType::RemoteCallException, $exception->getMessage(),
                ['aliyun checkCaptcha exception' => LogHelper::serializeException($exception)]);
            return false;
        }
    }
}