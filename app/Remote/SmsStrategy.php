<?php

namespace App\Remote;

use App\Constants\Http;
use App\Util\LogHelper;
use App\Util\LogType;

class SmsStrategy extends CommonService
{

    protected static $remote_interface = 'com.fangcloud.hermes.facade.SmsStrategyService';

    const URL_SMS_STRATEGY_INFO = "service/hermes/sms_strategy/info";

    private static function query($url, $body) {
        $ent = new self();
        return $ent->request(
            Http::METHOD_POST,
            $url,
            [
                'body' => $body,
                'skip_add_context' => true
            ]);
    }

    public static function hitSmsStrategy($captchaType, $countryCode, $phone)
    {
        $body = [
            'captchaType' => $captchaType,
            'countryCode' => $countryCode,
            'phone' => $phone
        ];
        $hit_strategy = false;
        try {
            $res = static::query(self::URL_SMS_STRATEGY_INFO, $body);
            LogHelper::info(LogType::WebAndServerService, "sms strategy :".json_encode($res));
            $hit_strategy = $res['data']['hitStrategy'];
        } catch (\Throwable $throwable) {
            LogHelper::error(LogType::RemoteCallException, $throwable->getMessage(), ['exception' => LogHelper::serializeException($throwable)]);
        }
        return $hit_strategy;
    }

}