<?php

namespace App\Remote;

use App\Constants\Http;
use App\Library\Janitor\RateWithFreezeJanitor;
use App\Library\TwoStep;

final class Wechat extends WebService
{
    private const URL_SEND_CODE = '/internal_api/account/two_step_send_code';

    private static $janitor = null;

    private const MSG_NORMAL = 'normal';

    private const MSG_FREEZE = 'freeze';

    public function __construct()
    {
        parent::__construct();

        self::$janitor = (new RateWithFreezeJanitor())->setFreezeTime(600)->setThresholdCount(10)->setThresholdTime(600);
    }

    /**
     * @param $wechat_id
     * @param $user_login
     * @param $code
     * @throws \App\Exceptions\InternalException
     */
    public function send($wechat_id, $user_login, $code)
    {
        if (!self::$janitor->setIdentifier($wechat_id)->fire($this->_send($wechat_id, $user_login, $code))) {
            (new RateWithFreezeJanitor())
                ->setFreezeTime(600)
                ->setThresholdCount(5)
                ->setThresholdTime(600)
                ->setIdentifier('freeze_' . $wechat_id)
                ->fire(
                    $this->_sendFreeze($wechat_id, $user_login)
                );
        }
    }

    private function _send($wechat_id, $user_login, $code)
    {
        return function() use($wechat_id, $user_login, $code) {
            $this->request(
                Http::METHOD_POST,
                self::URL_SEND_CODE,
                [
                    'body' => [
                        'id' => $wechat_id,
                        'user_name' => $user_login,
                        'type' => TwoStep::METHOD_WECHAT,
                        'wechat_type' => self::MSG_NORMAL,
                        'code' => $code,
                    ],
                ]);
        };
    }

    private function _sendFreeze($wechat_id, $user_login)
    {
        return function() use ($wechat_id, $user_login) {
            $this->request(
                Http::METHOD_POST,
                self::URL_SEND_CODE,
                [
                    'body' => [
                        'id' => $wechat_id,
                        'user_name' => $user_login,
                        'type' => TwoStep::METHOD_WECHAT,
                        'wechat_type' => self::MSG_FREEZE,
                    ],
                ]
            );
        };
    }
}