<?php

namespace App\Remote;
use App\Constants\Http;

/**
 * Class User
 * @package App\Remote
 *
 * @property int id
 * @property string name
 * @property Enterprise enterprise
 * @property int enterprise_id
 * @property int country_code
 * @property int phone
 * @property string lang
 * @property string email
 * @property string login
 * @property bool is_demo_user
 * @property bool has_payment_management
 */
class User extends CommonService
{
    use GetInfo;

    protected static $remote_interface = 'com.fangcloud.service.organization.common.service.UserService';

    const URL_CREATE_USER = "service/organization/user/create";
    const URL_GET_USER_INFO = "service/organization/user/get";
    const URL_UPDATE_USER_INFO = "service/organization/user/update";
    const URL_ACTIVATE_USER = "service/organization/user/activate";

    const URL_WEB_GET_USER_INFO = "internal_api/account/get_user_info";
    const URL_WEB_CHECK_IDENTIFIER_EXISTENCE = "internal_api/account/check_identifier_existence";
    const URL_WEB_DEAL_WITH_USER_CREATED = "internal_api/account/deal_with_user_created";
    const URL_WEB_REVOKE_DEVICE_TOKEN = "internal_api/account/revoke_device_token";
    const URL_WEB_DEAL_WITH_USER_LOGIN = "internal_api/account/deal_with_user_login";
    const URL_WEB_DEAL_WITH_USER_INIT_PASSWORD = "internal_api/account/deal_with_user_init_password";

    protected $web_service_uris = [
        self::URL_WEB_GET_USER_INFO,
        self::URL_WEB_CHECK_IDENTIFIER_EXISTENCE,
        self::URL_WEB_DEAL_WITH_USER_CREATED,
        self::URL_WEB_REVOKE_DEVICE_TOKEN,
        self::URL_WEB_DEAL_WITH_USER_LOGIN,
        self::URL_WEB_DEAL_WITH_USER_INIT_PASSWORD
    ];

    const USER_GROUP_ADMIN = 'admin';
    const USER_GROUP_USER = 'user';

    protected $_bulk_update = false;
    protected $_update_infos = [];

    public static $basic_attributes = [
        'name',
        'username',
        'user_group',
        'email',
        'phone',
        'name_first_letter',
        'full_name_pinyin',
        'enterprise_id',
        'profile_pic_key',
        'is_active',
        'email_verified_at',
        'phone_verified_at',
        'is_personal_user',
        'is_demo_user',
        'is_phone_public',
        'is_personal_space_closed',
        'is_frozen',
        'is_v2_enabled',
        'lang',
        'is_login_disabled',
        'is_delete_account',
    ];

    private static $attributes_from_web = [
        'company_name',
        'current_user_type',
        'login',

        'trash_period',
        'unread_message_count',
        'colleague_count',
        'can_invite',
        'enterprise_admin_user_real_name_identity_status',
        'speed_limit_info',

//        'is_new_device_verification_enabled',
//        'is_collab_related_functions_restricted',

        'enterprise' => 'app_enterprise_info',
        'has_department',
        'has_payment_management',

        'space_used',
        'space_total',
    ];


    protected function init(array $user = [])
    {
        if (isset($user['settings'])) {
            $new_user = array_merge($user, $user['settings']);
        } else {
            $new_user = $user;
        }

        foreach($new_user as $key => $value) {
            if ($key === 'enterprise') {
                continue;
            }
            $this->attributes[$key] = $value;
        }
    }

    public function prepareUpdate()
    {
        $this->_bulk_update = true;
        $this->_update_infos = [];
    }

    public function commitUpdate()
    {
        $this->_bulk_update = false;
        if(!$this->_update_infos) {
            return;
        }
        $merged_info = ['settings' => []];
        $merged_info = array_reduce($this->_update_infos, function($a, $b) {
            $settings = $a['settings'];
            if(isset($b['settings'])) {
                $settings = array_merge($a['settings'], $b['settings']);
            }
            $res = array_merge($a, $b);
            $res['settings'] = $settings;
            return $res;
        }, $merged_info);
        $this->_update_infos = [];
        $this->updateInfo($merged_info);
    }

    /**
     * @param $user_params
     * @return User
     * @throws \App\Exceptions\RemoteCallException
     */
    public static function create($user_params)
    {
        $handler = new self();
        $res = $handler->request(
            Http::METHOD_POST,
            self::URL_CREATE_USER,
            [
                'body' => [
                    'user' => $user_params,
                ]
            ]);
        $user = new User($res['user']);
        return $user;
    }

    /**
     * @param array $fields
     * @return mixed
     * @throws \App\Exceptions\RemoteCallException
     */
    private function fetchInfo($fields = [])
    {
        $fields_from_web = array_intersect(self::$attributes_from_web, $fields);
        $fields_from_service = array_diff($fields, $fields_from_web);

        $return = [];
        if ($fields_from_service) {
            $res = $this->request(
                Http::METHOD_POST,
                self::URL_GET_USER_INFO,
                [
                    'body' => [
                        'user_id' => $this->id,
                        'setting_keys' => $fields,
                    ]
                ]);
            $this->init($res['user']);
            $return = $res['user'];
        }

        if ($fields_from_web) {
            $res = $this->request(
                Http::METHOD_POST,
                self::URL_WEB_GET_USER_INFO,
                [
                    'body' => [
                        'user_id' => $this->id,
                        'fields' => $fields_from_web
                    ],
                ]
            );
            $web_user = $res['user'];
            foreach ($fields_from_web as $field) {
                if(!array_key_exists($field, $web_user)) {
                    $web_user[$field] = null;
                }
            }
            $this->init($web_user);
            $return = array_merge($return, $web_user);
        }

        return $return;
    }

    /**
     * @return mixed|\Psr\Http\Message\ResponseInterface
     * @throws \App\Exceptions\RemoteCallException
     */
    public function activate()
    {
        $res = $this->request(
            Http::METHOD_POST,
            self::URL_ACTIVATE_USER,
            [
                'body' => [
                    'user_id' => $this->id,
                ]
            ]);
        return $res;
    }

    /**
     * @param array $info
     * @return mixed|\Psr\Http\Message\ResponseInterface
     * @throws \App\Exceptions\RemoteCallException
     */
    public function updateInfo(array $info) : void
    {
        if($this->_bulk_update) {
            $this->_update_infos[] = $info;
            return;
        }
        $info['id'] = $this->id;
        $this->request(
            Http::METHOD_POST,
            self::URL_UPDATE_USER_INFO,
            [
                'body' => [
                    'user' => $info,
                ]
            ]);
    }

    /**
     * @param $type
     * @param null $verified_at
     * @return mixed|\Psr\Http\Message\ResponseInterface
     * @throws \App\Exceptions\RemoteCallException
     */
    public function verifyContact($type, $verified_at = null) : void
    {
        $info = [
            'settings' => [
                $type . '_verified' => true,
                $type . '_verified_at' => $verified_at ?: time(),
                'identity_type_' . $type . '_available' => true,
            ],
        ];
        $this->updateInfo($info);
    }

    /**
     * @param $type
     * @return mixed|\Psr\Http\Message\ResponseInterface
     * @throws \App\Exceptions\RemoteCallException
     */
    public function unverifyContact($type) : void
    {
        $info = [
            'settings' => [
                $type . '_verified' => false,
                $type . '_verified_at' => null
            ],
        ];
        $this->updateInfo($info);
    }

    public function setLoginTypeAvailable($type) : void
    {
        $info = [
            'settings' => [
                'identity_type_' . $type . '_available' => true,
            ],
        ];
        $this->updateInfo($info);
    }

    public function setLoginTypeUnavailable($type) : void
    {
        $info = [
            'settings' => [
                'identity_type_' . $type . '_available' => false,
            ],
        ];
        $this->updateInfo($info);
    }

    public function getName()
    {
        if(!$this->name) {
            $this->getInfo('name');
        }
        return $this->name;
    }

    public static function checkIdentifierExistence($identifier)
    {
        $handler = new static();
        return $handler->request(
            Http::METHOD_POST,
            self::URL_WEB_CHECK_IDENTIFIER_EXISTENCE,
            [
                'body' => [
                    'identifier' => $identifier,
                ]
            ]);
    }

    public static function dealWithUserCreated($user_id)
    {
        $handler = new static();
        return $handler->request(
            Http::METHOD_POST,
            self::URL_WEB_DEAL_WITH_USER_CREATED,
            [
                'body' => [
                    'user_id' => $user_id,
                ]
            ]);
    }

    public static function dealWithUserLogin($user_id)
    {
        $handler = new static();
        return $handler->request(
            Http::METHOD_POST,
            self::URL_WEB_DEAL_WITH_USER_LOGIN,
            [
                'body' => [
                    'user_id' => $user_id,
                ]
            ]);
    }

    public static function dealWithUserInitPassword($user_id)
    {
        $handler = new static();
        return $handler->request(
            Http::METHOD_POST,
            self::URL_WEB_DEAL_WITH_USER_INIT_PASSWORD,
            [
                'body' => [
                    'user_id' => $user_id,
                ]
            ]);
    }

    public function getEnterprise()
    {
        if(!isset($this->enterprise)) {
            if($this->enterprise_id) {
                $enterprise = new Enterprise(['id' => $this->enterprise_id]);
                $enterprise->getInfo();
                $this->enterprise = $enterprise;
            }
            else {
                $this->enterprise = Enterprise::generatePersonalEnterprise();
            }
        }
        return $this->enterprise;
    }
    public static function revokeDeviceToken($client_device_id)
    {
        $handler = new static();
        return $handler->request(
            Http::METHOD_POST,
            self::URL_WEB_REVOKE_DEVICE_TOKEN,
            [
                'body' => [
                    'client_device_id' => $client_device_id,
                ]
            ]);
    }

    public function __get($name)
    {
        if(!array_key_exists($name, $this->attributes)) {
            if ($name == 'enterprise') {
                return $this->getEnterprise();
            }
            return $this->getInfo($name);
        }
        else {
            return $this->getAttribute($name);
        }
    }
}