<?php

namespace App\Remote;

use App\Util\CommonHelper;
use App\Util\Context;
use App\Util\UserAgent;

trait RequestContextTrait
{
    public static $path_to_context_names_mapping = [
    ];

    /**
     * 请求 java 服务时需要的 request context
     * actor_type INPUT_USER_TYPE = "user" INPUT_GROUP_TYPE = "group"; INPUT_DEPARTMENT_TYPE = "department";
     *
     * @return array
     */
    protected function addRequestContext()
    {
        return [
            'Request-Context' => json_encode(self::getRequestContext()),
        ];
    }

    /**
     * 获取request context，用于服务间传递context信息
     * @return array
     */
    public static function getRequestContext()
    {
        $web_request = request();
        $current_user = Context::user();
        $current_enterprise = Context::enterprise();
        $user_agent = new UserAgent();
        return [
            'context_name' => $web_request->path(),
            'actor_id' => $current_user ? $current_user->user_id : null,
            'actor_type' => 'user',
            'actor_enterprise_id' => $current_enterprise ? $current_enterprise->id : null,
            'ip_address' => CommonHelper::getClientIp(),
            'timestamp' => round(microtime(true) * 1000),
            'service_id' => $user_agent->getServiceId(),
            'service_name' => $user_agent->getServiceName(),
            'additional_info' => [],
        ];
    }
}