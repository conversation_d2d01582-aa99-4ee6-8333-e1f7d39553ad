<?php

namespace App\Remote;

use App\Constants\Http;

class DedicatedEnterprise extends WebService
{
    private const URL_GET_PRODUCT_ID = 'auth/get_sso_product_id';


    /**
     * @param $alias
     * @throws \App\Exceptions\RemoteCallException
     */
    public function getSsoProductId($alias)
    {
        return $this->request(Http::METHOD_GET, self::URL_GET_PRODUCT_ID . '?' . http_build_query(['alias' => $alias]));
    }
}