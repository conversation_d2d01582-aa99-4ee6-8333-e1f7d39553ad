<?php

namespace App\Remote;

use App\Constants\Common;
use App\Constants\ErrorCode;
use App\Constants\Http;
use App\Constants\Plan;
use App\Exceptions\ExternalException;
use App\Exceptions\RemoteCallException;
use App\Remote\User as RemoteUser;
use App\Util\CommonHelper;

/**
 * Class Enterprise
 * @package App\Remote
 *
 * @property bool is_two_step_enabled
 * @property bool is_login_reminder_system_enabled
 * @property bool is_sso_enterprise
 * @property bool is_public_login_available
 * @property bool is_resource_custom_enterprise
 * @property int plan_id
 * @property int password_length_min
 * @property bool password_strength_require_special_symbol
 * @property bool password_strength_require_capital_letter
 * @property bool is_special_empty_enterprise
 * @property bool is_dlp_server_enabled
 * @property bool is_dlp_client_enabled
 * @property int max_device_count
 * @property bool is_login_exception_reminder_system_enabled
 * @property string login_exception_reminder_user_list
 */
class Enterprise extends CommonService
{
    use GetInfo;

    protected static $remote_interface = 'com.fangcloud.service.organization.common.service.EnterpriseService';

    const URL_CREATE_ENTERPRISE = "service/organization/enterprise/register";
    const URL_STATUS = "service/organization/enterprise/status";
    const URL_GET_ENTERPRISE_INFO = "service/organization/enterprise/get";
    const URL_DOUYIN_REGISTER_ACTIVE = "service/organization/enterprise/douyin_register_active";

    const URL_REGISTER_ENTERPRISE_SOURCE = "internal_api/account/register_enterprise_source";
    const URL_RECORD_SEM = "internal_api/account/record_sem";
    const URL_MOTIVATION_COLLECT = "internal_api/account/motivation_collect";
    const URL_CHECK_DLP_DEVICE = "internal_api/account/check_dlp_device";

    protected $web_service_uris = [
        self::URL_REGISTER_ENTERPRISE_SOURCE,
        self::URL_RECORD_SEM,
        self::URL_MOTIVATION_COLLECT,
        self::URL_CHECK_DLP_DEVICE,
    ];

    protected $admin_user;

    public static $basic_attributes = [
        'id',
        'admin_user_id',
        'name',
        'platform_id',
        'created_at',
        'trial_expires_at',
        'expires_at',
        'plan_id',
        'language_type',
        'password_length_min',
        'password_reset_timeinterval',
        'profile_pic_key',
        'in_trash_period',
        'notification_on_consecutive_failed_logins',
        'password_strength_require_special_symbol',
        'password_strength_require_capital_letter',
        'is_new_device_verification_enabled',
        'is_expired',
        'is_freeze',
        'forbidden_login',

        'department_visible',
        'is_video_enabled',
        'is_audio_enabled',
        'is_new_device_verification_enabled',
        'is_yiqixie_enabled',
        'is_colleague_hidden_in_app',
        'is_file_collection_enable',
        'is_encryption_enabled',
        'is_dlp_server_enabled',
        'is_dlp_client_enabled',
        'max_device_count',

        'is_hybrid_storage_plan',
        'is_private_storage_enabled',
        'is_sso_enterprise',
        'is_account_synchronize_enabled',
        'is_resource_custom_enterprise',
        'is_public_login_available',
        'is_external_login_enabled',

        'is_two_step_enabled',
        'is_login_reminder_system_enabled',
        'is_device_unlimited',
        'is_special_empty_enterprise',
        'is_skip_first_login_email',

        'is_from_wechat_enterprise',
        'seat_limit',
        'is_login_exception_reminder_system_enabled',
        'login_exception_reminder_user_list',
    ];

    /**
     * @param $name
     * @param $admin_user
     * @param $plan_id
     * @param $skip_bind_storage
     * @param $seat_limit
     * @param $expires_at
     * @param $trial_expires_at
     * @throws RemoteCallException
     */
    public static function create($request_id, $name, $admin_user, $plan_id, $skip_bind_storage = false, $seat_limit = null, $expires_at = null, $trial_expires_at = null, $register_position = null, $additional_params = [])
    {
        $body = [
            'admin' => $admin_user,
            'enterprise_name' => $name,
            'plan_id' => $plan_id,
            'skip_bind_storage' => $skip_bind_storage,
            'seat_limit' => $seat_limit,
            'expires_at' => $expires_at,
            'trial_expires_at' => $trial_expires_at,
            'register_position' => $register_position,
            'request_id' => $request_id,
            'additional_info' => $additional_params
        ];
        if (isset($additional_params['use_jd_storage']) && $additional_params['use_jd_storage']) {
            $body['storage_id'] = config('common.jd_storage_id');
            $body['file_server_id'] = config('common.jd_file_server_id');
        }
        if (isset($additional_params['gray_test']) && $additional_params['gray_test']) {
            $body['gray_test'] = true;
        }

        $ent = new self();
        for ($i = 0; $i < 120; $i++) {
            try {
                $res = $ent->request(
                    Http::METHOD_POST,
                    self::URL_CREATE_ENTERPRISE,
                    [
                        'body' => $body
                    ]);
                if ($res['finished']) {
                    $user = new User($res['user']);
                    $user->getInfo();
                    $ret['user'] = $user;
                    $ret['login_user_id'] = $res['loginUserId'];
                    $ret['enterprise'] = new Enterprise(['id' => $user->enterprise_id]);
                    return $ret;
                }
            } catch (\Exception $e) {

            }
            sleep(1);
        }
        throw RemoteCallException::createFrom(new ExternalException(ErrorCode::REMOTE_SERVICE_EXCEPTION));
    }

    public static function generateAdminToRegister($name, $phone, $encrypt_password, $time)
    {
        return [
            'ip_address' => CommonHelper::getClientIp(),
            'name' => $name,
            'phone' => $phone,
            'username' => $phone,
            'encrypt_password' => $encrypt_password,
            'settings' => [
                'phone_verified' => true,
                'phone_verified_at' => $time,
                'identity_type_phone_available' => true,
            ]
        ];
    }

    /**
     * @param array $fields
     * @return mixed
     * @throws RemoteCallException
     */
    public function fetchInfo($fields = [])
    {
        $res = $this->request(Http::METHOD_POST, self::URL_GET_ENTERPRISE_INFO, [
            'body' => [
                'enterprise_id' => $this->id,
                'setting_keys' => $fields,
            ],
        ]);
        $this->init($res['enterprise']);
        return $res['enterprise'];
    }

    public function activeDouYinRegisterEnterprise()
    {
        $res = $this->request(Http::METHOD_POST, self::URL_DOUYIN_REGISTER_ACTIVE, [
            'body' => [
                'enterprise_id' => $this->id
            ],
        ]);
        return $res['success'];
    }

    public static function activeDouYinRegisterEnterpriseByPhone($phone)
    {
        $ent = new self();
        $res = $ent->request(Http::METHOD_POST, self::URL_DOUYIN_REGISTER_ACTIVE, [
            'body' => [
                'phone' => $phone
            ],
        ]);
        return $res['success'];
    }

    protected function init(array $enterprise = [])
    {
        if (isset($enterprise['settings'])) {
            $new_enterprise = array_merge($enterprise, $enterprise['settings']);
        } else {
            $new_enterprise = $enterprise;
        }

        foreach ($new_enterprise as $key => $value) {
            $this->attributes[$key] = $value;
        }
    }

    public static function generatePersonalEnterprise()
    {
        $enterprise = new static();
        $params = [
            'id' => 0,
            'password_length_min' => Common::DEFAULT_PASSWORD_LENGTH_MIN,
            'password_strength_require_special_symbol' => false,
            'password_strength_require_capital_letter' => false,
        ];
        $enterprise->init($params);
        return $enterprise;
    }

    public function isPersonalEnterprise()
    {
        return $this->id == 0;
    }

    public function getPasswordPattern()
    {
        if ($this->isPersonalEnterprise()) {
            $password_pattern = "/^.*";
            $password_pattern .= sprintf("(?=.{%s,})", Common::DEFAULT_PASSWORD_LENGTH_MIN);
            $password_pattern .= ".*$/";
        } else {
            // ^.*(?=.{6,})(?=.*\W)(?=.*[A-Z]).*$
            $password_pattern = "/^.*";
            $password_pattern .= sprintf("(?=.{%s,})", $this->password_length_min);
            if ($this->password_strength_require_special_symbol) {
                $password_pattern .= "(?=.*(\W|_))";
            }
            if ($this->password_strength_require_capital_letter) {
                // i cannot accept that if capital letter is required, lowercase letter is required, too. Production insists so.
                $password_pattern .= "(?=.*[A-Z])(?=.*[a-z])";
            }
            $password_pattern .= ".*$/";
        }
        return $password_pattern;
    }

    public function getDlpEnterpriseMaxDeviceCount()
    {
        if ($this->is_dlp_client_enabled) {
            // 开启dlp后默认设备数为1
            return $this->max_device_count > 0 ? $this->max_device_count : 1;
        }
        return 0;
    }

    public function getMaxDeviceOnlineCount()
    {
        if ($this->isPersonalEnterprise()) {
            return 2;
        }
        if ($this->is_device_unlimited) {
            return PHP_INT_MAX;
        }
        if (in_array($this->plan_id, Plan::$experience_plans)) {
            return 1;
        }
        if (in_array($this->plan_id, array_merge(Plan::$team_plans, Plan::$starter_plans, [Plan::PLAN_TYPE_LITE]))) {
            return 2;
        }
        if (in_array($this->plan_id, array_merge(Plan::$enterprise_plans, [Plan::PLAN_TYPE_HYBRID_STORAGE, Plan::PLAN_TYPE_CER, Plan::PLAN_TYPE_CHANNEL_TEST]))) {
            return 3;
        }
        return 1;
    }

    public static function registerEnterpriseSource($enterprise_id, $source)
    {
        $handler = new static();
        return $handler->request(
            Http::METHOD_POST,
            self::URL_REGISTER_ENTERPRISE_SOURCE,
            [
                'body' => [
                    'enterprise_id' => $enterprise_id,
                    'source' => $source,
                ]
            ]);
    }

    public static function recordSem($user_id, $from, $keyword, $medium, $campaign, $content, $ip = null)
    {
        $handler = new static();
        return $handler->request(
            Http::METHOD_POST,
            self::URL_RECORD_SEM,
            [
                'body' => [
                    'user_id' => $user_id,
                    'from' => $from,
                    'keyword' => $keyword,
                    'medium' => $medium,
                    'campaign' => $campaign,
                    'content' => $content,
                    'client_ip' => $ip ?: CommonHelper::getClientIp(),
                ]
            ]);
    }

    public function getAdminUser($fetch_info = true): RemoteUser
    {
        if (!$this->admin_user) {
            $this->admin_user = new RemoteUser(['id' => $this->admin_user_id]);
            if ($fetch_info) {
                $this->admin_user->getInfo();
            }
        }
        return $this->admin_user;
    }

    //动机收集
    public static function motivationCollect($enterprise_id, $plan_id, $motivation)
    {
        $handler = new static();
        return $handler->request(
            Http::METHOD_POST,
            self::URL_MOTIVATION_COLLECT,
            [
                'body' => [
                    'enterprise_id' => $enterprise_id,
                    'plan_id' => $plan_id,
                    'motivation' => $motivation
                ]
            ]);
    }

    //检查DLP设备数
    public static function checkDlpDevice($enterprise_id)
    {
        $handler = new static();
        return $handler->request(
            Http::METHOD_POST,
            self::URL_CHECK_DLP_DEVICE,
            [
                'body' => [
                    'enterprise_id' => $enterprise_id,
                ]
            ]);
    }

    public function __get($name)
    {
        if (!isset($this->attributes[$name])) {
            if ($this->isPersonalEnterprise()) {
                return null;
            }
            return $this->getInfo($name);
        } else {
            return $this->getAttribute($name);
        }
    }
}
