<?php

namespace App\Remote;

use App\Exceptions\RemoteCallException;
use App\Library\HttpClient\HttpClientException;
use App\Library\HttpClient\ServerClient;
use App\Util\LogHelper;
use App\Util\LogType;

class ServerService
{
    use RequestContextTrait;

    protected static $client = null;

    protected static $remote_interface = null;

    public function __construct()
    {
        if (self::$client === null) {
            self::$client = new ServerClient(static::$remote_interface);
        }
    }

    /**
     * @param $method
     * @param string $uri
     * @param array $options
     * @return mixed
     * @throws RemoteCallException
     */
    public function request($method, $uri = '', array $options = [])
    {
        $options['remote_interface'] = static::$remote_interface;

        if (!array_key_exists('skip_add_context', $options) || !$options['skip_add_context']) {
            if (!isset($options['headers'])) {
                $options['headers'] = $this->addRequestContext();
            } else {
                $options['headers'] = array_merge($options['headers'], $this->addRequestContext());
            }
        }

        LogHelper::info(LogType::WebAndServerService, 'ServerService\\request\\request', ['method' => $method, 'uri' => $uri, 'options' => $options]);
        try {
            $response = self::$client->requestRemote($method, $uri, $options);
        } catch (HttpClientException $exception) {
            throw RemoteCallException::createFrom($exception);
        }
        LogHelper::info(LogType::WebAndServerService, 'ServerService\\request\\response', ['response' => $response]);
        return $response;
    }
}