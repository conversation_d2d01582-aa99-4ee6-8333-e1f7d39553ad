<?php

namespace App\Remote;

use App\Constants\ErrorCode;
use App\Constants\Http;
use App\Exceptions\ExternalException;
use App\Library\Janitor\RateWithFreezeJanitor;
use App\Library\TwoStep;

class Sms extends WebService
{
    private const URL_SEND_CODE = '/internal_api/account/two_step_send_code';

    public const TYPE_TXT = 'txt';
    public const TYPE_VOICE = 'voice';

    private static $janitor = null;

    const TWO_STEP_VERITY_LOGIN_SMS_SIGN_APP_ID = '59a0635d8d0a43c09f3ef505ef7993c0';

    const APP_REGISTER_SIGNATURE_APP_ID = '9e69cb8fecf94556afe9916a42c4542b';

    public function __construct()
    {
        parent::__construct();

        self::$janitor = (new RateWithFreezeJanitor())->setThresholdTime(600)->setThresholdCount(10)->setFreezeTime(600);
    }

    /**
     * @param $phone
     * @param $code
     * @param string $type
     * @throws ExternalException
     * @throws \App\Exceptions\InternalException
     */
    public function send($phone, $code, $type = self::TYPE_TXT)
    {
        if (!self::$janitor->setIdentifier('two_step_' . $phone)->fire($this->_send($phone, $code, $type))) {
            throw new ExternalException(ErrorCode::TWO_STEP_SMS_LIMIT);
        }
    }

    private function _send($phone, $code, $type = self::TYPE_TXT)
    {
        return function() use($phone, $code, $type) {
            $this->request(
                Http::METHOD_POST,
                self::URL_SEND_CODE,
                [
                    'body' => [
                        'id' => $phone,
                        'code' => $code,
                        'type' => TwoStep::METHOD_SMS,
                        'sms_type' => $type,
                    ],
                ]
            );
        };
    }

//    public function send($phone, $code, $type = 'txt')
//    {
//        if (!(new Janitor())->setIdentifier('two_step_' . $phone)->setThresholdCount(2)->fire(
//            function() use ($phone, $code, $type) {
//                $this->request(
//                    Http::METHOD_POST,
//                    self::URL_SEND_CODE,
//                    [
//                        'body' => [
//                            'id' => $phone,
//                            'code' => $code,
//                            'type' => TwoStep::METHOD_SMS,
//                            'sms_type' => $type,
//                        ],
//                    ]
//                );
//            }
//        )) {
//            throw new ExternalException(ErrorCode::SMS_LIMIT);
//        }
//    }
}