<?php

namespace App\Remote;

use App\Constants\Http;
use App\Util\LogHelper;
use App\Util\LogType;

class Referral extends CommonService
{
    const URL_WEB_BECOME_REFERRAL = "internal_api/account/become_referral";
    const URL_WEB_ADD_RECORD = "internal_api/account/add_referral_record";

    protected $web_service_uris = [
        self::URL_WEB_BECOME_REFERRAL,
        self::URL_WEB_ADD_RECORD
    ];

    public static function becomeReferral($phone)
    {
        $handler = new self();
        return $handler->request(
            Http::METHOD_POST,
            self::URL_WEB_BECOME_REFERRAL,
            [
                'body' => [
                    'phone' => $phone
                ]
            ]);
    }

    public static function add_record($referral_id, $phone, $username = '管理员', $company = '企业名称')
    {
        try {
            $handler = new self();
            return $handler->request(
                Http::METHOD_POST,
                self::URL_WEB_ADD_RECORD,
                [
                    'body' => [
                        'referral_id' => $referral_id,
                        'phone' => $phone,
                        'username' => $username,
                        'company' => $company,
                        'recommend_type' => 0,
                        'created' => time()
                    ]
                ]);
        } catch (\Exception $exception) {
            LogHelper::error(LogType::RemoteCallException, $exception->getMessage(), ['exception' => LogHelper::serializeException($exception)]);
        }
    }
}