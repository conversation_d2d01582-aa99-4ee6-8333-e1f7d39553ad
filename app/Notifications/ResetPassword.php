<?php

namespace App\Notifications;

use App\Util\Email;
use App\Models\Credential;
use App\Models\PasswordReset;
use App\Models\User;
use App\Notifications\Channels\QueuedEmailChannel;
use App\Notifications\Messages\QueuedEmailMessage;
use Illuminate\Notifications\Notification;

/**
 * Class ResetPassword
 * @package App\Notifications
 *
 * @property Credential credential
 * @property PasswordReset password_reset
 *
 * @deprecated use App\Jobs\Emails\SendResetPasswordEmail instead
 *
 */
class ResetPassword extends Notification
{
    protected $credential, $password_reset;
    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct(Credential $credential, PasswordReset $password_reset)
    {
        $this->credential = $credential;
        $this->password_reset = $password_reset;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return [QueuedEmailChannel::class];
    }

    public function toQueuedEmail(User $notifiable)
    {
        $credential = $this->credential;
        $password_reset = $this->password_reset;
        if($credential->login_user_id != $notifiable->id || $password_reset->login_user_id != $notifiable->id) {
            return null;
        }
        $params['to_name'] = $notifiable->getName();
        $params['to_login'] = $notifiable->getRemoteUser()->login;
        $params['url'] =  $password_reset->getPasswordResetUrl();
        return (new QueuedEmailMessage())
                    ->setSubjectType(Email::SUBJECT_RESET_PASSWORD)
                    ->setTo($credential->identifier)
                    ->setType(Email::TYPE_RESET_PASSWORD)
                    ->setParams($params);
    }
}
