<?php

namespace App\Notifications;

use App\Util\Email;
use App\Models\Credential;
use App\Models\User;
use App\Notifications\Channels\QueuedEmailChannel;
use App\Notifications\Messages\QueuedEmailMessage;
use Illuminate\Notifications\Notification;

/**
 * Class CustomEnterpriseEditionRegistered
 * @package App\Notifications
 *
 * @property array enterprise_info
 */
class CustomEnterpriseEditionRegistered extends Notification
{
    protected $enterprise_info;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct(array $enterprise_info)
    {
        $this->enterprise_info = $enterprise_info;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param mixed $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return [QueuedEmailChannel::class];
    }

    public function toQueuedEmail()
    {
        $to = config('mail.custom_sales_email');
        if (is_array($to)) {
            $message = [];
            foreach ($to as $email) {
                array_push($message, (new QueuedEmailMessage())
                    ->setSubjectType(Email::SUBJECT_CUSTOM_ENTERPRISE_EDITION_REGISTERED)
                    ->setTo($email)
                    ->setType(Email::TYPE_REGISTER_CUSTOM_ENTERPRISE_EDITION)
                    ->setParams($this->enterprise_info));
            }
            return $message;
        } else {
            return [
                (new QueuedEmailMessage())
                    ->setSubjectType(Email::SUBJECT_CUSTOM_ENTERPRISE_EDITION_REGISTERED)
                    ->setTo($to)
                    ->setType(Email::TYPE_REGISTER_CUSTOM_ENTERPRISE_EDITION)
                    ->setParams($this->enterprise_info)
            ];
        }
    }
}
