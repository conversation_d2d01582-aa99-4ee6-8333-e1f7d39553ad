<?php

namespace App\Notifications\Channels;

use App\Util\Email;
use Illuminate\Notifications\Notification;

class QueuedEmailChannel
{
    public function send($notifiable, Notification $notification)
    {
        $messages = $notification->toQueuedEmail($notifiable);
        if (is_array($messages)) {
            foreach ($messages as $message) {
                $this->real_send($message);
            }
        } else {
            $this->real_send($messages);
        }
    }

    private function real_send($message) {
        if(!$message){
            return;
        }
        Email::send($message->getTo(), $message->getType(), $message->getSubject(), $message->getParams(), $message->getLang(), $message->getProductId());
    }
}