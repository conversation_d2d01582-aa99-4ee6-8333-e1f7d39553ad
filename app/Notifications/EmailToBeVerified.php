<?php

namespace App\Notifications;

use App\Util\Email;
use App\Models\Credential;
use App\Models\User;
use App\Notifications\Channels\QueuedEmailChannel;
use App\Notifications\Messages\QueuedEmailMessage;
use Illuminate\Notifications\Notification;

/**
 * Class EmailToBeVerified
 * @package App\Notifications
 *
 * @property Credential credential
 */
class EmailToBeVerified extends Notification
{
    protected $credential;
    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct(Credential $credential)
    {
        $this->credential = $credential;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return [QueuedEmailChannel::class];
    }

    public function toQueuedEmail(User $notifiable)
    {
        $credential = $this->credential;
        if($credential->login_user_id != $notifiable->id) {
            return null;
        }
        $params['name'] = $notifiable->getName();
        $params['url'] =  $credential->getValidationUrl();
        return (new QueuedEmailMessage())
                    ->setSubjectType(Email::SUBJECT_REGISTER_VALIDATION)
                    ->setTo($credential->identifier)
                    ->setType(Email::TYPE_REGISTER)
                    ->setParams($params);
    }
}
