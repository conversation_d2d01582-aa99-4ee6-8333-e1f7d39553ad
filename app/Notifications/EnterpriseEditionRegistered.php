<?php

namespace App\Notifications;

use App\Util\Email;
use App\Models\Credential;
use App\Models\User;
use App\Notifications\Channels\QueuedEmailChannel;
use App\Notifications\Messages\QueuedEmailMessage;
use Illuminate\Notifications\Notification;

/**
 * Class EmailToBeVerified
 * @package App\Notifications
 *
 * @property array enterprise_info
 */
class EnterpriseEditionRegistered extends Notification
{
    protected $enterprise_info;
    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct(array $enterprise_info)
    {
        $this->enterprise_info = $enterprise_info;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return [QueuedEmailChannel::class];
    }

    public function toQueuedEmail()
    {
        return (new QueuedEmailMessage())
                    ->setSubjectType(Email::SUBJECT_ENTERPRISE_EDITION_REGISTERED)
                    ->setTo(config('mail.sales_email'))
                    ->setType(Email::TYPE_REGISTER_ENTERPRISE_EDITION)
                    ->setParams($this->enterprise_info);
    }
}
