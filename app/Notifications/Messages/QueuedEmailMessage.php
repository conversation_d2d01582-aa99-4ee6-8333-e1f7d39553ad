<?php
/**
 * Created by PhpStorm.
 * User: luther
 * Date: 01/11/2017
 * Time: 11:15
 */

namespace App\Notifications\Messages;

class QueuedEmailMessage
{
    protected $to;
    protected $type;
    protected $params;
    protected $product_id;
    protected $subject;
    protected $job_type;
    protected $lang;

    public function setTo($to)
    {
        $this->to = $to;
        return $this;
    }

    public function setType($type)
    {
        $this->type = $type;
        return $this;
    }

    public function setParams($params)
    {
        $this->params = $params;
        return $this;
    }

    public function setLang($lang)
    {
        $this->lang = $lang;
        return $this;
    }

    public function setProductId($product_id)
    {
        $this->product_id = $product_id;
        return $this;
    }

    public function setSubjectType($subject)
    {
        $this->subject = $subject;
        return $this;
    }

    public function getTo()
    {
        return $this->to;
    }

    public function getSubject()
    {
        return trans('email.'.$this->subject, [], $this->getLang());
    }

    public function getType()
    {
        return $this->type;
    }

    public function getParams()
    {
        return $this->params;
    }

    public function getLang()
    {
        return $this->lang;
    }

    public function getProductId()
    {
        return $this->product_id;
    }
}