<?php

namespace App\Services\Credential;

use App\Constants\ErrorCode;
use App\Constants\SessionCacheKey;
use App\Events\EmailUpdated;
use App\Events\PhoneUpdated;
use App\Exceptions\ExternalException;
use App\Models\Credential;
use App\Models\User;
use App\Services\BaseService;
use App\Util\CacheHelper;
use Illuminate\Support\Facades\Auth;

/**
 * Class UnbindCredential
 * @package App\Services\Credential
 *
 * @property Credential credential
 * @property User user
 */
class UnbindCredential extends BaseService
{
    protected $type;

    protected function rules()
    {
        return [
            'type' => ['required'],
        ];
    }

    protected function validate()
    {
        $user = Auth::user();
        $credential = Credential::getUserCredential($user->id, $this->type);
        if(!$credential){
            throw new ExternalException(ErrorCode::CREDENTIAL_NOT_FOUND);
        }
        $phone_credential = Credential::getUserCredential($user->id, Credential::TYPE_EMAIL);
        if(!$phone_credential) {
            throw new ExternalException(ErrorCode::PHONE_UNBIND_WHEN_UPDATE_EMAIL, 'email');
        }

        $this->credential = $credential;
        $this->user = $user;
    }

    protected function start()
    {
        $credential = $this->credential;
        $user = $this->user;

        $credential->delete();

        // TODO: remote
    }
}