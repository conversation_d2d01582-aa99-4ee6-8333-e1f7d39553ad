<?php

namespace App\Services\Credential;

use App\Constants\ErrorCode;
use App\Constants\SessionCacheKey;
use App\Exceptions\ExternalException;
use App\Models\Credential;
use App\Models\User;
use App\Services\BaseService;
use App\Util\CacheHelper;
use Illuminate\Support\Facades\Auth;

/**
 * Class PreVerifyEmail
 * @package App\Services\Credential
 *
 * @property User user
 * @property string email
 */
class PreVerifyEmail extends BaseService
{
    protected function validate()
    {
        $user = Auth::user();
        if(!($email = $user->getAdditionalValue('unverified_email'))) {
            throw new ExternalException(ErrorCode::CREDENTIAL_NOT_NEED_VALIDATED);
        }
        $origin_credential = Credential::getUserCredential($user->id, Credential::TYPE_EMAIL);
        if($origin_credential){
            throw new ExternalException(ErrorCode::CREDENTIAL_NOT_NEED_VALIDATED);
        }
        $credential = Credential::checkCredentialExistence($email, Credential::TYPE_EMAIL);
        if($credential) {
            throw new ExternalException(ErrorCode::EMAIL_ALREADY_OCCUPIED_WHEN_VERIFY);
        }

        $this->user = $user;
        $this->email = $email;
    }

    protected function start()
    {
        $user = $this->user;

        $credential = Credential::generate($user->id, $this->email, Credential::TYPE_EMAIL, Credential::STATUS_TO_BE_VALIDATED);
        $credential->save();
        $user->unsetAdditionalValue('unverified_email');
        $user->save();

        $remote_user = $user->getRemoteUser(false);
        $remote_user->prepareUpdate();
        $remote_user->setLoginTypeAvailable(Credential::EMAIL);
        $remote_user->unverifyContact(Credential::EMAIL);
        $remote_user->commitUpdate();

        return ['credential' => $credential];
    }
}