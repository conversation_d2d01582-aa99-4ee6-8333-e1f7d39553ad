<?php

namespace App\Services\Credential;
use App\Models\Credential;
use App\Services\BaseService;
use App\Util\UA\UAClient;
use App\Util\UA\UAMessageTypes;

/**
 * Class CredentialValidate
 * @package App\Services\Credential
 *
 * @property Credential credential
 */

class CredentialValidate extends BaseService
{
    protected $credential;

    protected function rules()
    {
        return [
            'credential' => 'required',
        ];
    }

    protected function start()
    {
        $now = time();

        $credential = $this->credential;
        $credential->status = Credential::STATUS_VALID;
        $credential->activation_code = '';
        $credential->activation_expires_at = 0;
        $credential->setAdditionalValue('verified_at', $now);
        $credential->save();

        $user = $credential->getUser();
        $user->activate($now);
        $user->save();

        $remote_user = $user->getRemoteUser(false);
        if($credential->isTypeEmail()) {
            $remote_user->verifyContact(Credential::EMAIL, $now);
        }
        // 讲道理只可能是邮箱，既然类名叫这个了象征性地处理一下
        elseif($credential->isTypePhone()) {
            $remote_user->verifyContact(Credential::PHONE, $now);
        }
        $remote_user->activate();
    }

    protected function log()
    {
        if ($this->credential->isTypeEmail()) {
            UAClient::publish(UAMessageTypes::USER_EMAIL_VALIDATE, ['email'=>$this->credential->identifier]);
        }
    }

}