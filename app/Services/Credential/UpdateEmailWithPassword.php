<?php

namespace App\Services\Credential;

use App\Constants\ErrorCode;
use App\Exceptions\ExternalException;
use App\Models\Credential;
use App\Models\User;
use App\Notifications\EmailToBeVerified;
use App\Rules\ValidEmail;
use App\Services\BaseService;
use App\Util\UA\UAClient;
use App\Util\UA\UAMessageTypes;
use Illuminate\Support\Facades\Auth;

/**
 * Class UpdateEmail
 * @package App\Services\Credential
 *
 * @property User user
 * @property Credential origin_credential
 */
class UpdateEmailWithPassword extends BaseService
{
    protected $email, $password;

    protected function rules()
    {
        return [
            'email' => ['required', new ValidEmail],
            'password' => ['required'],
        ];
    }

    protected function validate()
    {
        $user = Auth::user();

        if(!$user->authenticate($this->password)) {
            throw new ExternalException(ErrorCode::PASSWORD_INCORRECT, 'password');
        }
    }

    protected function start()
    {
        (new UpdateEmail())->handle(['email' => $this->email]);
    }
}