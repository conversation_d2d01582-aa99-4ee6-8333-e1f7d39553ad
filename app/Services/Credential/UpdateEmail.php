<?php

namespace App\Services\Credential;

use App\Constants\CacheKey;
use App\Constants\ErrorCode;
use App\Exceptions\ExternalException;
use App\Library\Janitor\BaseJanitor;
use App\Library\Janitor\OneDayJanitor;
use App\Models\Credential;
use App\Models\User;
use App\Notifications\EmailToBeVerified;
use App\Rules\ValidEmail;
use App\Services\BaseService;
use App\Util\CacheHelper;
use App\Util\LogHelper;
use App\Util\LogType;
use App\Util\UA\UAClient;
use App\Util\UA\UAMessageTypes;
use Illuminate\Support\Facades\Auth;

/**
 * Class UpdateEmail
 * @package App\Services\Credential
 *
 * @property User user
 * @property Credential origin_credential
 */
class UpdateEmail extends BaseService
{
    protected $email;

    const DAILY_LIMIT_KEY = 'update_email_daily_limit';
    const DAILY_LIMIT_CNT = 1;
    const ENTERPRISE_DAILY_LIMIT_KEY = 'enterprise_update_email_daily_limit';

    protected function rules()
    {
        return [
            'email' => ['required', new ValidEmail],
        ];
    }

    protected function validate()
    {
        $user = Auth::user();
        $enterprise = $user->getRemoteUser()->getEnterprise();
        if($enterprise->is_sso_enterprise && !$enterprise->is_public_login_available) {
            throw new ExternalException(ErrorCode::PUBLIC_LOGIN_ENABLED_SSO_ENTERPRISES_ONLY);
        }

        $origin_credential = Credential::getUserCredential($user->id, Credential::TYPE_EMAIL);
        if ($origin_credential) {
            $phone_credential = Credential::getUserCredential($user->id, Credential::TYPE_PHONE);
            if (!$phone_credential || !$phone_credential->isValid()) {
                throw new ExternalException(ErrorCode::PHONE_UNBIND_WHEN_UPDATE_EMAIL, 'email');
            }
        }
        $credential = Credential::checkCredentialExistence($this->email, Credential::TYPE_EMAIL);
        if ($credential) {
            // 邮箱已被占用
            if ($credential->login_user_id != $user->id) {
                if (!$origin_credential) {
                    throw new ExternalException(ErrorCode::EMAIL_ALREADY_OCCUPIED_WHEN_VERIFY, 'email');
                } else {
                    throw new ExternalException(ErrorCode::EMAIL_ALREADY_OCCUPIED, 'email');
                }
            } // 邮箱没变
            else {
                throw new ExternalException(ErrorCode::EMAIL_NOT_CHANGED, 'email');
            }
        }

        $janitor = static::getDailyJanitor($user->user_id);
        if ($janitor->isFrozen()) {
            throw new ExternalException(ErrorCode::UPDATE_EMAIL_EXCEED_LIMIT, 'email');
        }
        if ($enterprise->id > 0) {
            $enterprise_janitor = static::getEnterpriseDailyJanitor($enterprise->id, $enterprise->seat_limit);
            if ($enterprise_janitor->isFrozen()) {
                throw new ExternalException(ErrorCode::UPDATE_EMAIL_EXCEED_LIMIT, 'email');
            }
            $enterprise_janitor->hit();
        }
        $janitor->hit();

        $verify_identity_email_flag = false;
        try {
            $verify_identity_email_flag = CacheHelper::getCachedInfo(sprintf(CacheKey::VERIFY_IDENTITY_FLAG, $user->user_id));
        } catch (Throwable $ex) {
            LogHelper::info(LogType::Exception, $ex->getMessage(), ['exception' => LogHelper::serializeException($ex)]);
        }
        if ($verify_identity_email_flag)
        {
            throw new ExternalException(ErrorCode::PASSWORD_CHECK_FAILED, 'email');
        }


        $this->user = $user;
        $this->origin_credential = $origin_credential;
    }

    protected function start()
    {
        $user = $this->user;
        $origin_credential = $this->origin_credential;

        if ($origin_credential) {
            $origin_credential->delete();
        }
        if($user->getAdditionalValue('unverified_email')) {
            $user->unsetAdditionalValue('unverified_email');
            $user->save();
        }
        $credential = Credential::generate($user->id, $this->email, Credential::TYPE_EMAIL, Credential::STATUS_TO_BE_VALIDATED);
        $credential->regenerateActivationCode();

        $remote_user = $user->getRemoteUser(false);
        $remote_user->prepareUpdate();
        $remote_user->unverifyContact(Credential::EMAIL);
        $remote_user->updateInfo(['email' => $this->email]);
        $remote_user->commitUpdate();

        $user->notify(new EmailToBeVerified($credential));
    }

    protected function log()
    {
        $current_email = $this->email;
        UAClient::publish(UAMessageTypes::USER_UPDATE_EMAIL, ['email' => $current_email]);
    }

    private static function getDailyJanitor($identifier)
    {
        return (new OneDayJanitor(static::DAILY_LIMIT_KEY."_".$identifier))->setThresholdCount(static::DAILY_LIMIT_CNT);
    }

    private static function getEnterpriseDailyJanitor($identifier, $seat_limit)
    {
        return (new OneDayJanitor(static::ENTERPRISE_DAILY_LIMIT_KEY."_".$identifier))->setThresholdCount(static::DAILY_LIMIT_CNT * $seat_limit);
    }
}