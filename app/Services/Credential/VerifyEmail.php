<?php

namespace App\Services\Credential;

use App\Constants\ErrorCode;
use App\Exceptions\ExternalException;
use App\Models\Credential;
use App\Models\User;
use App\Notifications\EmailToBeVerified;
use App\Services\BaseService;
use Illuminate\Support\Facades\Auth;

/**
 * Class VerifyEmail
 * 不同于VerifyPhone，不是邮箱真正被验证的地方，只是用户点立即验证，然后重发一个验证邮件，
 * 最后验证是在用户打开验证链接，CredentialValidate里做的
 * @package App\Services\Credential
 *
 * @property User user
 * @property Credential credential
 */
class VerifyEmail extends BaseService
{
    protected function validate()
    {
        $user = Auth::user();
        $credential = Credential::getUserCredential($user->id, Credential::TYPE_EMAIL);
        if(!$credential) {
            throw new ExternalException(ErrorCode::CREDENTIAL_NOT_FOUND);
        }
        if($credential->status != Credential::STATUS_TO_BE_VALIDATED) {
            throw new ExternalException(ErrorCode::CREDENTIAL_NOT_NEED_VALIDATED);
        }

        $this->user = $user;
        $this->credential = $credential;
    }

    protected function start()
    {
        $user = $this->user;
        $credential = $this->credential;

        $credential->regenerateActivationCode();
        $user->notify(new EmailToBeVerified($credential));
    }

    protected function log()
    {

    }


}