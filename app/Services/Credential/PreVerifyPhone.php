<?php

namespace App\Services\Credential;

use App\Constants\ErrorCode;
use App\Constants\SessionCacheKey;
use App\Exceptions\ExternalException;
use App\Models\Credential;
use App\Models\User;
use App\Services\BaseService;
use App\Util\CacheHelper;
use Illuminate\Support\Facades\Auth;

/**
 * Class PreVerifyPhone
 * @package App\Services\Credential
 *
 * @property User user
 * @property string phone
 */
class PreVerifyPhone extends BaseService
{
    protected function validate()
    {
        $user = Auth::user();
        if(!($phone = $user->getAdditionalValue('unverified_phone'))) {
            throw new ExternalException(ErrorCode::CREDENTIAL_NOT_NEED_VALIDATED);
        }
        $origin_credential = Credential::getUserCredential($user->id, Credential::TYPE_PHONE);
        if($origin_credential){
            throw new ExternalException(ErrorCode::CREDENTIAL_NOT_NEED_VALIDATED);
        }
        $credential = Credential::checkCredentialExistence($phone, Credential::TYPE_PHONE);
        if($credential) {
            throw new ExternalException(ErrorCode::PHONE_ALREADY_OCCUPIED_WHEN_VERIFY);
        }

        $this->user = $user;
        $this->phone = $phone;
    }

    protected function start()
    {
        $user = $this->user;

        $credential = Credential::generate($user->id, $this->phone, Credential::TYPE_PHONE, Credential::STATUS_TO_BE_VALIDATED);
        $credential->save();

        $user->unsetAdditionalValue('unverified_phone');
        $user->save();

        $remote_user = $user->getRemoteUser(false);
        $remote_user->prepareUpdate();
        $remote_user->setLoginTypeAvailable(Credential::PHONE);
        $remote_user->unverifyContact(Credential::PHONE);
        $remote_user->commitUpdate();

        return ['credential' => $credential];
    }
}