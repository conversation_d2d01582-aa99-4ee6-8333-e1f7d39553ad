<?php

namespace App\Services\Credential;

use App\Constants\ErrorCode;
use App\Exceptions\ExternalException;
use App\Models\Credential;
use App\Models\User;
use App\Services\BaseService;
use App\Util\Captcha;
use Illuminate\Support\Facades\Auth;

/**
 * Class VerifyPhone
 * @package App\Services\Credential
 *
 * @property User user
 * @property Credential credential
 */
class VerifyPhone extends BaseService
{
    protected $sms_captcha;

    protected function rules()
    {
        return [
            'sms_captcha' => ['required'],
        ];
    }

    protected function validate()
    {
        $user = Auth::user();
        $credential = Credential::getUserCredential($user->id, Credential::TYPE_PHONE);
        if(!$credential) {
            throw new ExternalException(ErrorCode::CREDENTIAL_NOT_FOUND);
        }
        if($credential->status != Credential::STATUS_TO_BE_VALIDATED) {
            throw new ExternalException(ErrorCode::CREDENTIAL_NOT_NEED_VALIDATED);
        }
        if(!Captcha::check($this->sms_captcha, Captcha::TYPE_MODIFY_PHONE, Captcha::THROUGH_SMS, $credential->getIdentifier())) {
            throw new ExternalException(ErrorCode::SMS_CAPTCHA_INVALID, 'sms_captcha');
        }

        $this->user = $user;
        $this->credential = $credential;
    }

    protected function start()
    {
        $credential = $this->credential;
        $user = $this->user;

        $credential->status = Credential::STATUS_VALID;
        $credential->setAdditionalValue('verified_at', time());
        $credential->save();

        $remote_user = $user->getRemoteUser(false);
        $remote_user->verifyContact(Credential::PHONE);
    }
}