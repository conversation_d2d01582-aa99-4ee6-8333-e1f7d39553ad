<?php

namespace App\Services\Credential;

use App\Constants\CacheKey;
use App\Constants\ErrorCode;
use App\Exceptions\ExternalException;
use App\Models\Credential;
use App\Models\User;
use App\Rules\ValidPhone;
use App\Services\BaseService;
use App\Util\CacheHelper;
use App\Util\Captcha;
use App\Util\LogHelper;
use App\Util\LogType;
use App\Util\UA\UAClient;
use App\Util\UA\UAMessageTypes;
use Illuminate\Support\Facades\Auth;
use App\Library\Janitor\OneDayJanitor;
use Throwable;

/**
 * Class UpdatePhone
 * @package App\Services\Credential
 *
 * @property User user
 * @property Credential $origin_credential
 * @property Credential $credential
 */
class UpdatePhone extends BaseService
{
    protected $phone, $sms_captcha;

    const DAILY_PHONE_LIMIT_KEY = 'update_phone_daily_limit';
    const DAILY_LIMIT_CNT = 1;

    protected function rules()
    {
        return [
            'phone' => ['required', new ValidPhone],
            'sms_captcha' => ['required'],
        ];
    }

    protected function validate()
    {
        if(!Captcha::check($this->sms_captcha, Captcha::TYPE_MODIFY_PHONE, Captcha::THROUGH_SMS, $this->phone)) {
            throw new ExternalException(ErrorCode::SMS_CAPTCHA_INVALID, 'sms_captcha');
        }
        $user = Auth::user();
        $enterprise = $user->getRemoteUser()->getEnterprise();
        if($enterprise->is_sso_enterprise && !$enterprise->is_public_login_available) {
            throw new ExternalException(ErrorCode::PUBLIC_LOGIN_ENABLED_SSO_ENTERPRISES_ONLY);
        }

        $origin_credential = Credential::getUserCredential($user->id, Credential::TYPE_PHONE);
        $credential = Credential::checkCredentialExistence($this->phone, Credential::TYPE_PHONE);
        if($credential) {
            // 手机号已被占用
            if($credential->login_user_id != $user->id) {
                if(!$origin_credential) {
                    throw new ExternalException(ErrorCode::PHONE_ALREADY_OCCUPIED_WHEN_VERIFY, 'phone');
                }
                else {
                    throw new ExternalException(ErrorCode::PHONE_ALREADY_OCCUPIED, 'phone');
                }
            }
            // 手机号没变
            else {
                throw new ExternalException(ErrorCode::PHONE_NOT_CHANGED, 'phone');
            }
        }

        $verify_identity_flag = false;
        try {
            $verify_identity_flag = CacheHelper::getCachedInfo(sprintf(CacheKey::VERIFY_IDENTITY_FLAG, $user->user_id));
        } catch (Throwable $ex) {
            LogHelper::info(LogType::Exception, $ex->getMessage(), ['exception' => LogHelper::serializeException($ex)]);
        }
        if ($verify_identity_flag)
        {
            throw new ExternalException(ErrorCode::PASSWORD_CHECK_FAILED, 'phone');
        }

        self::checkOneDayOperationLimit($user);

        $this->user = $user;
        $this->origin_credential = $origin_credential;
        $this->credential = $credential;
    }

    protected function start()
    {
        $user = $this->user;
        $origin_credential = $this->origin_credential;

        if($origin_credential) {
            $origin_credential->delete();
        }
        if($user->getAdditionalValue('unverified_phone')) {
            $user->unsetAdditionalValue('unverified_phone');
            $user->save();
        }
        $credential = Credential::generate($user->id, $this->phone, Credential::TYPE_PHONE);
        $credential->setAdditionalValue('verified_at', time());
        $credential->save();

        $remote_user = $user->getRemoteUser(false);
        $remote_user->prepareUpdate();
        $remote_user->updateInfo(['phone' => $this->phone]);
        $remote_user->verifyContact(Credential::PHONE);
        $remote_user->commitUpdate();
    }

    protected function log()
    {
        $old_phone = $this->origin_credential ? $this->origin_credential->identifier : '';
        UAClient::publish(UAMessageTypes::USER_UPDATE_PHONE, ['from' => $old_phone, 'to' => $this->phone]);
    }

    private function checkOneDayOperationLimit($user)
    {
        $janitor = static::getDailyJanitor($user->user_id);
        if ($janitor->isFrozen()) {
            throw new ExternalException(ErrorCode::UPDATE_PHONE_EXCEED_LIMIT);
        }
        $janitor->hit();
    }

    private static function getDailyJanitor($identifier)
    {
        return (new OneDayJanitor(static::DAILY_PHONE_LIMIT_KEY."_".$identifier))->setThresholdCount(static::DAILY_LIMIT_CNT);
    }
}