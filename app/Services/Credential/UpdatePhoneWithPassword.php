<?php

namespace App\Services\Credential;

use App\Constants\ErrorCode;
use App\Exceptions\ExternalException;
use App\Models\Credential;
use App\Models\User;
use App\Rules\ValidPhone;
use App\Services\BaseService;
use App\Util\Captcha;
use App\Util\UA\UAClient;
use App\Util\UA\UAMessageTypes;
use Illuminate\Support\Facades\Auth;

/**
 * Class UpdatePhone
 * @package App\Services\Credential
 *
 * @property User user
 * @property Credential $origin_credential
 * @property Credential $credential
 */
class UpdatePhoneWithPassword extends BaseService
{
    protected $phone, $sms_captcha, $password;

    protected function rules()
    {
        return [
            'phone' => ['required', new ValidPhone],
            'sms_captcha' => ['required'],
            'password' => ['required'],
        ];
    }

    protected function validate()
    {
        $user = Auth::user();
        if(!$user->authenticate($this->password)) {
            throw new ExternalException(ErrorCode::PASSWORD_INCORRECT, 'password');
        }
    }

    protected function start()
    {
        (new UpdatePhone())->handle([
            'phone' => $this->phone,
            'sms_captcha' => $this->sms_captcha
        ]);
    }
}