<?php

namespace App\Services\Auth;

use App\Models\AuthToken;
use App\Remote\AuthToken as RemoteAuthToken;
use App\Services\BaseService;

/**
 * Class AuthTokenRevoke
 * @package App\Services\Auth
 * @property AuthToken $auth_token
 */
class AuthTokenRevoke extends BaseService
{
    protected $transaction_needed = false;

    protected $auth_token;

    protected function start()
    {
        RemoteAuthToken::delete($this->auth_token->id);
//        $this->auth_token->delete();
    }
}