<?php

namespace App\Services\Auth;

use App\Constants\ErrorCode;
use App\Exceptions\ExternalException;
use App\Models\AuthToken;
use App\Models\ClientDevice;
use App\Models\Service;
use App\Models\User;
use App\Services\BaseService;
use App\Services\Security\ClientDeviceDelete;
use App\Util\ArrayHelper;
use App\Util\LogHelper;
use App\Util\LogType;
use App\Util\UA\UAClient;
use App\Util\UA\UAMessageTypes;
use App\Remote\AuthToken as RemoteAuthToken;

/**
 * Class AuthTokenCreate
 * @package App\Services\Auth
 *
 * @property User $user
 * @property Service $service
 */
class AuthTokenCreate extends BaseService
{
    protected $transaction_needed = true;

    protected $user, $service;
    /**
     * @var AuthToken
     */
    protected $auth_token;

    /**
     * TODO object validate
     */
    protected function rules()
    {
        return [
            'user' => 'required',
            'service' => 'required',
        ];
    }

    /**
     * @return AuthToken|null
     * @throws \App\Exceptions\ValidationException
     * @throws \Exception
     * @throws \Throwable
     */
    protected function start()
    {
        $service = $this->service;
        $user = $this->user;

        if ($service->isMobile()) {
            $device_types = [ClientDevice::IOS_APP_NAME, ClientDevice::ANDROID_APP_NAME];
            $exist = AuthToken::findCountByServiceIds(
                $user->user_id,
                explode(',', config('common.service.mobile_app_service_ids'))
            );
        } else if ($service->isSync()) {
            $device_types = [ClientDevice::MAC_DESKTOP_NAME, ClientDevice::WIN_DESKTOP_NAME];
            $exist = AuthToken::findCountByServiceIds(
                $user->user_id,
                explode(',', config('common.service.sync_client_service_ids'))
            );
        } else {
            $device_types = [ClientDevice::UNKNOWN_DEVICE];
            $exist = 0;
        }

        if ($exist !== 0) {
            $max_count = $user->getMaxDeviceOnlineCount();
            if ($service->isSync()) {
                // dlp设备数量限制
                $dlp_max_device_count = $user->getDlpEnterpriseMaxDeviceCount();
                if ($dlp_max_device_count > 0) {
                    $max_count = $dlp_max_device_count;
                    LogHelper::info(LogType::WebAndServerService, 'dlp_client_count_check'.
                        ' user:'.$user->user_id.
                        ' time:'.time().
                        ' dlp_max_device_count:'.$dlp_max_device_count.
                        ' exist:'.$exist);
                }
            }

            if ($exist >= $max_count) {
                $devices = ClientDevice::findByUserId($user->user_id, true, $device_types);
                $devices = $devices->sort(function(ClientDevice $a, ClientDevice $b) {
                    return $b->getUpdatedAttr() <=> $a->getUpdatedAttr();
                });

                for($i = 0; $i < $max_count-1; $i++)
                {
                    $devices->shift();
                }

                LogHelper::info(LogType::WebAndServerService, 'client_device_kick_out_'.$user->user_id.
                    ': time:'.time().
                    ' count:'.count($devices).
                    ' exist:'.$exist.
                    ' max_count:'.$max_count);
                foreach($devices as $device)
                {
                    (new ClientDeviceDelete())->handle([
                        'id' => $device->id,
                        'user' => $user,
                    ]);
                }
                LogHelper::info(LogType::WebAndServerService, 'client_device_kick_out_'.$user->user_id.'_finished');
            }
        }

        $this->auth_token = RemoteAuthToken::create($user->user_id, $user->id, $service->id);
        return $this->auth_token;
    }

    protected function log()
    {
        $auth_token = $this->auth_token;
        if ($auth_token !== null && $auth_token->isSync()) {
            $remote_user = $this->user->getRemoteUser();
            $overrides['actor_id'] = $remote_user->id;
            $overrides['actor_enterprise_id'] = $remote_user->enterprise_id;
            UAClient::publish(UAMessageTypes::SYNC_CREATE_AUTH_TOKEN, [], $overrides);
        }
    }

}