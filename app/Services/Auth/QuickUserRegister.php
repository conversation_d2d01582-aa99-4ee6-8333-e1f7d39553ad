<?php

namespace App\Services\Auth;

use App\Constants\ErrorCode;
use App\Constants\Plan;
use App\Events\EnterpriseCreated;
use App\Events\QuickUserRegistered;
use App\Exceptions\ExternalException;
use App\Models\ChannelCoupon;
use App\Models\ChannelCouponUser;
use App\Models\Credential;
use App\Models\EnterprisePlanTraffic;
use App\Models\User;
use App\Remote\Enterprise as RemoteEnterprise;
use App\Rules\ValidPhone;
use App\Services\BaseService;
use App\Util\AuthHelper;
use App\Util\Captcha;
use App\Util\CommonHelper;
use App\Util\Context;
use App\Util\YiDun;
use App\Util\UA\UAClient;
use App\Util\UA\UAMessageTypes;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;
use App\Constants\Common;

/**
 * Class QuickUserRegister
 * @package App\Services\Auth
 *
 * @property int type
 * @property string sms_captcha
 * @property string phone
 */
class QuickUserRegister extends BaseService
{
    protected $plan_id, $accessToken, $token, $phone, $type, $sms_captcha;

    /**
     * @var User
     */
    protected $user;

    protected $admin_user, $enterprise;

    protected $channel_coupon;

    protected $register_channel;

    protected function rules()
    {
        return [
            'phone' => [new ValidPhone],
            'type' => 'required',
            'plan_id' => [
                Rule::in([Plan::PLAN_TYPE_PREMIUM_2020, Plan::PLAN_TYPE_ULTIMATE_2020, Plan::PLAN_TYPE_STARTER_2020, Plan::PLAN_TYPE_STARTER_2024,
                    Plan::PLAN_TYPE_FREE_2023, Plan::PLAN_TYPE_LITE, Plan::PLAN_TYPE_5T_INTELLIGENT_EDITION,
                    Plan::PLAN_TYPE_AI_5T_INTELLIGENT_EDITION, Plan::PLAN_TYPE_AI_20T_INTELLIGENT_EDITION,
                    Plan::PLAN_TYPE_20T_INTELLIGENT_EDITION])
            ],
        ];
    }

    protected function default()
    {
        if (empty($this->company_name)) {
            $this->company_name = trans('constants.' . Common::DEFAULT_COMPANY_NAME);
        }
        if (empty($this->name)) {
            $this->name = trans('constants.' . Common::DEFAULT_USER_NAME);
        }
    }

    /**
     * @throws ExternalException
     */
    protected function validate()
    {

        if ($this->type == 1) {
            $this->phone = YiDun::check($this->accessToken, $this->token);
            if (empty($this->phone)) {
                throw new ExternalException(ErrorCode::YIDUN_CHECK_INVALID);
            }
        } else if ($this->type == 2) {
            if (!Captcha::check($this->sms_captcha, Captcha::TYPE_REGISTER, Captcha::THROUGH_SMS, $this->phone)) {
                throw new ExternalException(ErrorCode::SMS_CAPTCHA_INVALID, 'sms_captcha');
            }
        } else {
            throw new ExternalException(ErrorCode::QUICK_LOGIN_TYPE_INVALID);
        }

        if (AuthHelper::hasExceedRegisterLimitWithSameIp(CommonHelper::getClientIp())) {
            throw new ExternalException(ErrorCode::REGISTER_EXCEED_LIMIT);
        }

        $credential = Credential::checkCredentialExistence($this->phone, Credential::TYPE_PHONE);
        if ($credential) {
            $user = $credential->getUser();
            $remote_user = null;

            if ($credential->status == Credential::STATUS_TO_BE_VALIDATED) {
                $remote_user = $user->getRemoteUser();
                $remote_enterprise = $remote_user->getEnterprise();
                if ($credential->activation_expires_at < time()) {
                    throw new ExternalException(ErrorCode::PHONE_ALREADY_OCCUPIED_ACTIVATION_EXPIRED, $remote_enterprise->name);
                } else {
                    throw new ExternalException(ErrorCode::PHONE_ALREADY_OCCUPIED_BUT_NOT_ACTIVATED,
                        $remote_enterprise->name);
                }
            }

            if (!$user) {
                throw new ExternalException(ErrorCode::PHONE_ALREADY_OCCUPIED_WHEN_REGISTER);
            }

            if (!$remote_user) {
                $remote_user = $user->getRemoteUser();
            }
            if ($remote_user->is_personal_user) {
                throw new ExternalException(ErrorCode::PHONE_ALREADY_OCCUPIED_BY_PERSONAL_USER_WHEN_REGISTER);
            } else {
                throw new ExternalException(ErrorCode::PHONE_ALREADY_OCCUPIED_WHEN_REGISTER);
            }
        }

        if (isset($this->plan_id))
        {
            if ($this->plan_id == Plan::PLAN_TYPE_FREE_2023)
            {
                $plan_register = EnterprisePlanTraffic::enterprise_plan_register_count();
                if ($plan_register >= 2000)
                {
                    throw new ExternalException(ErrorCode::PLAN_REGISTER_COUNT_EXCEED_LIMIT);
                }
            }
        }
    }

    protected function start()
    {
        $now = time();
        $request_id = Str::random();
        $this->password = Str::random();
        $sem_value = request()->cookie('sem');
        $parsed_sem_value = @json_decode($sem_value, true);
        $from = $parsed_sem_value['from'] ?? null;

        $this->trial_expires_at = null;
        $channelCouponUser = null;
        if (isset($this->channel_coupon)) {
            $channelCouponUser = ChannelCouponUser::findByTicketId(trim(strtolower($this->channel_coupon)));
            if ($channelCouponUser) {
                if ($channelCouponUser->enterprise_id > 0) {
                    throw new ExternalException(ErrorCode::CHANNEL_COUPON_TICKET_ALREADY_USED);
                }
                $channelCoupon = ChannelCoupon::findById($channelCouponUser->channel_coupon_id);
                if ($channelCoupon) {
                    if ($channelCoupon->expired_at > time()) {
                        $this->trial_expires_at = time() + $channelCoupon->getDaysAttr() * 24 * 3600;
                        $this->plan_id = $channelCoupon->getPlanIdAttr();
                        $from = "ChannelCoupon_" . $channelCoupon->getChannelCodeAttr();
                    } else {
                        throw new ExternalException(ErrorCode::CHANNEL_COUPON_TICKET_ALREADY_EXPIRED);
                    }
                }
            }
        } else  {
            if ($this->plan_id == Plan::PLAN_TYPE_LITE && isset($this->register_channel) && $this->register_channel == 'initial') {
                $this->trial_expires_at = time() + 7 * 24 * 3600;
            }
        }


        $remote_res = RemoteEnterprise::create(
            $request_id,
            '管理员的企业',
            RemoteEnterprise::generateAdminToRegister('管理员', $this->phone, bcrypt($this->password), $now),
            $this->plan_id,
            false,
            null,
            null,
            $this->trial_expires_at,
            null,
            ['gray_test' => request()->cookie('gray_test'), 'from' => $from]
        );

        if ($channelCouponUser) {
            $channelCouponUser->enterprise_id = $remote_res['enterprise']->id;
            $channelCouponUser->save();
        }

        AuthHelper::recordRegisterIp(CommonHelper::getClientIp());

        $admin_user = $remote_res['user'];
        $enterprise = $remote_res['enterprise'];
        $user = User::findById($remote_res['login_user_id']);

        $this->user = $user;
        $this->enterprise = $enterprise;
        $this->admin_user = $admin_user;
        return ['user' => $user];
    }

    protected function after()
    {
        event(new QuickUserRegistered($this->user, $this->phone, $this->password));
        event(new EnterpriseCreated($this->enterprise, $this->admin_user, Context::getSource(),
            $this->getAdditionalEnterpriseCreateInfo()));

        parent::after();
    }

    protected function getAdditionalEnterpriseCreateInfo()
    {
        $additionalCreateInfo = [];
        if (isset($this->ali_biz_id)) {
            $additionalCreateInfo['ali_biz_id'] = $this->ali_biz_id;
        }
        if (isset($this->ali_order_id)) {
            $additionalCreateInfo['ali_order_id'] = $this->ali_order_id;
        }
        if (isset($this->motivation)) {
            $additionalCreateInfo['motivation'] = $this->motivation;
        }
        if (isset($this->email)) {
            $additionalCreateInfo['email'] = $this->email;
        }
        if (request()->cookie('gray_test')) {
            $additionalCreateInfo['gray_test'] = true;
        }
        if (isset($this->invite_code)) {
            $additionalCreateInfo['invite_code'] = $this->invite_code;
        }
        if (isset($this->activity_code)) {
            $additionalCreateInfo['activity_code'] = $this->activity_code;
        }
        if (request()->cookie('qhclick_msg')) {
            $additionalCreateInfo['qhclick_msg'] = @json_decode(request()->cookie('qhclick_msg'), true);
        }
        if (request()->cookie('jlclick_msg')) {
            $additionalCreateInfo['jlclick_msg'] = @json_decode(request()->cookie('jlclick_msg'), true);
        }
        if (request()->cookie('bdclick_msg')) {
            $additionalCreateInfo['bdclick_msg'] = @json_decode(request()->cookie('bdclick_msg'), true);
        }
        return $additionalCreateInfo;
    }

    protected function log()
    {
        $remote_user = $this->user->getRemoteUser();
        $overrides['actor_id'] = $remote_user->id;
        $overrides['actor_enterprise_id'] = $remote_user->enterprise_id;
        $overrides['context_name'] = 'mobile_register';
        UAClient::publish(
            UAMessageTypes::USER_REGISTER,
            ['user' => $remote_user],
            $overrides
        );
    }

}
