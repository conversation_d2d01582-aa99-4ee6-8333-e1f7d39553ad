<?php

namespace App\Services\Auth;
use App\Constants\ErrorCode;
use App\Constants\ExceptionFieldKey;
use App\Exceptions\ExternalException;
use App\Exceptions\InternalException;
use App\Exceptions\V1AccountLoginException;
use App\Library\Janitor\BaseJanitor;
use App\Library\Janitor\RateWithFreezeJanitor;
use App\Models\Credential;
use App\Models\User;
use App\Notifications\EmailToBeVerified;
use App\Remote\V1Account;
use App\Rules\ValidIdentifier;
use App\Services\BaseService;
use App\Util\AuthHelper;
use App\Util\Captcha;
use App\Util\CommonHelper;
use App\Util\Context;
use App\Util\LoginHelper;
use Illuminate\Support\Facades\Auth;
use App\Remote\Enterprise as RemoteEnterprise;
/**
 * Class UserLogin
 * @package App\Services\Auth
 *
 * @property string login
 * @property string password
 * @property boolean remember_login
 * @property string pic_captcha
 * @property boolean v1_error
 *
 * @property User user
 * @property string ip
 */
class UserLogin extends BaseService
{
    protected $login, $password, $remember_login, $pic_captcha, $v1_error, $skip_pic_captcha;

    protected function rules()
    {
        return [
            'login' => ['required', new ValidIdentifier],
            'password' => ['required'],
        ];
    }

    protected function default()
    {
        $this->ip = CommonHelper::getClientIp();
        if (isset($this->v1_error) && $this->v1_error === true)
        {
            $this->v1_error = ErrorCode::V1_LOGIN_NOT_AllOWED;
        }
    }

    protected function getValidator()
    {
        $ip = $this->ip;
        $validator = parent::getValidator();
        $validator->sometimes('pic_captcha', 'required', function ($input) use ($ip){
            return !$this->skip_pic_captcha && AuthHelper::isLoginPicCaptchaRequired($input['login'], $ip);
        });
        return $validator;
    }

    /**
     * @return null|void
     * @throws ExternalException
     * @throws InternalException
     */
    protected function validate()
    {
        if (!$this->skip_pic_captcha) {
            if(AuthHelper::isLoginPicCaptchaRequired($this->login, $this->ip) && !Captcha::check($this->pic_captcha, Captcha::TYPE_LOGIN)) {
                throw new ExternalException(ErrorCode::PIC_CAPTCHA_INVALID, 'pic_captcha');
            }
        }
        AuthHelper::recordLogin($this->login, $this->ip);

        $credential = Credential::getUniqueCredentialByIdentifier($this->login);
        if(!$credential) {
            if (config('app.disable_v1')) {
                throw new ExternalException(ErrorCode::LOGIN_PASSWORD_INVALID, 'password');
            }
            $redirect = request()->get('redirect');
            if($redirect && !preg_match("/^https?/", $redirect)){
                $redirect = config('app.url') . ltrim($redirect, '/');
            }
            $v1_account = new V1Account();
            $v1_login_res = $v1_account->login([
                'identifier' => $this->login,
                'password' => $this->password,
                'device_token' => Context::getDeviceToken(),
                'remember_login' => $this->remember_login,
                'redirect' => $redirect,
            ]);
            if($v1_login_res['token']) {
                if ($this->v1_error) {
                    throw new ExternalException($this->v1_error, ExceptionFieldKey::LOGIN);
                }
                throw new V1AccountLoginException($v1_login_res['token']);
            }
            if($v1_login_res['error_code']) {
                throw new ExternalException($v1_login_res['error_code'], 'password');
            }
            throw new ExternalException(ErrorCode::LOGIN_PASSWORD_INVALID, 'password');
        }
        $user = $credential->getUser();
        if(!$user) {
            throw new ExternalException(ErrorCode::LOGIN_PASSWORD_INVALID, 'password');
        }
        if(AuthHelper::isLoginPasswordErrorLimit($user->user_id)) {
            $current_user = $user->getRemoteUser();
            $enterprise = $current_user->getEnterprise();
            LoginHelper::login_exception_reminder($enterprise,$current_user, LoginHelper::getDeviceName(), CommonHelper::getClientIp());
            throw new ExternalException(ErrorCode::PASSWORD_ERROR_FROZEN, 'password');
        }
        if(!$user->isActive()){
            throw new ExternalException(ErrorCode::LOGIN_PASSWORD_INVALID, 'password');
        }
        if($credential->status != Credential::STATUS_VALID) {
            if($credential->type == Credential::TYPE_EMAIL) {
                if($credential->activation_expires_at < time()) {
                    $credential->regenerateActivationCode();
                    $user->notify(new EmailToBeVerified($credential));
                }
                throw new ExternalException(ErrorCode::CREDENTIAL_EMAIL_UNVERIFIED, 'login');
            }
            elseif($credential->type == Credential::TYPE_PHONE) {
                throw new ExternalException(ErrorCode::CREDENTIAL_PHONE_UNVERIFIED, 'login');
            }
            throw new ExternalException(ErrorCode::LOGIN_UNVERIFIED, 'login');
        }

        if(!$user->authenticate($this->password)) {
            // TODO 这里暂时不记登录失败的日志
            $count = AuthHelper::recordPasswordError($user->user_id);
            if ($count == AuthHelper::LOGIN_PASSWORD_ERROR_LIMIT_CNT) {
                $current_user = $user->getRemoteUser();
                $enterprise = $current_user->getEnterprise();
                LoginHelper::login_exception_reminder($enterprise, $current_user, LoginHelper::getDeviceName(), CommonHelper::getClientIp());
                throw new ExternalException(ErrorCode::PASSWORD_ERROR_FROZEN, 'password');
            } else if ($count > 1) {
                $additional_info = [];
                $additional_info['replace'] = ['count' => AuthHelper::LOGIN_PASSWORD_ERROR_LIMIT_CNT - $count];
                throw new ExternalException(ErrorCode::LOGIN_PASSWORD_INVALID_BY_TIMES, 'password', $additional_info);
            }
            throw new ExternalException(ErrorCode::LOGIN_PASSWORD_INVALID, 'password');
        }
        $from_tp_register_login = false;
        if ($this->fromDouYinRegisterUnActive($user)) {
            $res = RemoteEnterprise::activeDouYinRegisterEnterpriseByPhone($this->login);
            if ($res) {
                $from_tp_register_login = true;
            }
        }
        if ($from_tp_register_login) {
            $db_user_data = User::find($credential->login_user_id);
            $this->user = $db_user_data;
        } else {
            $this->user = $user;
        }
    }

    protected function start()
    {
        $user = $this->user;

        AuthHelper::clearLoginRecord($this->login, $this->ip, $user->user_id);

        return ['user' => $user];
    }

    private function fromDouYinRegisterUnActive($user):bool
    {
        return ($user->settings & 4) > 0;
    }
}