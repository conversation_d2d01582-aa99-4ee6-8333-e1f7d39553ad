<?php

namespace App\Services\Auth;

use App\Constants\ErrorCode;
use App\Exceptions\ExternalException;
use App\Models\ChannelCoupon;
use App\Models\ChannelCouponUser;
use App\Services\BaseService;
use App\Util\Captcha;

class ChannelCoupons extends BaseService
{
    protected $ticket_id;
    protected $captcha;

    protected function rules()
    {
        return [
        ];
    }

    protected function default()
    {
    }

    protected function validate()
    {
        if (empty($this->ticket_id)) {
            throw new ExternalException(ErrorCode::CHANNEL_COUPON_TICKET_ID_NOT_EXIST, 'ticket_id');
        }
        if (empty($this->captcha)) {
            throw new ExternalException(ErrorCode::PIC_CAPTCHA_INVALID, 'pic_captcha');
        }
        if (!Captcha::check($this->captcha, Captcha::TYPE_REGISTER)) {
            throw new ExternalException(ErrorCode::PIC_CAPTCHA_INVALID, 'pic_captcha');
        }
    }

    protected function start()
    {
        $channelCouponUser = ChannelCouponUser::findByTicketId(trim(strtolower($this->ticket_id)));
        if ($channelCouponUser) {
            if ($channelCouponUser->enterprise_id > 0) {
                throw new ExternalException(ErrorCode::CHANNEL_COUPON_TICKET_ALREADY_USED);
            }
            $channelCoupon = ChannelCoupon::findById($channelCouponUser->channel_coupon_id);
            if ($channelCoupon) {
                if ($channelCoupon->expired_at < time()) {
                    throw new ExternalException(ErrorCode::CHANNEL_COUPON_TICKET_ALREADY_EXPIRED);
                }
            }
        } else {
            throw new ExternalException(ErrorCode::CHANNEL_COUPON_TICKET_ALREADY_EXPIRED);
        }
    }

}
