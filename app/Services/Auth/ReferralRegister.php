<?php

namespace App\Services\Auth;

use App\Constants\ErrorCode;
use App\Exceptions\ExternalException;
use App\Rules\ValidPhone;
use App\Rules\ValidText;
use App\Services\BaseService;
use App\Util\Captcha;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;
use App\Remote\Referral;

class ReferralRegister extends BaseService
{
    protected $phone, $sms_captcha;

    protected function rules()
    {
        return [
            'phone' => ['required', new ValidPhone],
            'sms_captcha' => 'required',
        ];
    }

    protected function validate()
    {
        if (!Captcha::check($this->sms_captcha, Captcha::TYPE_BECOME_REFERRAL, Captcha::THROUGH_SMS, $this->phone)) {
            throw new ExternalException(ErrorCode::SMS_CAPTCHA_INVALID, 'sms_captcha');
        }
    }

    protected function start()
    {
        $res = Referral::becomeReferral($this->phone);
        if ($res['success']) {
            return ['code' => $res['code'],'referral_id' => $res['referral_id']];
        } else {
            return null;
        }
    }

}
