<?php

namespace App\Services\Auth;

use App\Constants\ErrorCode;
use App\Constants\Plan;
use App\Exceptions\ExternalException;
use App\Notifications\CustomEnterpriseEditionRegistered;
use App\Notifications\EnterpriseEditionRegistered;
use App\Models\Credential;
use App\Rules\ValidEmail;
use App\Rules\ValidEnterpriseName;
use App\Rules\ValidPhone;
use App\Rules\ValidUserName;
use App\Services\BaseService;
use Illuminate\Notifications\AnonymousNotifiable;
use Illuminate\Validation\Rule;

class RegisterEnterpriseEdition extends BaseService
{
    protected $username, $phone, $enterprise_name, $enterprise_size, $plan_id, $email;

    protected function rules()
    {
        return [
            'enterprise_name' => ['required', new ValidEnterpriseName],
            'phone' => ['required', new ValidPhone],
            'username' => ['required', new ValidUserName],
            'enterprise_size' => ['required'],
            'email' => ['required', new ValidEmail],
            'plan_id' => ['required', Rule::in([Plan::PLAN_TYPE_INTERNATIONAL_PROFESSIONAL, Plan::PLAN_TYPE_ULTIMATE_2020,
                Plan::PLAN_TYPE_CUSTOM_2020, Plan::PLAN_TYPE_5T_INTELLIGENT_EDITION, Plan::PLAN_TYPE_20T_INTELLIGENT_EDITION,
                Plan::PLAN_TYPE_AI_5T_INTELLIGENT_EDITION, Plan::PLAN_TYPE_AI_20T_INTELLIGENT_EDITION
            ])]
        ];
    }

    protected function validate()
    {
        $credential = Credential::checkCredentialExistence($this->phone, Credential::TYPE_PHONE);
        if($credential) {
            throw new ExternalException(ErrorCode::PHONE_ALREADY_OCCUPIED, 'phone');
        }
    }

    protected function start()
    {
        $enterprise_info = [];
        $inputs = array('username', 'phone', 'enterprise_name', 'enterprise_size', 'email');
        foreach ($inputs as $input)
        {
            $enterprise_info[$input] = $this->$input;
        }
        $enterprise_info['plan_description'] = Plan::$plan_descs[$this->plan_id];
        $enterprise_info['time'] = date("Y-m-d h:i", time());
        if ($this->plan_id == Plan::PLAN_TYPE_CUSTOM_2020) {
            $enterprise_info['custom_enterprise'] = true;
            (new AnonymousNotifiable())->notify(new CustomEnterpriseEditionRegistered($enterprise_info));
        } else {
            (new AnonymousNotifiable())->notify(new EnterpriseEditionRegistered($enterprise_info));
        }
    }
}