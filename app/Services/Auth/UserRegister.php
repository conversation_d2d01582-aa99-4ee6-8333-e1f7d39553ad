<?php

namespace App\Services\Auth;

use App\Constants\ErrorCode;
use App\Constants\Plan;
use App\Events\EnterpriseCreated;
use App\Events\UserRegistered;
use App\Exceptions\ExternalException;
use App\Models\ChannelCoupon;
use App\Models\ChannelCouponUser;
use App\Models\Credential;
use App\Models\EnterprisePlanTraffic;
use App\Models\User;
use App\Remote\Enterprise as RemoteEnterprise;
use App\Rules\ValidEmail;
use App\Rules\ValidEnterpriseName;
use App\Rules\ValidPassword;
use App\Rules\ValidPhone;
use App\Rules\ValidText;
use App\Rules\ValidUserName;
use App\Services\BaseService;
use App\Util\AuthHelper;
use App\Util\Captcha;
use App\Util\CommonHelper;
use App\Util\Context;
use App\Util\UA\UAClient;
use App\Util\UA\UAMessageTypes;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;
use App\Constants\Common;

/**
 * Created by PhpStorm.
 * User: luther
 * Date: 18/08/2017
 * Time: 15:39
 */
class UserRegister extends BaseService
{
    const SELF_WORK = 1;//动机"个人存储/同步文件"
    const TEAM_WORK = 2;//动机"团队成员协作办公"

    protected $phone, $password, $company_name, $name, $plan_id, $sms_captcha, $login_type, $email, $motivation, $invite_code, $activity_code, $register_position;

    protected $channel_coupon;

    protected $register_channel;

    /**
     * 新官网参数
     */
    protected $from_page;

    /**
     * args below are only used by ali market register
     */
    protected $skip_bind_storage, $expires_at, $trial_expires_at, $seat_limit, $ali_biz_id, $ali_order_id;

    /**
     * @var User
     */
    protected $user;

    protected $admin_user, $enterprise;

    protected function rules()
    {
        return [
            'company_name' => [new ValidEnterpriseName],
            'phone' => ['required', new ValidPhone],
            'password' => ['required', new ValidPassword],
            'name' => [ new ValidUserName],
            'sms_captcha' => 'required',
            'plan_id' => [
                Rule::in([Plan::PLAN_TYPE_PREMIUM_2020, Plan::PLAN_TYPE_ULTIMATE_2020, Plan::PLAN_TYPE_STARTER_2020, Plan::PLAN_TYPE_STARTER_2024,
                    Plan::PLAN_TYPE_FREE_2023, Plan::PLAN_TYPE_LITE, Plan::PLAN_TYPE_5T_INTELLIGENT_EDITION,
                    Plan::PLAN_TYPE_20T_INTELLIGENT_EDITION, Plan::PLAN_TYPE_AI_5T_INTELLIGENT_EDITION,
                    Plan::PLAN_TYPE_AI_20T_INTELLIGENT_EDITION,])
            ],
            'motivation' => [Rule::in([self::SELF_WORK, self::TEAM_WORK])],
            'email' => [new ValidEmail],//当手机号为国际手机号,要求用户会传一个邮箱信息,导入到销售易
        ];
    }

    protected function default()
    {
        if (empty($this->company_name)) {
            $this->company_name = trans('constants.' . Common::DEFAULT_COMPANY_NAME);
        }
        if (empty($this->name)) {
            $this->name = trans('constants.' . Common::DEFAULT_USER_NAME);
        }
    }

    protected function validate()
    {

        if (!Captcha::check($this->sms_captcha, Captcha::TYPE_REGISTER, Captcha::THROUGH_SMS, $this->phone)) {
            throw new ExternalException(ErrorCode::SMS_CAPTCHA_INVALID, 'sms_captcha');
        }

        if (AuthHelper::hasExceedRegisterLimitWithSameIp(CommonHelper::getClientIp())) {
            throw new ExternalException(ErrorCode::REGISTER_EXCEED_LIMIT);
        }

        $credential = Credential::checkCredentialExistence($this->phone, Credential::TYPE_PHONE);
        if ($credential) {
            $user = $credential->getUser();
            $remote_user = null;

            if ($credential->status == Credential::STATUS_TO_BE_VALIDATED) {
                $remote_user = $user->getRemoteUser();
                $remote_enterprise = $remote_user->getEnterprise();
                if ($credential->activation_expires_at < time()) {
                    throw new ExternalException(ErrorCode::PHONE_ALREADY_OCCUPIED_ACTIVATION_EXPIRED, $remote_enterprise->name);
                } else {
                    throw new ExternalException(ErrorCode::PHONE_ALREADY_OCCUPIED_BUT_NOT_ACTIVATED,
                        $remote_enterprise->name);
                }
            }

            if (!$user) {
                throw new ExternalException(ErrorCode::PHONE_ALREADY_OCCUPIED_WHEN_REGISTER);
            }

            if (!$remote_user) {
                $remote_user = $user->getRemoteUser();
            }
            if ($remote_user->is_personal_user) {
                throw new ExternalException(ErrorCode::PHONE_ALREADY_OCCUPIED_BY_PERSONAL_USER_WHEN_REGISTER);
            } else {
                throw new ExternalException(ErrorCode::PHONE_ALREADY_OCCUPIED_WHEN_REGISTER);
            }
        }

        if (isset($this->plan_id))
        {
            if ($this->plan_id == Plan::PLAN_TYPE_FREE_2023)
            {
                $plan_register = EnterprisePlanTraffic::enterprise_plan_register_count();
                if ($plan_register >= 2000)
                {
                    throw new ExternalException(ErrorCode::PLAN_REGISTER_COUNT_EXCEED_LIMIT);
                }
            }
        }
    }

    protected function start()
    {
        $request_id = Str::random();
        $now = time();
        $sem_value = request()->cookie('sem');
        $parsed_sem_value = @json_decode($sem_value, true);
        $from = $parsed_sem_value['from'] ?? null;

        $channelCouponUser = null;
        if (isset($this->channel_coupon)) {
            $channelCouponUser = ChannelCouponUser::findByTicketId(trim(strtolower($this->channel_coupon)));
            if ($channelCouponUser) {
                if ($channelCouponUser->enterprise_id > 0) {
                    throw new ExternalException(ErrorCode::CHANNEL_COUPON_TICKET_ALREADY_USED);
                }
                $channelCoupon = ChannelCoupon::findById($channelCouponUser->channel_coupon_id);
                if ($channelCoupon) {
                    if ($channelCoupon->expired_at > time()) {
                        $this->trial_expires_at = time() + $channelCoupon->getDaysAttr() * 24 * 3600;
                        $this->plan_id = $channelCoupon->getPlanIdAttr();
                        $from = "ChannelCoupon_" . $channelCoupon->getChannelCodeAttr();
                    } else {
                        throw new ExternalException(ErrorCode::CHANNEL_COUPON_TICKET_ALREADY_EXPIRED);
                    }
                }
            }
        } else  {
            if ($this->plan_id == Plan::PLAN_TYPE_LITE && isset($this->register_channel) && $this->register_channel == 'initial') {
                $this->trial_expires_at = time() + 7 * 24 * 3600;
            }
        }

        $remote_res = RemoteEnterprise::create(
            $request_id,
            $this->company_name,
            RemoteEnterprise::generateAdminToRegister($this->name, $this->phone, bcrypt($this->password), $now),
            $this->plan_id,
            $this->skip_bind_storage ?? false,
            $this->plan_id == Plan::PLAN_TYPE_PROFESSIONAL ? Plan::PLAN_TYPE_PROFESSIONAL_INI_SEAT_LIMIT : $this->seat_limit,
            $this->expires_at,
            $this->trial_expires_at,
            $this->register_position,
            ['use_jd_storage' => $this->useJdStorage(), 'gray_test' => request()->cookie('gray_test'), 'from' => $from, 'register_from' => $this->get_register_from()]
        );

        if ($channelCouponUser) {
            $channelCouponUser->enterprise_id = $remote_res['enterprise']->id;
            $channelCouponUser->save();
        }

        AuthHelper::recordRegisterIp(CommonHelper::getClientIp());

        $admin_user = $remote_res['user'];
        $enterprise = $remote_res['enterprise'];
        $user = User::findById($remote_res['login_user_id']);

        $this->user = $user;
        $this->enterprise = $enterprise;
        $this->admin_user = $admin_user;
        return ['user' => $user];
    }

    /**
     * 注册来源，是APP还是WEB，如果是WEB，除了区分是否是WAP，还需要区分是否通过关键词搜索注册得到的，如果是的话，一并导入关键词
     * @param $user_data
     * @return string
     */
    private function get_register_from()
    {
        $sem_value = request()->cookie('sem');
        $sem_details = json_decode($sem_value, true) ?? [];

        $register_source = "";

        if (!empty($sem_details['from']) || !empty($sem_details['keyword'])) {
            $parts = array_filter([
                $sem_details['from'] ?? '',
                $sem_details['keyword'] ?? '',
                $sem_details['medium'] ?? '',
                $sem_details['campaign'] ?? '',
                $sem_details['content'] ?? ''
            ]);

            $register_source .= "（" . implode(" ", $parts) . "）";
        } elseif (!empty($sem_details['register_origin']) &&
            in_array($sem_details['register_origin'], [
                'zxbj_home', 'zxbj_online', '360business_premium',
                '360business_start', '360wechat'
            ])
        ) {
            $register_source .= "（" . $sem_details['register_origin'] . "）";
        }

        if (!empty($sem_details['register_origin']) && $sem_details['register_origin'] === 'starter_360') {
            $register_source .= "（360yunpanjdt）";
        }

        return $register_source;
    }

    protected function after()
    {
        event(new UserRegistered($this->user));
        event(new EnterpriseCreated($this->enterprise, $this->admin_user, Context::getSource(),
            $this->getAdditionalEnterpriseCreateInfo()));

        parent::after();
    }

    protected function getAdditionalEnterpriseCreateInfo()
    {
        $additionalCreateInfo = [];
        if (isset($this->ali_biz_id)) {
            $additionalCreateInfo['ali_biz_id'] = $this->ali_biz_id;
        }
        if (isset($this->ali_order_id)) {
            $additionalCreateInfo['ali_order_id'] = $this->ali_order_id;
        }
        if (isset($this->motivation)) {
            $additionalCreateInfo['motivation'] = $this->motivation;
        }
        if (isset($this->email)) {
            $additionalCreateInfo['email'] = $this->email;
        }
        if (request()->cookie('gray_test')) {
            $additionalCreateInfo['gray_test'] = true;
        }
        if (isset($this->invite_code)) {
            $additionalCreateInfo['invite_code'] = $this->invite_code;
        }
        if (isset($this->activity_code)) {
            $additionalCreateInfo['activity_code'] = $this->activity_code;
        }
        if (isset($this->from_page)) {
            $additionalCreateInfo['from_page'] = $this->from_page;
        }
        if (request()->cookie('qhclick_msg')) {
            $additionalCreateInfo['qhclick_msg'] = @json_decode(request()->cookie('qhclick_msg'), true);
        }
        if (request()->cookie('jlclick_msg')) {
            $additionalCreateInfo['jlclick_msg'] = @json_decode(request()->cookie('jlclick_msg'), true);
        }
        if (request()->cookie('bdclick_msg')) {
            $additionalCreateInfo['bdclick_msg'] = @json_decode(request()->cookie('bdclick_msg'), true);
        }
        return $additionalCreateInfo;
    }

    protected function log()
    {
        $remote_user = $this->user->getRemoteUser();
        $overrides['actor_id'] = $remote_user->id;
        $overrides['actor_enterprise_id'] = $remote_user->enterprise_id;
        if ($this->login_type === 'app') {
            $overrides['context_name'] = 'mobile_register';
        }
        UAClient::publish(
            UAMessageTypes::USER_REGISTER,
            ['user' => $remote_user],
            $overrides
        );
    }

    private function useJdStorage()
    {
//        $location = CommonHelper::getClientLocation();
//        if(!in_array($location['province'], ['广东', '浙江', '北京', '上海', '江苏']) && $location['region'] == '中国') {
//            return true;
//        }
        return false;
    }

}
