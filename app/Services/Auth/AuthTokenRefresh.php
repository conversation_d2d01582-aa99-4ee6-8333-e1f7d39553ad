<?php

namespace App\Services\Auth;

use App\Exceptions\PermissionDeniedException;
use App\Models\AuthToken;
use App\Remote\AuthToken as RemoteAuthToken;
use App\Services\BaseService;
use App\Util\Context;
use App\Util\UA\UAClient;
use App\Util\UA\UAMessageTypes;

/**
 * 刷新 auth token。可以选择重建auth token.
 * refresh 的时候 refresh token 不变
 *
 * Class AuthTokenRefresh
 * @package App\Services\Auth
 */
class AuthTokenRefresh extends BaseService
{
    protected $refresh_token, $rebuild;

    protected $transaction_needed = false;

    protected function rules()
    {
        return [
            'refresh_token' => 'required|string|max:32',
            'rebuild' => 'boolean'
        ];
    }

    protected function validate()
    {
        $this->auth_token = RemoteAuthToken::getByRefreshToken($this->refresh_token);
    }

    /**
     * @return AuthToken|null
     * @throws \Exception
     */
    protected function start()
    {
        $auth_token = $this->auth_token;
        $return = null;
        // rebuild 会造成 client device 中的关联关系呗破坏，暂时禁止 rebuild
//        if ($this->rebuild) {
//            $return = AuthToken::reBuild($auth_token);
//        } else {
        if ($auth_token) {
            $return = RemoteAuthToken::refresh($auth_token->refresh_token);
        }
//            $auth_token->refresh();
//            $return = $auth_token;
//        }
        return $return;
    }

    /**
     * @var AuthToken $auth_token
     */
    protected $auth_token;

    protected function log()
    {
        $auth_token = $this->auth_token;
        if ($auth_token !== null && $auth_token->isSync()) {
            $user = $auth_token->user();
            $remote_user = $user->getRemoteUser();
            $overrides['actor_id'] = $remote_user->id;
            $overrides['actor_enterprise_id'] = $remote_user->enterprise_id;
            Context::setAuthToken($auth_token->auth_token);
            UAClient::publish(UAMessageTypes::SYNC_CREATE_AUTH_TOKEN, [], $overrides);
        }
    }

}