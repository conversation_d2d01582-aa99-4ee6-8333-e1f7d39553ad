<?php

namespace App\Services\Auth;
use App\Constants\CookieKey;
use App\Constants\ErrorCode;
use App\Exceptions\ExternalException;
use App\Exceptions\InternalException;
use App\Exceptions\NeedRedirectException;
use App\Models\User;
use App\Services\BaseService;
use App\Util\CacheHelper;
use App\Util\Context;
use App\Util\HttpHelper;
use App\Util\LogHelper;
use App\Util\LogType;
use Illuminate\Support\Facades\Cookie;
use App\Util\UserAgent;

/**
 * Class PreLogin
 * @package App\Services\Auth
 *
 * @property User user
 * @property boolean remember_login
 * @property string login_type
 */
class PreLogin extends BaseService
{
    protected $user, $remember_login, $login_type;

    protected function rules()
    {
        return [
            'user' => ['required'],
        ];
    }

    /**
     * @return null|void
     * @throws ExternalException
     * @throws NeedRedirectException
     */
    protected function validate()
    {
        $user_agent = new UserAgent();
        $platform = $user_agent->getBrowserPlatform();
        $user = $this->user;
        if($user->getRemoteUser()->is_delete_account == true) {
            throw new ExternalException(ErrorCode::USER_LOGIN_HAS_BEEN_WRITTEN_OFF, 'login');
        }

        if($user->getRemoteUser()->is_frozen) {
            throw new ExternalException(ErrorCode::USER_HAS_BEEN_FROZEN, 'login');
        }
        if($user->getRemoteUser()->is_login_disabled) {
            throw new ExternalException(ErrorCode::USER_LOGIN_HAS_BEEN_DISABLED, 'login');
        }
        if(!$user->is_personal_user) {
            $enterprise = $user->getRemoteUser()->getEnterprise();
            if($enterprise->forbidden_login) {
                Context::logout();
                throw new ExternalException(ErrorCode::ENTERPRISE_HAS_BEEN_FORBIDDEN_LOGIN, 'login');
            }
            if($enterprise->is_expired){
                if ($this->login_type != 'web' || $platform == 'mobile') {
                    if ($user->getRemoteUser()->has_payment_management) {
                        throw new ExternalException(ErrorCode::ENTERPRISE_HAS_BEEN_EXPIRED_ADMIN);
                    }
                    throw new ExternalException(ErrorCode::ENTERPRISE_HAS_BEEN_EXPIRED);
                }
                // 判断是否为100天内的普通用户
                if (!$user->getRemoteUser()->has_payment_management) {
                    if ($enterprise->expires_at > 0 && $enterprise->expires_at < (time() - 3600 * 24 * 100)) {
                        throw new ExternalException(ErrorCode::ENTERPRISE_HAS_BEEN_EXPIRED_NORMAL_USER);
                    }
                    if ($enterprise->expires_at == 0 && $enterprise->trial_expires_at < (time() - 3600 * 24 * 100)) {
                        throw new ExternalException(ErrorCode::ENTERPRISE_HAS_BEEN_EXPIRED_NORMAL_USER);
                    }
                }

            }
            if($enterprise->is_freeze) {
                throw new ExternalException(ErrorCode::ENTERPRISE_HAS_BEEN_FROZEN, 'login');
            }
            if ($enterprise->is_special_empty_enterprise) {
                $e = new ExternalException(ErrorCode::LOGIN_DENIED_FOR_EMPTY_ENTERPRISE, 'login');
                if ($this->login_type == 'web')
                {
                    $regKey = md5(sprintf("ali_market_register_info_key_%s_%s", $enterprise->id, microtime()));
                    $expire = config('auth.ali_market_registry_expire');
                    CacheHelper::cacheInfo(
                        $regKey,
                        [
                            'plan_info' => [],
                            'user_info' => [
                                'enterprise_id' => $enterprise->id,
                                'user_id' => $user->user_id
                            ]
                        ],
                        $expire
                    );
                    Cookie::queue(CookieKey::ALI_MARKET_REG, $regKey, $expire / 60);
                    $redirect = config('app.fangcloud_url') . "ali_biz_auth/aliyun_market_bind?ent_id={$enterprise->id}";
                    $e = new NeedRedirectException($redirect, $e);
                }
                throw $e;
            }
        }
    }

    protected function start()
    {
        $user = $this->user;

        return ['user' => $user];
    }
}