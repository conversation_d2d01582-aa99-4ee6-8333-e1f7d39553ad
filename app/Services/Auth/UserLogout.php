<?php

namespace App\Services\Auth;
use App\Constants\Common;
use App\Models\ClientDevice;
use App\Services\BaseService;
use App\Util\Context;
use App\Util\LogHelper;
use App\Util\LogType;
use App\Util\UA\UAClient;
use App\Util\UA\UAMessageTypes;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cookie;

class UserLogout extends BaseService
{
    protected function start()
    {
        $user_id = Context::user()->user_id;
        $device_token = Context::getDeviceToken();
        if($device_token) {
            $client_device = ClientDevice::findByUserIdDeviceToken($user_id, $device_token);
            if ($client_device) {
                $client_device->logout();
            } else {
                LogHelper::info(LogType::DEVICE_TOKEN, 'no client device found!', ['user_id' => $user_id, 'device_token' => $device_token]);
            }
        } else {
            LogHelper::info(LogType::DEVICE_TOKEN, 'device token not passed in!', ['user_id' => $user_id]);
        }

        Auth::guard()->logout();
        Cookie::queue(Cookie::forget(Common::PUBLIC_PRODUCT_ID_COOKIE_NAME));

        UAClient::publish(UAMessageTypes::USER_LOGOUT, [], ['actor_id' => $user_id]);
    }
}