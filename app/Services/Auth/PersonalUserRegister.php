<?php

namespace App\Services\Auth;

use App\Constants\ErrorCode;
use App\Events\UserRegistered;
use App\Exceptions\ExternalException;
use App\Models\Credential;
use App\Models\User;
use App\Remote\User as RemoteUser;
use App\Rules\ValidPassword;
use App\Rules\ValidPhone;
use App\Rules\ValidUserName;
use App\Services\BaseService;
use App\Util\Captcha;
use App\Util\UA\UAClient;
use App\Util\UA\UAMessageTypes;

class PersonalUserRegister extends BaseService
{
    protected $phone, $password, $name, $sms_captcha;
    /**
     * @var User
     */
    protected $user;

    protected function rules()
    {
        return [
            'phone' => ['required', new ValidPhone],
            'password' => ['required', new ValidPassword],
            'name' => ['required', new ValidUserName],
            'sms_captcha' => 'required',
        ];
    }

    protected function validate()
    {
        if (!Captcha::check($this->sms_captcha, Captcha::TYPE_REGISTER, Captcha::THROUGH_SMS, $this->phone)) {
            throw new ExternalException(ErrorCode::SMS_CAPTCHA_INVALID, 'sms_captcha');
        }
        $credential = Credential::checkCredentialExistence($this->phone, Credential::TYPE_PHONE);
        if ($credential) {
            throw new ExternalException(ErrorCode::PHONE_ALREADY_OCCUPIED, 'phone');
        }
    }

    protected function start()
    {
        $now = time();

        $personal_user = RemoteUser::create(
            [
                'user_group' => RemoteUser::USER_GROUP_USER,
                'phone' => $this->phone,
                'name' => $this->name,
            ]
        );

        $user = User::create([
            'user_id' => $personal_user->id,
            'password' => bcrypt($this->password),
            'activated' => $now,
        ]);
        $phone_credential = Credential::generate($user->id, $this->phone, Credential::TYPE_PHONE);
        $phone_credential->setAdditionalValue(Credential::VALUE_VERIFIED_AT, $now);
        $phone_credential->save();

        $personal_user->activate();
        $personal_user->prepareUpdate();
        $personal_user->setLoginTypeAvailable(Credential::PHONE);
        $personal_user->updateInfo(['phone' => $this->phone]);
        $personal_user->verifyContact(Credential::PHONE);
        $personal_user->commitUpdate();

        event(new UserRegistered($user));

        $this->user = $user;
        return ['user' => $user];
    }

    protected function log()
    {
        $remote_user = $this->user->getRemoteUser();
        $overrides['actor_id'] = $remote_user->id;
        $overrides['actor_enterprise_id'] = $remote_user->enterprise_id;
        UAClient::publish(UAMessageTypes::USER_REGISTER, ['user' => $remote_user], $overrides);
    }

}