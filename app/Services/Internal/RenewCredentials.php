<?php

namespace App\Services\Internal;
use App\Constants\ErrorCode;
use App\Exceptions\ExternalException;
use App\Models\Credential;
use App\Models\User;
use App\Rules\ValidIdentifier;
use App\Rules\ValidPassword;
use App\Services\BaseService;
use App\Util\CommonHelper;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Str;

/**
 * Class DeleteUser
 * @package App\Services\Internal
 *
 * @property iterable credentials
 */
class RenewCredentials extends BaseService
{
    protected $credentials;

    protected function start()
    {
        $credentials = [];
        foreach ($this->credentials as $credential) {
            if(!$credential->isValid()) {
                $credential->renewExpiresAt();
            }
            $credentials[] = $credential;
        }
        return ['credentials' => $credentials];
    }
}