<?php

namespace App\Services\Internal;
use App\Models\Credential;
use App\Models\User;
use App\Rules\ValidThirdIdentifier;
use App\Services\BaseService;
use Illuminate\Support\Str;

/**
 * Class CreateUser
 * @package App\Services\Internal
 *
 * @property string identifier
 * @property int user_id
 * @property string password
 * @property boolean enable_active
 * @property boolean password_require_init
 */
class CreateCredential extends BaseService
{
    protected $identifier, $user_id, $password, $enable_active, $password_require_init;

    protected function rules()
    {
        return [
            'identifier' => [new ValidThirdIdentifier()],
            'user_id' => 'required|int',
            'enable_active' => 'boolean',
            'password_require_init' => 'boolean',
        ];
    }

    protected function validate()
    {
    }

    /**
     * @return array|null
     */
    protected function start()
    {
        $now = time();

        $user = User::findByMainUserId($this->user_id);
        if(empty($user))
        {
            $user = new User([
                'user_id' => $this->user_id,
            ]);
        }
        $user->setBitmaskField('password_require_init', true);
        $user->password = bcrypt($this->password);
        $user->save();

        $credential = null;
        if($this->identifier) {
            $identifier = $this->identifier;
            $credential = Credential::generate($user->id, $identifier);
            if($this->enable_active) {
                $credential->setAdditionalValue('verified_at', $now);
            }
            else {
                $credential->status = Credential::STATUS_TO_BE_VALIDATED;
                $credential->regenerateActivationCode();
            }
            $credential->save();
        }
        return [
            'user' => $user,
            'credential' => $credential,
        ];
    }
}