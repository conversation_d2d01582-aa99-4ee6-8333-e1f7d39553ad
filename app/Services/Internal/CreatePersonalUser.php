<?php

namespace App\Services\Internal;

use App\Constants\ErrorCode;
use App\Exceptions\ExternalException;
use App\Models\Credential;
use App\Models\User;
use App\Rules\ValidThirdIdentifier;
use App\Services\BaseService;
use App\Util\CommonHelper;
use Illuminate\Support\Str;
use App\Remote\User as RemoteUser;

/**
 * Class CreatePersonalUser
 * @package App\Services\Internal
 *
 * @property string identifier
 * @property string password
 */
class CreatePersonalUser extends BaseService
{
    protected $identifier, $password;

    protected function rules()
    {
        return [
            'identifier' => [new ValidThirdIdentifier()],
        ];
    }

    protected function validate()
    {
        if ($this->identifier) {
            $credential = Credential::checkCredentialExistence($this->identifier);
            if ($credential) {
                throw new ExternalException(ErrorCode::IDENTIFIER_ALREADY_OCCUPIED, 'identifier');
            }
        }
    }

    /**
     * @return array|null
     */
    protected function start()
    {
        $now = time();

        $identifier = $this->identifier;
        if (CommonHelper::isValidEmail($identifier)) {
            $personal_user = RemoteUser::create(
                [
                    'user_group' => RemoteUser::USER_GROUP_USER,
                    'email' => $identifier,
                    'name' => $identifier,
                ]
            );
            $personal_user->activate();
            $personal_user->prepareUpdate();
            $personal_user->setLoginTypeAvailable(Credential::EMAIL);
            $personal_user->updateInfo(['email' => $identifier]);
            $personal_user->verifyContact(Credential::EMAIL);
            $personal_user->commitUpdate();
        } elseif (CommonHelper::isValidPhoneNumber($identifier)) {
            $personal_user = RemoteUser::create(
                [
                    'user_group' => RemoteUser::USER_GROUP_USER,
                    'phone' => $identifier,
                    'name' => $identifier,
                ]
            );
            $personal_user->setLoginTypeAvailable(Credential::PHONE);
            $personal_user->activate();
            $personal_user->prepareUpdate();
            $personal_user->setLoginTypeAvailable(Credential::PHONE);
            $personal_user->updateInfo(['phone' => $identifier]);
            $personal_user->verifyContact(Credential::PHONE);
            $personal_user->commitUpdate();
        }

        $user = new User([
            'user_id' => $personal_user->id,
            'password' => $this->password ? bcrypt($this->password) : '',
        ]);
        $user->setBitmaskField('password_require_init', true);
        $user->activated = $now;
        $user->save();

        $identifier = $this->identifier;
        $credential = Credential::generate($user->id, $identifier);
        $credential->setAdditionalValue('verified_at', $now);
        $credential->save();
        return [
            'user' => $user,
            'credential' => $credential,
        ];
    }
}