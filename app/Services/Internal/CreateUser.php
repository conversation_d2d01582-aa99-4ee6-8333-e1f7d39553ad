<?php

namespace App\Services\Internal;
use App\Constants\ErrorCode;
use App\Exceptions\ExternalException;
use App\Models\Credential;
use App\Models\User;
use App\Rules\ValidIdentifier;
use App\Rules\ValidPassword;
use App\Rules\ValidThirdIdentifier;
use App\Services\BaseService;
use App\Util\CommonHelper;
use Illuminate\Support\Str;

/**
 * Class CreateUser
 * @package App\Services\Internal
 *
 * @property string identifier
 * @property int user_id
 * @property string password
 * @property boolean enable_active
 * @property boolean password_require_init
 */
class CreateUser extends BaseService
{
    protected $identifier, $user_id, $password, $enable_active, $password_require_init;

    protected function rules()
    {
        return [
            'identifier' => [new ValidThirdIdentifier()],
            'user_id' => 'required|int',
            'enable_active' => 'boolean',
            'password_require_init' => 'boolean',
        ];
    }

    protected function getValidator()
    {
        $validator = parent::getValidator();
        $validator->sometimes('password', new ValidPassword(), function ($in) {
            return isset($in['password_require_init']) && !$in['password_require_init'];
        });
        return $validator;
    }

    protected function validate()
    {
        if($this->identifier) {
            $credential = Credential::checkCredentialExistence($this->identifier);
            if($credential) {
                throw new ExternalException(ErrorCode::IDENTIFIER_ALREADY_OCCUPIED, 'identifier');
            }
        }
        $user = User::findByMainUserId($this->user_id);
        if($user) {
            throw new ExternalException(ErrorCode::IDENTIFIER_ALREADY_OCCUPIED, 'user_id');
        }
    }

    /**
     * @return array|null
     */
    protected function start()
    {
        $now = time();

        $user = new User([
            'user_id' => $this->user_id,
            'password' => $this->password ? bcrypt($this->password) : '',
        ]);
        $user->setBitmaskField('password_require_init', $this->password_require_init);
        if ($this->enable_active) {
            $user->activated = $now;
        }
        $user->save();

        $credential = null;
        if($this->identifier) {
            $identifier = $this->identifier;
            $credential = Credential::generate($user->id, $identifier);
            if($this->enable_active) {
                $credential->setAdditionalValue('verified_at', $now);
            }
            else {
                $credential->status = Credential::STATUS_TO_BE_VALIDATED;
                $credential->regenerateActivationCode();
            }
            $credential->save();
        }
        return [
            'user' => $user,
            'credential' => $credential,
        ];
    }
}