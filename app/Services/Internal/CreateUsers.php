<?php

namespace App\Services\Internal;
use App\Exceptions\ExternalException;
use App\Services\BaseService;

class CreateUsers extends BaseService
{
    protected $credential_infos;

    protected function rules()
    {
        return [
            'credential_infos' => 'required',
        ];
    }

    protected function validate()
    {
    }

    protected function start()
    {
        $create_user = app(CreateUser::class);
        $created_users = [];
        foreach ($this->credential_infos as $credential_info) {
            $create_result = $create_user->handle($credential_info);
            $created_users[$credential_info['user_id']] = $create_result;
        }
        return ['created_users' => $created_users];
    }
}