<?php

namespace App\Services\Internal;

use App\Constants\ErrorCode;
use App\Exceptions\ExternalException;
use App\Models\Credential;
use App\Models\User;
use App\Rules\ValidThirdIdentifier;
use App\Services\BaseService;
use App\Util\CommonHelper;
use App\Util\UA\UAClient;
use App\Util\UA\UAMessageTypes;
use Illuminate\Support\Str;
use App\Remote\User as RemoteUser;

/**
 * Class ChangePassword
 * @package App\Services\Internal
 *
 * @property int user_id
 * @property string password
 */
class ChangePassword extends BaseService
{
    protected $user_id, $password;

    protected function rules()
    {
        return [
            'user_id' => 'required|int',
        ];
    }

    protected function validate()
    {
    }

    /**
     * @return array|null
     */
    protected function start()
    {
        $user = User::findByMainUserId($this->user_id);
        if (!$user) {
            throw new ExternalException(ErrorCode::ACCOUNT_NOT_FOUND, 'user_id');
        }
        $user->setBitmaskField('password_require_init', true);
        $user->password = bcrypt($this->password);
        $user->save();
        UAClient::publish(UAMessageTypes::USER_RESET_PASSWORD, [], ['actor_id' => $user->user_id,'actor_enterprise_id' => $user->enterprise_id]);
        return [
            'user' => $user,
        ];
    }
}