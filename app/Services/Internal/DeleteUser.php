<?php

namespace App\Services\Internal;
use App\Constants\ErrorCode;
use App\Exceptions\ExternalException;
use App\Models\Credential;
use App\Models\User;
use App\Rules\ValidIdentifier;
use App\Rules\ValidPassword;
use App\Services\BaseService;
use App\Util\CommonHelper;
use Illuminate\Support\Str;

/**
 * Class DeleteUser
 * @package App\Services\Internal
 *
 * @property User user
 */
class DeleteUser extends BaseService
{
    protected $user_id;

    protected function rules()
    {
        return [
            'user_id' => 'required|int',
        ];
    }

    protected function validate()
    {
    }

    protected function start()
    {
        $user = User::findByMainUserId($this->user_id);
        if(!$user) {
            return;
        }

        $user->deleteAll();
    }
}