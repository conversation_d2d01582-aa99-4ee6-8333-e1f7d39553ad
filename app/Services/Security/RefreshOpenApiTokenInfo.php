<?php

namespace App\Services\Security;

use App\Remote\OpenApiTokenInfo;
use App\Services\BaseService;
use App\Util\Context;

/**
 * Class RefreshOpenApiTokenInfo
 * @package App\Services\Security
 *
 * @property int due_time_type
 */
class RefreshOpenApiTokenInfo extends BaseService
{

    protected $due_time_type;


    protected function rules()
    {
        return [
            'due_time_type' => 'required',
        ];
    }


    /**
     * @return null
     */
    protected function start()
    {
        $user = Context::user();

        return (new OpenApiTokenInfo())->refreshOpenApiTokenInfo($user->user_id, $this->due_time_type);
    }


}