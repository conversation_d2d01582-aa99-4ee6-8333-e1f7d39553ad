<?php

namespace App\Services\Security;

use App\Exceptions\PermissionDeniedException;
use App\Models\ClientDevice;
use App\Models\User;
use App\Services\BaseService;
use App\Util\Context;
use App\Util\UA\UAClient;
use App\Util\UA\UAMessageTypes;

/**
 * Class ClientDeviceDelete
 * @package App\Services\Security
 *
 * @property int id
 * @property User user
 */
class ClientDeviceDelete extends BaseService
{
    protected $id, $user;

    protected $transaction_needed = false;

    protected function rules()
    {
        return [
            'id' => 'required|min:1',
        ];
    }

    /**
     * @return null|void
     * @throws PermissionDeniedException
     */
    protected function validate()
    {
        $user = $this->user ?? Context::user();
        $this->client_device = ClientDevice::findById($this->id);
        if (!$this->client_device || $user->user_id !== $this->client_device->user_id) {
            throw new PermissionDeniedException;
        }

        $this->user = $user;
    }

    /**
     * @return null|void
     * @throws \Exception
     */
    protected function start()
    {
        $this->client_device->setInvalid();

        $related_client_device = ClientDevice::findByRealId($this->client_device->id);
        if ($related_client_device) {
            foreach($related_client_device as $device) {
                /**
                 * @var ClientDevice $device
                 */
                $device->setInvalid();
            }
        }
    }

    /**
     * @var ClientDevice $client_device
     */
    protected $client_device;

    protected function log()
    {
        $remote_user = $this->user->getRemoteUser();
        $overrides['actor_id'] = $remote_user->id;
        $overrides['actor_enterprise_id'] = $remote_user->enterprise_id;
        UAClient::publish(UAMessageTypes::USER_DELETE_DEVICE, [], $overrides);
    }

}