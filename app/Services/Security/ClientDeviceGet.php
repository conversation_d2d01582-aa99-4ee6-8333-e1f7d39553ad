<?php

namespace App\Services\Security;

use App\Models\ClientDevice;
use App\Services\BaseService;
use App\Util\Context;

/**
 * Class ClientDeviceGet
 * @package App\Services\Security
 */
class ClientDeviceGet extends BaseService
{
    protected $transaction_needed = false;

    /**
     * @return \Illuminate\Database\Eloquent\Collection|static[]
     */
    protected function start()
    {
        $user = Context::user();
        $client_devices = ClientDevice::findByUserId($user->user_id);
        $client_devices = $client_devices->sort(function(ClientDevice $a, ClientDevice $b){
            return $b->getUpdatedAttr() <=> $a->getUpdatedAttr();
        });

        return $client_devices;
    }
}