<?php

namespace App\Services\Security;

use App\Remote\OpenApiTokenInfo;
use App\Services\BaseService;
use App\Util\Context;

/**
 * Class GetOpenApiTokenInfo
 * @package App\Services\Security
 *
 */
class GetOpenApiTokenInfo extends BaseService
{

    /**
     * @return null
     */
    protected function start()
    {
        $user = Context::user();

        return (new OpenApiTokenInfo())->getOpenApiTokenInfo($user->user_id);
    }

}