<?php

namespace App\Services\Security;

use App\Exceptions\ExternalException;
use App\Jobs\SendClientDeviceEmail;
use App\Jobs\SendSystemMessage;
use App\Models\AuthToken;
use App\Models\ClientDevice;
use App\Remote\AuthToken as RemoteAuthToken;
use App\Services\Auth\PreLogin;
use App\Services\BaseService;
use App\Util\CommonHelper;
use App\Util\Context;
use App\Util\UA\UAClient;
use App\Util\UA\UAMessageTypes;
use App\Util\UserAgent;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;

/**
 * Class ClientDeviceRegister
 * 检查 user_id, device_token 所对应的设备是否有效。如果没有该设备，进行注册
 *
 * device_additional_info
 * 如果没给，web端为 user agent， 其他端为 egeio client info
 *
 * @package App\Services\Security
 *
 * @property string|null $auth_token  该设备关联的设备另一条设备，主要用于主动踢出。api 设备就是本身
 */
class ClientDeviceRegister extends BaseService
{
    protected $user_agent, $device_token, $mark_credit, $additional_info, $auth_token, $scene;

    protected $client_device;

    protected $transaction_needed = true;

    protected $ua_old_client_device, $ua_new_client_device;

    protected function rules()
    {
        return [
            'user_agent' => 'nullable|string',
            'device_token' => 'bail|filled|max:64',
            'mark_credit' => 'nullable|boolean',
            'additional_info' => 'nullable|string',
        ];
    }

    /**
     * @return bool|null
     * @throws \Exception
     * @throws \Throwable
     */
    protected function start()
    {
        if (!$this->device_token) {
            $this->skip_log = true;
            return true;
        }
        $user = Context::user();

        $client_device = ClientDevice::findByUserIdDeviceTokenWithLock($user->user_id, $this->device_token);
        $this->ua_old_client_device = $client_device;
        $time = time();

        if (!$client_device) {
            $client_device = new ClientDevice();

            $client_device->user_id = $user->user_id;
            $client_device->device_token = $this->device_token;

            $this->email_status = self::NEW_DEVICE_EMAIL;
        }

        $user_agent = new UserAgent($this->user_agent, $this->auth_token);

        if ($this->auth_token) {
            $auth_token = RemoteAuthToken::get($this->auth_token);
//            $auth_token = AuthToken::findByAuthTokenGently($this->auth_token);

            if ($auth_token) {
                $related_client_device = ClientDevice::findByAuthToken($auth_token->id);
                if ($related_client_device && $client_device->id != $related_client_device->id && !$related_client_device->real_id) {
                    $client_device->real_id = $related_client_device->id;
                    $this->email_status = self::NO_EMAIL;
                }
                $client_device->auth_token_id = $auth_token->id;
            }
        }

        $session_id = Session::getId();
        $client_device->session_id = $session_id;

        /**
         * 检查企业状态
         */
        try {
            (new PreLogin())->handle(['user' => $user]);
        } catch (ExternalException $e) {
            $client_device->logout();
            return false;
        }

        /**
         * sync web view 并且没有绑定一个 其他设备的情况下
         */
        if ($user_agent->isWebviewSync() && !$client_device->auth_token_id) {
            $this->email_status = self::NO_EMAIL;
            return true;
        }

        // 更新 ip
        $ip = CommonHelper::getClientIp();
        $client_device->ip_address = $ip;

        // 标记为可信设备
        if (isset($this->mark_credit) && $this->mark_credit !== null) {
            if ($this->mark_credit) {
                $client_device->credited_time = $time;
            } else {
                $client_device->credited_time = 0;
            }
        }

        // 设置 device type
        // 通过 user agent 拿到对应的 device type
        $client_device->device_type = $user_agent->getDeviceType();

        // 移动端、sync 端的 device additional info 是 egeio client info
        // web 端是 user agent 字符串
        if ($this->additional_info) {
            $client_device->device_additional_info = $this->additional_info;
        } else {
            if ($user_agent->isWebBrowser()) {
                $client_device->device_additional_info = $_SERVER['HTTP_USER_AGENT'] ?? '';
            } else {
                $client_device->device_additional_info = $_SERVER['HTTP_EGEIO_CLIENT_INFO'] ?? '';
            }
        }

        // 对已删除的设备进行操作需要发特殊邮件
        if ($client_device->isDeleted()) {
            // 理论上已经不存在删除了，但是logout_time 不为0 的情况
            // 这种数据是 account 站点上线前的老数据
            // 如果是登录请求，希望不是直接踢出去
            if ($client_device->isOnline() && !($this->scene === 'login')) {
                $client_device->logout();
                return false;
            } else {
                $this->email_status = self::NEW_DEVICE_EMAIL;
                $client_device->deleted = 0;
            }
        } else if ($client_device->getUpdatedAttr()
            && ($time - $client_device->getUpdatedAttr()) > config('common.two_step.device_last_login_period')
        ) {
            $this->email_status = self::NEW_DEVICE_EMAIL;
        }

        // 置为在线设备
        $client_device->logout_time = 0;

        $client_device->updated = $time;

        $client_device->save();
        $this->client_device = $client_device;
        $this->ua_new_client_device = $client_device;
        return true;
    }

    protected function after()
    {
        if(!$this->client_device) {
            return;
        }
        if ($this->email_status != self::NO_EMAIL) {
            $user = Context::user();
            $enterprise = Context::enterprise() ? : $user->getRemoteUser()->getEnterprise();

            if (!$enterprise->is_skip_first_login_email && $user->identity_type_email_available) {
                SendClientDeviceEmail::dispatch($this->client_device);
            }
        }
    }

    protected function log()
    {
        UAClient::publish(UAMessageTypes::USER_CLIENT_DEVICE_LOGIN, [
            'device_last_login_period' => (int)config('common.two_step.device_last_login_period'),
            'old' => $this->ua_old_client_device,
            'new' => $this->ua_new_client_device
        ]);
    }

    /**
     * 0  =>  不需要发邮件
     * 1  =>  发送设备登录提醒邮件
     *
     * @var int $email_status
     */
    protected $email_status = self::NO_EMAIL;

    private const NO_EMAIL = 0;
    private const NEW_DEVICE_EMAIL = 1;
}