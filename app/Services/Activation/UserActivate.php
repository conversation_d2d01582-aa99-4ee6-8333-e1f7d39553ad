<?php

namespace App\Services\Activation;
use App\Constants\ErrorCode;
use App\Events\UserActivated;
use App\Exceptions\ExternalException;
use App\Models\User;
use App\Util\Captcha;
use App\Models\Credential;
use App\Rules\ValidPassword;
use App\Rules\ValidPhone;
use App\Rules\ValidUserName;
use App\Services\BaseService;
use App\Util\UA\UAClient;
use App\Util\UA\UAMessageTypes;

/**
 * Class UserActivate
 * @package App\Services\Auth
 *
 * @property Credential credential
 * @property string name
 * @property string email
 * @property string phone
 * @property string password
 * @property string sms_captcha
 *
 * @property User user
 */
class UserActivate extends BaseService
{
    protected $credential, $name, $phone, $password, $sms_captcha;
    protected $remote_user;

    protected function rules()
    {
        return [
            'credential' => ['required'],
            'name' => ['required', new ValidUserName],
            'password' => ['required', new ValidPassword],
        ];
    }

    protected function getValidator()
    {
        $validator = parent::getValidator();
        $validator->sometimes('sms_captcha', 'required', function ($input) {
            return $this->credential->type == Credential::TYPE_EMAIL;
        });
        $validator->sometimes('phone', ['required', new ValidPhone], function ($input) {
            return $this->credential->type == Credential::TYPE_EMAIL;
        });
        return $validator;
    }

    protected function validate()
    {
        $credential = $this->credential;
        if(!$credential || $credential->status != Credential::STATUS_TO_BE_VALIDATED) {
            throw new ExternalException(ErrorCode::ACTIVATION_CODE_INVALID);
        }
        if($credential->activation_expires_at < time()) {
            throw new ExternalException(ErrorCode::ACTIVATION_CODE_INVALID);
        }
        if($credential->isTypeEmail()) {
            if(!Captcha::check($this->sms_captcha, Captcha::TYPE_ACTIVATE, Captcha::THROUGH_SMS, $this->phone)) {
                throw new ExternalException(ErrorCode::SMS_CAPTCHA_INVALID, 'sms_captcha');
            }
            $phone_credential = Credential::checkCredentialExistence($this->phone, Credential::TYPE_PHONE);
            if($phone_credential) {
                throw new ExternalException(ErrorCode::PHONE_ALREADY_OCCUPIED, 'phone');
            }
        }

        $user = $credential->getUser();
        $enterprise = $user->getRemoteUser()->getEnterprise();
        if(!preg_match($enterprise->getPasswordPattern(), $this->password)) {
            throw new ExternalException(ErrorCode::PASSWORD_NOT_MATCH_ENTERPRISE_SETTING, 'password');
        }

        $this->user = $user;
    }

    protected function start()
    {
        $credential = $this->credential;
        $user = $this->user;

        $now = time();
        $user->setPassword($this->password);
        $user->activate($now);
        $user->save();

        $credential->activation_code = '';
        $credential->activation_expires_at = 0;
        $credential->status = Credential::STATUS_VALID;
        $credential->setAdditionalValue('verified_at', $now);
        $credential->save();

        $remote_user = $user->getRemoteUser(false);
        $remote_user->prepareUpdate();
        $remote_user->updateInfo([
            'name' => $this->name,
        ]);
        if($credential->isTypeEmail()) {
            $phone_credential = Credential::generate($user->id, $this->phone, Credential::TYPE_PHONE);
            $phone_credential->setAdditionalValue('verified_at', $now);
            $phone_credential->save();

            $remote_user->verifyContact(Credential::EMAIL, $now);
            $remote_user->updateInfo(['phone' => $this->phone]);
            $remote_user->verifyContact(Credential::PHONE, $now);
        }
        else {
            $remote_user->verifyContact(Credential::PHONE);
        }

        $remote_user->commitUpdate();
        $remote_user->activate();

        event(new UserActivated($user));

        $this->remote_user = $remote_user;
        return ['user' => $user];
    }

    protected function log()
    {
        $overrides['actor_id'] = $this->remote_user->id;
        $overrides['actor_enterprise_id'] = $this->remote_user->enterprise_id;
        UAClient::publish(UAMessageTypes::USER_ACTIVATION, [], $overrides);
    }

}