<?php

namespace App\Services\Activation;
use App\Constants\ErrorCode;
use App\Constants\SessionCacheKey;
use App\Exceptions\ExternalException;
use App\Exceptions\V1AccountActivateException;
use App\Models\Credential;
use App\Remote\V1Account;
use App\Rules\ValidPhone;
use App\Services\BaseService;
use App\Util\CacheHelper;
use App\Util\Captcha;

/**
 * Class ConfirmActivateAccount
 * @package App\Services\Activation
 *
 * @property Credential credential
 */
class ConfirmActivateAccount extends BaseService
{
    protected $phone, $pic_captcha, $allow_v1;

    protected function rules()
    {
        return [
            'phone' => ['required', new ValidPhone()],
            'pic_captcha' => 'required',
        ];
    }

    protected function default()
    {
        if (!isset($this->allow_v1) || $this->allow_v1 === null)
        {
            $this->allow_v1 = true;
        }
    }

    protected function validate()
    {
        if(!Captcha::check($this->pic_captcha, Captcha::TYPE_ACTIVATE_BY_PHONE)) {
            throw new ExternalException(ErrorCode::PIC_CAPTCHA_INVALID, 'pic_captcha');
        }
        $credential = Credential::getUniqueCredentialByIdentifier($this->phone, Credential::TYPE_PHONE);
        if(!$credential) {
            if (config('app.disable_v1')) {
                throw new ExternalException(ErrorCode::CREDENTIAL_TO_BE_ACTIVATED_NOT_FOUND, 'phone');
            }
            $v1_account = new V1Account();
            $v1_res = $v1_account->activate([
                'phone' => $this->phone,
            ]);
            if($v1_res['token']) {
                if (!$this->allow_v1) {
                    throw new ExternalException(ErrorCode::V1_ACTIVATE_NOT_AllOWED, 'phone');
                }
                throw new V1AccountActivateException($v1_res['token']);
            }
            if($v1_res['error_code']) {
                throw new ExternalException($v1_res['error_code'], 'phone');
            }
            throw new ExternalException(ErrorCode::CREDENTIAL_TO_BE_ACTIVATED_NOT_FOUND, 'phone');
        }
        if($credential->status != Credential::STATUS_TO_BE_VALIDATED || !$credential->activation_code) {
            throw new ExternalException(ErrorCode::CREDENTIAL_TO_BE_ACTIVATED_NOT_FOUND, 'phone');
        }
        $user = $credential->getUser();
        if(!$user) {
            throw new ExternalException(ErrorCode::CREDENTIAL_TO_BE_ACTIVATED_NOT_FOUND, 'phone');
        }

        $this->credential = $credential;
    }

    protected function start()
    {
        $credential = $this->credential;

        CacheHelper::cacheSessionInfo(SessionCacheKey::ACTIVATION_INFO, ['activation_code' => $credential->activation_code, 'phone' => $this->phone]);
    }
}