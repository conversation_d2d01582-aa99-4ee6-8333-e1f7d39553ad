<?php

namespace App\Services\Activation;
use App\Constants\ErrorCode;
use App\Constants\SessionCacheKey;
use App\Exceptions\ExternalException;
use App\Models\Credential;
use App\Rules\ValidPhone;
use App\Services\BaseService;
use App\Util\CacheHelper;
use App\Util\Captcha;

/**
 * Class VerifyActivationPhone
 * @package App\Services\Activation
 *
 * @property Credential credential
 */
class VerifyActivationPhone extends BaseService
{
    protected $sms_captcha;

    protected function rules()
    {
        return [
            'sms_captcha' => 'required',
        ];
    }

    protected function validate()
    {
        $activation_info = CacheHelper::getCachedSessionInfo(SessionCacheKey::ACTIVATION_INFO) ?: [];
        if(!$activation_info || !$activation_info['activation_code'] || !$activation_info['phone']) {
            throw new ExternalException(ErrorCode::ACTIVATION_INFO_EXPIRED);
        }
        if(!Captcha::check($this->sms_captcha, Captcha::TYPE_ACTIVATE_BY_PHONE, Captcha::THROUGH_SMS, $activation_info['phone'])) {
            throw new ExternalException(ErrorCode::SMS_CAPTCHA_INVALID, 'sms_captcha');
        }
        $credential = Credential::getCredentialByActivationCode($activation_info['activation_code']);
        if(!$credential) {
            throw new ExternalException(ErrorCode::ACTIVATION_INFO_EXPIRED);
        }

        $this->credential = $credential;
    }

    protected function start()
    {
        $credential = $this->credential;

        CacheHelper::clearCachedSessionInfo(SessionCacheKey::ACTIVATION_INFO);
        return ['activation_url' => $credential->getActivationUrl()];
    }
}