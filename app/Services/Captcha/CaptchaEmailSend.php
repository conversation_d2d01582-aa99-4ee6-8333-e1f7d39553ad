<?php

namespace App\Services\Captcha;

use App\Constants\ErrorCode;
use App\Exceptions\ExternalException;
use App\Rules\ValidEmail;
use App\Rules\ValidPhone;
use App\Util\Captcha;
use App\Util\Email;
use App\Util\Sms;
use App\Services\BaseService;
use Illuminate\Validation\Rule;

class CaptchaEmailSend extends BaseService
{
    protected $email, $captcha_type, $ip;

    protected function rules()
    {
        return [
            'email' => ['required', new ValidEmail],
            'captcha_type' => ['required', Rule::in([
            ])],
            'ip' => ['required'],
        ];
    }

    protected function validate()
    {
    }

    protected function start()
    {
        $captcha = Captcha::generateCaptcha($this->captcha_type, Captcha::THROUGH_EMAIL, $this->email);
        Email::sendCaptcha($captcha, $this->email);
    }
}