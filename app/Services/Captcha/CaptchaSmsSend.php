<?php

namespace App\Services\Captcha;

use App\Constants\ErrorCode;
use App\Exceptions\ExternalException;
use App\Remote\SmsStrategy;
use App\Rules\ValidPhone;
use App\Util\Captcha;
use App\Util\CommonHelper;
use App\Util\Context;
use App\Util\LogHelper;
use App\Util\LogType;
use App\Util\Sms;
use App\Services\BaseService;
use Illuminate\Validation\Rule;
use App\Library\Janitor\OneDayJanitor;

class CaptchaSmsSend extends BaseService
{
    protected $phone, $message_type, $captcha_type, $pic_captcha, $ip, $skip_sdk_captcha_check;

    const DAILY_PHONE_LIMIT_KEY = 'update_phone_daily_limit';
    const DAILY_LIMIT_CNT = 1;

    protected function rules()
    {
        return [
            'phone' => ['required', new ValidPhone],
            'message_type' => ['required', Rule::in([
                Sms::MESSAGE_TXT,
                Sms::MESSAGE_VOICE,
            ])],
            'captcha_type' => ['required', Rule::in([
                Captcha::TYPE_REGISTER,
                Captcha::TYPE_INVITE_LINK_REGISTER,
                Captcha::TYPE_FORGOT_PASSWORD_BY_PHONE,
                Captcha::TYPE_ACTIVATE_BY_PHONE,
                Captcha::TYPE_ACTIVATE,
                Captcha::TYPE_MODIFY_PHONE,
                Captcha::TYPE_TWO_STEP,
                Captcha::TYPE_TWO_STEP_LOGIN,
                Captcha::TYPE_BECOME_REFERRAL,
            ])],
            'ip' => ['required'],
        ];
    }

    protected function getValidator()
    {
        $validator = parent::getValidator();
        LogHelper::info(LogType::WebAndServerService, "skip_sdk_captcha_check".$this->skip_sdk_captcha_check);
        $validator->sometimes('pic_captcha', 'required', function ($input) {
            return isset($this->skip_sdk_captcha_check) && !$this->skip_sdk_captcha_check && Sms::isPicCaptchaRequired($this->getCaptchaIdentifier($this->captcha_type));
        });
        return $validator;
    }

    private function getCaptchaIdentifier($captcha_type) {
        switch ($captcha_type) {
            case Captcha::TYPE_REGISTER:
            case Captcha::TYPE_BECOME_REFERRAL:
                return [$captcha_type.'_'.$this->phone, $captcha_type.'_'.$this->ip];
                break;
            case Captcha::TYPE_FORGOT_PASSWORD_BY_PHONE:
            case Captcha::TYPE_ACTIVATE_BY_PHONE:
            case Captcha::TYPE_ACTIVATE:
            case Captcha::TYPE_TWO_STEP:
            case Captcha::TYPE_TWO_STEP_LOGIN:
                return $captcha_type.'_'.$this->ip;
                break;
            case Captcha::TYPE_MODIFY_PHONE:
                $user = Context::user();
                if (!$user) {
                    throw new ExternalException(ErrorCode::PERMISSION_DENIED);
                }
                return $captcha_type.'_'.$user->id;
                break;
        }
        return null;
    }

    protected function validate()
    {
        $picCaptchaRequired = Sms::isPicCaptchaRequired($this->getCaptchaIdentifier($this->captcha_type));
        $checkCaptchaTypes = [Captcha::TYPE_BECOME_REFERRAL, Captcha::TYPE_MODIFY_PHONE];
        if (in_array($this->captcha_type, $checkCaptchaTypes))
        {
            $picCaptchaRequired = true;
        }
        if ($this->captcha_type == Captcha::TYPE_FORGOT_PASSWORD_BY_PHONE) {
            $picCaptchaRequired = true;
        }
        $user = Context::user();
        if ($this->captcha_type == Captcha::TYPE_MODIFY_PHONE && $user) {
            self::checkOneDayOperationLimit($user);
        }
        $chineseIp = CommonHelper::isChineseIpExcludeSpecialRegion(CommonHelper::getClientIp());
        $requiredCaptcha = $picCaptchaRequired || !$chineseIp;
        if($requiredCaptcha && isset($this->skip_sdk_captcha_check) &&  !$this->skip_sdk_captcha_check && !Captcha::check($this->pic_captcha, $this->captcha_type)) {
            throw new ExternalException(ErrorCode::PIC_CAPTCHA_INVALID, self::picRequiredMsg());
        }
        if ($this->captcha_type == Captcha::TYPE_REGISTER)
        {
            if (strpos($this->phone, '(+') === 0 && strpos($this->phone, '(+86)') !== 0)
            {
                list($country_code, $identifier) = CommonHelper::extractCountryCodeAndPhone($this->phone);
                if ($country_code != 86)
                {
                    $hit_strategy = SmsStrategy::hitSmsStrategy($this->captcha_type, $country_code, $this->phone);
                    if ($hit_strategy)
                    {
                        throw new ExternalException(ErrorCode::REGISTER_EXCEED_LIMIT);
                    }
                }
            }
        }
    }

    private function checkOneDayOperationLimit($user)
    {
        $janitor = static::getDailyJanitor($user->user_id);
        if ($janitor->isFrozen()) {
            throw new ExternalException(ErrorCode::UPDATE_PHONE_EXCEED_LIMIT, 'phone');
        }
        $janitor->hit();
    }

    private static function getDailyJanitor($identifier)
    {
        return (new OneDayJanitor(static::DAILY_PHONE_LIMIT_KEY."_".$identifier))->setThresholdCount(static::DAILY_LIMIT_CNT);
    }

    private function picRequiredMsg(): string
    {
        return isset($this->skip_sdk_captcha_check) && !$this->skip_sdk_captcha_check ? 'pic_captcha' : 'sliding_pic_captcha';
    }

    protected function start()
    {
        $captcha = Captcha::generateCaptcha($this->captcha_type, Captcha::THROUGH_SMS, $this->phone);
        Sms::sendCaptcha($captcha, $this->captcha_type, $this->message_type, $this->phone, $this->ip, config('app.locale'), null, $this->getCaptchaIdentifier($this->captcha_type));
    }
}