<?php

namespace App\Services;

use App\Exceptions\ValidationException;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Mockery\Exception;

class BaseService
{
    protected $transaction_needed = true;
    protected $valid_params = [];
    protected $skip_log = false;

    /**
     * @param array $params
     * @return null
     * @throws ValidationException
     * @throws \Exception
     * @throws \Throwable
     */
    public function handle(array $params = [])
    {
        $this->init($params);
        $this->regularValidate();
        $this->validate();
        $res = null;

        $this->before();
        if($this->transaction_needed){
            DB::transaction(function() use (&$res){
                $res = $this->start();
            });
        }
        else {
            $res = $this->start();
        }
        $this->after();
        return $res;
    }

    protected function rules()
    {
        return [];
    }

    protected function getValidator()
    {
        return Validator::make($this->valid_params, $this->rules());
    }

    /**
     * @param array $params
     */
    private function init(array $params)
    {
        $vars = get_object_vars($this);
        foreach (array_keys($vars) as $key){
            if(isset($params[$key])) {
                $this->$key = $params[$key];
                $this->valid_params[$key] = $params[$key];
            }
        }
        $this->default();
    }

    protected function regularValidate()
    {
        $validator = $this->getValidator();
        if($validator->fails()) {
            throw new ValidationException($validator->errors()->toArray());
        }
    }

    protected function validate()
    {
        return null;
    }

    protected function default()
    {
        return null;
    }

    protected function log()
    {
        return null;
    }

    protected function start()
    {
        return null;
    }

    /**
     * do something before run function start
     */
    protected function before()
    {
        return ;
    }

    /**
     * do something after ran function start
     */
    protected function after()
    {
        if (!$this->skip_log) {
            $this->log();
        }
    }

}