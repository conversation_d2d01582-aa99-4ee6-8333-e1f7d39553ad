<?php

namespace App\Services\Password;
use App\Constants\ErrorCode;
use App\Constants\SessionCacheKey;
use App\Exceptions\ExternalException;
use App\Jobs\Emails\SendResetPasswordEmail;
use App\Models\Credential;
use App\Models\PasswordReset;
use App\Models\User;
use App\Notifications\ResetPassword;
use App\Rules\ValidIdentifier;
use App\Util\ArrayHelper;
use App\Util\AuthHelper;
use App\Rules\ValidPhone;
use App\Services\BaseService;
use App\Util\CacheHelper;
use App\Util\Captcha;
use App\Util\CommonHelper;
use App\Util\Sms;
use App\Util\UA\UAMessageTypes;
use App\Util\UA\UAClient;
use Illuminate\Support\Arr;
use Illuminate\Validation\Rule;

/**
 * Class ForgotPassword
 * @package App\Services\Password
 *
 * @property User user
 */
class ForgotPassword extends BaseService
{
    protected $reset_by, $sms_captcha;

    protected function rules()
    {
        return [
            'reset_by' => ['required', Rule::in([
                Credential::TYPE_EMAIL,
                Credential::TYPE_PHONE,
            ])],
        ];
    }

    protected function getValidator()
    {
        $validator = parent::getValidator();
        $validator->sometimes('sms_captcha', 'required', function ($input) {
            return $input['reset_by'] == Credential::TYPE_PHONE;
        });
        return $validator;
    }

    protected function validate()
    {
        $user_info = CacheHelper::getCachedSessionInfo(SessionCacheKey::FORGOT_PASSWORD_INFO) ?: [];
        if(!$user_info || !$user_info['user_id']) {
            throw new ExternalException(ErrorCode::FORGOT_PASSWORD_INFO_EXPIRED );
        }
        $user = User::find($user_info['user_id']);
        if(!$user) {
            throw new ExternalException(ErrorCode::FORGOT_PASSWORD_INFO_EXPIRED );
        }
        $credential = Credential::getUserCredential($user->id, $this->reset_by);
        if(!$credential || !$credential->isValid()) {
            throw new ExternalException(ErrorCode::FORGOT_PASSWORD_INFO_EXPIRED);
        }
        if($this->reset_by == Credential::TYPE_PHONE && !Captcha::check($this->sms_captcha,
                Captcha::TYPE_FORGOT_PASSWORD_BY_PHONE, Captcha::THROUGH_SMS, $credential->getIdentifier())) {
            throw new ExternalException(ErrorCode::SMS_CAPTCHA_INVALID, 'sms_captcha');
        }

        $this->user = $user;
        $this->credential = $credential;
    }

    protected function start()
    {
        $user = $this->user;

        $password_reset = PasswordReset::createForUser($user->id);
        $res = [];
        if($this->reset_by == Credential::TYPE_PHONE) {
            $res['redirect'] = $password_reset->getPasswordResetUrl();
        }
        elseif($this->reset_by == Credential::TYPE_EMAIL) {
            SendResetPasswordEmail::dispatch($user, $password_reset->getPasswordResetUrl());
        }
        CacheHelper::clearCachedSessionInfo(SessionCacheKey::FORGOT_PASSWORD_INFO);
        return $res;
    }

    protected function log()
    {
        UAClient::publish(UAMessageTypes::USER_FORGOT_PASSWORD);
    }
}