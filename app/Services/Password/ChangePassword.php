<?php

namespace App\Services\Password;
use App\Constants\ErrorCode;
use App\Events\PasswordChanged;
use App\Exceptions\ExternalException;
use App\Models\PasswordReset;
use App\Models\User;
use App\Rules\ValidPassword;
use App\Services\BaseService;
use App\Util\Context;
use App\Util\UA\UAMessageTypes;
use App\Util\UA\UAClient;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

/**
 * Class ChangePassword
 * @package App\Services\Password
 *
 * @property string old_password
 * @property string password
 * @property string password_confirmation
 *
 * @property User user
 */
class ChangePassword extends BaseService
{
    protected $old_password, $password, $password_confirmation;

    protected function rules()
    {
        return [
            'old_password' => ['required'],
            'password' => ['required'],
            'password_confirmation' => ['required'],
        ];
    }

    private function validPassword($value) {
        $len = Str::length($value);
        if($len < 8 || $len > 32) {
            return false;
        }
        if ( ! preg_match("/^[^\s][ -~]*[^\s]$/", $value)) {
            return false;
        }
        if ( ! preg_match("/^.*(?=.*\d)(?=.*[a-zA-Z]).*$/", $value)) {
            return false;
        }
        return true;
    }

    protected function validate()
    {
        if (!$this->validPassword($this->password)) {
            throw new ExternalException(ErrorCode::INVALID_PASSWORD, 'password');
        }
        $user = Context::user();
        if(!$user->authenticate($this->old_password)) {
            throw new ExternalException(ErrorCode::OLD_PASSWORD_INCORRECT, 'old_password');
        }
        if($this->password != $this->password_confirmation) {
            throw new ExternalException(ErrorCode::PASSWORD_NOT_CONFIRMED, 'password_confirmation');
        }
        $enterprise = $user->getRemoteUser()->getEnterprise();
        if(!preg_match($enterprise->getPasswordPattern(), $this->password)) {
            throw new ExternalException(ErrorCode::PASSWORD_NOT_MATCH_ENTERPRISE_SETTING, 'password');
        }

        $this->user = $user;
    }

    protected function start()
    {
        $user = $this->user;

        $user->setPassword($this->password);
        $user->save();

        Auth::guard()->logout();
        PasswordReset::deleteByUser($user->id);

        event(new PasswordChanged($user));
    }

    protected function log()
    {
        $user = $this->user;
        if(isset($user)) {
            UAClient::publish(UAMessageTypes::USER_CHANGE_PASSWORD, [], ['actor_id' => $user->user_id]);
        } else {
            UAClient::publish(UAMessageTypes::USER_CHANGE_PASSWORD);
        }
    }

}