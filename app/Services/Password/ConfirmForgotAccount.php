<?php

namespace App\Services\Password;
use App\Constants\ErrorCode;
use App\Constants\SessionCacheKey;
use App\Exceptions\ExternalException;
use App\Exceptions\V1AccountForgotPasswordException;
use App\Models\Credential;
use App\Remote\V1Account;
use App\Rules\ValidIdentifier;
use App\Util\ArrayHelper;
use App\Util\AuthHelper;
use App\Rules\ValidPhone;
use App\Services\BaseService;
use App\Util\CacheHelper;
use App\Util\Captcha;
use App\Util\CommonHelper;
use Illuminate\Support\Arr;

class ConfirmForgotAccount extends BaseService
{
    protected $login, $pic_captcha;

    protected function rules()
    {
        return [
            'login' => ['required', new ValidIdentifier],
            'pic_captcha' => 'required',
        ];
    }

    protected function validate()
    {
        if(!Captcha::check($this->pic_captcha, Captcha::TYPE_FORGOT_PASSWORD)) {
            throw new ExternalException(ErrorCode::PIC_CAPTCHA_INVALID, 'pic_captcha');
        }
        $credential = Credential::getUniqueCredentialByIdentifier($this->login);
        if(!$credential) {
            if (config('app.disable_v1')) {
                throw new ExternalException(ErrorCode::ACCOUNT_NOT_FOUND, 'login');
            }
            $v1_account = new V1Account();
            $v1_res = $v1_account->forgot_password([
                'identifier' => $this->login,
                'is_international' => CommonHelper::isValidInternationalPhone($this->login),
            ]);
            if($v1_res['token']) {
                throw new V1AccountForgotPasswordException($v1_res['token']);
            }
            if($v1_res['error_code']) {
                throw new ExternalException($v1_res['error_code'], 'login');
            }
            throw new ExternalException(ErrorCode::ACCOUNT_NOT_FOUND, 'login');
        }
        if($credential->status != Credential::STATUS_VALID) {
            $error_code = $credential->isTypeEmail() ? ErrorCode::CREDENTIAL_EMAIL_UNVERIFIED :
                ($credential->isTypePhone() ? ErrorCode::CREDENTIAL_PHONE_UNVERIFIED : ErrorCode::LOGIN_UNVERIFIED);
            throw new ExternalException($error_code, 'login');
        }
        $user = $credential->getUser();
        if(!$user) {
            throw new ExternalException(ErrorCode::ACCOUNT_NOT_FOUND, 'login');
        }

        $this->user = $user;
    }

    protected function start()
    {
        $user = $this->user;
        $credentials = Credential::getUserCredentials($user->id, [Credential::TYPE_EMAIL, Credential::TYPE_PHONE]);

        $res = [];
        $credentials_index_by_type = ArrayHelper::indexObjectsByField('type', $credentials);
        $session_info = [];
        if(isset($credentials_index_by_type[Credential::TYPE_PHONE]) && ($phone_credential = $credentials_index_by_type[Credential::TYPE_PHONE]) && $phone_credential->isValid()) {
            $phone = $phone_credential->getIdentifier();
            $res['phone'] = CommonHelper::hideIdentifier($phone);
            $session_info['user_id'] = $user->id;
            $session_info['phone'] = $phone;
        }
        if(isset($credentials_index_by_type[Credential::TYPE_EMAIL]) && ($email_credential = $credentials_index_by_type[Credential::TYPE_EMAIL]) && $email_credential->isValid()) {
            $email = $email_credential->getIdentifier();
            $res['email'] = CommonHelper::hideIdentifier($email);
            $session_info['user_id'] = $user->id;
        }
        if($session_info) {
            CacheHelper::cacheSessionInfo(SessionCacheKey::FORGOT_PASSWORD_INFO, $session_info);
        }
        return $res;
    }
}