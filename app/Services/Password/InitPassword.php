<?php

namespace App\Services\Password;

use App\Constants\ErrorCode;
use App\Exceptions\ExternalException;
use App\Models\User;
use App\Rules\ValidPassword;
use App\Services\BaseService;
use App\Util\Context;
use App\Util\UA\UAMessageTypes;
use App\Util\UA\UAClient;

/**
 * Class InitPassword
 * @package App\Services\Password
 *
 * @property string password
 *
 * @property User user
 */
class InitPassword extends BaseService
{
    protected $password;

    protected function rules()
    {
        return [
            'password' => ['required', new ValidPassword],
        ];
    }

    protected function validate()
    {
        $user = Context::user();
        $enterprise = $user->getRemoteUser()->getEnterprise();
        if(!preg_match($enterprise->getPasswordPattern(), $this->password)) {
            throw new ExternalException(ErrorCode::PASSWORD_NOT_MATCH_ENTERPRISE_SETTING, 'password');
        }

        $this->user = $user;
    }

    protected function start()
    {
        $user = $this->user;

        $user->unsetPasswordRequireInitAttr();
        $user->setPassword($this->password);
        $user->save();
    }

    protected function log()
    {
        UAClient::publish(UAMessageTypes::USER_CHANGE_PASSWORD);
    }

}