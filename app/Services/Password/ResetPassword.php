<?php

namespace App\Services\Password;
use App\Constants\ErrorCode;
use App\Events\PasswordChanged;
use App\Exceptions\ExternalException;
use App\Models\Credential;
use App\Models\PasswordReset;
use App\Models\User;
use App\Rules\ValidPassword;
use App\Util\AuthHelper;
use App\Services\BaseService;
use App\Util\Captcha;
use App\Util\UA\UAMessageTypes;
use App\Util\UA\UAClient;

/**
 * Class ResetPassword
 * @package App\Services\Password
 *
 * @property User user
 * @property string password
 * @property string password_confirmation
 */
class ResetPassword extends BaseService
{
    protected $user, $password, $password_confirmation;

    protected function rules()
    {
        return [
            'user' => ['required'],
            'password' => ['required', new ValidPassword],
            'password_confirmation' => ['required'],
        ];
    }

    protected function validate()
    {
        if($this->password != $this->password_confirmation) {
            throw new ExternalException(ErrorCode::PASSWORD_NOT_CONFIRMED, 'password_confirmation');
        }

        $user = $this->user;
        $enterprise = $user->getRemoteUser()->getEnterprise();
        if(!preg_match($enterprise->getPasswordPattern(), $this->password)) {
            throw new ExternalException(ErrorCode::PASSWORD_NOT_MATCH_ENTERPRISE_SETTING, 'password');
        }
    }

    protected function start()
    {
        $user = $this->user;

        $user->setPassword($this->password);
        $user->save();
        PasswordReset::deleteByUser($user->id);

        event(new PasswordChanged($user));
    }

    protected function log()
    {
        UAClient::publish(UAMessageTypes::USER_RESET_PASSWORD);
    }
}