<?php

namespace App\Services\Password;
use App\Constants\ErrorCode;
use App\Constants\SessionCacheKey;
use App\Exceptions\ExternalException;
use App\Models\Credential;
use App\Models\PasswordReset;
use App\Models\User;
use App\Rules\ValidPassword;
use App\Util\AuthHelper;
use App\Services\BaseService;
use App\Util\CacheHelper;
use App\Util\Captcha;
use App\Util\UA\UAMessageTypes;
use App\Util\UA\UAClient;
use Illuminate\Support\Facades\Auth;

/**
 * Class SetPasswordForSsoUser
 * @package App\Services\Password
 *
 * @property string password
 *
 * @property User user
 */
class SetPasswordForSsoUser extends BaseService
{
    protected $password;

    protected function rules()
    {
        return [
            'password' => ['required', new ValidPassword],
        ];
    }

    protected function validate()
    {
        $user = Auth::user();

        $enterprise = $user->getRemoteUser()->getEnterprise();
        if(!$enterprise->is_sso_enterprise || !$enterprise->is_public_login_available) {
            throw new ExternalException(ErrorCode::PUBLIC_LOGIN_ENABLED_SSO_ENTERPRISES_ONLY);
        }

        if(!$user->isPasswordUnset()) {
            throw new ExternalException(ErrorCode::PASSWORD_ALREADY_SET);
        }

        if(!preg_match($enterprise->getPasswordPattern(), $this->password)) {
            throw new ExternalException(ErrorCode::PASSWORD_NOT_MATCH_ENTERPRISE_SETTING, 'password');
        }

        $this->user = $user;
    }

    protected function start()
    {
        $user = $this->user;

        $user->setPassword($this->password);
        $user->save();

        PasswordReset::deleteByUser($user->id);

        $expiration = config('cache.expiration.password_verified');
        CacheHelper::cacheSessionInfo(SessionCacheKey::IDENTITY_VERIFIED, time() + $expiration, $expiration);
    }
}