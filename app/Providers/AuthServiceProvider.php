<?php

namespace App\Providers;

use App\Auth\AuthTokenGuard;
use App\Auth\RedisGuard;
use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Auth;
use App\Auth\EloquentUserProvider;
use App\Auth\SessionGuard;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * The policy mappings for the application.
     *
     * @var array
     */
    protected $policies = [
        'App\Model' => 'App\Policies\ModelPolicy',
    ];

    /**
     * Register any authentication / authorization services.
     *
     * @return void
     */
    public function boot()
    {
        $this->registerPolicies();

        Auth::provider('custom_eloquent', function($app, array $config){
            return new EloquentUserProvider($app['hash'], $config['model']);
        });

        Auth::extend('custom_session', function($app, $name, array $config){
            $provider = Auth::createUserProvider($config['provider']);

            $guard = new SessionGuard($name, $provider, $app['session.store']);

            // When using the remember me functionality of the authentication services we
            // will need to be set the encryption instance of the guard, which allows
            // secure, encrypted cookie values to get generated for those cookies.
            if (method_exists($guard, 'setCookieJar')) {
                $guard->setCookieJar($this->app['cookie']);
            }

            if (method_exists($guard, 'setDispatcher')) {
                $guard->setDispatcher($this->app['events']);
            }

            if (method_exists($guard, 'setRequest')) {
                $guard->setRequest($this->app->refresh('request', $guard, 'setRequest'));
            }

            return $guard;
        });

        Auth::extend('auth_token', function($app, $name, array $config){
            $provider = Auth::createUserProvider($config['provider']);

            $guard = new AuthTokenGuard($name, $provider);

            if (method_exists($guard, 'setRequest')) {
                $guard->setRequest($this->app->refresh('request', $guard, 'setRequest'));
            }

            return $guard;
        });

        Auth::extend('redis', function($app, $name, array $config){
            $provider = Auth::createUserProvider($config['provider']);

            $guard = new RedisGuard($name, $provider);

            if (method_exists($guard, 'setRequest')) {
                $guard->setRequest($this->app->refresh('request', $guard, 'setRequest'));
            }

            return $guard;
        });
    }
}
