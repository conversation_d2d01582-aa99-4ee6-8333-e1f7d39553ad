<?php

namespace App\Providers;

use App\Util\CommonHelper;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        //
//        DB::listen(function($query){
//            $singleSql = $query->sql;
//            if ($query->bindings) {
//                foreach ($query->bindings as $replace) {
//                    $value = is_numeric($replace) ? $replace : "'" . $replace . "'";
//                    $singleSql = preg_replace('/\?/', $value, $singleSql, 1);
//                }
//                dump($singleSql);
//            } else {
//                dump($singleSql);
//            }
//
//        });
    }

    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        URL::forceRootUrl(config('app.url'));
        // 注册 ide helper
        if ($this->app->environment() !== 'production') {
            $this->app->register(\Barryvdh\LaravelIdeHelper\IdeHelperServiceProvider::class);
        }
    }
}
