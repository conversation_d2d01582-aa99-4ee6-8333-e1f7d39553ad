<?php

namespace App\Providers;

use App\Events\QuickUserRegistered;
use Illuminate\Support\Facades\Event;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event listener mappings for the application.
     *
     * @var array
     */
    protected $listen = [
        'App\Events\PasswordChanged' => [
            'App\Listeners\ChangePasswordListener',
        ],
        'App\Events\UserCreated' => [
            'App\Listeners\UserCreatedListener',
        ],
        'App\Events\QuickUserRegistered' => [
            'App\Listeners\QuickUserCreatedListener',
        ],
        'App\Events\EnterpriseCreated' => [
            'App\Listeners\EnterpriseCreatedListener',
        ],
        'App\Events\DeviceLoggedOut' => [
            'App\Listeners\DeviceLoggedOutListener',
        ],
    ];

    /**
     * Register any events for your application.
     *
     * @return void
     */
    public function boot()
    {
        parent::boot();

        //
    }
}
