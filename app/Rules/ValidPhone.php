<?php

namespace App\Rules;

use App\Constants\ErrorCode;
use App\Util\CommonHelper;
use Illuminate\Support\Str;

class ValidPhone extends BaseRule
{
    public function __construct()
    {
        $this->error_code = ErrorCode::INVALID_PHONE;
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
        return CommonHelper::isValidPhoneNumber($value);
    }
}
