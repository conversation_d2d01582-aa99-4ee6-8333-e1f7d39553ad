<?php

namespace App\Rules;

use App\Constants\ErrorCode;
use Illuminate\Support\Str;

class ValidPassword extends BaseRule
{
    public function __construct()
    {
        $this->error_code = ErrorCode::INVALID_PASSWORD;
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
        $len = Str::length($value);
        if($len < 8 || $len > 32) {
            return false;
        }
        if ( ! preg_match("/^[^\s][ -~]*[^\s]$/", $value)) {
            return false;
        }
        if ( ! preg_match("/^.*(?=.*\d)(?=.*[a-zA-Z]).*$/", $value)) {
            return false;
        }
        return true;
    }
}
