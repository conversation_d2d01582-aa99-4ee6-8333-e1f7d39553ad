<?php

namespace App\Rules;

use App\Constants\ErrorCode;
use App\Util\CommonHelper;
use Illuminate\Support\Str;
use const Fangcloud_Composer\Content_Detection\INVALID;

class ValidEnterpriseName extends BaseRule
{
    public function __construct()
    {
        $this->error_code = ErrorCode::INVALID_ENTERPRISE_NAME;
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
        if(Str::length($value) > 30) {
            $this->error_code = ErrorCode::ENTERPRISE_NAME_TOO_LONG;
            return false;
        }
        if(preg_match("/[\\\\\/\?:\*\"><\|\^\$]/", $value) || $value[-1] === '.'){
            return false;
        }
        if(CommonHelper::isValidText($value) === INVALID){
            $this->error_code = ErrorCode::INVALID_TEXT;
            return false;
        }
        return true;
    }
}
