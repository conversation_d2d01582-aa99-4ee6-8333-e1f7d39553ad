{"name": "auth", "version": "0.0.1", "description": "auth module", "main": "index.js", "scripts": {"dev": "npm run env dev && cross-env NODE_ENV=dev webpack server", "build": "cross-env NODE_ENV=test webpack --progress", "dist": "cross-env NODE_ENV=production webpack --progress", "lang": "./node_modules/.bin/babel-node translator.js", "env": "node switchenv.js"}, "author": "fc", "dependencies": {"@fingerprintjs/fingerprintjs": "^2.1.4", "axios": "^0.17.0", "core-js": "^3.19.1", "crypto-js": "^3.1.9-1", "es5-shim": "^4.5.9", "es6-promise": "^4.1.1", "fetch-ie8": "^1.5.0", "fingerprintjs2": "^1.1.3", "js-cookie": "^2.1.4", "jsencrypt": "3.0.0-rc.1", "lodash": "^3.10.1", "object.observe": "^0.2.6", "popper.js": "^1.12.9", "promise-polyfill": "^6.0.2", "tooltip.js": "^1.1.7", "vconsole": "^3.1.0", "webpack-bundle-analyzer": "^2.9.2", "webpack-obfuscator": "^3.5.1"}, "devDependencies": {"@babel/core": "^7.13.15", "@babel/plugin-proposal-class-properties": "^7.13.0", "@babel/plugin-proposal-export-default-from": "^7.12.13", "@babel/plugin-transform-object-assign": "^7.18.6", "@babel/plugin-transform-runtime": "^7.13.15", "@babel/preset-env": "^7.13.15", "@babel/preset-react": "^7.13.13", "@q/babel-plugin-fang-intl": "^1.2.2", "@q/fang-intl": "~1.1.1", "babel-eslint": "^10.1.0", "babel-loader": "^8.2.2", "babel-plugin-import": "^1.13.3", "babel-plugin-jsx-control-statements": "^4.1.0", "babel-plugin-lodash": "^3.3.4", "babel-polyfill": "^6.26.0", "cross-env": "^5.0.5", "css-loader": "^7.1.2", "es3ify-loader": "^0.2.0", "eslint": "^4.5.0", "eslint-loader": "^1.9.0", "handlebars-loader": "^1.5.0", "hash-assets-webpack-plugin": "^1.0.0", "mini-css-extract-plugin": "^1.5.0", "sass": "^1.34.0", "sass-loader": "^16.0.1", "style-loader": "^4.0.0", "url-loader": "^4.1.1", "webpack": "^5.34.0", "webpack-cli": "^4.6.0", "webpack-dashboard": "^1.0.0-5", "webpack-dev-server": "^3.11.2"}}