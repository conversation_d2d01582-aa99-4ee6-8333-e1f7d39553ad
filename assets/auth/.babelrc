{"presets": [["@babel/preset-env", {"corejs": {"version": "3", "proposals": true}, "useBuiltIns": "usage", "targets": {"browsers": ["edge >= 16", "safari >= 9", "firefox >= 57", "ie >= 11", "ios >= 9", "chrome >= 49"]}}], "@babel/preset-react"], "plugins": ["@babel/plugin-transform-runtime", ["@q/fang-intl", {"messagesDir": "../../public/auth/static/messages", "enforceDescriptions": false}]], "env": {"development": {}}}