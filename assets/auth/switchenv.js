// Switch static environment to test static resource distribute between dev, test.
const fs = require('fs');

const argv = require('minimist')(process.argv.slice(2));
const env = argv._[0];

const webpack_server = {
  'dev': 'http://devstatic.fangcloud.net:8000/',
  'test': '',
  'prd': ''
};
const asset_path = {
  'dev': 'auth/static/',//blade中图片可以显示
  'test': 'auth/static/',
  'prd': 'auth/dist/'
};

const env_value = {
  'dev': 'dev',
  'test': 'local',
  'prd': 'production'
};

// switch env
var filepath = '../../.custom.env';
var code = fs.readFileSync(filepath, 'utf8');

// code = code.replace(/APP_ENV\s*=[^\n]*/, 'APP_ENV = ' + env_value[env]);
code = code.replace(/DEV_ASSET_URL_AUTH\s*=[^\n]*/, 'DEV_ASSET_URL_AUTH = ' + webpack_server[env]);
code = code.replace(/ASSET_PATH_AUTH\s*=[^\n]*/, 'ASSET_PATH_AUTH = ' + asset_path[env]);
fs.writeFileSync(filepath, code);

console.log('Now static environment is: ' + env + '\n');
