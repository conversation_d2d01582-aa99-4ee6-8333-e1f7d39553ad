'use strict';
const path = require('path');

const AssetsPlugin = require('hash-assets-webpack-plugin');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const WebpackObfuscator = require('webpack-obfuscator');

const isPRD = process.env.NODE_ENV === 'production';
const isDEV = process.env.NODE_ENV === 'dev';

const publicPath = '../../public/';
const srcPath = path.join(__dirname, '/src');
const outputPath = isPRD ? `${publicPath}auth/dist` : `${publicPath}auth/static`;
const chunkJsFilename = isPRD ? 'js/[name]_[chunkhash:7].js' : 'js/[name].js';
const chunkFilename = isPRD ? 'js/[id].[chunkhash:7].js' : 'js/[id].js';
const chunkCssFilename = isPRD ? 'css/[name]_[contenthash:7].css' : 'css/[name].css';
const urlLoaderFilename = isPRD ? '[name]_[hash:7].[ext]' : '[name].[ext]';

module.exports = {
  mode: isPRD ? 'production' : 'development',
  entry: {
    // js entry
    'login': srcPath + '/login.js',
    'login_v3': srcPath + '/login_v3.js',
    'other_login': srcPath + '/other_login.js',
    'hybrid_login': srcPath + '/hybrid_login.js',
    'register': srcPath + '/register.js',
    'mobile_register': srcPath + '/mobile_register.js',
    'quick_register': srcPath + '/quick_register.js',
    'forgot': srcPath + '/forgot.js',
    'login_verify': srcPath + '/login_verify.js',
    'force_two_step': srcPath + '/force_two_step.js',
    'user_settings': srcPath + '/user_settings.js',
    'activation': srcPath + '/activation.js',
    'mobileConfirm': srcPath + '/mobileConfirm.js',
    'password_init': srcPath + '/password_init.js',
    'set_two_step_verify': srcPath + '/set_two_step_verify.js',
    'refresh_token': srcPath + '/refresh_token.js',
    'base': srcPath + '/base.js',
    'validate': srcPath + '/validate.js',
    'check_channel_coupon': srcPath + '/check_channel_coupon.js',
    'new_enterprise_register': srcPath + '/new_enterprise_register.js',
    'introduction_register': srcPath + '/introduction_register.js',

    // css entry
    'web': srcPath + '/css/web.scss',
    'web_v3': srcPath + '/css/web_v3.scss',
    'sync_v3': srcPath + '/css/sync_v3.scss',
    'quick-register': srcPath + '/css/quick-register.scss',
    'sync': srcPath + '/css/sync.scss',
    'mobile': srcPath + '/css/mobile.scss'
  },
  output: {
    path: path.join(__dirname, outputPath),
    filename: chunkJsFilename,
    chunkFilename: chunkFilename
  },
  module: {
    rules: [
      {
        test: /\.js$/,
        use: ['babel-loader'],
        include: [srcPath]
      }, {
        test: /images\/.*?\.(png|jpg|gif)$/,
        use: [
          {
            loader: 'url-loader',
            options: {
              name: 'images/' + urlLoaderFilename,
              limit: 256
            }
          }
        ]
      }, {
        test: /\.scss$/,
        use: [
          {
            loader: MiniCssExtractPlugin.loader,
            options: {
              publicPath: '../'
            }
          },
          'css-loader',
          'sass-loader'
      ]}
    ]
  },
  devtool: isPRD ? false : 'source-map',
  plugins: [
    new MiniCssExtractPlugin({
      filename: chunkCssFilename,
      ignoreOrder: true
    })
  ],
  optimization: {
    splitChunks: {
      cacheGroups: {
        'vendor': {
          test: (module) => {
            // 确定模块在 node_modules 或其他指定路径下
            return module.context && (module.context.indexOf('node_modules') !== -1 ||
              module.context.indexOf('auth/src/utils') !== -1 ||
              module.context.indexOf('auth/src/constants') !== -1);
          },
          name: 'vendor',
          chunks: 'all',
          reuseExistingChunk: true // 重用已有的 chunk
        }
      }
    },
    // runtimeChunk: {
    //   name: 'manifest'
    // }
  },
  devServer: {
    contentBase: path.join(__dirname, 'static'),
    compress: true,
    https: true,
    disableHostCheck: true,
    host: 'devstatic.fangcloud.net',
    port: '8000',
    headers: {
      'Access-Control-Allow-Origin': '*'
    }
  }

};

if (isPRD) {
  module.exports.plugins = module.exports.plugins.concat([
    new AssetsPlugin({
      hash: isPRD,
      path: outputPath,
      keyTemplate: function (filename) {
        var match = /(js|css|images|media)\/([\w\-\.]+)_(\w{7})\.\1/.exec(filename);
        return [match[1] + '/' + match[2] + '.' + match[1], match[3]];
      },
      srcPath: './src',
      srcMatch: {
        '../../resources/views/auth/components/aliyun_header.blade.php': /['"]([^'"]+\.(?:png|jpg|gif))['"]/gi,
        '../../resources/views/auth/components/header.blade.php': /['"]([^'"]+\.(?:png|jpg|gif))['"]/gi,
        '../../resources/views/auth/quick_register.blade.php': /['"]([^'"]+\.(?:png|jpg|gif))['"]/gi,
        '../../resources/views/auth/register.blade.php': /['"]([^'"]+\.(?:png|jpg|gif))['"]/gi,
        '../../resources/views/auth/mobile_register.blade.php': /['"]([^'"]+\.(?:png|jpg|gif))['"]/gi
      },
      assetNameTemplate: '[name]_[hash]',
      hashLength: 7,
      prettyPrint: true
    })
  ]);
}

if (!isDEV) {
  module.exports.plugins = module.exports.plugins.concat([
    new WebpackObfuscator({
      compact: true,
      // controlFlowFlattening: true,
      // controlFlowFlatteningThreshold: 1,  // 100%的代码将受到控制流扁平化影响
      // debugProtection: true,             // 启用调试保护（阻止在开发者工具中查看代码）
      // debugProtectionInterval: true,     // 使调试保护在运行时更加可靠
      // disableConsoleOutput: true,
      // renameGlobals: false,               // 重命名全局变量（需要慎用）
      // selfDefending: true,

      deadCodeInjection: true,
      deadCodeInjectionThreshold: 0.3,   // 30%的代码将被注入无用代码
      splitStrings: true,                // 将代码中的字符串分割成多个较小的片段。
      splitStringsChunkLength: 5,        // 将字符串按每 5 个字符一组进行分割
      stringArray: true,                 // 将代码中的字符串放入一个数组中，并通过数组引用来使用它们
      stringArrayEncoding: ['rc4'],      // RC4 加密算法对数组中的字符串进行加密
      stringArrayRotate: true,           // 访问字符串数组时，对字符串数组的顺序进行旋转
      stringArrayThreshold: 1,           // 将所有字符串放入字符串数组中
      transformObjectKeys: true,         // 混淆对象的键
      unicodeEscapeSequence: true        // 将字符串转换为 Unicode 转义序列
    }, ['**/vendor*.js'])
  ]);
}
