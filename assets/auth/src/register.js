// import { validate<PERSON>hone } from './validate';
import SmsValidate from './utils/SmsValidate';
import <PERSON><PERSON><PERSON>alidate from './utils/PicValidate';
import CountryPhoneGroup from './utils/CountryPhoneGroup';
import MobilePhoneSelect from './utils/MobilePhoneSelect';

import {defineMessage} from '@q/fang-intl';
import * as API from './constants/API';
import {ACCOUNT_REGISTERED, PERSONAL_ACCOUNT_REGISTERED, PHONE_ALREADY_OCCUPIED_BUT_NOT_ACTIVATED, PHONE_ALREADY_OCCUPIED_ACTIVATION_EXPIRED, FREE_PLAN_LIMIT} from './constants/code';
import fc from './utils/common';
import Cookies from 'js-cookie';
import Encrypt from './utils/rsaEncrypt';
// import './css/web.scss';

import CryptoJS from 'crypto-js';

let smsValidate;
let picValidate;
let countryPhoneGroup;
let v2_picValidate;

const personal_register = !!document.querySelector('[name=personal_register]');
const ali_market_register = !!document.querySelector('.aliyun-plan');

fc.ready(() => {
    // 手机号验证
    const isEnterpriseRegister = !!document.querySelector('.enterprise-register');
    if (document.querySelector('.phone .input-group')) {
        initCountryPhone(isEnterpriseRegister);
    }

    if (isEnterpriseRegister) {
        enterpriseRegister();
    } else {
        normalRegister();
        createSmsValidate();
        validatePhone('register');
    }

    // 用户离开页面时做的事
    window.onbeforeunload = reportRegisterInfo;

    // TODO 版本选择移除后添加默认plan id

    // 填写邮箱、密码、企业信息
    // placeholder 兼容
    //多语言
});

function initCountryPhone(isEnterpriseRegister) {
    if(fc.config.isMobile) {
        countryPhoneGroup = new MobilePhoneSelect({
            element: document.querySelector('.phone .input-group')
        });
    } else {
        countryPhoneGroup = new CountryPhoneGroup({
            element: document.querySelector('.phone .input-group'),
            showCountry: true,
            countrySelectOpitons: {
                showSelector: true,
                default_code: 86
                // align_element: $('.register-form .phone .input-group')
            }
        });
    }

    countryPhoneGroup.on('code_change', function({code}) {
        const $input = document.getElementById('phone');
        const $emailGroup = document.querySelector('.email.form-group');
        if(code == 86) {
            $input.setAttribute('maxLength', 11);
            if (!isEnterpriseRegister) fc.addClass($emailGroup, 'hidden');
        } else {
            $input.setAttribute('maxLength', 20);
            if (!isEnterpriseRegister) fc.removeClass($emailGroup, 'hidden');
        }
    });
}

function validatePhone(scene) {
    const $input = document.getElementById('phone');
    ['change_delay', 'change'].forEach((eventName) => {
        countryPhoneGroup.on(eventName, function() {
            const $input = document.getElementById('phone');
            const phone = encodeURIComponent(countryPhoneGroup.getValue());
            // 输入为空时不判断
            // $input.focus();
            if(!fc.trim($input.value)) {
                return false;
            }
            const url = `${API.CHECK_PHONE}${scene}?phone=${phone}`;
            fc.ajax({
                url,
                method: 'GET',
                accept_all_errors: true,
                parser: (res) => {
                    const { is_pic_captcha_needed } = res;
                    if(res.errors) {
                        if(eventName === 'change') {
                            res.errors.forEach((error) => {
                                fc.insertError(error.field, error.error_msg, document.querySelector('.normal-register'));
                            });
                        }
                        smsValidate.getState() !== 'working' && smsValidate.setDisabled(1, 'get');
                    } else {
                        fc.clearError($input);
                        smsValidate.getState() !== 'working' && smsValidate.setDisabled(0, 'get');
                    }

                    // if(is_pic_captcha_needed) {
                    //     if( !picValidate ) {
                    //         createPicValidate();
                    //     } else {
                    //         picValidate.show();
                    //     }
                    // } else {
                    //     picValidate && picValidate.destroy();
                    // }
                }
            });
        });

    });

    ['keyup', 'afterpaste'].forEach(eventName => {
        $input.addEventListener(eventName, function() {
            this.value = this.value.replace(/\D/g, '');
        });
    });
}

function createPicValidate() {
    const $form = document.querySelector('.normal-register');
    picValidate = new PicValidate({
        picUrl: API.REGISTER_PIC_CAPTHA
    });

    smsValidate.setDisabled(1, 'get');

    picValidate.on('destroy', function() {
        fc.removeElement(this.$el);
        picValidate = null;
    });

    picValidate.on('change', function(value) {
        if(smsValidate.getState() !== 'working') {
            smsValidate.setDisabled(value ? 0 : 1, 'get');
        }
    });

    $form.insertBefore(picValidate.$el, $form.querySelector('.phone').nextSibling);
}

function createSmsValidate() {
    smsValidate = new SmsValidate({
        syncs: function() {
            let newDate = Math.floor(new Date().getTime() / 1000);
            let data = {
                type: 'register',
                phone: countryPhoneGroup.getValue(),
                ts: newDate,
                'register-sign': CryptoJS.MD5(`appId=${fc.app_key}&ts=${newDate}&phone=${countryPhoneGroup.getValue()}&type=register`).toString()
            };

            if(picValidate) {
                data.pic_captcha = fc.trim(picValidate.getValue());
            }

            if (v2_picValidate && v2_picValidate.captchaData) {
                data = Object.assign(data, v2_picValidate.captchaData);
                v2_picValidate.destroy();
            }

            return data;
        }
    });

    smsValidate.setDisabled(1, 'get');
    smsValidate.on('destroy', function() {
        fc.removeElement(this.$el);
    });

    smsValidate.on('send_error', function(res){
        res.errors.forEach((error) => {
            fc.insertError(error.field, error.error_msg, document.querySelector('.normal-register'));
            if(error.field === 'pic_captcha' && picValidate) {
                picValidate.refresh();
            }
        });
    });

    smsValidate.on('send_error', function (res) {
        res.errors.forEach((error) => {
            fc.insertError(error.field || 'sms_captcha', error.error_msg, document.querySelector('.normal-register'));
            if (error.field === 'pic_captcha') {
                if (picValidate) {
                    picValidate.refresh();
                } else {
                    createPicValidate();
                }
            } else if (error.field === 'sliding_pic_captcha') {
                v2_picValidate = fc.qHPassCaptcha.init('');
                return;
            }
        });
    });

    smsValidate.on('send_success', function(){
        if (picValidate) {
            picValidate.destroy();
            picValidate = null;
        }
    });

    const $form = document.querySelector('.normal-register');
    $form.insertBefore(smsValidate.$el, $form.querySelector('.phone').nextSibling);
}

function normalRegister() {
    const $form = document.querySelector('.normal-register');
    $form.addEventListener('submit', (e) => {
        e.preventDefault();

        const phone = countryPhoneGroup.getValue();
        const code = countryPhoneGroup.getCode();
        const sms_captcha = fc.trim(smsValidate.getValue());

        const name = fc.trim($form.querySelector('[name=name]').value);
        const password = $form.querySelector('[name=password]').value;
        const company_name = $form.querySelector('[name=company_name]') && fc.trim($form.querySelector('[name=company_name]').value);
        const email = fc.trim(document.querySelector('[name=email]').value);
        const plan_id = $form.querySelector('[name=plan_id]').value;
        const invite_code = $form.querySelector('[name=invite_code]').value;
        const bind = !!GetQueryString('bind');
        const from_page = !!GetQueryString('from_page');
        const redirect = $form.querySelector('[name=redirect]').value;
        const motivation = document.querySelector('input[name="motivation"]:checked') && document.querySelector('input[name="motivation"]:checked').value;
        const referral_id = !!Cookies.get('referral_id');

        let data = { phone, sms_captcha, plan_id, password, company_name, name, login_type: fc.config.login_type, is_mobile: fc.config.isMobile};

        data = Encrypt.makeDataSafely(data);

        if(motivation) {
            data.motivation = motivation;
        }

        if(picValidate) {
            data.pic_captcha = picValidate.getValue();
        }
        if(redirect) {
            data.redirect = redirect;
        }
        if (invite_code) {
            data.invite_code = invite_code;
        }
        if (code !== '86') {
            data.email = email;
        }

        let url = bind ? API.BIND_REGISTER_ACTION : API.REGISTER_ACTION;

        if(ali_market_register) {
            url = API.ALI_MARKET_REGISTER;
            delete(data.plan_id);
        }
        if(personal_register) {
            url = API.PERSONAL_REGISTER;
            delete(data.plan_id);
            delete(data.company_name);
        }

        let hasEmpty = checkEmpty(data, $form);
        if (hasEmpty) {
            return false;
        }

        const $protocol = document.getElementById('protocol');
        if($protocol && !$protocol.checked) {
            fc.insertNormalError($form, defineMessage({id: 'base.needAcceptProtocal', defaultMessage: '请阅读并接受服务条款和服务等级协议'}));
            return false;
        }

        if(from_page) {
            data.from_page = decodeURIComponent(GetQueryString('from_page'))
        }

        if (referral_id) {
            data.referral_id = decodeURIComponent(Cookies.get('referral_id'))
        }

        fc.ajax({
            data,
            url,
            block: true,
            accept_all_errors: true,
            parser: (res) => {
                if(res.success) {
                    window._agl && window._agl.push(['track',['success',{t:3}]]);
                    window._baq && window._baq.track('active_register', { assets_id: 1714126423115789, plan_id: plan_id });
                    window.sguic && window.sguic('track','FORM_SUBMIT_SUCCEESS','2730649');
                    window.gdt && window.gdt('track','REGISTER',{});
                    fc.removeHomepageCookie();
                    window.onbeforeunload = null;
                    if(plan_id == 13 && !personal_register && motivation == '2') {
                        let domain_array = window.location.hostname.split('.');
                        let domain = domain_array.slice(1).join('.');
                        Cookies.set('motivation', 1, {domain});
                    }
                    if(window.fangcloud && window.fangcloud.login){
                        window.fangcloud.login(JSON.stringify(res));
                    } else if (window.register) {
                        window.register && window.register.saveAccessToken && window.register.saveAccessToken(JSON.stringify(res));
                    } else {
                        // 官网嵌入的注册页面
                        if(/fuli=true/.test(window.location.search)) {
                            window.parent.postMessage({
                                success: true
                            }, '*');
                        } else if(/embed=true/.test(window.location.search)) {
                            window.parent.location.href = res.redirect;
                        } else {
                            window.location.href = res.redirect;
                        }
                    }
                } else {
                    res.errors.forEach(({field, error_code, error_msg}) => {
                        if(field && ![PHONE_ALREADY_OCCUPIED_BUT_NOT_ACTIVATED, PHONE_ALREADY_OCCUPIED_ACTIVATION_EXPIRED].includes(error_code)) {
                            fc.insertError(field, error_msg, $form);
                        } else {
                            let msg = error_msg;
                            let errorOptions = {};
                            if (error_code == ACCOUNT_REGISTERED) {
                                // 手机号已注册
                                msg = defineMessage({id: 'auth.register.phoneRegistered', defaultMessage: '该手机号已被注册，请直接<a href="{0}">登录</a>或联系客服'},  API.LOGIN);
                            } else if (error_code == PERSONAL_ACCOUNT_REGISTERED) {
                                // 手机号已注册为个人账号
                                msg = defineMessage({id: 'auth.register.phoneRegisteredAsPersonal', defaultMessage: '你已注册个人帐号，请直接<a href="{0}">登录</a>或前往网页版、PC客户端登录后进行套餐升级'}, API.LOGIN);
                            } else if (error_code == PHONE_ALREADY_OCCUPIED_BUT_NOT_ACTIVATED) {
                                // 手机号被邀请加入企业
                                msg = defineMessage({id: 'auth.register.phoneAlreadyAccupied', defaultMessage: '你已被邀请加入{0} <a href="{1}">立即激活</a>'}, field, `/activate_by_phone?phone=${code}-${phone}`);
                                errorOptions.className = 'black';
                            } else if (error_code == PHONE_ALREADY_OCCUPIED_ACTIVATION_EXPIRED) {
                                // 邀请过期
                                msg = defineMessage({id: 'auth.register.phoneAlreadyAccupiedAndExpired', defaultMessage: '你在30天前被邀请加入{0}，请联系管理员重发邀请后去<a href="{1}">激活账号</a>'}, field, `/activate_by_phone?phone=${code}-${phone}`);
                                errorOptions.className = 'black';
                            } else if(error_code == FREE_PLAN_LIMIT) {
                                msg = defineMessage({id: 'auth.register.freePlanLimit', defaultMessage: '太火爆了免费版本月名额已被领完。请下个月再来。或选择其他套餐试用。'});
                            }
                            fc.insertNormalError($form, msg, errorOptions);
                        }

                    });
                }
            }
        });
    });

    fc.initFormEvents($form, true);
    listenNormalError($form);

    // validatePassword();

    // const $protocol = document.getElementById('protocol');
    // if($protocol) {
    //     $protocol.addEventListener('change', fc.checkValidate.bind(null, $form));
    // }
}

function listenNormalError($form) {
    $form.addEventListener('focus', e => clearRegisterError($form, e.target), true);

    const $protocol = document.getElementById('protocol');
    if($protocol) {
        $protocol.addEventListener('change', e => clearRegisterError($form, e.target));
    }

    const $motivation = $form.querySelectorAll('[name=motivation]');
    if($motivation) {
        [].forEach.call($motivation, radio => radio.addEventListener('change', e => {
            fc.clearError(e.target);
            clearRegisterError($form, e.target);
        }));
    }
}

function clearRegisterError($form, target) {
    let input = fc.matchSelector(target, 'input');
    let button = $form.querySelector('.btn-primary');
    let error = fc.nextElementSibling(button);
    if(input && error) {
        fc.removeElement(error);
        fc.removeClass(button.parentNode, 'error');
    }
}

function checkEmpty(data, $form) {
    let hasEmpty = false;
    for (let key in data) {
        if (data[key] === '' || data[key] === null) {
            fc.insertError(key, '', $form);
            hasEmpty = true;
        }
    }

    if (hasEmpty) {
        fc.insertNormalError($form, defineMessage({ id: 'base.emptyValue', defaultMessage: '请输入正确的信息后再试' }));
    }
    return hasEmpty;
}

function checkPhone(phone) {
    const isphone =  /^1(3|4|5|6|7|8|9)\d{9}$/.test(phone);
    if (!isphone) {
        fc.insertError('phone', '请输入正确的手机号', document.querySelector('.enterprise-register'));
    } else {
        fc.clearError(document.getElementById('phone'));
    }
    return isphone;
}

function checkEmail(email) {
    const reg = new RegExp("^[a-z0-9]+([._\\-]*[a-z0-9])*@([a-z0-9]+[-a-z0-9]*[a-z0-9]+.){1,63}[a-z0-9]+$");
    const isemail =  reg.test(email);
    if (!isemail) {
        fc.insertError('email', '请输入正确的邮箱', document.querySelector('.enterprise-register'));
    } else {
        fc.clearError(document.getElementById('email'));
    }
    return isemail;
}

function enterpriseRegister() {
    const $form = document.querySelector('.enterprise-register');
    $form.addEventListener('submit', (e) => {
        e.preventDefault();
        const phone = countryPhoneGroup.getValue();
        const username = fc.trim($form.querySelector('[name=username]').value);
        const enterprise_name = fc.trim($form.querySelector('[name=enterprise_name]').value);
        const enterprise_size = $form.querySelector('[name=enterprise_size]') ? fc.trim($form.querySelector('[name=enterprise_size]').value) : '';
        const plan_id = $form.querySelector('[name=plan_id]').value;
        const email = fc.trim(document.querySelector('[name=email]').value);
        const position_name = fc.trim($form.querySelector('[name=position_name]').value);

        let data = { phone, plan_id, enterprise_name, username, email, position_name};
        if(plan_id != 'cooperate'){
            data.enterprise_size = enterprise_size;
        }

        // data = Encrypt.makeDataSafely(data);

        if( picValidate ) {
            data.pic_captcha = picValidate.getValue();
        }

        let hasEmpty = checkEmpty(data, $form);
        let validatePhone = checkPhone(phone);
        let validateEmail = checkEmail(email);

        if (hasEmpty || !validatePhone || !validateEmail) {
            return false;
        }
        $form.querySelector('.action-group .btn').type = 'button';
        $form.querySelector('.action-group button').innerHTML = '提交中...'
        let url = API.ENTERPRISE_REGISTER_ACTION;
        data.login_type = fc.config.login_type;
        const formData = {
            "联系人": encrypt(data.username),
            "手机号": encrypt(phone),
            "公司名称": encrypt(data.enterprise_name),
            "公司职位": encrypt(data.position_name),
            "邮箱": encrypt(email),
            "企业ID": encrypt(''),
            "版本": encrypt(plan_id === 'custom' ? '定制版申请' : '合作伙伴申请'),
            "企业规模": encrypt(data.enterprise_size)
        };
        wlRegisterComp.linkToWL({
            wl_qrcode_id: plan_id === 'custom' ? "1474328573614628864" : "1474328150132531200", // 需要使⽤的卫瓴⾼级智能码ID
            client_user_id: encrypt(phone), // ⽤⼾在客⼾平台的唯⼀ID
            formData: formData// ⽤⼾的其他信息
        }, (data) => {
            if (data && (data.data && data.data.live_code)) {
                $form.querySelector('.action-group button').innerHTML = '<i class="iconfont icon-success"></i>提交成功'
                setTimeout(() => {
                    fc.addClass(document.querySelector('.register-qrcode-modal'), 'show');
                    document.querySelector('.register-qrcode-modal .qrcode-img').src = data.data.live_code;
                }, 1000);
            } else {
                var errmsg = '请求失败请重试';
                if (data && data.msg) {
                    errmsg = data.msg;
                }
                $form.querySelector('.action-group .btn').type = 'submit';
                $form.querySelector('.action-group button').innerHTML = '立即申请'
                fc.insertNormalError($form, errmsg);
            }
        });

        // fc.ajax({
        //     data,å
        //     url,
        //     block: true,
        //     accept_all_errors: true,
        //     parser: (res) => {
        //         if(res.success) {
        //             let redirect = API.ENTERPRISE_REGISTER_SUCCESS;
        //             if(fc.config.isMobile && res.redirect) {
        //                 redirect = res.redirect;
        //             }
        //             window.location.href = redirect;//res.redirect;
        //         } else {
        //             res.errors.forEach((error) => {
        //                 fc.insertError(error.field, error.error_msg, $form);
        //             });
        //         }
        //     }
        // });
    });

    fc.initFormEvents($form, true);
    listenNormalError($form);

    const $options = document.querySelector('.enterprise-size .select-group-options');
    $form.querySelector('.enterprise-size .select-group-content').addEventListener('click', (e) => {
        e.stopPropagation();
        const target = fc.matchSelector(e.target, '.enterprise-size .select-group-content');
        $options.style.display = 'block';
        fc.addClass(target.querySelector('.select-arrow'), 'icon-arrow-up');
        fc.removeClass(target.querySelector('.select-arrow'), 'icon-arrow-down');
    });

    window.addEventListener('click', handleCloseOption);
    $options.addEventListener('click', (e) => {
        const target = fc.matchSelector(e.target, 'li');
        const $input = $form.querySelector('[name=enterprise_size]');
        const $formGroup = fc.matchSelector($input, '.form-group');
        $input.value = target.innerText; //target.getAttribute('data-value');
        $form.querySelector('.enterprise-size .select').innerText = target.innerText;
        handleCloseOption();

        if (fc.hasClass($formGroup, 'error')) {
            fc.removeClass($formGroup, 'error');
        }
    });
}

function encrypt(word, keyStr) {
    // 加密⽅法 //默认密钥
    keyStr = keyStr ? keyStr : '1ca14106d4f52974b48ed168910ea4fa';
    let key = CryptoJS.enc.Utf8.parse(keyStr);
    let srcs = CryptoJS.enc.Utf8.parse(word);
    let encrypted = CryptoJS.AES.encrypt(srcs, key, { mode: CryptoJS.mode.ECB, padding: CryptoJS.pad.Pkcs7 });
    return encrypted.toString();
}
function handleCloseOption() {
    const $content = document.querySelector('.enterprise-size .select-group-content');
    const $options = document.querySelector('.enterprise-size .select-group-options');
    $options.style.display = 'none';
    fc.removeClass($content.querySelector('.select-arrow'), 'icon-arrow-up');
    fc.addClass($content.querySelector('.select-arrow'), 'icon-arrow-down');
}

function GetQueryString(name) {
    const reg = new RegExp(`(^|&)${name}=([^&]*)(&|$)`, 'i');
    const r = window.location.search.substr(1).match(reg);
    if ( r != null) {
        return r[2];
    }
    return null;
}

function reportRegisterInfo(event) {
    event.preventDefault();

    const $form = document.querySelector('.normal-register');
    const phone = countryPhoneGroup.getValue();
    const captcha = fc.trim(smsValidate.getValue());
    let password = $form.querySelector('[name=password]').value;
    password = new Array(password.length + 1).join('*');
    const user_name = fc.trim($form.querySelector('[name=name]').value);
    const company_name = $form.querySelector('[name=company_name]') && fc.trim($form.querySelector('[name=company_name]').value);
    let motivation = document.querySelector('input[name="motivation"]:checked') && document.querySelector('input[name="motivation"]:checked').value;
    motivation = motivation === '1' ? '个人存储/同步文件' : (motivation === '2' ? ' 团队成员协作办公' : '');

    let data = { phone, captcha, password, user_name, company_name, motivation};

    fc.ajax({
        url: API.REPORT_WHY_LEAVE,
        method: 'POST',
        accept_all_errors: true,
        data
    });

    event.returnValue = '';
}
