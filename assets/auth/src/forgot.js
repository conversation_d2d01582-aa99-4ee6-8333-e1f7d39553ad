import fc from './utils/common';
import PicValidate from './utils/PicValidate';
import SmsValidate from './utils/SmsValidate';
import {defineMessage} from '@q/fang-intl';
import CountryPhoneGroup from './utils/CountryPhoneGroup';
import MobilePhoneSelect from './utils/MobilePhoneSelect';
// import './css/web.scss';
import * as API from './constants/API';
import Encrypt from './utils/rsaEncrypt';
import CryptoJS from 'crypto-js';

let $currentContainer;
let countryPhoneGroup;
let picValidate;
let picValidateInStep2;
let smsValidate;
let v2_picValidate;

let isInternational = !!document.getElementById('is_international');

fc.ready(() => {
    if(document.querySelector('.reset-password')) {
        // 重设密码
        resetPassword();
        document.querySelector('.password-hint').innerHTML = fc.passwordStrength();
        document.querySelector('.get-detail') && document.querySelector('.get-detail').addEventListener('click', function() {
            if(window.fangcloud && window.fangcloud.popWebUrl) {
                window.fangcloud.popWebUrl(this.getAttribute('href'));
            }
        });
    } else {
        // 忘记密码第一步
        confirmAccount();
        createPicValidate();

        if(isInternational) {
            initCountryPhone();
        }
    }
    bindEvents();
});

function createPicValidate() {
    picValidate = new PicValidate({
        focusOnrender: false,
        picUrl: API.FORGOT_PASSWORD_PIC_CAPTHA
    });

    picValidate.on('destroy', function() {
        fc.removeElement(this.$el);
    });
    picValidate.on('hide', function(){
        // this.$el.closest('.form-control-group').find('.error-msg').remove();
    });


    const $form = document.querySelector('.confirm-account');
    $form.insertBefore(picValidate.$el, $form.querySelector('.next-step'));
}

// 为了防止刷验证码 在第二步也加了图形验证码校验
function createPicValidateInStep2() {
    picValidateInStep2 = new PicValidate({
        focusOnrender: false,
        picUrl: API.FORGOT_PASSWORD_BY_PHONE_PIC_CAPTHA
    });

    picValidateInStep2.on('destroy', function() {
        fc.removeElement(this.$el);
    });
    picValidateInStep2.on('hide', function(){
        this.$el.closest('.form-control-group').find('.error-msg').remove();
    });


    const $form = document.querySelector('.identity-verification');
    $form.insertBefore(picValidateInStep2.$el, $form.querySelector('.sms-item'));
}

function initCountryPhone() {
    if(fc.config.isMobile) {
        countryPhoneGroup = new MobilePhoneSelect({
            element: document.getElementById('login').parentNode,
            default_code: 1
        });
    } else {
        countryPhoneGroup = new CountryPhoneGroup({
            element: document.getElementById('login').parentNode,
            showCountry: true,
            countrySelectOpitons: {
                showSelector: true,
                default_code: 1
                // align_element: $('.register-form .phone .input-group')
            }
        });
    }
}

function confirmAccount() {
    const $form = document.querySelector('form');
    $form.addEventListener('submit', submitConfirmAccount);
    const $confirmAccount = document.querySelector('.confirm-account');
    $currentContainer = $confirmAccount;
}

function submitConfirmAccount(ev) {
    ev.preventDefault();
    let login = fc.trim(document.getElementById('login').value);
    if(isInternational) {
        login = countryPhoneGroup.getValue();
    }
    let pic_captcha = picValidate.getValue();
    let data = {login, pic_captcha};
    let url = API.FORGOT_CONFIRM_ACCOUNT;

    fc.ajax({
        data,
        url,
        accept_all_errors: true,
        parser: (res) => {
            if(res.success) {
                this.removeEventListener('submit', submitConfirmAccount);
                validateAccount(res);
                validateCode(res);

            } else {
                res.errors.forEach((error) => {
                    if(!error.field) {
                        error.field = 'login';
                    }
                    fc.insertError(error.field, error.error_msg);
                    if(error.field === 'pic_captcha' && picValidate) {
                        picValidate.refresh();
                    }
                });
            }
        }
    });
}

function validateAccount(data) {
    changeStep(2);
    const {phone, email} = data;
    let template;
    if(fc.config.isMobile) {
        document.querySelector('.logo-large').style.display = 'none';
    }
    if(phone && !email) {
        template = `<input type="hidden" name="reset_by" value="2" checked>
                    <span class="hint">${defineMessage({id: 'forgot.byPhone', defaultMessage: '通过手机号{0}找回密码'}, phone)}</span>`;
    } else if(email && !phone) {
        template = `<input type="hidden" name="reset_by" value="1" checked>
                    <span class="hint">${defineMessage({id: 'forgot.byEmail', defaultMessage: '通过邮箱{0}找回密码'}, email)}</span>`;
    } else {
        template = `
        <label class="text-label top">${defineMessage({id: 'forgot.validateMode', defaultMessage: '验证方式：'})}</label>
        <div class="radio">
            <label>
                <i class="radiobox-inner"></i>
                <input type="radio" name="reset_by" value="2" checked>
                ${defineMessage({id: 'forgot.phoneValidate', defaultMessage: '手机验证'})}
                <span class="hint">${defineMessage({id: 'forgot.byPhone', defaultMessage: '通过手机号{0}找回密码'}, phone)}</span>
            </label>
        </div>
        <div class="radio">
            <label>
                <i class="radiobox-inner"></i>
                <input type="radio" name="reset_by" value="1">
                ${defineMessage({id: 'forgot.emailValidate', defaultMessage: '邮箱验证'})}
                <span class="hint">${defineMessage({id: 'forgot.byEmail', defaultMessage: '通过邮箱{0}找回密码'}, email)}</span>
            </label>
        </div>`;
    }

    $currentContainer.querySelector('.validate-account').innerHTML = template;
    if(email && phone) {
        let $radios = $currentContainer.querySelectorAll('[name=reset_by]');
        [].forEach.call($radios, radio => fc.initRadioAction(radio));

        $radios[0].addEventListener('click', function() {
            smsValidate.show();
            $currentContainer.querySelector('.btn').disabled = !smsValidate.getValue();
        });

        $radios[1].addEventListener('click', function() {
            smsValidate.hide();
            $currentContainer.querySelector('.btn').disabled = false;
        });
    }

    if(phone) {
        createSmsValidate(!!phone && !email);
    }

    if(!phone && email) {
        $currentContainer.querySelector('button').disabled = false;
    }

    $currentContainer.querySelector('.go-back').addEventListener('click', function(ev) {
        ev.preventDefault();
        changeStep(1);
    });
}

function resetPassword() {
    let $form = document.querySelector('form');
    $form.addEventListener('submit', resetConfirm);
    const $resetPassword = document.querySelector('.reset-password');
    $currentContainer = $resetPassword;

}

function resetConfirm(ev) {
    ev.preventDefault();
    ev.stopPropagation();
    let code = fc.trim(document.querySelector('[name=code]').value);
    let password = fc.trim(document.querySelector('[name=password]').value);
    let password_confirmation = fc.trim(document.querySelector('[name=password_confirmation]').value);
    let data = {code, password, password_confirmation, login_type: fc.config.login_type};

    data = Encrypt.makeDataSafely(data);

    let url = API.RESET_PASSWORD;
    fc.ajax({
        data,
        url,
        accept_all_errors: true,
        parser: (res) => {
            if(res.success) {
                $currentContainer.style.display = 'none';
                let tpl = `<div class="forgot-done">
                    <div class="reset-success"></div>
                    <p>${defineMessage({id: 'forgot.resetSuccess', defaultMessage: '重设成功！请使用新密码登录'})}</p>
                    <a class="btn btn-primary return-login" href="${API.LOGIN}?_fstate=${fc.config._fstate}#form">${defineMessage({id: 'forgot.login', defaultMessage: '立即登录'})}</a>
                </div>`;
                renderSuccessContent(tpl);
            } else {
                res.errors.forEach((error) => {
                    fc.insertError(error.field, error.error_msg);
                });
            }
        }
    });
}

function createSmsValidate(trigger) {
    const $container = document.querySelector('form .identity-verification');

    if(smsValidate) {
        smsValidate.show();
    } else {
        smsValidate = new SmsValidate({
            accept_errors: true,
            syncs: function() {

                let newDate = Math.floor(new Date().getTime() / 1000);
                let data = {
                    type: 'forgot_password_by_phone',
                    ts: newDate,
                    'forgot-sign': CryptoJS.MD5(`appId=${fc.forget_password_app_id}&ts=${newDate}&phone=f_phone&type=forgot_password_by_phone`).toString()
                };

                if (picValidateInStep2) {
                    data.pic_captcha = picValidateInStep2.getValue();
                }

                if (v2_picValidate && v2_picValidate.captchaData) {
                    data = Object.assign(data, v2_picValidate.captchaData);
                    v2_picValidate.destroy();
                }

                return data;
            }
        });

        smsValidate.on('destroy', function() {
            fc.removeElement(this.$el);
        });

        smsValidate.on('send_error', function(res){
            res.errors.forEach((error) => {
                if(!error.field) {
                    error.field = 'login';
                }
                fc.insertError(error.field, error.error_msg, $container);
                if(error.field === 'pic_captcha') {
                    if (picValidateInStep2) {
                        picValidateInStep2.refresh();
                    } else {
                        createPicValidateInStep2();
                    }
                } else if (error.field === 'sliding_pic_captcha') {
                    v2_picValidate = fc.qHPassCaptcha.init('');
                    return;
                }
            });
        });

        smsValidate.on('send_success', function(){
            if (picValidateInStep2) {
                picValidateInStep2.destroy();
                picValidateInStep2 = null;
            }
        });
    }

    if(trigger) {
        smsValidate.send();
    }

    $container.insertBefore(smsValidate.$el, $container.querySelector('.next-step'));
}

let email;
function validateCode(res) {
    email = res.email;
    const $form = document.querySelector('form');
    $form.addEventListener('submit', submitValidateCode);
}

function submitValidateCode(e) {
    e.preventDefault();
    e.stopPropagation();
    // phone: 2, email: 1
    let reset_by = 2;
    let $reset_by = document.querySelector('[name=reset_by]:checked');
    if($reset_by) {
        reset_by = $reset_by.value;
    } else {
        reset_by = document.querySelector('[name=reset_by]').value;
    }
    let data = {
        reset_by
    };

    if(smsValidate) {
        data.sms_captcha = smsValidate.getValue();
    }

    let url = API.CONFIRM_FORGOT;
    fc.ajax({
        data,
        url,
        accept_all_errors: true,
        parser: (res) => {
            if(res.success) {
                if(reset_by == 2) {
                    window.location.href = res.redirect;
                } else {
                    $currentContainer.style.display = 'none';
                    let tpl = `<div class="forgot-done">
                            <div class="reset-success"></div>
                            <p>${defineMessage({id: 'forgot.resetEmailSended', defaultMessage: '重设密码链接已发送至{0}<br />请登录你的邮箱查看并按照邮件内容操作'}, email)}</p>
                            <div class="hint">${defineMessage({id: 'forgot.resetEmailSendedTips', defaultMessage: '邮件可能会被你的邮箱拦截，如在收件箱中未找到，可尝试在垃圾邮件中查看。'})}</div>
                            <a class="btn btn-default return-login" href="${API.LOGIN}?_fstate=${fc.config._fstate}#form">${defineMessage({id: 'forgot.returnLogin', defaultMessage: '返回到登录'})}</a>
                        </div>`;
                    renderSuccessContent(tpl);
                }
            } else {
                res.errors.forEach((error) => {
                    fc.insertError(error.field, error.error_msg);
                });
            }
        }
    });
}

function renderSuccessContent(tpl) {
    let $form = document.querySelector('form');
    $form.outerHTML = tpl;
    if(!fc.config.isMobile) {
        fc.removeElement(document.querySelector('.forgot-progress'));
    } else if(window.fangcloud && window.fangcloud.close) {
        document.querySelector('.return-login').addEventListener('click', (e) => {
            e.preventDefault();
            window.fangcloud.close();
        });
    }
}

function changeStep(step) {
    const $stepInfos = document.querySelectorAll('.step-info');
    const $current = document.querySelector('.step-info.current');
    $current && fc.removeClass($current, 'current');

    if(step === 1){
        $stepInfos.length && fc.removeClass($stepInfos[step - 1], 'done');
        document.querySelector('form').removeEventListener('submit' ,submitValidateCode);
        document.querySelector('form').addEventListener('submit' ,submitConfirmAccount);
    } else {
        $current && fc.addClass($current, 'done');
    }

    $stepInfos.length && fc.addClass($stepInfos[step - 1], 'current');
    $currentContainer.style.display = 'none';
    $currentContainer = document.querySelector(`.step-${step}`);
    $currentContainer.style.display = 'block';
}

function bindEvents() {
    let $form = document.querySelector('.form');
    fc.initFormEvents($form);
}

// ajax Setup

// placeholder 兼容处理

// 多语言选项
