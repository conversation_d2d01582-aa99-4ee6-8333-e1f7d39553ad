// 移动端是否需要使用rem html的默认font-size 如何区分各端计算宽度；
@import 'utils/variables';
@import 'utils/mixins';
@import 'iconfont';
@import 'dialog';
@import 'tooltip';

$toast-bg: #5d666b;
$toast-action: #8dccff;

// base style
html {
    background: $white-color;
    color: $font-darker;
}

body {
    font-family: Arial, 'PingFang SC', 'Helvetica Neue', Helvetica, "Microsoft YaHei", "微软雅黑", \5fae\8f6f\96c5\9ed1, "WenQuanYi Micro Hei", \5b8b\4f53, sans-serif;
    font-size: 12px;
    line-height: 1.4;
}

a {
    color: $primary-color;
    outline: none;
    text-decoration: none;

    &:hover {
        color: $primary-active-color;
    }

    &:active {
        color: $primary-color;
    }
}

// reset css
body, div, dl, dt, dd, ul, ol, li, h1, h2, h3, h4, h5, h6, pre, code, form, fieldset, legend, input, textarea, p, blockquote, th, td {
    margin: 0;
    padding: 0;
}

table {
    border-collapse: collapse;
    border-spacing: 0;
}

fieldset, img {
    border: 0;
}

address, caption, cite, code, dfn, em, strong, th, var {
    font-style: normal;
    font-weight: normal;
}

ol, ul {
    list-style: none;
}

caption, th {
    text-align: left;
}

h1, h2, h3, h4, h5, h6 {
    font-size: 100%;
    font-weight: normal;
}

q:before, q:after {
    content: '';
}

abbr, acronym {
    border: 0;
    font-variant: normal;
}

sup {
    vertical-align: text-top;
}

sub {
    vertical-align:text-bottom;
}

button, input, textarea, select {
    font-family: inherit;
    font-size: inherit;
    font-weight: inherit;
    outline: none;
    resize: none;
}

input:-webkit-autofill,
textarea:-webkit-autofill,
select:-webkit-autofill {
    -webkit-box-shadow: 0 0 0px 1000px white inset;
}

img {
    vertical-align: middle;
}

/* Enable image placeholders */
@-moz-document url-prefix(http), url-prefix(file) {
    img:-moz-broken {
        -moz-force-broken-image-icon:1;
    }
}

input::-webkit-input-placeholder,
textarea::-webkit-input-placeholder {
   color: $font-placeholder !important;
}

input::-moz-placeholder,
textarea::-moz-placeholder {  /* Firefox 19+ */
   color: $font-placeholder !important;
}

input:-ms-input-placeholder,
textarea:-ms-input-placeholder {
    color: $font-placeholder !important;
}

input::-ms-clear,
input::-ms-reveal {
    display: none;
}

textarea {
    overflow: auto;  /* IE10 */
}

div:focus {
    outline: none;
}

// type icon
.type-icon {
    fill: currentColor;
    height: 1em;
    overflow: hidden;
    vertical-align: -.15em;
    width: 1em;
}

.iconfont {
    display: inline-block;
}

.wrapper {
    min-width: 1280px;

    &.sync-wrapper {
        min-width: 0;
    }
}

.open-wrapper {
    min-width: 0;

    & .open-none {
        display: none;
    }

    & .qrcode-login {
        margin-bottom: 0;
    }
}

.container {
    // padding-left: 12px;
    // padding-right: 12px;
    max-width: 1280px;
    margin: 0 auto;
}

.action-box {
    cursor: pointer;

    &:hover {
        &>* {
            opacity: .7;
        }

        .badge,
        .action-icon {
            opacity: 1;
        }
    }

    &:active {
        &>* {
            opacity: 1;
        }
    }

    &.disabled,
    &[disabled] {
        &>* {
            opacity: .4;
        }

        .action-icon {
            opacity: 1;
        }
    }
}
.action-icon {
    @include clickable;
}

// form style start
.radio,
.checkbox {
    display: block;
    margin-bottom: 8px;
    margin-top: 8px;
    position: relative;
    white-space: nowrap;

    label {
        cursor: pointer;
        margin-bottom: 0;
        min-height: 20px;
    }

    .hint {
        color: $font-color-breadcrumb;
    }
}

.radiobox {
    cursor: pointer;
}

.checkbox-inner {
    width: 12px;
    height: 12px;
    position: relative;
    display: inline-block;
    border: 1px solid $font-lightest;
    background-color: $white-color;
    box-sizing: border-box;
    cursor: pointer;
    top: -1px;
    left: 0;
    vertical-align: middle;
    line-height: 1;

    &:after {
        @include transition(all .2s cubic-bezier(.12,.4,.29,1.46) .1s);
        @include transform(rotate(45deg) scale(0));
        position: absolute;
        left: 3px;
        top: 0;
        display: table;
        width: 3px;
        height: 6px;
        border: 2px solid $primary-color;
        border-top: 0;
        border-left: 0;
        content: '';
    }

    &.chk-checked {
        &:after {
            @include transform(rotate(45deg) scale(1));
        }
    }

    &.disabled {
        background-color: $font-lightest;
        border-color: $font-lightest;

        &:before {
            background-color: $font-lightest;
        }

        &:after {
            border-color: $white-color;
        }

        &:hover {
            border-color: $font-lightest;
        }
    }

    &:hover {
        border-color: $primary-color;
    }

    + input[type=checkbox] {
        display: none
    }
}

.radiobox-inner {
    @include radiobox(1);
}


.hidden {
    display: none;
}

.btn {
    background: none;
    border: 0;
    border-radius: 2px;
    box-sizing: border-box;
    cursor: pointer;
    font-size: 14px;
    padding: 0 10px;

    &:focus {
        outline: 0;
    }

    .iconfont {
        margin-right: 5px;
    }

    .icon-state-success {
        font-size: 18px;
        margin: 0;
        vertical-align: bottom;
    }
}

.btn-primary,
.btn-danger,
.btn-default,
.btn-text,
.btn-text-primary {
    height: 36px;
    line-height: 34px;
}

.btn-default {
    background-color: $white-color;
    border: 1px solid $border-input;
    color: $font-base;

    .icon-state-success {
        color: $primary-color;
    }

    &:hover {
        background-color: $bg-fa;
    }

    &:active {
        background-color: $bg-f2;
    }

    &.btn-disabled,
    &[disabled] {
        border-color: $border-color;
        color: $font-lightest;

        &:hover,
        &:active {
            background-color: $white-color;
        }
    }

    &.btn-loading {
        border: 1px solid $border-light;
    }
}

.btn-icon {
    height: 30px;
    line-height: 28px;
    color: $font-base;

    .iconfont {
        color: $font-light;
    }

    .type-icon {
        margin-right: 8px;
    }

    &:hover {
        opacity: 0.7;
    }

    &:active {
        opacity: 1;
    }

    &.dropdown {
        &:active {
            opacity: 0.7;
        }
    }
    &.btn-disabled,
    &[disabled] {
        opacity: .4;
        pointer-events: none;
    }
}

.btn-primary {
    background-color: $primary-color;
    border: 1px solid $primary-color;
    color: $white-color;
    text-align: center;

    .icon-state-success {
        color: $white-color;
        display: inline;
    }

    &:hover {
        background-color: $primary-hover-color;
        border-color: $primary-hover-color;
        color: $white-color;
    }

    &:active {
        background-color: $primary-active-color;
        color: $white-color;
    }

    &.btn-disabled,
    &[disabled] {
        opacity: .4;
        pointer-events: none;
    }
}

.btn-primary-light {
    @extend .btn-primary;
    color: $primary-color;
    background: none;

    .loading-rotate {
        border-color: $primary-color;
        border-bottom-color: $item-selected;
    }

    &:hover {
        background: #f5f9ff;
    }

    &:active {
        background: #ebf2ff;
    }

    &.btn-disabled,
    &[disabled] {
        color: $font-lightest;
        background: none;
        border-color: $border-color;
    }
}

.btn-danger,
.btn-danger-light {
    color: $error-color;
    border: solid 1px $error-color;
    background: none;
    box-shadow: none;

    .loading-rotate {
        border-color: $error-color;
        border-bottom-color: $item-selected;
    }

    &:hover {
        background: #fff8f8;
    }

    &:active {
        background: #fff2f2;
        box-shadow: none;
    }

    &.btn-disabled,
    &[disabled] {
        color: $font-lightest;
        background: none;
        border-color: $border-color;
    }
}

.btn-text {
    color: $font-base;

    &.btn-disabled,
    &[disabled] {
        color: $font-lighter-more;
    }
}

.btn-text-primary {
    color: $primary-color;
}

.btn-text-danger {
    color: $error-color;
}

.btn-pure-icon {
    border: 1px solid $border-base;
    color: $font-light;
    height: 24px;
    line-height: 22px;
    min-width: 0;
    padding: 0;
    width: 24px;

    .iconfont {
        margin-right: 0;
        vertical-align: middle;
    }

    &.btn-primary {
        border-color: $primary-color;
        color: $white-color;
    }
}

a.btn {
    display: inline-block;
}

/***global-blocker****/
.global-blocker {
    height: 100%;
    left: 0;
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 9999;
    background: rgba(255, 255, 255, 0);
}

// form style end
.switch-box {
    box-sizing: border-box;
    color: $font-base;
    margin: 0 auto;
    overflow: hidden;
    width: 240px;
    text-align: center;

    .tab {
        box-sizing: border-box;
        font-size: 20px;
        height: 28px;
        line-height: 28px;
        cursor: pointer;
        color: $font-light;

        + .tab {
            margin-left: 20px;
            position: relative;
            overflow: hidden;
        }
    }

    .active {
        color: $font-darker;
        font-weight: 500;
        cursor: default;

        &::after {
            background-color: $font-darker !important;
        }
    }
}

// header
.logo-large,
.center-logo-mini {
    height: 110px;
    line-height: 110px;
    text-align: center;

    .default-logo {
        display: inline-block;
        height: 40px;
        width: 165px;
        vertical-align: middle;
        background-image: url(../images/logo.png);
        background-repeat: no-repeat;
        background-size: contain;

        &.ai {
            height: 24px;
            width: 220px;
            background-image: url(../images/logo_ai.png);
        }
    }

    img {
        height: 40px;
    }
}

.oauth-des {
    color: $font-darker;
    font-size: 14px;
    margin-top: 30px;
    margin-bottom: -10px;

    &.center {
        text-align: center;
    }
}

.oauth-tip {
    color: $font-light;
    // text-align: center;
    // position: fixed;
    line-height: 18px;
    margin-top: 20px;
    width: 100%;
}

.qrcode-login {
    font-size: 12px;
    margin-top: 38px;
    margin-bottom: 68px;
    text-align: center;

    .qr-header-hint {
        color: $font-base;
        margin-top: 24px;
        font-size: 0;

        span {
            font-size: 14px;
            line-height: 22px;
        }

        a {
            color: $font-darker;
            margin: 0 2px;

            &:hover {
                color: $primary-color;
            }
        }

        .icon-retry {
            cursor: pointer;
            font-size: 14px;
            margin-left: 8px;
            vertical-align: middle;

            &:hover {
                color: $primary-color;
                opacity: 1;
            }
        }

        .qr-expire-tip {
            display: none;
        }
    }

    .qr-code {
        position: relative;
        border: 0;
        height: 188px;
        margin: 20px auto 7px;
        width: 188px;

        img {
            height: 188px;
            width: 188px;
        }

        .qrcode-mask {
            background-color: rgba(255,255,255, 0.9);
            height: 188px;
            line-height: 188px;
            position: absolute;
            top:0;
            width: 188px;

            .iconfont {
                color: #aab0c6;
                font-size: 68px;
            }
        }
    }

    .scan-success-container,
    .login-expire-container {
        display: none;
    }

    .scan-success i {
        margin-top: 42px;
        margin-bottom: 12px;
        font-size: 70px;
        line-height: 70px;
        color: $success-color;
    }

    .login-expire {
        background-image: url('../images/login-expire.png');
        background-image: -webkit-image-set(url('../images/login-expire.png') 1x, url('../images/<EMAIL>') 2x);
        background-position: center center;
        background-repeat: no-repeat;
        height: 60px;
        margin: 43px auto 20px;
        width: 100px;
    }

    .success-title,
    .expire-title {
        color: $font-darker;
        font-size: 14px;
        line-height: 25px;
    }

    .success-message {
        color: $font-base;
        margin-top: 10px;
    }

    .return-scan,
    .refresh-scan {
        font-size: 12px;
        height: 24px !important;
        line-height: 22px !important;
        margin-top: 16px;
        padding: 0 10px;
        color: $font-darker;
    }
}

.login-box,
.register-box,
.forgot-box,
.activation-box,
.sso-entry-box {
    margin: 0 auto;
    width: 360px;

    .btn {
        height: 40px;
        line-height: 38px;
    }
}

.step-2,
.step-3 {
    display: none;
}

.progress {
    margin: 0 auto 58px;

    &.register-progress {
        width: 220px;
    }

    &.forgot-progress {
        margin-top: 30px;
        width: 440px;
    }

    .radius-box {
        width: 20px; height: 20px;
        line-height: 20px;
        position: relative;
        overflow: hidden;
        z-index: 100;

        .radius {
            color: #e9ebf5;
            background-color: currentColor;
            border: 19px dotted;
            border-radius: 50%;
            border-width: 0vw;
            /* IE7,IE8圆尺寸要小1像素同时有1像素偏移 */
            margin: 0 0 1px 1px;
            margin: 0vw;
            position: absolute;
            width: 100%; height: 100%;

        }

        .text {
            color: $font-base;
            font-size: 12px;
            position: relative;
            text-align: center;
        }

    }

    .line {
        background-color: #e9ebf5;
        height: 4px;
        position: relative;
        width: 200px;
        border-top: 8px solid $white-color;
        border-bottom: 8px solid $white-color;
        z-index: 99;
    }

    .line,
    .step-info {
        display: table-cell;
    }

    .step-info {
        position: relative;

        .explanation {
            color: $font-base;
            left: -40px;
            position: absolute;
            text-align: center;
            top: 30px;
            width: 100px;
        }
    }

    .current {
        .radius {
            color: $font-base;
        }

        .text {
            color: $white-color;
        }

        .explanation {
            color: $font-darker;
        }
    }

    .done {
        .radius {
            color: $font-light;
        }

        .text {
            color: $white-color;
        }

        .explanation {
            color: $font-light;
        }

        + .line {
            background-color: $font-light;
        }
    }
}

.input-group {
    @include transition(border-color .5s);
    border-collapse: separate;
    display: table;
    position: relative;
}

.input-group-addon {
    font-size: 14px;
    line-height: 1;
    text-align: center;
    vertical-align: middle;
    white-space: nowrap;
    width: auto;
}

.input-group .form-control,
.input-group-addon {
    display: table-cell;
}

.form {
    margin-top: 30px;

    .form-group {
        margin-top: 24px;
        min-height: 17px;
        position: relative;

        &.action-group {
            @include clearfix;

            span.error {
                margin-bottom: 12px;
                text-align: center;
                width: 100%;

                &.black {
                    color: $font-darker;
                    word-break: break-all;

                    i {
                        display: none;
                    }
                }
            }
        }

        .remember-login label {
            cursor: pointer;
        }

        .forgot-pwd:hover {
            opacity: 1;
            color: $primary-color;
        }

        &.error {
            .input-group {
                border-color: $error-color;
            }

            // + .form-group {
            //     margin-top: 6px;

            //     &.fangcloud-protocol {
            //         margin-top: 16px;
            //     }
            // }
        }
    }

    .input-group {
        border: 1px solid $border-input;
        border-radius: 2px;
        box-sizing: border-box;
        display: inline-table;
        vertical-align: middle;

        &::hover {
            border-color: $primary-color;
        }

        &.active,
        &.focus {
            border-color: $primary-color;
        }

        &.disabled {
            background-color: $item-hover-bg;
            border-color: $item-hover-bg;

            .select-group {
                cursor: default;
            }
        }

        [disabled] {
            background-color: $item-hover-bg;
        }
    }

    .select-group {
        cursor: pointer;
        line-height: 30px;

        &.input-group-addon {
            width: 100px;
        }
    }

    .enterprise-size {
        line-height: 37px;
    }

    .text {
        border: 0;
        box-sizing: border-box;
        font-size: 14px;
        height: 38px;
        line-height: 38px;
        padding: 0 10px;
        width: 100%;
    }

    .clear-text,
    .toggle-pwd {
        display: none;
    }

    .clear-text,
    .toggle-pwd {
        cursor: pointer;
        color: $font-base;
        width: 16px;
        height: 16px;
        padding-right: 10px;
    }

    .toggle-pwd {
        font-size: 22px;

        &:hover {
            color: $primary-color;
        }
    }

    .get-captcha {
        width: 114px;
    }

    .get-code {
        color: $primary-color;
        cursor: pointer;
        font-size: 14px;
        // 去除 table cell 自定义了padding
        width: 1%;
        padding: 0 10px;
        // padding-right: 8px;

        &:hover {
            color: $primary-active-color;
        }

        &:active {
            color: $primary-color;
        }

        &[get-disabled] {
            color: $font-placeholder;
            cursor: default;
            opacity: 1;
        }

    }

    .getting {
        color: $font-placeholder;
        cursor: default;
    }

    .voice-captcha {
        float: right;
        font-size: 12px;
        margin-top: 2px;

        a {
            color: $font-base;

            &:hover {
                color: $primary-color
            }
        }
    }

    span.error,
    .hint {
        float: left;
        font-size: 12px;
        line-height: 17px;
        margin-top: 1px;
    }

    span.error {
        color: $error-color;
        display: block;
        // left: 0;
        // position: absolute;
        // top: 40px;

        .icon-error {
            font-size: 12px;
            margin-right: 6px;
        }
    }

    .hint {
        color: $font-color-breadcrumb;
    }

    .form-right {
        color: $font-darker;
        float: right;
    }

    button {
        width: 360px;
    }

    .go-back {
        color: $font-darker;
        display: block;
        margin-top: 16px;
        text-align: center;

        .iconfont {
            color: $font-base;
            margin-right: 5px;
            vertical-align: -1px;
        }
    }

    .inline {

        + .inline {
            margin-top: 10px;
        }

        &.activate-form {
            text-align: center;

            .label {
                color: $font-darker;
            }

            .form-control a {
                color: $primary-color;
            }
        }

        .label {
            color: $font-light;
            display: inline-block;
        }

        .form-control {
            display: inline-block;

            a {
                color: $font-base;
                display: inline-block;
                padding-right: 10px;
            }
        }
    }

    .bind-register,
    .bind-help {
        display: inline-block;
        margin-top: 20px;
        text-align: center;
        width: 360px;
    }

    .bind-help {
        width: 360px;
    }

    .pic-captcha.show ~ .form-footer {
        margin-top: 16px;
    }

    .form-footer {
        overflow: hidden;
        margin-top: 25px;

        .inline {
            margin-top: 0px;
        }

        .other-login-container {
            @include display-flex;
            @include flex-column;
            margin-top: 42px;

            .other-login {
                @include display-flex;
                @include align-content-start;

                .prompt-title {
                    @include flex-shrink-value(0);
                    @include display-inline-flex;
                    @include flex-align-center;

                    text-align: center;
                    color: $font-light;
                    margin-right: 10px;
                    width: 65px;
                }

                .other-login-content {
                    @include display-flex;
                    @include align-content-start;
                    @include flex-wrap;
                    @include flex-grow-value(1);

                    a {
                        @include display-flex;
                        @include align-content-start;
                        @include flex-align-center;
                        @include flex-grow-value(1);
                        @include flex-shrink-value(1);
                        @include flex-basis-value(0);

                        color: $font-darker;
                        font-size: 0;
                        max-width: 95px;

                        svg {
                            font-size: 20px;
                            margin-right: 4px;
                        }

                        &:hover {
                            color: $primary-color;
                            opacity: 1;

                            i {
                                color: $primary-color;
                            }
                        }

                        &>* {
                            display: inline-block;
                            vertical-align: middle;
                        }

                        span {
                            font-size: 12px;
                        }

                        i {
                            margin-right: 4px;
                            color: $font-base;
                            font-size: 20px;
                        }
                    }
                }
            }
        }
    }
}

.logos-header {
    margin: 100px auto 0;
    text-align: center;
    width: 300px;


    .bind-logo {
        background-position: center center;
        background-repeat: no-repeat;
        height: 54px;
        margin: 0 auto;
        width: 96px;
    }

    .logo-dingtalk {
        background-image: url('../images/yifangyun-dingtalk.png');
        background-image: -webkit-image-set(url('../images/yifangyun-dingtalk.png') 1x, url('../images/<EMAIL>') 2x);
    }

    .logo-wechat {
        background-image: url('../images/yifangyun-wechat.png');
        background-image: -webkit-image-set(url('../images/yifangyun-wechat.png') 1x, url('../images/<EMAIL>') 2x);
    }

    .logo-qihoo360 {
        background-image: url('../images/yifangyun-360.png');
        background-image: -webkit-image-set(url('../images/yifangyun-360.png') 1x, url('../images/<EMAIL>') 2x);
    }

    h3 {
        font-size: 16px;
        margin-top: 22px;
        margin-bottom: 40px;
    }
}

.forgot-done {
    font-size: 14px;
    text-align: center;

    .reset-success {
        background-image: url('../images/empty-success.png');
        background-image: -webkit-image-set(url('../images/empty-success.png') 1x, url('../images/<EMAIL>') 2x);
        background-position: center center;
        background-repeat: no-repeat;
        height: 100px;
        margin: 73px auto 20px;
        width: 100px;
    }

    p {
        line-height: 20px;
        margin-bottom: 10px;
    }

    .hint {
        color: $font-color-breadcrumb;
    }

    .btn {
        margin-top: 30px;
        padding: 0 65px;
    }
}

// 失败页面

.failure-state {
    text-align: center;
    margin-top: 160px;

    .error-icon {
        background-image: url('../images/warn-stop.png');
        background-image: -webkit-image-set(url('../images/warn-stop.png') 1x,
        url('../images/<EMAIL>') 2x);
        background-position: center center;
        background-repeat: no-repeat;
        height: 100px;
        margin: 0 auto 6px;
        width: 100px;

        &.login-failure-error {
            background-image: url('../images/login-expire.png');
            background-image: -webkit-image-set(url('../images/login-expire.png') 1x,
            url('../images/<EMAIL>') 2x);
        }
    }

    .error-title {
        color: $font-darker;
        font-size: 16px;
        line-height: 22px;
    }

    .error-message {
        color: $font-base;
        font-size: 12px;
        margin-top: 4px;
    }

    .error-message-14 {
        font-size: 14px;
    }

}


// country group
.select-group-content {
    padding: 0 12px;
    text-align: left;

    .select {
        display: inline-block;
    }

    .select-arrow {
        @include transform(scale(.67));
        color: $font-base;
        float: right;
        font-size: 12px;
    }
}

.select-group-options {
    background-color: $white-color;
    border: 1px solid #d0d0d0;
    box-sizing: border-box;
    display: none;
    left: -1px;
    max-height: 300px;
    overflow: hidden;
    padding: 6px 0;
    position: absolute;
    top: 39px;
    // width: 100%;
    z-index: 99;

    ul {
        max-height: 240px;
        overflow: auto;
    }

    li {
        cursor: pointer;
        font-size: 14px;
        line-height: 30px;
        padding: 0 15px;
        text-align: left;

        &:hover {
            background-color: $item-selected;
        }

        &.selected {
            background-color: $item-selected;
        }

        .country-code {
            float: right;
            text-align: right;
        }
    }
}

// toast
.toast {
    font-size: 14px;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 10001;
}

.toast-notice {
    width: auto;
    height: 0;
    text-align: center;
    vertical-align: middle;
    animation: notifyIn .2s ease;
    -webkit-animation: notifyIn .2s ease;
}

.toast-notice-content {
    @include ellipsis;

    background: $toast-bg;
    border-radius: 4px;
    box-sizing: border-box;
    color: $white-color;
    display: inline-block;
    font-size: 12px;
    margin-top: 10px;
    padding: 7px 20px;
    max-width: 500px;
    min-width: 220px;
    text-align: center;
    animation: notifyOut .3s linear 2.2s both;
    -webkit-animation: notifyOut .3s linear 2.2s both;

    .toast-delay & {
        text-align: left;
        animation: notifyOut .3s linear 4.7s both;
        -webkit-animation: notifyOut .3s linear 4.7s both;
    }

    .no-animation & {
        text-align: left;
        animation: none;
        -webkit-animation: none;
    }

    .content {
        float: left;
    }

    .action {
        color: $toast-action;
        cursor: pointer;
        float: right;
    }
}

.show-country-select {
    .select-group-options {
        top: 38px;

        .input-group {
            background-color: #f8fafd;
            border: 0;
            line-height: 30px;
            margin: 15px;
            width: 230px;
        }

        .iconfont {
            color: $font-light;
            font-size: 18px;
            width: 32px;
            text-align: center;
            padding: 0;
        }

        .text {
            background-color: #f8fafd;
            line-height: 30px;
            height: 30px;
            padding: 0;
        }

        .empty {
            color: $font-light;
        }
    }
}

@keyframes notifyIn {
    from {transform: translateY(-10px); opacity: .2;}
    to {transform: translateY(0); opacity: 1;}
}

@-webkit-keyframes notifyIn {
    from {-webkit-transform: translateY(-10px); opacity: .2;}
    to {-webkit-transform: translateY(0); opacity: 1;}
}

@keyframes notifyOut {
    from {transform: translateY(0); opacity: 1;}
    to {transform: translateY(-10px); opacity: 0;}
}

@-webkit-keyframes notifyOut {
    from {-webkit-transform: translateY(0); opacity: 1;}
    to {-webkit-transform: translateY(-10px); opacity: 0;}
}

.ellipsis {
    @include ellipsis;
}

.i18n-en {
    .switch-box {
        width: 280px;

        .tab {
            font-size: 16px;
        }
    }

    .form {
        .inline .form-control a {
            padding-right: 20px;
        }

        .form-footer {

            .other-login-container {

                .other-login {

                    span {
                        font-size: 11px;
                    }
                    .prompt-title {
                        margin-right: 4px;
                        width: 60px;
                    }

                    .other-login-content {
                        a {
                            @include flex-grow;
                            max-width: 900px;
                            margin-right: 0;
                        }
                    }
                }
            }
        }
    }
}

.quc-slide-con {
    z-index: 10000 !important;
}
