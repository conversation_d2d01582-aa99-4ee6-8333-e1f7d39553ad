// .wrapper {
//     width: 1180px;
//     margin: 0 auto;
// }

.sem-wrapper {
    min-width: auto;
    width: 460px;

    .header,
    .plan-list,
    .register-top,
    .register-hint {
        display: none;
    }
}

.plan-list {
    // border: 1px solid #e8e8e8;
    height: 50px;
    margin: 30px auto;
    width: 840px;

    &:after {
        content: ' ';
        display: block;
        clear: both;
    }

    &.personal-plan {
        width: 280px;
    }

    &.aliyun-plan {
        width: 360px;

        .plan {
            background-color: $border-color;
            border-color: $border-color;
            width: 360px;
        }

        .plan-head {
            padding: 0 10px;
        }

        .hint {
            color: $font-light;
        }

        .deadline {
            float: right;
            font-size: 14px;
        }
    }

    .plan {
        background-color: $item-hover-bg;
        border: 1px solid $font-lightest;
        box-sizing: border-box;
        cursor: pointer;
        float: left;
        height: 50px;
        margin-left: -1px;
        position: relative;
        width: 280px;
        z-index: 1;

        &:first-child {
            margin-left: 0;
        }

        &.active {
            border-color: $primary-color;
            z-index: 3;

            .plan-head {
                background-color: $item-selected;
            }

            .plan-info {
                border-color: $primary-color;
            }
        }

        &:hover {
            z-index: 2;

            .plan-info {
                display: block;
            }

            .select-arrow {
                &:before {
                    content: '\e7a5'
                }
            }

        }

        .plan-info {
            background-color: $white-color;
            border: 1px solid $font-lightest;
            border-top: 0;
            // border-top-color: $item-selected;
            box-sizing: border-box;
            display: none;
            // height: 208px;
            list-style-type: square;
            margin-left: -1px;
            padding: 20px 33px;
            width: 280px;


            li {
                font-size: 14px;
                line-height: 2;
                color: $font-light;

                span {
                    color: $font-dark;
                }
            }
        }
    }

    .plan-head {
        height: 48px;
        line-height: 48px;
        padding: 0 20px;


        .edition {
            font-size: 16px;
            color: #000;
        }

        .hint {
            font-size: 14px;
            color: $font-base;
            margin-left: 5px;
        }

        .select-arrow {
            color: $font-light;
            float: right;
            font-size: 12px;

            &:before {
                content: '\e7e7';
            }

        }
    }

}

.plan-only {
    position: relative;
    width: 400px;
    margin: 0 auto 50px;
}

/* v2 register finish page */
.v2-download-container {
    border-radius: 4px;
    margin: 42px auto 50px;
    width: 630px;

    h4 {
        color: $font-darker;
        font-size: 16px;
        font-weight: 500;
        letter-spacing: 0.2px;

        &::before {
            background-color: #3ba0f2;
            border-radius: 50%;
            content: '';
            display: inline-block;
            height: 8px;
            vertical-align: 1px;
            width: 8px;
        }

        .sub {
            color: $font-base;
            font-size: 14px;
            font-weight: normal;
            margin-top: 4px;
            padding-left: 14px;
        }
    }

    .pc-client {
        float: left;
        width: 310px;
    }

    .thumb-pic {
        background: no-repeat left bottom;
        background-image: url('../images/<EMAIL>');
        background-image: -webkit-image-set(url('../images/guide.png') 1x, url('../images/<EMAIL>') 2x);
        background-size: contain;
        display: inline-block;
        margin-bottom: -4px;
        margin-left: 14px;
        padding-bottom: 4px;
        width: 100px;
    }

    .pc-client {
        .btns {
            font-size: 0;
            margin-left: 30px;
            width: 340px;
        }

        .already {
            color: $font-base;
            font-size: 12px;
            letter-spacing: 0.1px;
            margin-top: 10px;
            margin-left: 14px;
        }
    }

    .thumb-pic,
    .qrcode {
        height: 100px;
    }

    .pc-download-btn,
    .app-download-btn {
        display: block;
        width: 160px;
        height: 36px;
        line-height: 36px;
        border-radius: 2px;
        border: solid 1px $font-base;
        box-sizing: border-box;
        padding: 0 15px;
        font-size: 14px;
        vertical-align: middle;
        color: $font-base;
        margin-top: 20px;
        margin-left: 14px;
    }

    .pc-download-btn {
        border-color: $primary-color;
        color: $primary-color;

        .iconfont {
            margin-right: 10px;
        }

        .icon-win {
            font-size: 12px;
        }
    }

    .app {
        float: right;
        width: 225px;
    }

    .qrcode {
        width: 255px;
        margin: 0 28px 0 16px;

        img {
            width: 80px;
            height: 80px;
            margin: 20px 24px 10px -4px;
        }

        p {
            display: inline-block;
            font-size: 14px;
            color: $font-base;
            vertical-align: middle;
        }
    }

    .app-download {
        width: 500px;

        .icon-android {
            position: relative;
            top: -1px;
        }
    }

    .app-download-btn {
        border: solid 1px $font-dark;
        color: $font-dark;
    }

    .app-download-btn {
        .iconfont {
            margin-right: 10px;
        }
    }

    .get-started-arrow {
        display: inline-block;
        font-size: 14px;
        margin: 36px 0 0 8px;

        &::after {
            content: '';
            width: 13px;
            height: 12px;
            display: inline-block;
            background: no-repeat center center;
            background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMgAAADICAMAAACahl6sAAAAaVBMVEUAAAA4n/M6oPI7oPEymv8Aqv87oPI5n/Q6n/I6n/I6oPI6oPI6oPM6n/E6oPI6oPE6oPI6oPI5oPg6n/I6oPI6oPI4oPI3nvU7n/M6ofI5n/Q6oPI5nPEzo/8A//85oPI6oPM7oPI6oPIQhL/FAAAAIXRSTlMAQGG3BQOxWv2P+Xhm8une06Qgyr60Jxh9Xkg7EgwBhVN1TvPmAAAB9UlEQVR42u3d20rDQBRG4d20UXPoubWedef9H1J6IUojiBNw/tmuDwK5XcxsQi6GMQBAALu+OTbVyQpXd62fLXsr28E/XFnJOv80X1ixbluPUdK4xyjZepCSrV+WWJk69xhr8uBBSupNlJLbNkrJvbvHmPhqiLImlOihRA8leijRQ4keSvRQoocSPZTooUQPJXoo0UOJHkr0UKKHEj2U6KFEDyV6KNETvKS2An1XckXJb1BCSUEo0UOJHkr0UKKHEj2U6KFEDyV6KNFDiR5K9FDyo93sj70OftEyTC/ZzZeewzB8eT8/+2klD0sXMawXlu5xcB17S/a89oxGg9JbqsqlrCzV1nMaL8mLJVp7RuMQf7NEK89pHHJtiY6e03hrLSzRnWc0Dmkt1WnlShpLNlP6IG5sgse15zGe9M3Cpni+23sOw2XHobapnmZ/rAnya9V7jI4qSkeQfUWHFjq00KGFDi10aKFDCx1a6NBChxY6tNChJcoRviiHKunQQocWOrTQoYUOLXRooUMLHVro0EKHFjq00KGFDi10aKFDS5SO6ygdN3QooUMLHVro0EKHFjq0hOnwS2VeNB/l6v+XTYwOmwXpsC5Ih20jzPlZE2M9zKogHfa0itFhdh+kw+zgH45WtLpr/WxZWel2fTNvqpMBAP6JdyBTm1KP8YzHAAAAAElFTkSuQmCC');
            background-size: 100%;
            vertical-align: middle;
            margin-left: 2px;
        }
    }
}

.register-done,
.validate-done {
    // margin: 26px auto 30px;
    text-align: center;
    background-color: #f8fafd;
    padding: 26px 0 30px;

    &.enterprise-register-done {
        margin-top: 170px;
        background-color: $white-color;
    }

    .status {
        .iconfont {
            font-size: 72px;
        }

        .icon-fail {
            color: $warning-color;
        }

        .icon-success {
            color: $success-color;
        }
    }

    p {
        font-weight: 500;
    }

    .title {
        color: $font-darker;
        font-size: 20px;
        font-weight: 500;
        line-height: 28px;
        margin: 6px auto;
    }

    .subtitle {
        color: $font-base;
        font-size: 14px;
        font-weight: 500;
        line-height: 20px;

        b {
            color: $font-darker;
            font-weight: normal;
        }
    }

    .btn-primary,
    .btn-default {
        margin-top: 20px;
        padding: 0 22px;
    }
}

.validate-done {
    background: $white-color;
    margin-top: 150px;
}


/* 表单相关样式 */
$input-width: 360px;

.register-box {
    width: $input-width;

    .form {
        margin-top: 24px;
    }

    .form-group {
        margin-top: 24px;
        &.phone {
            .select-group {
                vertical-align: middle;
            }
        }
    }

    .input-group {
        border-color: $font-lightest;
        width: $input-width;
    }


    .enterprise-size {
        .select-group-options {
            width: $input-width;
        }

        .select {
            font-size: 14px;
        }

        .placeholder {
            color: $font-placeholder;
        }
    }

    button {
        width: $input-width;

        + .error {
            display: inline-block;
            width: 360px;
            text-align: center;
        }
    }

    .action-group {
        .btn{
            &.loading{
                pointer-events: none;
            }
        }
    }
}
.register-qrcode-modal {
    min-width: 1280px;
    height: 100vh;
    min-height: 700px;
    display: none;
    align-items: center;
    justify-content: center;
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background-color: rgba($black-color, .5);
    z-index: 99;
    .qrcode-dialog{
        position: relative;
        width: 290px;
        height: 344px;
        background-color: #fff;
        border-radius: 6px;
        text-align: center;
        padding: 45px 0;
        box-sizing: border-box;
        .title{
            font-size: 20px;
            color: #333333;
            margin-bottom: 8px;
        }
        .hint{
            font-size: 12px;
            color: #666666;
            line-height: 16px;
            margin-bottom: 20px;
        }
        .qrcode-img{
            width: 120px;
            height: 120px;
            background: #d8d8d8;
            margin-bottom: 30px;
        }
        .btn{
            width: 100%;
            color: #666;
            &:hover{
                color: #3ba0f2;
            }
        }
        .close{
            width: 36px;
            height: 36px;
            border: 2px solid #fff;
            border-radius: 50%;
            position: absolute;
            right: -70px;
            top: 0px;
            line-height: 36px;
            cursor: pointer;
            i{
                color: #fff;
                font-weight: bold;
            }
        }
    }
    &.show{
        display: flex;
    }
}


