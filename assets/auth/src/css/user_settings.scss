@import 'dialog';

.header {
    padding-bottom: 0;

    .logo {
        img {
            height: 32px;
            max-width: 190px;
            max-height: 32px;
        }
    }
}
// slide-nav
.side-nav {
    border-top: 1px solid #ededed;
    float: left;
    width: 219px;

    .nav-item {
        a {
            color: $font-darker;
            display: block;
            font-size: 14px;
            height: 40px;
            line-height: 40px;
            padding-left: 30px;
        }

        .iconfont {
            color: $font-base;
        }

        &.selected {
            background: $item-selected;
        }
    }
}

// content
.container-main {
    border-left: 1px solid #ededed;
    border-top: 1px solid #ededed;
    font-size: 12px;
    margin-left: 219px;
    min-height: 700px;
    padding: 20px 30px 0 30px;

    &.sync-container {
        margin-left: 0;
    }
}

.fieldset {
    border-bottom: 1px solid $border-color;
    margin-bottom: 26px;
    padding-bottom: 30px;

    &:last-child {
        border-bottom: 0;
    }

    h4 {
        color: $font-darker;
        font-size: 14px;
        font-weight: 500;
        line-height: 1.43;
    }

    .field-section {
        margin-top: 15px;
        margin-left: 115px;

        &.third-party {
            margin-top: 0;
        }
    }

    .account-settings,
    .third-party {
        margin: 15px 0;
        margin-left: 15px;

        + .title-hint {
            height: 30px;
            line-height: 30px;
            margin: 0 0 0 115px;
        }

        .label {
            width: 160px;
        }
    }
}

.settings-form {
    li {
        line-height: 20px;
        margin-bottom: 10px;
    }

    .label {
        color: $font-base;
        float: left;
        width: 60px;
        text-align: right;
    }

    .info-list {
        display: inline-block;
        width: auto;

        .unformatted {
            color: $font-light;
        }

        .link-button {
            margin-left: 5px;
        }

        .type-icon {
            cursor: pointer;
        }

        .icon-list-edit {
            color: $font-light;
            font-size: 12px;
        }
    }

    .validate-status {
        display: block;
        line-height: 17px;
    }

    .plain-text {
        line-height: 17px;
        opacity: 0.6;
    }

    .icon-unverification {
        color: #fc575a;
    }

    .icon-verification {
        color: #00cf72;
    }

    .btn-default {
        color: $font-base;
        font-size: 12px;
        height: 30px;
        line-height: 30px;
        padding: 0 12px;
    }
}

.title-hint {
    color: $font-light;
    font-size: 12px;
    line-height: 20px;
    margin: 20px 0 15px 15px;
}

.third-party {
    .account {
        @include ellipsis;
        color: $font-darker;
    }

    .actions {
        display: inline-block;
        vertical-align: top;
    }

    .link-button {
        color: $primary-color;
        display: inline-block;
        font-size: 12px;
        margin-right: 20px;
        text-align: center;
    }
}

.account-list {
    li {
        height: 30px;
        line-height: 30px;
    }

    .svg-icon {
        float: left;
        margin-right: 10px;
    }

    .type-icon {
        display: inline-block;
        height: 32px;
        vertical-align: middle;
        width: 32px;
    }

    .account-info {
        display: inline-block;
    }

    .party-name {
        color: $font-base;
        width: 60px;
        text-align: right;
    }

    .account {
        @include ellipsis;
        color: $font-darker;
        font-size: 12px;
        line-height: 1.42;
    }

    .has-bind {
        margin: 12px 0 10px;

        .party-name {
            line-height: 20px;
            margin-bottom: 1px;
        }
    }

    .actions {
        display: inline-block;
        vertical-align: top;
    }

    .link-button {
        color: $primary-color;
        display: inline-block;
        font-size: 12px;
        margin-right: 20px;
        text-align: center;
    }
}

.dialog-wrap {
    .form {
        width: 300px;
        margin: 55px auto 0;

        .text,
        .get-code,
        .select {
            font-size: 12px;
        }

        .input-group {
            width: 100%;

        }

        .select-group {
            line-height: 30px;
        }

        span.error {
            top: 32px;
        }
    }

    .email-set,
    .phone-set,
    .phone-validate,
    .password-edit {
        width: 300px;
    }

    .email-set {
        height: 58px;
        margin: 67px auto;
    }

    .phone-set {
        min-height: 110px;
        margin: 0 auto 24px;
    }

    .phone-validate {
        height: 84px;
        margin: 56px auto 52px;
    }


    .password-edit {
        margin: 24px auto;
    }

    .label-hint {
        color: $font-base;
        font-size: 12px;
        margin-bottom: -10px;
    }

    .validate-email {
        margin: 55px auto;
        text-align: center;

        .content {
            color: $font-darker;
            line-height: 20px;
            margin: 0 25px;
        }

        .hint {
            color: #9ba1b7;
            font-size: 12px;
            line-height: 17px;
            margin-top: 15px;
        }
    }

    .select-group-options {
        .input-group {
            width: 230px;
        }
    }
}

// device list
.device-list {
    margin-left: 5px;
    margin-top: 10px;
}

.list-row {
    @include display-flex;
    width: 100%;
    height: 48px;
    min-height: 48px;

    &::after {
        content: " ";
        width: 0;
        height: 0;
        clear: both;
    }
}

.list-col {
    color: #747b93;
    vertical-align: middle;
    line-height: 48px;
    box-sizing: border-box;
    word-break: break-all;

    .tooltip {
        line-height: 18px;
    }
}

.device-list {
    color: #747b93;
    font-size: 12px;
    text-align: left;

    .show-more-button {
        display: inline-block;
        margin: 20px 0 20px 20px;
        cursor: pointer;
    }

    .devices-container {
        max-height: 245px;
        overflow: hidden;

        &.show-more {
            max-height: none;
        }
    }
}

.device-name {
    @include ellipsis;
    width: 44%;
    padding-left: 10px;

    .device-name-detail {
        @include ellipsis;
        display: inline-block;
        max-width: 100%;
    }

    .iconfont {
        padding-right: 5px;
        color: $font-base;
        vertical-align: middle;
    }

    .current {
        display: inline-block;
        margin-left: 4px;
        padding: 0 4px;
        font-size: 12px;
        line-height: 16px;
        height: 16px;
        color: $white-color;
        background: $primary-color;
        border-radius: 2px;
        vertical-align: middle;
    }

    .creditable {
        display: inline-block;
        padding: 0 4px;
        font-size: 12px;
        line-height: 16px;
        height: 16px;
        color: $white-color;
        background: $success-color;
        border-radius: 2px;
        vertical-align: middle;
    }
}

.list-head {
    .list-col {
        line-height: 30px;
    }
    .list-row {
        color: $font-light;
        height: 30px;
        min-height: 30px;
        // padding-right: 80px;
        position: relative;
    }

}

.list-body {
    .device-item {
        &:hover {
            background-color: $item-hover-bg;

            .type-icon {
                display: inline-block;
            }
        }
    }

    .device-name {
        color: $font-darker;
    }

    .empty-item {
        line-height: 40px;
        text-align: center;
    }
}

.device-last-login {
    width: 22%;
}

.device-last-location {
    width: 22%;

    span {
        @include ellipsis;
        display: inline-block;
        vertical-align: middle;
        max-width: calc(100% - 30px);
    }

    .iconfont {
        margin-left: 5px;
        line-height: 18px;
        vertical-align: middle;
    }
}

.device-operate {
    width: 60px;
    padding-right: 20px;
    height: 50px;
    line-height: 50px;
    text-align: left;

    .type-icon {
        cursor: pointer;
        display: none;
        height: 20px;
        width: 20px;
        vertical-align: middle;
    }
}

.two-step-verification {
    .info-item {
        line-height: 30px;
    }

    .validate-success {
        color: $success-color;
    }

    .validate-type-title {
        color: $font-base;
    }

    .icon-list-edit {
        color: $font-light;
        font-size: 12px;
    }

    .hint {
        margin-bottom: 10px;
    }
}

// sync styles
.sync-wrapper {
    .settings-form {
        .info-list {
            width: 370px;
        }
    }

    .get-detail {
        display: none;
    }

    .device-name {
        width: 35%;
    }

    .device-last-login {
        width: 23%;
    }

    .device-last-location {
        width: 23%;
    }
}

.i18n-en {
    .settings-form {
        .label {
            margin-right: 10px;
        }

        .info-list {
            line-height: 20px;
        }
    }

    .fieldset {
        .account-settings,
        .third-party {
            + .title-hint {
                margin-left: 72px;
            }
        }
    }
}


.api-token-filed {
    display: none;

    .info-item {
        @include display-flex;

        .info-item-label {
            width: 52px;
            color: #657083;
        }

        .action-link {
            margin-left: 16px;
            cursor: pointer;
        }

        .content-wp {
            margin-top: 16px;
            padding: 12px 16px;
            border-radius: 8px;
            background: var(---02, #F7F9FA);

            li {
                @include display-flex;
                margin-bottom: 12px;

                &:last-child {
                    margin-bottom: 0;
                }
            }

            .content-label {
                width: 76px;
                color: #657083;
            }

            .copy-icon {
                width: 16px;
                height: 16px;
                margin-left: 10px;
                cursor: pointer;
            }
        }
    }

    .api-select-group {
        width: 84px;
        padding: 0 25px 0 10px;
        height: 30px;
        line-height: 28px;
        border-radius: 2px;
        background-color: #ffffff;
        border: solid 1px #e3e3e3;
        color: #252e36;
        position: relative
    }

    .select-caret {
        position: absolute;
        top: 14px;
        right: 10px;
        border: 4px solid transparent;
        border-top: 4px solid #b2b7ca;
    }

    .api-setting-wp {
        @include display-flex;
        @include flex-align-center;
    }

    .select-option-panel {
        display: none;
        position: absolute;
        left: 0;
        top: 32px;
        background-color: #fff;
        width: 120px;
        box-shadow: 0 3px 8px 0 rgba(0, 0, 0, 0.19);
        border-radius: 2px;
        border: 0;

        &.show {
            display: block;
        }

        li {
            padding: 0 10px;
            line-height: 30px;
            cursor: pointer;

            &:hover {
                background-color: #f5f5f5;
            }
        }
    }
}