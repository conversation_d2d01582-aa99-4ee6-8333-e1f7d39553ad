// 移动端是否需要使用rem html的默认font-size 如何区分各端计算宽度；
@import 'utils/variables';
@import 'utils/mixins';
@import 'iconfont';
@import 'dialog';
// base style


// 0.026667rem 1px
// 0.08rem 3px
// 兼容部分浏览器不显示边框问题
// $border-width-default: 0.053333rem; //2像素边框
$border-width-default: 0.026667rem; //1像素边框

$font-size-small: 0.373333rem; //14px
$font-size-normal: 0.426667rem; //16px
$font-size-big: 0.48rem; //18px


html {
    background: $white-color;
    color: $font-darker;
}

body {
    font-family: Arial, 'Helvetica Neue', Helvetica, "Microsoft YaHei", "微软雅黑", 'PingFang SC', \5fae\8f6f\96c5\9ed1, "WenQuanYi Micro Hei", \5b8b\4f53, sans-serif;
    font-size: 14px;
    line-height: 1.4;
}

a {
    color: $primary-color;
    outline: none;
    text-decoration: none;
}

// reset css
body, div, dl, dt, dd, ul, ol, li, h1, h2, h3, h4, h5, h6, pre, code, form, fieldset, legend, input, textarea, p, blockquote, th, td {
    margin: 0;
    padding: 0;
}

table {
    border-collapse: collapse;
    border-spacing: 0;
}

fieldset, img {
    border: 0;
}

address, caption, cite, code, dfn, em, strong, th, var {
    font-style: normal;
    font-weight: normal;
}

ol, ul {
    list-style: none;
}

caption, th {
    text-align: left;
}

h1, h2, h3, h4, h5, h6 {
    font-size: 100%;
    font-weight: normal;
}

q:before, q:after {
    content: '';
}

abbr, acronym {
    border: 0;
    font-variant: normal;
}

sup {
    vertical-align: text-top;
}

sub {
    vertical-align:text-bottom;
}

button, input, textarea, select {
    font-family: inherit;
    font-size: inherit;
    font-weight: inherit;
    outline: none;
    resize: none;
}

img {
    vertical-align: middle;
}

/* Enable image placeholders */
@-moz-document url-prefix(http), url-prefix(file) {
    img:-moz-broken {
        -moz-force-broken-image-icon:1;
    }
}

input::-webkit-input-placeholder,
textarea::-webkit-input-placeholder {
   color: $font-lighter-more !important;
}

input:-webkit-autofill,
textarea:-webkit-autofill,
select:-webkit-autofill {
    -webkit-box-shadow: 0 0 0px 1000px white inset;
}

input::-moz-placeholder,
textarea::-moz-placeholder {  /* Firefox 19+ */
   color: $font-lighter-more !important;
}

input:-ms-input-placeholder,
textarea:-ms-input-placeholder {
    color: $font-lighter-more !important;
}

input::-ms-clear,
input::-ms-reveal {
    display: none;
}

textarea {
    overflow: auto;  /* IE10 */
}

div:focus {
    outline: none;
}

.iconfont {
    font-size: $font-size-normal;
    display: inline-block;
}

.hidden {
    display: none;
}

.checkbox-inner {
    width: 12px;
    height: 12px;
    position: relative;
    display: inline-block;
    border: 1px solid $font-lightest;
    background-color: $white-color;
    box-sizing: border-box;
    cursor: pointer;
    top: 0;
    left: 0;
    vertical-align: middle;
    line-height: 1;

    &:after {
        @include transition(all .2s cubic-bezier(.12,.4,.29,1.46) .1s);
        @include transform(rotate(45deg) scale(0));
        position: absolute;
        left: 3px;
        top: 0;
        display: table;
        width: 3px;
        height: 6px;
        border: 0.053rem solid $primary-color;
        border-top: 0;
        border-left: 0;
        content: '';
    }

    &.chk-checked {
        &:after {
            @include transform(rotate(45deg) scale(1));
        }
    }

    &.disabled {
        background-color: $font-lightest;
        border-color: $font-lightest;

        &:before {
            background-color: $font-lightest;
        }

        &:after {
            border-color: $white-color;
        }

        &:hover {
            border-color: $font-lightest;
        }
    }

    &:hover {
        border-color: $primary-color;
    }

    + input[type=checkbox] {
        display: none
    }
}

.radiobox-inner {
    @include radiobox(1);
}

.checkbox-inner {
    @include checkbox(1);
}

.btn {
    border: solid 0.026667rem;
    border-radius: 0.106667rem;
    box-sizing: border-box;
    cursor: pointer;
    display: inline-block;
    font-size: $font-size-big;
    height: 1.173333rem;
    line-height: 1.173333rem;
    margin: 0 auto;

    &:disabled {
        opacity: .4;
    }
}

.btn-default {
    background-color: $white-color;
    border-color: $border-input;
    color: $font-base;
}

.btn-primary {
    background-color: $primary-color;
    border-color: $primary-color;
    color: $white-color;
    text-align: center;
    width: 100%;
}

.btn-danger {
    background-color: $error-color;
    border: 1px solid $error-color;
    box-shadow: 0 4px 6px rgba($error-color, .4);
    color: $white-color;

    .icon-state-success {
        color: $white-color;
    }

    &:hover,
    &:active,
    &.btn-disabled {
        background-color: $error-hover-color;
    }

    &:active {
        box-shadow: inset 0 2px 4px 0 rgba($black-color, .1);
    }

    &.btn-disabled {
        box-shadow: none;
        color: rgba($white-color, .2);
    }
}

/***global-blocker****/
.global-blocker {
    height: 100%;
    left: 0;
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 9999;
    background: rgba(255, 255, 255, 0);
}

.wrapper {
    @include flex-column;
    @include flex-space-between;
    min-height: 11.2rem;
    padding: 0 0.533333rem;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    height: 100%;

    &.mobile-register {
        padding: 0;

        .register-box {
            background-image: url('https://p3.ssl.qhimg.com/d/inn/ae9704c60556/register_bg_2a90102.png');
            background-repeat: no-repeat;
            background-size: 100% auto;
            padding-top: 0.716809rem;

            .register_top {
                width: 9.360117rem;
                margin: 0 auto;

                img {
                    width: 100%;
                }
            }

            .form {
                display: flex;
                flex: none;
                width: 9.146781rem;
                background: #FFEEE4;
                margin: auto;
                margin-top: 0.53334rem;
                border-radius: 0.160002rem;
                padding: 0.640008rem 0.426672rem 0;
                box-sizing: border-box;

                > p {
                    font-size: 0.426672rem;
                    line-height: 0.640008rem;
                    color: #1B2532;
                    font-weight: 500;
                }

                .form-group {
                    height: 1.173348rem;
                    .input-group {
                        height: 100%;
                        border-bottom: none;
                        input, select {
                            height: 100%;
                            padding-left: 0.320004rem;
                            border-radius: 0.106668rem;
                            font-size: 0.373338rem;
                            box-sizing: border-box;
                        }

                        select {
                            color: #1B2532;
                        }

                        .text {
                            bottom: 0;
                        }

                        &.sms-group {
                            display: flex;
                            input {
                                border-radius: 0.106668rem 0 0 0.106668rem;
                            }

                            .get-code {
                                display: flex;
                                height: 100%;
                                align-items: center;
                                padding-right: 0.320004rem;
                                padding-left: 0.320004rem;
                                text-align: right;
                                background: #fff;
                                border-radius: 0 0.106668rem 0.106668rem 0;
                                color: #017FFD;
                                font-size: 0.373338rem;
                                position: relative;

                                &[get-disabled] {
                                    color: #B0BBCA;
                                }

                                &:after {
                                    content: '';
                                    position: absolute;
                                    left: 0;
                                    top: 50%;
                                    transform: translateY(-50%);
                                    height: 35%;
                                    width: 1px;
                                    background: #D9D9D9;
                                }
                            }
                        }
                    }

                    .select-group {
                        &:after {
                            top: 50%;
                            right: 0.320004rem;
                            transform: translateY(-50%);
                        }
                    }

                    &.phone {
                        height: 1.226682rem;
                        .input-group {
                            border: 0.026667rem solid #FFEEE4;
                        }

                    }

                    &.password {

                        .input-group {
                            > input {
                                border-top-right-radius: 0;
                                border-bottom-right-radius: 0;
                            }

                            >.toggle-pwd {
                                background: #fff;
                            }
                        }
                    }

                    &.error {
                        .input-group {
                            border: 0.026667rem solid #f55;
                        }
                    }

                    &.action-group {
                        margin-top: 0.7rem;
                        height: auto;

                        .btn {
                            background: linear-gradient(90deg, #EA5944 0%, #E6335A 100%);
                            border-radius: 0.826677rem;
                            border: none;
                            font-size: 0.426672rem;
                            font-weight: bold;
                        }
                    }

                    &.fangcloud-protocol {
                        margin-top: 0.273333rem;
                        
                        label {
                            line-height: 0.53334rem;
                            height: 0.53334rem;
                            display: flex;
                            align-items: center;
                            justify-content: center;

                            .checkbox-inner {
                                // top: 0.080001rem;
                                top: 0;
                            }
                        }
                        .clause {
                            font-size: 0.320004rem;
                            line-height: 0.53334rem;
                            color: #637387;
                        }
                    }

                    span.error {
                        margin-top: 0.16rem;
                    }
                }

                .pic-captcha {
                    img {
                        right: 0.320004rem;
                        top: 50%;
                        transform: translateY(-50%);

                    }
                }
            }

            .show_img {
                background-image: url(../images/register-27.png);
                background-image: url(../images/register-28.png);
                background-image: url(../images/register-29.png);
                background-image: url(../images/register-other.png);
                background-image: url(../images/register-top-other.png);
                background-image: url(../images/register-top-29.png);
                background-image: url(../images/register-top-28.png);
                background-image: url(../images/register-top-27.png);
                background-image: url(../images/register_success.png);
                background-image: url(../images/register_tips.png);
            }

            .register_bottom {
                width: 9.173448rem;
                margin: 1.06668rem auto;
                img {
                    width: 100%;
                }
            }
        }

        .register_success {
            display: none;
            position: fixed;
            height: 100%;
            width: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1;

            img {
                width: 2.13336rem;
                height: 1.706688rem;
                position: absolute;
                top: -0.746676rem;
                left: 50%;
                transform: translateX(-50%);
            }

            > div {
                display: flex;
                flex-direction: column;
                justify-content: space-between;
                align-items: center;
                width: 7.866765rem;
                height: 4.853394rem;
                padding: 1.440018rem 1.06668rem 0.426672rem;
                border-radius: 0.213336rem;
                border: 0.026667rem solid #F5ECEB;
                background: #F5ECEB;
                position: fixed;
                margin: auto;
                left: 0;
                right: 0;
                top: 0;
                bottom: 0;
                box-sizing: border-box;

                p {
                    font-size: 0.373338rem;
                    color: #1B2532;
                    text-align: center;
                }

                span {
                    padding: 0.186669rem 0.80001rem;
                    background: #E73B56;
                    border-radius: 0.106668rem;
                    font-size: 0.373338rem;
                    color: #FFFFFF;
                }

                .register_close_2 {
                    width: 0.400005rem;
                    height: 0.400005rem;
                    position: absolute;
                    top: 0.453339rem;
                    right: 0.453339rem;

                    &::after, &::before {
                        content: '';
                        display: block;
                        width: 0.400005rem;
                        height: 0.0400005rem;
                        background: #747B93;
                        position: absolute;
                        top: 50%;
                        left: 50%;
                        transform: rotateZ(45deg);
                    }

                    &::before {
                        transform: rotateZ(-45deg);
                    }
                }
            }

            .register_success_tips {
                display: flex;
                align-items: center;
                font-size: 0.320004rem;
                line-height: normal;
                position: relative;
                top: -0.320004rem;

                img {
                    width: 0.426672rem;
                    height: 0.426672rem;
                    position: initial;
                    transform: none;
                    margin-right: 0.106668rem;
                }
            }

            &.show {
                display: block;
            }
        }
    }

    .padding-left_right {
        padding: 0 0.533333rem;
        height: 100%;
    }
}

.logo-large {
    height: 3.733333rem;
    line-height: 3.733333rem;
    padding-bottom: 0.533333rem;
    text-align: center;

    &.register-logo {
        height:3.093333rem;
        line-height:3.093333rem;
        margin-bottom: -0.533333rem;
    }

    .default-logo {
        display: inline-block;
        height: 1.12rem;
        width: 4.4rem;
        vertical-align: middle;
        background-image: url(../images/logo.png);
        background-repeat: no-repeat;
        background-size: contain;
    }

    img {
        height: 1.12rem;
    }
}

.login-box,
.register-box,
.forgot-box,
.activation-box,
.failure-state,
.sso-entry-box {
    @include flex-column;
    @include flex(1);
}

.register-confirm {
    display: none;
}


[data-dpr="1"] {
    .logos-header {
        font-size: 16px;
    }

    .bind-register {
        font-size: 14px;
    }
}


[data-dpr="2"] {
    .logos-header {
        font-size: 32px;
    }

    .bind-register {
        font-size: 28px;
    }
}

[data-dpr="3"] {
    .logos-header {
        font-size: 48px;
    }

    .bind-register {
        font-size: 42px;
    }
}

.form {
    @include flex-column;
    @include flex(1);

    .step-2,
    .step-3 {
        display: none;
    }

    .form-group {
        margin-top: 0.533333rem;
        position: relative;

        &.action-group {
            margin-top: 0.8rem;

            span.error {
                text-align: center;
                width: 100%;
            }
        }

        &.error {
            .input-group {
                border-color: $error-color;
            }
        }

    }

    .text-label {
        display: block;
        font-size: $font-size-big;
        height: 0.666667rem;
        line-height: 0.93;
        margin-bottom: 0.8rem;
    }

    .radio {
        font-size: $font-size-normal;
        line-height: 0.586667rem;
        margin-bottom: 0.533333rem;
        position: relative;

        input {
            height: $font-size-big;
            width: $font-size-big;
            margin-right: 0.026667rem;
            margin-top: -0.133333rem;
            position: relative;
            top: 0.08rem;
        }

        .hint {
            color: $font-color-breadcrumb;
            display: block;
            font-size: $font-size-small;
            margin-left: 0.693333rem;
        }
    }

    .input-group {
        @include transition(border-color .5s ease-in-out 0s);
        border-bottom: 1px solid $border-input;
        border-collapse: separate;
        box-sizing: border-box;
        display: inline-table;
        position: relative;
        vertical-align: middle;
        width: 100%;

        &.active {
            border-color: $primary-color;
        }

        select {
            -webkit-appearance: none;
            background: $white-color;
            border: 0;
            font-size: $font-size-normal;
            line-height: 0.586667rem;
            padding: 0.08rem 0;
            width: 100%;
        }

    }

    .select-group {
        &:after {
            content: '\e7e7';
            color: $font-base;
            font-family: "iconfont";
            font-size: 0.32rem;
            line-height: 0.586667rem;
            padding: 0.08rem 0;
            position: absolute;
            right: 0;
        }
    }

    .input-group-addon {
        line-height: 1;
        text-align: center;
        vertical-align: middle;
        white-space: nowrap;
        width: auto;
    }

    .input-group .form-control,
    .input-group-addon {
        display: table-cell;
    }

    .select-group-content {
        .select {
            display: inline-block;
        }
    }

    .text {
        border: 0;
        font-size: $font-size-normal;
        line-height: 0.586667rem;
        padding: 0.08rem 0;
        width: 100%;
        position: relative;
        bottom: 1px;
    }

    .clear-text,
    .toggle-pwd {
        display: none;
    }

    .get-code {
        color: $primary-color;
        font-size: $font-size-normal;

        &[get-disabled] {
            color: $font-placeholder;
            cursor: default;
        }
    }

    .voice-captcha {
        float: right;

        a {
            color: $font-base;
        }
    }


    .clear-text,
    .toggle-pwd {
        color: $border-input;
        cursor: pointer;
    }

    .error,
    .hint {
        color: $font-darker;
        font-size: 0.32rem;
        line-height: 0.453333rem;

    }

    span.error {
        color: $error-color;
        display: inline-block;
        margin-top: 0.16rem;

        .icon-error {
            font-size: 0.32rem;
        }
    }

    .hint {
        color: $font-color-breadcrumb;
        display: inline-block;
        margin-top: 0.16rem;

        &.hidden {
            display: none;
        }
    }

    .pic-captcha {
        input {
            box-sizing: border-box;
            margin-right: 0.533333rem;
            padding-right: 2.666667rem;
        }

        img {
            position: absolute;
            right: 0;
            top: 0;
            width: 2.133333rem;
        }
    }

    .sms-captcha {
        .get-code {
            bottom: 0.08rem;
            color: $primary-color;
            font-size: $font-size-normal;
            position: absolute;
            right: 0;
        }

        .clear-text {
            right: 2.4rem;
        }
    }

    .next-step {
        margin-top: 1.573333rem;
    }

    .register-purpose {

        .label {
            font-size: $font-size-small;
            color: $font-base;
            display: block;
            margin-bottom: 0.24rem;
        }

        .radiobox {
            font-size: $font-size-normal;

            .radiobox-inner {
                margin-right: 0.133333rem;
            }
            & + .radiobox {
                float: right;
            }
            // display: block;
        }

        &.error {
            .label {
                color: $error-color;
            }
        }
    }

    .fangcloud-protocol {
        @include display-flex;
        color: #a3a7bf;
        font-size: $font-size-small;
        line-height: 1.57;
        margin-top: 0.373333rem;
        margin-bottom: 0.373333rem;
        text-indent: 0.133333rem;
        align-items: center;

        .checkbox-inner {
            margin-right: 0.133333rem;
        }
    }

    .activate-account {
        @include display-flex;
        @include flex-space-between;

        a {
            color: $font-darker;
            font-size: $font-size-small;
        }
    }


    .bind-register {
        margin-top: 40px;
        text-align: center;
    }

    .form-footer {
        @include display-flex;
        @include flex(1);
        @include flex-align-end;
        margin-bottom: 0.666667rem;

        .other-login {
            @include display-flex;
            @include align-content-start;

            .prompt-title {
                @include flex-shrink-value(0);
                @include display-inline-flex;
                @include flex-align-center;

                text-align: center;
                margin-right: 5px;
            }

            .other-login-content {
                @include display-flex;
                @include align-content-start;
                @include flex-wrap;
                @include flex-grow-value(1);
            }
        }

        a {
            @include flex-grow;
            margin-left: 16px;
            color: $font-darker
        }

        svg {
            display: none;
        }

        i {
            display: none;
        }
    }

    .inline {
        &:first-child {
            order: 1;
        }

        .label {
            color: $font-color-breadcrumb;
            display: inline-block;
        }

        .form-control {
            display: inline-block;

            a {
                color: $font-darker;
                display: inline-block;
                padding-right: 0.266667rem;
            }
        }
    }
}

.forgot-done {
    margin: 0 auto;
    text-align: center;
    width: 8.96rem;

    .reset-success {
        background-image: url('../images/<EMAIL>');
        background-position: center center;
        background-repeat: no-repeat;
        background-size: 100%;
        height: 4.266667rem;
        margin: 0 auto;
        width: 3.2rem;
    }

    p {
        font-size: $font-size-normal;
        line-height: 1.5;
        margin-bottom: 0.266667rem;
    }

    .hint {
        color: $font-base;
        font-size: $font-size-small;
        line-height: 0.533333rem;
    }

    .btn {
        margin: 0.8rem auto;
        padding: 0 0.24rem;
    }
}

.main-container {
    text-align: center;

    &.download-notice {
        h3 {
            margin-top: 3.2rem;
        }

        .btn {
            margin-top: 0.533333rem;
            width: 100%;
        }
    }

    h3 {
        color: $font-darker;
        font-size: $font-size-normal;
        margin: 3.546667rem auto 0.533333rem;
    }

    .iconfont {
        color: #dce2eb;
        font-size: 25vw;
        text-align: center;
        margin-bottom: 0.8rem;
    }

    .cancel {
        color: $font-darker;
        display: block;
        font-size: $font-size-normal;
        text-align: center;
    }

    .expire-box {
        display: none;

        &.show {
            display: block;
        }
    }

    .warning {
        color: $error-color;
        font-size: $font-size-normal;
    }

    .re-scan {
        border-color: $border-color;
        color: $font-darker;
        margin-top: 1.066667rem;
        padding: 0 0.746667rem;
    }

    .app-icon {
        background-image: url('../images/<EMAIL>');
        background-position: center center;
        background-repeat: no-repeat;
        background-size: 100%;
        height: 3.2rem;
        margin: 0 auto;
        width: 3.2rem;
    }

    .download-tip {
        color: $font-base;
        margin-top: 0.8rem;

        span {
            color: $font-darker;
        }
    }
}

.verify-form {
    .desc {
        font-size: 0.373333rem;
    }
}

// dialog
.alert-wrap,
.confirm-wrap {
    min-width: 8.0rem;
    border-right: 0.16rem;

    .confirm-body {
        font-size: 0.426667rem;

        .message {
            padding-bottom: 0.266667rem;

            span {
                font-size: .36rem;
            }
        }

    }

    .confirm-actions {
        .btn {
            line-height: 0.96rem;
            height: 0.96rem;
            width: auto;
        }
    }
}

.alert-wrap {
    .alert-content {
        padding: 0.8rem 0.533333rem 0.533333rem;

        .alert-body {
            font-size: $font-size-small;
            min-height: 1.066667rem;
            padding: 0 0 0.4rem;

            .message {
                span {
                    font-size: $font-size-small;
                }
            }
        }
    }
}

.alert-actions,
.confirm-actions {
    height: 0.96rem;
    margin-top: 0.533333rem;

    .btn {
        height: 0.96rem;
        line-height: 0.96rem;
        margin-left: 0.133333rem;
        padding: 0 0.266667rem;
    }
}



// 失败页面

.failure-state {
    @include flex-center;
    text-align: center;


    .icon-fail {
        font-size: 1.92rem;
        color: $warning-color;

        + .error-title {
            color: $font-darker;
        }
    }

    .error-icon {
        background-image: url('../images/<EMAIL>');
        background-position: center center;
        background-repeat: no-repeat;
        background-size: 100%;
        height: 3.2rem;
        margin: 0 auto;
        width: 3.2rem;
    }

    .error-title {
        color: $warning-color;
        font-size: 0.373333rem;
    }

    .error-message {
        color: $font-base;
    }

}

.not-completed {
    font-size: 0.48rem;
    line-height: 100%;
    text-align: center;
}

// 1px边框 兼容安卓
[data-dpr="1"] {
    .input-group {
        border-bottom: 2px solid $border-input;
    }
}

[data-dpr="2"] {
    .input-group {
        border-bottom: 2px solid $border-input;
    }

    .radiobox-inner {
        @include radiobox(40 / 13);
    }

    .checkbox-inner {
        @include checkbox(28 / 13);
    }
}

[data-dpr="3"] {
    .input-group {
        border-bottom: 3px solid $border-input;
    }

    .radiobox-inner {
        @include radiobox(60 / 13);
    }

    .checkbox-inner {
        @include checkbox(42 / 13);
    }
}

.select-group.enterprise-size {
    font-size: 0.42667rem;
    line-height: 0.586667rem;
    padding: 0.08rem 0;

    i {
        display: none;
    }

    &::after {
        top: 0;
    }

    .select-group-options {
        background-color: $white-color;
        border: 1px solid #d0d0d0;
        box-sizing: border-box;
        display: none;
        left: -1px;
        overflow: hidden;
        padding: 0.05rem 0;
        position: absolute;
        top: .75rem;
        width: 100%;
        z-index: 99;

        ul {
            max-height: 240px;
            overflow: auto;
        }

        li {
            cursor: pointer;
            font-size: 0.42667rem;
            line-height: 0.7rem;
            padding: 0 0.12rem;
            text-align: left;

            &:hover {
                background-color: $item-selected;
            }

            &.selected {
                background-color: $item-selected;
            }

            .country-code {
                float: right;
                text-align: right;
            }
        }
    }
}


.i18n-en {
    .fangcloud-protocol {
        font-size: 0.32rem;
    }

    .register-purpose {
        .radiobox {
            display: block;
            margin-top: 0.346667rem;
            & + .radiobox {
                float: none;
            }
        }
    }

    .form {
        .form-footer {

            .other-login-container {

                .other-login {

                    .prompt-title {
                        @include flex-align-start;
                    }
                }
            }
        }
    }
}


// validate.scss
div.wrapper {
    min-width: 0;
}

.validate-done {
    margin-top: 1.3333rem;
}

.vdcw-left {
    display: none;
}

.vdcw-tip {
    display: flex;
    flex-wrap: nowrap;
    padding-bottom: 0.42667rem;
    justify-content: center;
}

.vdcw-tip .icon-success{
    width: 0.85333rem;
    height: 0.85333rem;
}

.vdcw-tip .vdcw-welcome-msg {
    font-size: 0.5333rem;
    color: #252e36;
    line-height: 0.85333rem;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    width: 0;
    flex-grow: 1;
    padding-left: 0.32rem;
}

.vd-content-wp {
    padding: 0 0.8rem;
}

.vdcw-feature-list {
    display: flex;
    margin-bottom: 1.6rem;
    flex-wrap: wrap;
    padding: 0 0.42rem;
    font-size: 0.32rem;
    color: #017ffd;
    line-height: 0.85333rem;
    justify-content: center;
}

.vdcw-feature-list .vdcwf-item {
    position: relative;
    box-sizing: border-box;
    text-align: left;
    width: 50%;
    color: #747b93;
    padding-left: 0.26667rem;
    height: 0.64rem;
    line-height: 0.64rem;
    width: 3.176rem;
}

.vdcw-feature-list .vdcwf-item::before {
    margin-right: 0.1333rem;
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translatey(-50%);
    display: inline-block;
    width: 0;
    height: 0;
    border: 2px solid #017FFD;
    border-radius: 2px;

}

.vdcw-pc {
    display: none;
}

.vdcw-download-item {
    display: flex;
    margin-bottom: 1.7rem;
    justify-content: center;
}

.vdcw-download-icon {
    width: 1.06667rem;
    height: 1.06667rem;
}

.vdcw-download-main {
    width: 5.7rem;
    padding-left: 0.426667rem;
    // flex-grow: 1;
    text-align: left;
}

.vdcw-tip1 {
    font-size: 0.42667rem;
    color: #252e36;
    line-height: 0.64rem;
    margin-bottom: 0.053rem;
}

.vdcw-tip2 {
    margin-bottom: 0.16rem;
    font-size: 0.34667rem;
    color: #747b93;
    line-height: 0.58667rem;
}

.vdcw-tip3 {
    width: 100%;
    height: 0.58667rem;
    display: block;
    margin-bottom: 0.29333rem;
    font-size: 0.29333rem;
    color: #252e36;
    line-height: 0.58667rem;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    border: none;
    background-color: #fff;
    opacity: 1;
}

.vdcw-download-main .vdcw-download-btn {
    display: inline-block;
    margin-top: 0.16rem;
    width: 2.4rem;
    height: 0.8533rem;
    line-height: 0.8533rem;
    background: #017ffd;
    border-radius: 0.08rem;
    color: #fff;
    text-align: center;
    font-size: 0.37333rem;
}

.vdcw-download-main .vdcw-copy {
    color: #017ffd;
    font-size: 0.37333rem;
}


// 激活
.aiw-content {
    box-sizing: border-box;
    padding: 0.64rem 0.5333rem;
    background: #f9f9fa;
    margin-bottom: 0.8533rem;
}

.aiw-content .aiwc-guide-img {
    display: none;
}

.aiw-content .aiwc-text.at-pc {
    display: none;
}

.aiw-content .aiwc-text {
    font-size: 0.4266rem;
}

.aiw-content .aiwc-text-sub {
    font-size: 0.37333rem;
    color: #747b93;
    line-height: 0.5866rem;
}

.user-profile {
    display: flex;
    align-items: center;
    font-size: 0.32rem;
    margin-bottom: 0.16rem;
}

.up-photo {
    display: inline-block;
    margin-right: 0.32rem;
    width: 0.64rem;
    height: 0.64rem;
    border-radius: 0.64rem;
}

.up-admin {
    display: inline-block;
    margin-left: 0.32rem;
}

.aiwc-text {
    margin-bottom: 0.16rem;
}

.aiwc-text-main {
    margin-bottom: 0.2666rem;
    color: #252e36;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 0.4266rem;
    max-width: 100%;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    font-weight: bold;
}

.fullfill-account {
    box-sizing: border-box;
    padding: 0 0.5333rem;
}

.user-info {
    font-size: 0.3733rem;
    margin-bottom: 0.9777rem;
}

.activation-help {
    margin-top: 1.9466rem;
    font-size: 0.37333rem;
    text-align: center;
    line-height: 0.58667rem;
}

.activation-help.ah-pc {
    display: none;
}

.toast {
    font-size: 0.37333rem;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 10001;
}

.toast-notice {
    width: auto;
    height: 0;
    text-align: center;
    vertical-align: middle;
    animation: notifyIn .2s ease;
    -webkit-animation: notifyIn .2s ease;
}

.toast-notice-content {
    background: $toast-bg;
    border-radius: 4px;
    box-sizing: border-box;
    color: $white-color;
    display: inline-block;
    font-size: 0.32rem;
    margin-top: 0.267rem;
    padding: 0.187rem 0.53rem;
    max-width: 13rem;
    min-width: 5rem;
    text-align: center;
    animation: notifyOut .3s linear 2.2s both;
    -webkit-animation: notifyOut .3s linear 2.2s both;

    .toast-delay & {
        text-align: left;
        animation: notifyOut .3s linear 4.7s both;
        -webkit-animation: notifyOut .3s linear 4.7s both;
    }

    .no-animation & {
        text-align: left;
        animation: none;
        -webkit-animation: none;
    }

    .content {
        float: left;
    }

    .action {
        color: $toast-action;
        cursor: pointer;
        float: right;
    }
}

.active-info-wp {
    .user-avatar {
        color: rgb(255, 255, 255);
        background-color: rgb(114, 220, 162);
        user-select: none;
        display: inline-block;
        text-align: center;
        vertical-align: middle;
        line-height: 0.853rem;
        font-size: 0.427rem;
        border-radius: 50%;
        height: 0.853rem;
        width: 0.853rem;
    }
}


.vdcw-tip3-wp {
    display: inline-block;

    .span-helper {
        height: 0px;
        display: inline-block;
        opacity: 0;
        pointer-events: none;
    }

    .vdcw-tip3:disabled {
        color: #252e36;
        opacity: 1;
    }
}