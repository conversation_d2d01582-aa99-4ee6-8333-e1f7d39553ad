@import 'utils/variables';
@import 'utils/mixins';

$dialog-header-bg: #f8fafd;
$active-color: #dbedff;
$collab-role-bg: #eeefef;
$toast-bg: #5d666b;
$toast-action: #8dccff;
$dialog-title-color: #0c0f11;
$dialog-sub-color: #56585a;
$overlay-color: #62666a;
$dialog-border-color: $border-input;
$message-color: #33bc5a;
$dialog-text-highlight: #fc9a46;
$qr-border-color: #f6f7f9;

.overlay {
    background-color: $overlay-color;
    opacity: .95;
}

.dialog-wrap {
    background-color: $white-color;
    border-radius: 2px;
    margin: 0 auto;
    overflow: visible;
    position: relative;
    width: 480px;
    box-shadow: 0 2px 6px 0 rgba(118, 118, 118, .4);

    .dialog-header {
        background-color: $dialog-header-bg;
        border-bottom: 1px solid $border-dark;
        border-radius: 2px 2px 0 0;
        height: 40px;

        .dialog-title {
            @include pre-ellipsis;
            color: $dialog-title-color;
            font-size: 16px;
            line-height: 40px;
            margin: 0 50px;
            text-align: center;

            span {
                color: $dialog-sub-color;
            }
        }

        span {
            font-size: 14px;
        }
    }

    .dialog-close-x {
        @include icon-font;
        cursor: pointer;
        width: 16px;
        height: 16px;
        font-size: 0;
        overflow: hidden;
        position: absolute;
        right: 10px;
        top: 12px;
        line-height: 16px;

        &:before {
            content: "\e632";
            color: $font-lighter;
            font-size: 16px;
        }
    }

    .dialog-body {
        padding: 24px;
        box-sizing: border-box;
        font-size: 14px;
    }


    // .form {
    //     bottom: 66px;
    //     left: 0;
    //     margin: auto;
    //     position: absolute;
    //     right: 0;
    //     top: 40px;
    // }

    .show-country-select {
        .select-group-options {
            top: 31px;
        }
    }
}

.dialog-wrap,
.alert-wrap,
.confirm-wrap {
    .btn {
        margin-left: 5px;
        vertical-align: middle;
    }
}

.dialog-hidden {
    display: none;
}

.dialog-actions,
.confirm-actions {
    border-top-style: none;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
    text-align: right;

    .btn {
        vertical-align: top;
        padding: 0 26px;
    }
}

.alert-actions {
    border-top-style: none;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
    text-align: right;

    .btn {
        vertical-align: top;
        padding: 0 26px;
    }
}

.confirm-actions {
    margin: 0;

    .btn {
        padding: 0 20px;
    }
}

//confirm box
.alert-wrap,
.confirm-wrap {
    position: relative;
    margin: 0 auto;
    opacity: 1;
    z-index: 1000;
    max-width: 300px;
    border-radius: 2px;
    background-color: $white-color;
    box-shadow: 0 3px 8px 0 rgba(0, 0, 0, .19);

    .message-main {
        margin-bottom: 7px;
    }
}

.confirm-wrap {
    .confirm-content {
        padding: 24px;
    }

    .confirm-body {
        font-size: 14px;
        color: $font-darker;
        word-wrap: break-word;

        .message {
            padding-bottom: 30px;

            span {
                color: $font-base;
                font-size: 12px;
            }
        }
    }

    .confirm-actions {
        text-align: right;
    }
}

.alert-wrap {
    .alert-content {
        padding: 24px;

        .alert-body {
            font-size: 14px;
            color: $font-darker;
            min-height: 40px;

            span {
                color: $font-base;
                font-size: 12px;
                line-height: 20px;
            }

            .alert-actions {
                margin-top: 16px;
            }
        }
    }
}

.confirm-primary {
    .message {
        text-align: left;
    }

    .message-main {
        text-align: center;
    }
}

