@import 'utils/variables';
@import 'utils/mixins';
@import 'iconfont';
@import 'newIconfont';
@import 'base';


html,
body,
.wrapper,
.quick-register {
    width: 100%;
    height: 100%;
    background-color: transparent;
}

.sem-wrapper .quick-register {
    min-height: 0;
}

.quick-register {
    position: relative;
    min-height: 630px;

    .demo {
        background: url(../images/quick-register-13-bg-tl.png);
        background: url(../images/quick-register-15-bg-tl.png);
        background: url(../images/quick-register-16-bg-tl.png);
        background: url(../images/quick-register-bg-tc.png);
        background: url(../images/quick-register-bg-tr.png);
        background: url(../images/quick-register-bg-cl.png);
        background: url(../images/quick-register-13-bg-bl.png);
        background: url(../images/quick-register-15-bg-bl.png);
        background: url(../images/quick-register-16-bg-bl.png);
    }

    // .product-bg {
    //     @include display-flex;
    //     @include flex-column;

    //     width: 100%;
    //     height: 100%;
    //     background-color: #fff;

    //     .top,
    //     .bottom {
    //         @include display-flex;
    //         @include flex-shrink;
    //     }

    //     .top {
    //         height: 388px;

    //         .center {
    //             width: 296px;
    //         }
    //     }

    //     .bottom {
    //         height: 190px;
    //     }

    //     .right {
    //         width: 378px;
    //     }

    //     .left {
    //         width: 506px;
    //     }

    //     .center {
    //         @include flex-grow;

    //         img {
    //             height: 100%;
    //         }
    //     }

    //     div.center {
    //         height: 0;
    //     }
    // }
    ul {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .product-bg {
        width: 100%;
        height: 100%;
        background-color: #fff;
        position: relative;
        height: 100%;
        min-width: 1180px;
        min-height: 600px;
        overflow: hidden;
        @include display-flex;
        @include flex-column;
    }
    .header {
        @include display-flex;
        @include flex-align-center;
        background-color: #fff;
        box-sizing: border-box;
        height: 50px;
        border-bottom: 1px solid #e6e7ea;
    }
    .logo {
        @include display-inline-flex;
        @include flex-align-center;
        height: 100%;
        width: 280px;
        color: #017ffd;
        margin-left: 20px;
        line-height: 44px;
    }
    .search {
        margin: 0 auto 0 28px;
        text-align: left;
        width: 480px;
        position: relative;
        align-items: center;
        display: flex;
    }

    .search .new-iconfont {
        width: 24px;
        font-size: 24px;
        color: #999;
    }

    .search span {
        width: 400px;
        height: 36px;
        line-height: 36px;
        padding: 0 12px;
        font-size: 14px;
        color: #5c637c;
    }

    .header-right {
        display: flex;
        align-items: center;
        line-height: 1;

        i {
            font-size: 24px;
            margin-right: 20px;
            color: #999;
        }

        span {
            margin-right: 20px;
            color: #017ffd;
        }

        img {
            width: 24px;
            height: 24px;
            margin-right: 20px;
        }
    }

    .user {
        height: 24px;
        width: 24px;
        border-radius: 50%;
        background-color: #f0f0f0;
        align-self: stretch;
        margin-right: 20px;
    }

    .content-wrapper {
        @include display-flex;
        @include flex-grow;
        min-height: 0;
        overflow: hidden;
        position: relative;
        height: auto;
        min-width: 1180px;
    }
    .first-nav {
        background-color: #f9fafb;
        width: 64px;
        padding-top: 24px;
        border-right: 1px solid #eeeff5;
        text-align: center;
        position: relative;
    }
    .first-nav .nav-icon {
        width: 28px;
        height: 28px;
        box-shadow: 0 6px 8px 0 rgba(56,128,253, .24);
        border-radius: 50%;
        margin-bottom: 28px;
    }
    .first-nav .nav-item {
        @include display-flex;
        @include flex-column;
        display: block;
        margin-bottom: 28px;
        color: #747b93;
        width: 100%;
    }
    .first-nav .nav-item .icon-image {
        display: inline-block;
        width: 36px;
        height: 36px;
    }
    .first-nav .nav-item i {
        font-size: 24px;
        display: block;
    }
    .first-nav .nav-item .nav-text {
        margin-top: 2px;
        display: block;
        font-size: 12px;
        line-height: 20px;
    }
    .first-nav .nav-item.active {
        color: #017ffd;
    }
    .first-nav .nav-bottom {
        position: absolute;
        bottom: 0;
        @include display-flex;
        @include flex-column;
        display: block;
        text-align: center;
        width: 100%;
    }
    .second-nav {
        background: #fff;
        @include display-flex;
        @include flex-column;
        position: relative;
        background-color: #fff;
        border-right: 1px solid #ededed;
        width: 236px;
        box-sizing: border-box;
        padding: 16px 0 0;
        font-size: 12px;
    }
    .second-nav .second-nav-header {
        position: relative;
        color: #676e87;
        padding-left: 20px;
        line-height: 32px;
        @include display-flex;
        @include flex-space-between;
    }
    .second-nav .second-nav-header i {
        font-size: 24px;
        color: #676e87;
        margin-right: 8px;
    }

    .second-nav .second-list ul {
        padding: 0 12px;
    }

    .second-nav .second-list li {
        position: relative;
        height: 40px;
        line-height: 40px;
        @include display-flex;
        @include flex-align-center;
        padding: 0 10px 0 20px;
    }

    .second-nav .second-list li.active {
        background-color: #ebf4ff;
    }

    .second-nav .second-list li i {
        font-size: 16px;
        margin-right: 10px;
        color: #676e87;
    }
    .second-nav .second-list li .type-icon {
        width: 16px;
        height: 16px;
        margin-right: 10px;
    }
    .second-nav .second-list li span {
        color: #252e36;
    }
    .second-nav .second-list li i.icon-arrow_right {
        font-size: 12px;
        color: #747b93;
        position: absolute;
        left: 10px;
        transform: scale(0.7);
    }
    .main-content-wrapper  {
        @include display-flex;
        @include flex-column;
        @include flex-grow;
        width: 0;
        position: relative;
    }
    .main-wrapper {
        @include display-flex;
        @include flex-column;
        @include flex-grow;
        position: relative;
        height: 0;
    }
    .file-list-wrapper {
        @include display-flex;
        @include flex-row;
        @include flex-grow;
        min-height: 0;
        position: relative;
    }
    .file-list-wrapper .file-list {
        @include display-flex;
        @include flex-column;
        @include flex-grow;
        position: relative;
        min-height: 0;
    }
    .file-list-wrapper .file-side {
        width: 52px;
        -moz-box-shadow: inset 1px 0 0 #eeeff5;
        -webkit-box-shadow: inset 1px 0 0 #eeeff5;
        box-shadow: inset 1px 0 0 #eeeff5;
        background-color: #fff;
        @include display-flex;
        @include flex-column;
        position: relative;
        margin-left: 4px;
    }
    .file-list-wrapper .file-side .pithy-side-close-box {
        height: 48px;
        position: relative;
        border-bottom: 1px solid #eeeff5;
        @include display-flex;
        @include flex-center;
    }
    .file-list-wrapper .action-btn {
        margin-top: 20px;
        padding: 0 20px 13px 40px;
    }
    .file-list-wrapper .action-btn .btn-list {
        @include display-flex;
    }
    .file-list-wrapper .action-btn .btn-list .btn {
        margin-right: 12px;
        height: 32px;
        line-height: 30px;
        text-align: center;
        background-color: #fff;
        border: 1px solid #d8dbe2;
        padding: 0 10px;
        font-size: 14px;
        border-radius: 3px;
        min-width: 80px;
        color: #252e36;
        font-size: 14px;
        box-sizing: border-box;
        @include display-flex;
        @include flex-center;
    }

    .file-list-wrapper .action-btn .btn-list .btn i {
        font-size: 12px;
        margin: 0 0 0 5px;
        color: #747b93;
        transform: scale(0.7);
    }
    .file-list-wrapper .action-btn .btn-list .btn.upload {
        background-color: #017ffd;
        border: 1px solid #017ffd;
        color: #fff;
    }
    .file-list-wrapper .action-btn .btn-list .btn.upload i {
        color: #fff;
    }
    .file-list-wrapper .action-btn .path {
        height: 24px;
        line-height: 1;
        margin-top: 14px;
        font-size: 12px;
        color: #252e36;
        @include display-flex;
        @include flex-space-between
    }
    .file-list-wrapper .action-btn .path .switch {
        @include display-flex;
        @include flex-center;
    }
    .file-list-wrapper .action-btn .path .switch i {
        font-size: 24px;
        color: #676e87;
    }
    .file-list-wrapper .action-btn .path .switch i.icon-arrow_down {
         font-size: 12px;
         transform: scale(0.6);
    }
    .list-container ul{
        -webkit-touch-callout: none;
        -webkit-user-select: none;
        -khtml-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
        @include display-flex;
        padding: 0 40px;
        position: relative;
    }
    .list-container ul li {
        box-sizing: border-box;
    }
    .list-container .header-cell {
        color: #b2b7ca;
        display: inline-block;
        height: 30px;
        line-height: 30px;
    }
    .list-container .cell-main {
        -ms-flex: 1 1 auto;
        -moz-flex: 1 1 auto;
        -webkit-flex: 1 1 auto;
        flex: 1 1 auto;
        width: 0;
    }
    .list-container ul li span {
        color: #252e36;
    }
    .list-container ul li.cell-status {
        width: 220px;
    }
    .list-container ul li.cell-size {
        width: 80px;
    }
    .list-container ul li .sort-icon {
        width: 12px;
        height: 12px;
    }
    .list-container .list-header .header-cell>span {
        font-size: 12px;
        color: #252e36;
    }
    .file-list-wrapper .file-side .pithy-side-close-box i {
        font-size: 24px;
        color: #676e87;
    }
    .file-list-wrapper .file-side .pithy-side-wrap {
        padding-top: 19px;
        text-align: center;
    }
    .file-list-wrapper .file-side .pithy-side-wrap i {
        display: block;
        margin-bottom: 28px;
        color: #676e87;
        font-size: 24px;
    }
    .list-container .list-body {
        overflow: hidden;
        position: relative;
        color: #747b93;
        padding-bottom: 100px;
    }
    .list-container .list-body ul {
        height: 50px;
        line-height: 50px;
        @include display-flex;
        @include flex-align-center;
    }
    .list-container .list-body ul .svg-icon  {
        width: 20px;
        height: 20px;
        margin-right: 12px;
    }
    .list-container .list-body ul .svg-icon svg {
        width: 20px;
        height: 20px;
    }
    .list-container .list-body .cell-main {
        @include display-flex;
        @include flex-align-center;
    }
    .list-container .list-body .cell-main .name,
    .list-container .list-body .cnt-left,
    .list-container .list-body .cnt-size {
        font-size: 12px;
        color: #252e36;
    }
    .list-container .list-body .cnt-left,
    .list-container .list-body .cnt-size {
        font-size: 12px;
        color: #999;
    }

    .register-modal {
        @include display-flex;
        @include flex-center;

        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        background-color: rgba($black-color, .5);
    }

    .register-dialog {
        position: relative;
        box-sizing: border-box;
        padding: 60px 100px;
        margin-top: 0;
        width: 560px;
        background-color: $white-color;
        border-radius: 4px;

        .language-switch {
            position: absolute;
            display: inline-block;
            font-size: 12px;
            top: 16px;
            right: 16px;
            cursor: pointer;

            .iconfont {
                color: $font-light;
            }

            .icon-language {
                position: relative;
                top: 2px;
            }

            .icon-arrow {
                @include transform(scale(.67));
                font-size: 12px;
                color: $font-base;

                &::before {
                    content: "\e623";
                }
            }

            span {
                color: $font-base;
                display: inline-block;
                padding-right: 5px;
            }

            &:hover {
                .language-switch-dropdown {
                    display: block;
                }

                .icon-arrow::before {
                    content: "\e625";
                }
            }

            .language-switch-dropdown {
                background-color: #FFFFFF;
                border: 1px solid #e7e7e7;
                display: none;
                margin-left: 1.5px;
                margin-top: -1px;
                padding: 5px 0;
                position: absolute;
                right: 0;
                text-align: center;
                width: 100px;
                z-index: 9;
                box-shadow: 0 3px 8px 0 rgba(0, 0, 0, .19);

                li {
                    line-height: 32px;

                    &:hover {
                        background-color: $item-hover-bg;
                    }
                }

                a {
                    color: #404040;
                }

            }
        }

        .title {
            font-size: 28px;
            line-height: 36px;
            font-weight: 300;
            color: $font-darker;
            text-align: center;
            margin-bottom: 40px;
        }

        .select-group {
            vertical-align: middle;

            &.input-group-addon {
                width: 90px;
            }
        }

        .input-group {
            width: 360px;
        }

        .btn-primary {
            height: 40px;
            line-height: 38px;
        }

        .register-hint,
        .fangcloud-protocol {
            clear: both;
            margin-top: 12px;
            line-height: 20px;
            color: $font-base;
            text-align: center;
        }
    }
}

.sem-wrapper {
    min-width: auto;
    width: 460px;
    background-color: #fff;

    .product-bg {
        display: none;
    }

    .register-modal {
        background-color: transparent;
    }

    .register-dialog {
        width: 460px;
        padding: 50px 50px;

        .language-switch {
            display: none;
        }
    }
}

.check_channel_coupon {
    margin: 0;
    padding: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(180deg, #E0EFFF 0%, rgba(255, 255, 255, 0.00) 65.76%), #FFF;
    text-decoration: none;

    .page-recommend-head {
        display: flex;
        width: 100%;
        height: 60px;
        padding: 0px 20px;
        justify-content: space-between;
        align-items: center;
        box-sizing: border-box;

        > div {
            display: flex;
            align-items: center;
        }

        .head-link {
            height: 24px;
        }

        .logo {
            width: 130px;
            height: 24px;
        }

        .title {
            color: #252E36;
            font-family: PingFang SC;
            font-size: 16px;
            font-style: normal;
            font-weight: 500;
            margin-left: 25px;
            position: relative;

            &:after {
                content: '';
                display: block;
                height: 24px;
                width: 1px;
                position: absolute;
                left: -13px;
                top: 0;
                background-color: #bacadd;
            }
        }

        .app {
            display: flex;
            align-items: center;
            color: #252E36;
            font-family: PingFang SC;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 1;
            margin-right: 32px;

            img {
                width: 24px;
                height: 24px;
                margin-right: 4px;
            }
        }

        .phone {
            display: flex;
            align-items: center;
            color: #252E36;
            font-family: PingFang SC;
            font-size: 16px;
            font-style: normal;
            font-weight: 600;
            line-height: 1;
            margin-right: 32px;

            img {
                width: 24px;
                height: 24px;
                margin-right: 4px;
            }
        }

        .login {
            display: flex;
            width: 80px;
            height: 32px;
            justify-content: center;
            align-items: center;
            border-radius: 3px;
            background: #017FFD;
            color: #FFF;
            font-family: PingFang SC;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            cursor: pointer;
        }
    }

    .page {
        width: 494px;
        margin: 0 auto;
        margin-top: 8px;

        .check {
            width: 494px;
            height: 435px;
            background: url('https://p3.ssl.qhimg.com/t01c5bc9eccad144552.png') no-repeat;
            background-size: 100%;
            padding: 40px;
            padding-top: 204px;
            display: flex;
            flex-direction: column;
            box-sizing: border-box;

            > div, > a {
                height: 40px;
                display: flex;
                align-items: center;
                margin-bottom: 24px;

                span {
                    color: #05417E;
                    font-family: PingFang SC;
                    font-size: 20px;
                    font-style: normal;
                    font-weight: 600;
                    display: block;
                    width: 80px;
                    margin-right: 8px;
                }

                input {
                    display: flex;
                    width: 308px;
                    height: 40px;
                    padding: 10px 12px;
                    flex-direction: column;
                    justify-content: center;
                    box-sizing: border-box;
                    font-size: 16px;
                    border-radius: 3px;
                    border: 1px solid #CBDBEC;
                    background: #FFF;
                }
            }

            .capt {
                .input-group {
                    display: flex;
                }

                input {
                    width: 194px;
                    margin-right: 12px;
                }

                img {
                    width: 100px;
                    height: 40px;
                }
            }

            > span {
                display: flex;
                width: 396px;
                height: 48px;
                justify-content: center;
                align-items: center;
                border-radius: 24px;
                background: linear-gradient(122deg, #CA34FF 1.09%, #7857FF 67.01%);
                color: #FFF;
                font-family: PingFang SC;
                font-size: 20px;
                font-style: normal;
                font-weight: 500;
                margin-top: 16px;
                cursor: pointer;
            }
        }

        .check_channel {
            width: 482px;
            height: 174px;
            background: url('https://p1.ssl.qhimg.com/t0122f2ef2bb9991ae3.png') no-repeat;
            background-size: 100%;
            margin-top: 32px;
        }
    }

    .success {
        display: none;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.7);
        position: fixed;
        left: 0;
        top: 0;

        .suc {
            width: 300px;
            background: rgba(255, 255, 255, 1);
            position: fixed;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            font-family: PingFang SC;
            font-size: 14px;
            font-style: normal;
            font-weight: normal;
            color: #252e36;
            padding: 24px;
            box-sizing: border-box;

            span {
                width: 80px;
                height: 32px;
                border: 1px solid #d8dbe2;
                display: flex;
                justify-content: center;
                align-items: center;
                background: rgba(255, 255, 255, 1);
                margin-top: 24px;
                float: right;
                cursor: pointer;
            }
        }

        &.showSuccess {
            display: flex;
        }
    }
}


.new-enterprise-register {
    margin: 0;
    padding: 0;
    width: 100%;
    height: 100%;
    min-height: 800px;
    position: relative;
    background-image: url('https://p0.ssl.qhimg.com/t014e0798474c317434.png');
    background-repeat: no-repeat;
    background-size: 2200px 1200px;
    background-position: center center;
    text-decoration: none;
    box-sizing: border-box;

    .page-recommend-head {
        display: flex;
        width: 100%;
        height: 64px;
        padding: 0px 40px;
        justify-content: space-between;
        align-items: center;
        box-sizing: border-box;

        > div {
            display: flex;
            align-items: center;
        }

        .logo {
            width: 214px;
            height: 31px;

            &.ai-logo {
                width: auto;
                height: 40px;
            }
        }

        .login {
            display: flex;
            width: 116px;
            height: 41px;
            justify-content: center;
            align-items: center;
            color: #FFF;
            font-family: PingFang SC;
            font-size: 16px;
            font-style: normal;
            font-weight: 400;
            border-radius: 80px;
            background: #006BFF;
            cursor: pointer;
        }
    }

    .page-common {
        @include display-flex;
        @include flex-space-between;
        width: 1280px;
        height: 615px;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        padding: 0 79px;
        box-sizing: border-box;


        .step-image {

            img {
                display: none;
                height: 615px;

                &.active {
                    display: block;
                }
            }
        }

        .step-common {
            @include display-flex;
            @include flex-column;
            width: 480px;
            height: 528px;
            padding: 48px 24px 0 24px;
            box-sizing: border-box;
            border-radius: 24px;
            border: 1px solid #FFF;
            background: #FFF;
            position: relative;

            .step-header {
                @include display-flex;
                @include flex-align-center;
                height: 24px;
                padding: 0 23px;
                box-sizing: border-box;

                > p {
                    @include display-flex;
                    @include flex-align-center;
                    color: #1B2532;
                    font-family: PingFang SC;
                    font-size: 16px;
                    font-style: normal;
                    font-weight: 600;
                    line-height: normal;

                    span {
                        display: block;
                        width: 24px;
                        height: 24px;
                        border-radius: 50%;
                        background: #D8E0E8;
                        color: #FFF;
                        font-size: 14px;
                        margin-right: 8px;
                        line-height: 24px;
                        text-align: center;
                    }

                    &.has-line {
                        width: 144px;
                        position: relative;

                        &::after {
                            content: '';
                            display: block;
                            width: 32px;
                            height: 2px;
                            border-radius: 12px;
                            background: #BCCAD6;
                            position: absolute;
                            top: 50%;
                            right: 8px;
                            transform: translateY(-50%);
                        }
                    }

                    &.active {
                        span {
                            background: #006BFF;
                        }

                        &::after {
                            background: #006BFF;
                        }
                    }
                }
            }

            .signup-common {
                margin-top: 8px;
                padding: 0 23px;

                > div {
                    display: none !important;

                    .title {
                        color: #1B2532;
                        font-family: PingFang SC;
                        font-size: 24px;
                        font-style: normal;
                        font-weight: 600;
                        line-height: 36px;
                    }

                    .submit-btn {
                        display: flex;
                        height: 48px;
                        width: 100%;
                        justify-content: center;
                        align-items: center;
                        border-radius: 28px;
                        background: #006BFF;
                        color: #FFF;
                        font-family: PingFang SC;
                        font-size: 14px;
                        font-style: normal;
                        font-weight: 600;
                        margin-top: 32px;
                        opacity: 0.3;

                        &.active {
                            opacity: 1;
                        }
                    }

                    &.active {
                        display: flex !important;
                        @include flex-column
                    }
                }

                .step-three {
                    a {
                        display: flex;
                        width: 100%;
                    }

                    img {
                        width: 100%;
                        margin-top: 24px;
                        cursor: pointer;
                    }

                    .submit-btn {
                        margin-top: 60px;
                    }
                }

                .step-one {
                    &.form {
                        margin-top: 0;

                        span.error, .hint, .voice-captcha {
                            display: block;
                            position: absolute;
                            left: 12px;
                            bottom: -21px;
                        }

                        .voice-captcha {
                            left: auto;
                            right: 12px;
                        }

                        .input-group {
                            width: 100%;
                            height: 48px;
                            border-radius: 28px;
                            background: #F5F8FA;
                            padding: 5px 8px;
                            box-sizing: border-box;

                            input {
                                background: #F5F8FA;
                                -webkit-box-shadow: 0 0 0px 1000px #F5F8FA inset;
                            }
                        }

                        .select-group-content {
                            box-sizing: border-box;
                        }

                        .submit-btn {
                            margin-top: 12px;
                        }

                        .register-group {
                            span.error {
                                bottom: auto;
                            }
                        }
                    }
                }

                .step-two {
                    & .section-title {
                        color: #1B2532;
                        font-family: PingFang SC;
                        font-size: 16px;
                        font-style: normal;
                        font-weight: 400;
                        line-height: 24px;
                        margin-top: 32px;
                    }

                    & .section-content {
                        display: flex;
                        flex-wrap: wrap;

                        & .item {
                            display: flex;
                            width: 87px;
                            height: 34px;
                            justify-content: center;
                            align-items: center;
                            color: #1B2532;
                            font-family: PingFang SC;
                            font-size: 14px;
                            font-style: normal;
                            font-weight: 400;
                            box-sizing: border-box;
                            border-radius: 4px;
                            background: #F7F9FA;
                            margin-top: 16px;
                            margin-right: 12px;
                            box-sizing: border-box;
                            cursor: pointer;

                            &.active {
                                background: #006BFF;
                                color: #FFF;
                            }

                            &:nth-child(4n) {
                                margin-right: 0;
                            }
                        }
                    }

                    & .team-name {
                        margin-top: 24px;

                        & > p {
                            color: #1B2532;
                            font-family: PingFang SC;
                            font-size: 16px;
                            font-style: normal;
                            font-weight: 400;
                            line-height: 24px;
                        }

                        & > input {
                            display: flex;
                            width: 100%;
                            height: 48px;
                            align-items: center;
                            border-radius: 28px;
                            background: #F5F8FA;
                            border: none;
                            padding: 5px 20px;
                            color: #10182E;
                            font-family: PingFang SC;
                            font-size: 16px;
                            font-style: normal;
                            font-weight: 400;
                            box-sizing: border-box;
                            margin-top: 16px;

                            &::placeholder {
                                color: #B2B7CA;
                            }
                        }
                    }
                }
            }

            .step-login {
                @include display-flex;
                @include flex-center;
                width: 100%;
                height: 46px;
                border-radius: 0 0 24px 24px;
                color: #626F84;
                font-family: PingFang SC;
                font-size: 14px;
                font-style: normal;
                font-weight: 400;
                position: absolute;
                left: 0;
                bottom: 0;
                background: #F5F9FF;

                a {
                    color: #006BFF;
                    cursor: pointer;
                }

                &.hidden {
                    display: none;
                }
            }
        }
    }

    .page-recommend-foot {
        width: 100%;
        padding-bottom: 16px;
        color: #9BA7BA;
        text-align: center;
        font-family: PingFang SC;
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 20px;
        position: absolute;
        left: 0;
        bottom: 0;
    }
}

.invite-introduction {
    width: 100%;
    min-height: 100%;
    background: #F4F6FB;
    line-height: normal;

    .introduction-header {
        @include display-flex;
        @include flex-space-between;
        @include flex-align-center;
        width: 100%;
        height: 60px;
        padding: 0px 143px;
        background: #FFF;
        box-shadow: 0px 1px 0px 0px #ECECEC;
        box-sizing: border-box;

        .header-left {
            @include display-flex;
            @include flex-align-center;

            > img {
                width: 152px;
            }

            > span {
                color: #000;
                font-family: "360shouhu Type";
                font-size: 24px;
                font-style: normal;
                font-weight: bold;
                margin-left: 12px;
            }
        }
    }

    .introduction-form {
        @include display-flex;
        @include flex-column;
        @include flex-align-center;

        .title {
            color: #252E36;
            font-family: "PingFang SC";
            font-size: 28px;
            font-style: normal;
            font-weight: 400;
            line-height: 36px;
        }

        .button, .get-code {
            @include display-flex;
            @include flex-center;
            height: 44px;
            padding: 10px;
            color: #FFF;
            font-family: "PingFang SC";
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            border-radius: 4px;
            background: #006BFF;
            box-sizing: border-box;
            cursor: pointer;

            &.disabled {
                opacity: 0.3;
                cursor: not-allowed;
            }
        }
    }

    .introduction-sms {
        width: 1080px;
        padding: 80px 0px;
        margin: 80px auto;
        border-radius: 4px;
        background: #FFF;

        .form-group {
            @include display-flex;
            @include flex-column;

            .input-group {
                border: none;

                input {
                    display: flex;
                    height: 44px;
                    padding: 10px 12px;
                    align-items: center;
                    box-sizing: border-box;
                    border-radius: 4px;
                    border: 1px solid #D8DBE2;
                    color: #252E36;
                    font-family: "PingFang SC";
                    font-size: 14px;
                    font-style: normal;
                    font-weight: 400;
                }
            }

            .error {
                margin-top: 4px;
            }
        }

        #phone {
            width: 360px;
            margin-top: 16px;
        }

        .sms-group {
            @include display-flex;
            @include flex-align-center;
            width: 360px;

            input {
                max-width: 228px;
            }

            .get-code {
                width: 173px;
                margin-left: 12px;

                &[get-disabled] {
                    opacity: 0.3;
                    background: #006BFF;
                }
            }
        }

        .pic-captcha {

            .input-group {
                display: flex;
            }

            input {
                width: 228px;
            }

            .get-captcha {
                display: flex;
                margin-left: 12px;

                img {
                    width: 120px;
                    height: 44px;
                }
            }
        }

        > span {
            width: 360px;
            margin-top: 40px;
        }

        .register-btn {
            opacity: 0.3;
            cursor: not-allowed;

            &.can-submit {
                opacity: 1;
                cursor: pointer;
            }
        }
    }
}

@media (min-width: 2100px){
    .new-enterprise-register {
        background-size: cover;
    }
}