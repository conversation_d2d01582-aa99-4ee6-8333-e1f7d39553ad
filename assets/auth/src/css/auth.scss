.header {
    height: 50px;
    line-height: 50px;
    padding: 0 32px 10px;

    &::after {
        background-image: linear-gradient(to bottom , #f8f9fb, $white-color);
        content: '';
        display: block;
        height: 10px;
        margin: 0 -30px;
    }

    .logo {
        display: inline-block;
        height: 50px;
        vertical-align: middle;
        // width: 115px;
    }

    .default-logo {
        display: inline-block;
        height: 28px;
        width: 98px;
        margin-top: 11px;
        vertical-align: middle;
        background-image: url(../images/logo.png);
        background-repeat: no-repeat;
        background-size: contain;
    }

    .logo-des {
        color: $font-darker;
        display: inline-block;
        margin-left: 10px;
        padding-left: 10px;
        border-left: 1px solid $border-input;
        font-size: 16px;
        line-height: 20px;
        vertical-align: middle;
    }

    .account-box {
        float: right;
        font-size: 0;
        line-height: 48px;

        .btn {
            padding: 0 16px;
            height: 30px;
            line-height: 28px;

            &+.btn {
                margin-left: 12px;
            }
        }

        .btn-register {
            border-color: $primary-color;
            color: $primary-color;
        }
    }

    .language-switch {
        cursor: pointer;
        display: inline-block;
        font-size: 12px;
        margin-right: 28px;

        .iconfont {
            color: $font-light;
        }

        .icon-language {
            position: relative;
            top: 2px;
        }

        .icon-arrow {
            @include transform(scale(.83));
            font-size: 12px;

            &::before {
                content: "\e623";
            }
        }

        span {
            color: $font-base;
            display: inline-block;
            padding-right: 5px;
        }

        &:hover {
            .language-switch-dropdown {
                display: block;
            }

            .icon-arrow::before {
                content: "\e625";
            }
        }

        .language-switch-dropdown {
            background-color: #FFFFFF;
            border: 1px solid #e7e7e7;
            display: none;
            margin-left: 1.5px;
            margin-top: -1px;
            position: absolute;
            text-align: center;
            width: 100px;
            z-index: 9;

            li {
                line-height: 20px;
                padding: 5px 0;
            }

            a {
                color: #404040;
            }

        }
    }
}

.open-header {
    display: none;
}

.aliyun-logo {
    height: 34px;
    margin-bottom: 40px;
    margin-top: 60px;
    text-align: center;

    img {
        height: 100%;
        padding: 0 12px;

        + img {
            border-left: 1px solid #eeeff5;
        }
    }
}

// aliyun bind style
.bind-info {
    color: $font-base;
    line-height: 20px;
    margin: 0 auto;
    width: 360px;
    white-space: nowrap;
}

// login-box
.login-box {
    margin-top: 80px;

    .qrcode-login {
        display: none;
    }

    .international-form,
    .ohter-login-form,
    .password-init-form {
        display: block;
    }
}

.register-top,
.auth-top,
.forgot-top,
.aliyunbind-top {
    font-size: 18px;
    margin: 100px auto 35px;
    text-align: center;
}

.two-step-top {
    margin-top: 20px;
}

.register-top {
    margin-top: 30px;
    margin-bottom: 10px;
}

.auth-top-tip {
    color: $font-base;
    margin: -25px auto 0;
    text-align: center;
}

.aliyunbind-top {
    margin-top: 60px;
}

.register-hint {
    color: $font-base;
    font-size: 12px;
    line-height: 17px;
    margin: 0 auto 24px;
    text-align: center;

    b {
        font-weight: normal;
        color: $font-darker;
    }
}

.web-form {
    &.internal-form,
    &.bind-form,
    &.verify-form  {
        display: block;
    }

    .form-group {
        position: relative;

        .text-label {
            color: $font-base;
            height: 38px;
            left: -100px;
            line-height: 40px;
            position: absolute;
            text-align: right;
            width: 100px;

            &.top {
                height: 17px;
                line-height: 17px;
            }
        }
    }

    .input-group {
        width: 360px;
    }

    .text {
        border-radius: 2px;
        height: 38px;
        line-height: 38px;
    }


    .pic-captcha {
        .pic-container {
            padding: 0;
            width: 120px;
        }

        img {
            width: 108px;
        }
    }
}

.register-purpose {
    .label {
        color: $font-base;
    }

    .radiobox {
        margin-left: 8px;

        .radiobox-inner {
            margin-right: 2px;
        }

        & + .radiobox {
            float: right;
        }
    }

    &.error {
        .label {
            color: $error-color;
        }
    }
}

.fangcloud-protocol {

    input,
    label {
        display: inline-block;
        line-height: 17px;
        vertical-align: top;
        cursor: pointer;
    }

    .checkbox-inner {
        float: left;
        margin-right: 7px;
        top: 2px;
    }

    label {
        width: 360px;
    }
}

.activation-box {
    h1 {
        font-size: 16px;
        margin: 20px auto 40px;
        text-align: center;
    }

    .fullfill-account {
        padding-top: 10px;

        .user-info {
            li {
                line-height: 20px;
            }

            span {
                vertical-align: top;
            }

            .label {
                color: $font-base;
                font-size: 12px;
            }

            .content {
                color: $font-darker;
                font-size: 14px;
            }
        }
    }
}

.sso-entry-box {
    text-align: center;

    .btn {
        width: 100%;
    }
}

.footer {
    width: 960px;
    margin: 0 auto;
    background: none;
    font-size: 14px;
    color: #999;

    .container-bottom,
    .container-middle {
        height: 22px;
        padding: 0 17px 10px;
        text-align: center;
    }

    .nav a {
        color: #404040;
        margin-right: 25px;
    }

    .copyright {
        display: inline-block;
        color: #999;
    }

    .certificate {
        display: inline-block;
        margin-left: 15px;
        color: #999;
    }
}

.i18n-en {
    .v2-download-container {

        .qrcode p {
            width: 120px;
        }

        .app-download-btn {
            width: 200px;
        }

    }

    .register-purpose {
        .radiobox {
            display: block;
            height: 17px;
            font-size: 14px;
            margin-left: 0;
            margin-top: 10px;
            .radiobox-inner {
                margin-right: 5px;
            }

            & + .radiobox {
                float: none;
            }

        }
    }

}
