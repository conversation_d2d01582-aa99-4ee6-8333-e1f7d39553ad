.wrapper {
    .header::after {
        display: none;
    }

    .login-box {
        position: relative;
        background-color: #fff;
        border-radius: 12px;
        padding: 40px 48px;
        box-shadow: 0px 1px 4px 0px rgba(75, 85, 105, 0.17), 0px 3px 8px 0px rgba(27, 37, 50, 0.13);
    }

    .btn-register {
        display: none;

    }

    .btn-login {
        display: none;
    }

    .btn-download {
        color: var(---, #1D2531);
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px;
        background-color: transparent;
        border: none;

        &:hover {
            background-color: transparent;
            color: #006BFF;
        }
    }

    .form {
        margin-top: 24px;

        .action-group {
            margin-top: 40px;
        }
    }

    .btn-primary, .btn-primary-light {
        border-radius: 8px;
        background: var(---, #006BFF);

        &:hover {
            background: var(---, #006BFF);
            opacity: 0.8;
        }
    }

    .checkbox-inner {
        border-color: #006BFF;
        margin-right: 6px;

        &::after {
            border-color: #006BFF;
        }
    }
}

.hide {
    display: none !important;
}

.forgot-pwd {
    display: none;
}

.form .form-footer .other-login-container .other-login.third-login {
    display: flex;
    flex-direction: column;

    .prompt-title {
        width: 100%;
        font-size: 14px;
        color: var(---, #9EA7B8);
        display: flex;
        justify-content: center;
        align-items: center;
        margin-bottom: 16px;

        .line  {
            width: 122px;
            background-color: #EDF1F5;
            height: 1px;
        }

        .title {
            margin: 0 12px;
        }
    }

    .other-login-content {
        display: flex;
        justify-content: center;

        .bind-link span {
            display: none;
        }

        a {
            flex: 0 0 auto !important;
            margin-right: 32px;

            &:last-child {
                margin-right: 0;
            }

            svg, i {
                font-size: 28px;
            }
        }
    }
}

.switch-icon {
    position: absolute;
    width: 68px;
    height: 68px;
    top: 0;
    right: 0;
    background-image: url('https://p3.ssl.qhimg.com/d/inn/1ea885146144/12x.png');
    background-size: 100% 100%;
    cursor: pointer;
}

.login-box {
    .switch-box {
        .tab {
            color: #1D2531;
            font-weight: 600;
            font-size: 22px;
        }
    }
}

.login-box.qrcode {
    .switch-icon {
        background-image: url('https://p1.ssl.qhimg.com/d/inn/66e01604bff5/212334.png');
    }

    .switch-box {
        .qrcode-title {
            display: block !important;
        }

        .form-title {
            display: none !important;
        }
    }

    .qrcode-login {
        display: block !important;
    }

    .web-form {
        display: none !important;
    }
}

.login-box .form .form-footer {
    display: none !important;
}

.copyright {
    display: none !important;
}

.switch-icon {
    display: none !important;
}

.login-box .form-group  button {
    background-color: #000000 !important;
    color: #fff !important;
}

.login-box .input-group {

}


.form .input-group.active, .form .input-group.focus {
    border-color: #000000 !important;
}

.wrapper .checkbox-inner {
    border-color: #000000 !important;
}

.wrapper .checkbox-inner::after {
    border-color: #000000 !important;
}

.language-switch {
    display: none !important;
}

.login-box {
    margin-top: 0 !important;
    position: absolute !important;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.container {
    max-width: 100% !important;
}
