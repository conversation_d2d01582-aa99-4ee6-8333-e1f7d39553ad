/** dialog-two-step-verify */
.dialog-two-step-verify {
    font-size: 14px;

    .dialog-wrap {
        padding: 0;
        position: relative;
        width: 480px;
    }

    .check-password,
    .sms-set {
        margin: 67px auto;
    }

    .input-group {
        width: 100%;
    }

    .set-password {
        margin: 59px auto;
    }

    .sms-set {
        min-height: 84px;
        margin: 40px auto 24px;

        width: 360px;

        .country-phone {
            overflow: visible;
        }

        .dialog-actions {
            margin-top: 24px;
        }
    }

    .select-list {
        height: 240px;
        width: 521px;

        .item {
            border: 2px solid $border-color;
            cursor: pointer;
            float: left;
            height: 236px;
            margin-left: 10px;
            position: relative;
            width: 163px;

            &.selected {
                background: $item-selected;
                border-color: $item-selected;
            }

            &:first-child {
                margin-left: 0;
            }

            &:hover {
                border-color: $item-hover-bg;
            }
        }

        &[data-items-count="2"] {
            .item {
                width: 251px;
            }

            .item-title {
                margin-bottom: 28px;
            }
        }

        &[data-items-count="1"] {
            .item {
                margin: 0;
                width: 100%;
            }
        }

        .item-title {
            color: $font-darker;
            line-height: 24px;
            margin-top: 31px;
            margin-bottom: 44px;
            text-align: center;

            .iconfont {
                font-size: 20px;
                margin-right: 9px;
            }
        }

        .item-des {
            color: $font-base;
            padding: 0 10px 8px 20px;

            &.item-indent {
                margin-left: 0.9em;
                text-indent: -0.9em;
            }
        }

        .recommend {
            background: $primary-color;
            border-radius: 2px;
            color: $white-color;
            height: 16px;
            left: 9px;
            line-height: 16px;
            padding: 0 4px;
            position: absolute;
            text-align: center;
            top: 10px;
        }
    }

    .wechat-set,
    .google-set {
        line-height: 20px;
        margin-left: 85px;
        margin-top: 15px;
        position: relative;
        width: 100%;

        .form-group {
            margin-top: 16px;
        }

        .input-group {
            width: 160px;
        }
    }

    .wechat-set {
        .qrcode-box {
            margin-left: -8px;
        }
    }
}

.verify-form {
    margin-top: 40px;
}

.help-link {
    color: $font-darker;
    line-height: 17px;
    font-size: 12px;
    height: 17px;
    margin-top: 10px;
    margin-bottom: 40px;
    float: right;
}

.verify-tip {
    color: $font-darker;
    margin-bottom: 20px;
    margin-top: 20px;
}

.verify-tip-des {
    color: $font-base;
    line-height: 20px;
    margin-bottom: 4px;

}

.qrcode-box {
    height: 150px;
    float: left;
    margin-left: -14px;
}

.qrcode-tip {
    color: $font-base;
    height: 90px;
    line-height: 20px;
    padding-top: 60px;
}

.verify-secret {
    background: #fbebc6;
    color: #4e5660;
    height: 30px;
    line-height: 30px;
    margin: 20px 0;
    width: 264px;
    text-align: center;
}

.to-qrcode {
    display: block;
    margin-bottom: 44px;
    line-height: 12px;
}

.force-set-two-step {
    &.dialog-two-step-verify {
        width: 520px;
        position: relative;
        margin: 0 auto;
    }

    .dialog-actions {
        margin: 40px auto 0;
        text-align: center;
        width: 360px;

        .btn {
            width: 360px;
        }

        .backward {
            color: $font-darker;
            cursor: pointer;
            display: block;
            line-height: 17px;
            height: 17px;
            margin-top: 20px;
            text-align: left;
        }

        .iconfont {
            color: $font-light;
            margin-right: 5px;
        }
    }
}

.i18n-en {
    .dialog-two-step-verify .input-group input {
        margin: auto;
        width: 100%;
    }
}
