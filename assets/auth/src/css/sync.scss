@import 'base';
@import 'two_step_verify';
@import 'activation.scss';

.wrapper {
    min-width: 0;
}

.hybrid-top {
    margin-bottom: 88px;

    .auth-top {
        font-size: 30px;
        margin: 64px 0 16px;
    }


    .auth-top-tip {
        font-size: 16px;
    }

}

.register-top,
.auth-top,
.forgot-top {
    font-size: 16px;
    margin: 15px auto 5px;
    text-align: center;
}

.auth-top-tip {
    color: $font-base;
    margin: 0 auto;
    text-align: center;
}

// content
.sync-register {
    bottom: 24px;
    font-size: 12px;
    position: absolute;
    text-align: center;
    width: 360px;

    a {
        color: $font-darker;
        display: inline-block;
        padding: 0 15px;
    }
}

.sso-entry-box,
.login-box {

    .qrcode-login {
        display: none;

        .qr-header-hint {
            margin-top: 10px;
        }
    }

    .international-form,
    .ohter-login-form {
        display: block;
    }
}

.sync-form {
    &.internal-form,
    &.bind-form,
    &.verify-form {
        display: block;
    }

    .input-group {
        width: 100%;
    }

    .pic-captcha {
        .get-captcha {
            width: 104px;
            padding-bottom: 2px;
        }
        img {
            width: 100px;
        }
    }

    span.error {
        top: 32px;
    }
}

.form {

    .form-footer {

        .other-login-container {

            margin-top: 16px;
        }
    }
}


.i18n-en {
    .login-box {
        .switch-box .tab {
            font-size: 14px;
        }

        .form {
            margin-top: 20px;

            .form-group {
                margin-top: 15px;
            }

            .remember-login {
                margin-top: 10px;
            }

            .form-footer {

                .other-login-container {

                    margin-top: 22px;
                    .other-login {

                        span {
                            font-size: 11px;
                        }
                        .prompt-title {
                            margin-right: 4px;
                            width: 60px;
                        }

                        .other-login-content {
                            a {
                                @include flex-grow;
                                max-width: 900px;
                                margin-right: 0;
                            }
                        }
                    }
                }
            }
        }
    }

    .sync-register {
        a {
            padding: 0 10px;
        }
    }
}


// .action

.activation-help {
    padding-top: 10px;
}

.aiw-content .aiwc-guide-img {
    display: none;
}