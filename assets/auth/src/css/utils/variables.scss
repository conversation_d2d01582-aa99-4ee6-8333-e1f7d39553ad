$primary-color: #6ca2ff;
$primary-hover-color: #6394ea;
$primary-active-color: #2877FF;

$primary-shadow-color: #3d95f2;
$white-color: #fff;
$black-color: #000;
$error-color: #f55;
$error-hover-color: #ff5a58;
$warning-color: #ffb743;
$success-color: #00cf72;

// v2.5
// font color level
$font-lighter: #a5a9aa;
$font-light: #b2b7ca;
$font-base: #747b93;
$font-dark: #6a6c6d;
$font-darker: #252e36;

$font-nav-main: #4b545c;
$font-nav-icon: #8b98a9;

$icon-color: $font-light;
$item-hover-bg: #f6f7f9;
$item-selected: #ebf2ff;
$border-color: #f5f6f9;
$side-header: #f7faff;

$font-list: $font-darker;
$font-sub: $font-base;

$font-color-breadcrumb: #a3a8be;
$tab-active: #333;

$font-list-light: #bcbcbc;
$font-lighter-more: #cecece;
$font-warn: #ffa847;
$font-lightest: #d8dbe2;
$font-placeholder: $font-light;

// background color level
$bg-base: #d9dcde;
$bg-darker: #f0f0f0;
$bg-dark: #f3f3f3;
$bg-f2: #f2f2f2;
$bg-f5: #f5f5f5;
$bg-light: #f9f9f9;
$bg-fa: #fafafa;
$bg-lighter: #fbfbfb;
$bg-lighter-more: #fcfcfc;

// border color level
$border-base: #e1e5e7;
$border-dark: #eef1f2;
$border-light: #f1f4f5;
$border-lighter: #f4f6f7;
$border-ec: #ececec;
$border-input: #D8DBE2;
$border-btn: #cfcfcf;

// common hover color
$bg-hover-base: $border-lighter;

// file list item
$list-item-select: #f2fafe;
$list-item-error: #fff8f8;

//highlight background color
$highlight-color: #fff9a9;

// - FOLDERS AND SO ON ...
$yellow-color: #ffe17b;
$pink-color: #ff7398;
$green-color: #72dca2;
$blue-color: #92d9ff;
$purple-dark: #aec2ff;
$violet-color: #8aa6f9;

// progress bar base color
$progress-bar-base: $success-color;
$progress-bar-pause: $font-light;
$progress-bar-background: #eaecf3;
