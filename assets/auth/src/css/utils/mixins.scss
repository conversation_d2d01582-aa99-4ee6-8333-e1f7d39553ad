@mixin ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

@mixin pre-ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: pre;
}

@mixin keyframes($name) {
    @-webkit-keyframes #{$name} {
        @content;
    }
    @-moz-keyframes #{$name} {
        @content;
    }
    @-ms-keyframes #{$name} {
        @content;
    }
    @keyframes #{$name} {
        @content;
    }
}

@mixin transition($transitions) {
    -moz-transition: $transitions;
    -o-transition: $transitions;
    -ms-transition: $transitions;
    -webkit-transition: $transitions;
    transition: $transitions;
}

@mixin transform($transforms) {
    -moz-transform: $transforms;
    -o-transform: $transforms;
    -ms-transform: $transforms;
    -webkit-transform: $transforms;
    transform: $transforms;
}

@mixin transform-origin($origin...) {
    -moz-transform-origin: $origin;
    -o-transform-origin: $origin;
    -ms-transform-origin: $origin;
    -webkit-transform-origin: $origin;
    transform-origin: $origin;
}

@mixin animation($animation) {
    -webkit-animation: $animation;
    -moz-animation: $animation;
    -o-animation: $animation;
    animation: $animation;
}

@mixin display-flex {
    display: -webkit-box;
    display: -webkit-flex;
    display: -moz-flex;
    display: -ms-flexbox;
    display: flex;
}

@mixin display-inline-flex {
    display: -webkit-inline-box;
    display: -webkit-inline-flex;
    display: -moz-inline-flex;
    display: -ms-inline-flexbox;
    display: -inline-flex;
}

@mixin flex($flex-value) {
    -ms-flex: $flex-value;
    -moz-flex: $flex-value;
    -webkit-flex: $flex-value;
    flex: $flex-value;
}

@mixin flex-space-between {
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    -moz-justify-content: space-between;
    -webkit-justify-content: space-between;
    justify-content: space-between;
}

@mixin flex-space-around {
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    -moz-justify-content: space-around;
    -webkit-justify-content: space-around;
    justify-content: space-around;
}

@mixin flex-center {
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    -moz-justify-content: center;
    -webkit-justify-content: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    -moz-align-items: center;
    -webkit-align-items: center;
    align-items: center;
}

@mixin flex-align-center {
    -webkit-box-align: center;
    -ms-flex-align: center;
    -moz-align-items: center;
    -webkit-align-items: center;
    align-items: center;
}

@mixin flex-align-start {
    -webkit-box-align: flex-start;
    -ms-flex-align: flex-start;
    -moz-align-items: flex-start;
    -webkit-align-items: flex-start;
    align-items: flex-start;
}

@mixin flex-align-end {
    -webkit-box-align: flex-end;
    -ms-flex-align: flex-end;
    -moz-align-items: flex-end;
    -webkit-align-items: flex-end;
    align-items: flex-end;
}

@mixin align-content-start {
    -ms-align-content: flex-start;
    align-content: flex-start;
}

@mixin flex-justify-space-between {
    -webkit-box-pack: space-between;
    -ms-flex-pack: space-between;
    -moz-justify-content: space-between;
    -webkit-justify-content: space-between;
    justify-content: space-between;
}

@mixin flex-justify-center {
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    -moz-justify-content: center;
    -webkit-justify-content: center;
    justify-content: center;
}

@mixin flex-justify-end {
    -webkit-box-pack: flex-end;
    -ms-flex-pack: flex-end;
    -moz-justify-content: flex-end;
    -webkit-justify-content: flex-end;
    justify-content: flex-end;
}

@mixin align-self-center {
    -moz-align-self: center;
    -ms-flex-item-align: center;
    -webkit-align-self: center;
    align-self: center;
}

@mixin align-self-flex-end {
    -moz-align-self: flex-end;
    -ms-flex-item-align: flex-end;
    -webkit-align-self: flex-end;
    align-self: flex-end;
}

@mixin flex-wrap {
    -ms-flex-wrap: wrap;
    -moz-flex-wrap: wrap;
    -webkit-flex-wrap: wrap;
    flex-wrap: wrap;
}

@mixin align-content-start {
    align-content: flex-start;
    -ms-align-content: flex-start;
}

@mixin align-content-end {
    align-content: flex-end;
    -ms-align-content: flex-end;
}

// -ms-flex: <positive-flex> <negative-flex> <preferred-size>
// -ms-flex-positive, -ms-flex-negative and -ms-flex-preferred-size
@mixin flex-grow-value($val) {
    display: inline-block\9;
    -moz-flex-grow: $val;
    -ms-flex-positive: $val;
    -webkit-flex-grow: $val;
    flex-grow: $val;
}

@mixin flex-shrink-value($val) {
    display: inline-block\9;
    -moz-flex-shrink: $val;
    -ms-flex-negative: $val;
    -webkit-flex-shrink: $val;
    flex-shrink: $val;
}

@mixin flex-grow {
    -ms-flex: 1 1 auto;
    -moz-flex: 1 1 auto;
    -webkit-flex: 1 1 auto;
    flex: 1 1 auto;
}

@mixin flex-shrink {
    -ms-flex: 0 0 auto;
    -moz-flex: 0 0 auto;
    -webkit-flex: 0 0 auto;
    flex: 0 0 auto;
}

@mixin flex-basis {
    -moz-flex: 1;
    -ms-flex: 1;
    -webkit-flex: 1;
    flex: 1;
}

@mixin flex-basis-value($val) {
    -moz-flex-basis: $val;
    -ms-flex-basis: $val;
    -webkit-flex-basis: $val;
    flex-basis: $val;
}

@mixin flex-column {
    display: -moz-flex;
    display: -ms-flexbox;
    -moz-flex-direction: column;
    -ms-flex-direction: column;

    display: -webkit-flex;
    -webkit-flex-direction: column;
    display: flex;
    flex-direction: column;
}

@mixin flex-column-reverse {
    display: -moz-flex;
    display: -ms-flexbox;
    -moz-flex-direction: column-reverse;
    -ms-flex-direction: column-reverse;

    display: -webkit-flex;
    -webkit-flex-direction: column-reverse;
    display: flex;
    flex-direction: column-reverse;
}

@mixin flex-row {
    display: -moz-flex;
    display: -ms-flexbox;
    -moz-flex-direction: row;
    -ms-flex-direction: row;

    display: -webkit-flex;
    -webkit-flex-direction: row;
    display: flex;
    flex-direction: row;
}

@mixin flex-grow-column {
    display: -moz-flex;
    display: -ms-flexbox;
    -moz-flex-direction: column;
    -ms-flex-direction: column;
    -ms-flex: 1 1 auto;
    -moz-flex: 1 1 auto;

    display: -webkit-flex;
    -webkit-flex-direction: column;
    -webkit-flex: 1 1 auto;
    display: flex;
    flex: 1 1 auto;
    flex-direction: column;
}

@mixin flex-grow-row {
    display: -moz-flex;
    display: -ms-flexbox;
    -moz-flex-direction: row;
    -ms-flex-direction: row;
    -ms-flex: 1 1 auto;
    -moz-flex: 1 1 auto;

    display: -webkit-flex;
    -webkit-flex-direction: row;
    -webkit-flex: 1 1 auto;
    display: flex;
    flex: 1 1 auto;
    flex-direction: row;
}

@mixin flex-wrap {
    -ms-flex-wrap: wrap;
    -moz-flex-wrap: wrap;
    -webkit-flex-wrap: wrap;
    flex-wrap: wrap;
}

@mixin ie-flex {
    -ms-flex: 0 1 auto;
    display: inline-block;
}

@mixin icon-font {
    font-family:"iconfont" !important;
    font-size:16px;
    font-style:normal;
    -webkit-font-smoothing: antialiased;
    -webkit-text-stroke-width: 0.2px;
    -moz-osx-font-smoothing: grayscale;
}

@mixin triangle($width, $height, $color) {
    border-style: solid;
    border-width: $width $height 0 0;
    border-color: $color transparent transparent transparent;
    content: '';
    height: 0;
    left: 0;
    position: absolute;
    width: 0;
    top: 0;
}

@mixin user-select-none {
    -webkit-touch-callout: none; /* iOS Safari */
    -webkit-user-select: none; /* Chrome/Safari/Opera */
    -khtml-user-select: none; /* Konqueror */
    -moz-user-select: none; /* Firefox */
    -ms-user-select: none; /* Internet Explorer/Edge */
    user-select: none; /* Non-prefixed version, currently not supported by any browser */
}

@mixin radiobox($dpi) {
    width: 13px * $dpi;
    height: 13px * $dpi;
    border-radius: 13px * $dpi;
    border: 1px * $dpi solid $font-lightest;
    display: inline-block;
    position: relative;
    background-color: $white-color;
    box-sizing: border-box;
    top: -1px * $dpi;
    left: 0;
    vertical-align: middle;

    &:after {
        @include transition(all .2s cubic-bezier(.12,.4,.29,1.46) .1s);
        @include transform(scale(0));
        opacity: 0;
        position: absolute;
        width: 5px * $dpi;
        height: 5px * $dpi;
        left: 3px * $dpi;
        top: 3px * $dpi;
        border-radius: 5px * $dpi;
        display: table;
        border-top: 0;
        border-left: 0;
        background-color: $primary-color;
        content: ' ';
    }

    &.checked {
        &:after {
            @include transform(scale(1));
            opacity: 1;
        }
    }

    &.disabled {
        background-color: $font-lightest;

        &:after {
            background-color: $white-color;
        }
    }

    &:hover {
        border-color: $primary-color;
    }

    + input[type=radio] {
        display: none
    }
}

@mixin checkbox($dpi) {
    width: 12px * $dpi;
    height: 12px * $dpi;
    border: 1px * $dpi solid $font-lightest;
    display: inline-block;
    position: relative;
    background-color: $white-color;
    box-sizing: border-box;
    top: -1px * $dpi;
    left: 0;
    vertical-align: middle;
    line-height: 1;

    &:after {
        @include transition(all .2s cubic-bezier(.12,.4,.29,1.46) .1s);
        @include transform(rotate(45deg) scale(0));
        opacity: 0;
        position: absolute;
        width: 3px * $dpi;
        height: 6px * $dpi;
        left: 3px * $dpi;
        top: 0;
        border: 2px * $dpi solid $primary-color;
        display: table;
        border-top: 0;
        border-left: 0;
        content: '';
    }

    &.chk-checked {
        &:after {
            @include transform(rotate(45deg) scale(1));
            opacity: 1;
        }
    }

    &.disabled {
        background-color: $font-lightest;

        &:after {
            background-color: $white-color;
        }
    }

    &:hover {
        border-color: $primary-color;
    }

    + input[type=radio] {
        display: none
    }
}

@mixin clickable {
    cursor: pointer;

    &:hover {
        opacity: .7;
    }

    &:active {
        opacity: 1;
    }

    &.disabled {
        cursor: default;
        opacity: .4;
    }
}

@mixin clearfix {
    &::after {
        content: ' ';
        display: table;
        clear: both;
    }
}
