@import 'base';
@import 'auth';
@import 'register';
@import 'user_settings';
@import 'two_step_verify';
@import 'validate.scss';
@import 'activation.scss';

html,body, .wrapper {
    width: 100%;
    height: 100%;
    line-height: 1;
}

.check_channel_coupon {
    margin: 0;
    padding: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(180deg, #E0EFFF 0%, rgba(255, 255, 255, 0.00) 65.76%), #FFF;

    .page-recommend-head {
        display: flex;
        width: 100%;
        height: 60px;
        padding: 0px 20px;
        justify-content: space-between;
        align-items: center;
        box-sizing: border-box;

        > div {
            display: flex;
            align-items: center;
        }

        .head-link {
            height: 24px;
        }

        .logo {
            width: 130px;
            height: 24px;
        }

        .title {
            color: #252E36;
            font-family: PingFang SC;
            font-size: 16px;
            font-style: normal;
            font-weight: 500;
            margin-left: 25px;
            position: relative;

            &:after {
                content: '';
                display: block;
                height: 24px;
                width: 1px;
                position: absolute;
                left: -13px;
                top: 0;
                background-color: #bacadd;
            }
        }

        .app {
            display: flex;
            align-items: center;
            color: #252E36;
            font-family: PingFang SC;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 1;
            margin-right: 32px;

            img {
                width: 24px;
                height: 24px;
                margin-right: 4px;
            }
        }

        .phone {
            display: flex;
            align-items: center;
            color: #252E36;
            font-family: PingFang SC;
            font-size: 16px;
            font-style: normal;
            font-weight: 600;
            line-height: 1;
            margin-right: 32px;

            img {
                width: 24px;
                height: 24px;
                margin-right: 4px;
            }
        }

        .login {
            display: flex;
            width: 80px;
            height: 32px;
            justify-content: center;
            align-items: center;
            border-radius: 3px;
            background: #017FFD;
            color: var(--unnamed, #FFF);
            font-family: PingFang SC;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            cursor: pointer;
        }
    }

    .page {
        width: 494px;
        margin: 0 auto;
        margin-top: 8px;

        .check {
            width: 494px;
            height: 435px;
            background: url('https://p3.ssl.qhimg.com/t01c5bc9eccad144552.png') no-repeat;
            background-size: 100%;
            padding: 40px;
            padding-top: 204px;
            display: flex;
            flex-direction: column;
            box-sizing: border-box;

            > div, > a {
                height: 40px;
                display: flex;
                align-items: center;
                margin-bottom: 24px;

                span {
                    color: #05417E;
                    font-family: PingFang SC;
                    font-size: 20px;
                    font-style: normal;
                    font-weight: 600;
                    display: block;
                    width: 80px;
                    margin-right: 8px;
                }

                input {
                    display: flex;
                    width: 308px;
                    height: 40px;
                    padding: 10px 12px;
                    flex-direction: column;
                    justify-content: center;
                    box-sizing: border-box;
                    font-size: 16px;
                    border-radius: 3px;
                    border: 1px solid #CBDBEC;
                    background: #FFF;
                }
            }

            .capt {
                .input-group {
                    display: flex;
                }

                input {
                    width: 194px;
                    margin-right: 12px;
                }

                img {
                    width: 100px;
                    height: 40px;
                }
            }

            > span {
                display: flex;
                width: 396px;
                height: 48px;
                justify-content: center;
                align-items: center;
                border-radius: 24px;
                background: linear-gradient(122deg, #CA34FF 1.09%, #7857FF 67.01%);
                color: #FFF;
                font-family: PingFang SC;
                font-size: 20px;
                font-style: normal;
                font-weight: 500;
                margin-top: 16px;
                cursor: pointer;
            }
        }

        .check_channel {
            width: 482px;
            height: 174px;
            background: url('https://p1.ssl.qhimg.com/t0122f2ef2bb9991ae3.png') no-repeat;
            background-size: 100%;
            margin-top: 32px;
        }
    }
}