import fc, { config } from './utils/common';
import <PERSON>c<PERSON>alidate from './utils/PicValidate';
import CountryPhoneGroup from './utils/CountryPhoneGroup';
import MobilePhoneSelect from './utils/MobilePhoneSelect';
import {defineMessage} from '@q/fang-intl';
import * as API from './constants/API';
import Tooltip from 'tooltip.js';
import Encrypt from './utils/rsaEncrypt';
// import { QWebChannel } from './utils/qwebchannel';

let countryPhoneGroup;
let picValidate;
let qrcode;
let ApiKey;

function GetQueryString(name) {
    const reg = new RegExp(`(^|&)${name}=([^&]*)(&|$)`, 'i');
    const r = window.location.search.substr(1).match(reg);
    if ( r != null) {
        return r[2];
    }
    return null;
}

class Qrcode {
    constructor() {
        this.status = 0;
        this.refresh = true;
        this.timer = null;
        this.init();
    }

    init() {
        this.$container = document.querySelector('.qrcode-container');
        this.$qrcode = document.querySelector('.qr-code img');
        this.$success = document.querySelector('.scan-success-container');
        this.$expire = document.querySelector('.login-expire-container');
        this.bindEvents();
    }

    getQrcodeImage() {
        return `${API.QR_LOGIN_IMAGE}${this.token}?t=${new Date().getTime()}`;
    }

    checkToken() {
        let data = {};
        if(this.token) {
            data.token = this.token;
        }

        const isSync = !!document.getElementById('is_sync');
        let url = "";
        if (document.getElementById('oauth_login')) {
            url = API.OAUTH_QR_LOGIN_CHECK_TOKEN;
        } else {
            url = API.QR_LOGIN_CHECK_TOKEN;
            if(isSync) {
                url += '?from=sync';
            }
        }

        let headers = {};

        if(ApiKey) {
            headers.ApiKey = ApiKey;
        }

        if (fc.config.deviceSerialNumber) {
            headers['X-Device-Serial-Number'] = fc.config.deviceSerialNumber;
        }

        fc.ajax({
            url,
            method: 'POST',
            headers: {...headers},
            data,
            parser: (res) => {
                const { new_token, status, success, redirect } = res;
                if(success) {
                    if(status === undefined || status === 3 || redirect) {
                        fc.loginHandle(res);
                        return;
                    }

                    if(status === 1) {
                        if(this.refresh) {
                            this.refresh = false;
                            this.status = status;
                            this.token = new_token;
                            this.getQrcode();
                        } else {
                            this.QrcodeExpired();
                            this.stopScan();
                        }
                    }

                    if(status !== 3 && this.token) {
                        this.timer = setTimeout(() => {
                            this.checkToken();
                        }, 2000);
                    }

                    if(status === 2 && this.status !== 2) {
                        this.status = 2;
                        this.scanSuccess();
                    }
                }
            }
        });
    }
    QrcodeExpired() {
        if(!this.$mask) {
            this.$mask = document.createElement('div');
            this.$mask.innerHTML = '<i class="iconfont icon-retry action-icon"></i>';
            document.querySelector('.qr-code').appendChild(this.$mask);
            fc.addClass(this.$mask, 'qrcode-mask');
            this.$mask.querySelector('.icon-retry').addEventListener('click', () => {
                this.refreshScan();
            });
        } else {
            this.$mask.style.display = 'block';
        }

        document.querySelector('.qr-expire-tip').style.display = 'inline';
        document.querySelector('.scan-tip').style.display = 'none';

    }

    getQrcode() {
        let src = this.getQrcodeImage();
        this.$qrcode.src = src;
    }

    stopScan(keepToken) {
        if(!keepToken) {
            this.token = null;
        }
        if(this.timer) {
            clearTimeout(this.timer);
            this.timer = null;
        }

        if(this.expireTimer) {
            clearTimeout(this.expireTimer);
            this.expireTimer = null;
        }
    }

    refreshScan() {
        this.refresh = true;
        this.stopScan();
        this.checkToken();
        if(this.$mask) {
            this.$mask.style.display = 'none';
            document.querySelector('.qr-expire-tip').style.display = 'none';
            document.querySelector('.scan-tip').style.display = 'inline';
        }
    }

    scanSuccess() {
        this.state = 'confirm';
        this.$container.style.display = 'none';
        this.$success.style.display = 'block';

        // 60秒内多次扫码
        if(this.expireTimer) {
            clearTimeout(this.expireTimer);
            this.expireTimer = null;
        }

        this.expireTimer = setTimeout(() => {
            this.$success.style.display = 'none';
            this.$expire.style.display = 'block';
            this.stopScan();
        }, 60000);
    }

    bindEvents() {
        this.$success.querySelector('.return-scan').addEventListener('click', () => {
            this.$container.style.display = 'block';
            this.$success.style.display = 'none';
            this.refreshScan();
        });

        this.$expire.querySelector('.refresh-scan').addEventListener('click', () => {
            this.$container.style.display = 'block';
            this.$expire.style.display = 'none';
            this.refreshScan();
        });

        this.$container.querySelector('.icon-retry').addEventListener('click', () => {
            this.refreshScan();
        });
    }
}

fc.ready(() => {

    [].forEach.call(document.querySelectorAll('.only-v2'), tip => {
        new Tooltip(tip, {
            html: true,
            placement: 'bottom',
            title: tip.getAttribute('data-title')
        });
    });

    // 获取 deviceSerialNumber 可能是异步的 所以需要放到延时里面
    setTimeout(() => {
        [].forEach.call(document.querySelectorAll('.bind-link'), link => {
            var href = link.getAttribute('href');
            if (config.deviceSerialNumber) {
                link.setAttribute('href', href + '&sn_code=' + config.deviceSerialNumber);
            }
        });
    }, 100);

    ApiKey = document.getElementById('api_key') && document.getElementById('api_key').value;

    // 中间页直接使用当前js 避免添加新的中间页跳转入口
    const $response = document.getElementById('results');
    let loginJson = $response && JSON.parse($response.value);
    if(loginJson) {
        if(window.IS_WEBENGINE){
            // require.ensure([
            //     './utils/qwebchannel'
            // ], (require) => {
                // TODO 异步加载
                const QWebChannel = require('./utils/qwebchannel').QWebChannel;
                /* global qt */
                new QWebChannel(qt.webChannelTransport, function(channel) {
                    window.fangcloud = channel.objects.sync_v2;
                    fc.loginHandle(loginJson);
                });
            // });
        } else {
            fc.loginHandle(loginJson);
        }
        return;
    }
    // init varibles
    // const scene = 'login';
    // const loginReadonly = document.getElementById('login').getAttribute('readonly');

    // bind events
    if(document.querySelector('.switch-box')) {
        qrcode = new Qrcode();
        initTabChange();
    }

    initCountryPhone(!!document.querySelector('.international-form'));
    validatePhone();
    stantardLogin();

    // sync register
    if(document.querySelector('.client-register')) {
        document.querySelector('.client-register').addEventListener('click', function(ev){
            ev.preventDefault();
            var url = API.REGISTER;
            if (!!GetQueryString('is_ai_app')) {
                url = API.AI_REGISTER;
            }
            if(window.fangcloud && window.fangcloud.openInBrowser){
                window.fangcloud.openInBrowser(JSON.stringify({
                    url: window.location.origin + url
                }));
            }
        });

        document.querySelector('.forgot-pwd').addEventListener('click', function(ev){
            ev.preventDefault();
            var url = API.FORGOT;
            if(this.href.indexOf('forgot_for_international') >= 0) {
                url = API.INTERNATIONAL_FORGOT;
            }
            if(window.fangcloud && window.fangcloud.openInBrowser){
                window.fangcloud.openInBrowser(JSON.stringify({
                    url: window.location.origin + url
                }));
            }
        });
        if(document.querySelector('.qr-header-hint a')) {
            document.querySelector('.qr-header-hint a').addEventListener('click', function(ev){
                ev.preventDefault();
                let url = this.getAttribute('href');
                if(window.fangcloud && window.fangcloud.openInBrowser){
                    window.fangcloud.openInBrowser(JSON.stringify({
                        url
                    }));
                }
            });
        }
    }

});


function createPicValidate() {
    picValidate = new PicValidate({
        picUrl: API.LOGIN_PIC_CAPTHA
    });

    picValidate.on('destroy', function() {
        fc.removeElement(this.$el);
    });
    // picValidate.on('hide', function(){
    //     fc.checkValidate();
    // });

    // picValidate.on('show', function(){
    //     fc.checkValidate();
    // });

    const $form = document.querySelector('form');

    $form.insertBefore(picValidate.$el, fc.config.isMobile ? $form.querySelector('.action-group') : $form.querySelector('.remember-login'));
}


function validatePhone() {
    const $input = document.getElementById('login');
    $input.addEventListener('change', function() {
        const login = encodeURIComponent(countryPhoneGroup.getValue());
        const url = `${API.CHECK_LOGIN}?login=${login}`;
        fc.ajax({
            url,
            method: 'GET',
            parser: (res) => {
                const { is_pic_captcha_needed } = res;
                if(res.errors) {
                    // TODO
                    fc.insertError('phone',  defineMessage({id: 'validate.wrongPhone', defaultMessage: '请输入有效的手机号'}));
                }

                if( is_pic_captcha_needed) {
                    if( !picValidate ) {
                        createPicValidate();
                    } else {
                        picValidate.show();
                    }
                } else {
                    picValidate && picValidate.hide();
                }
            }
        });
    });
}

function initTabChange() {
    let currentTab = window.location.hash.slice(1);
    const tabs = document.querySelector('.switch-box');
    const $oauthDes = document.querySelector('.oauth-des');

    tabs.addEventListener('click', function(e) {
        const { target } = e;
        const { className } = target;
        const sibling = target.nextElementSibling || target.previousElementSibling;

        if(className && className.indexOf('active') < 0) {
            fc.addClass(target, 'active');
            fc.removeClass(sibling, 'active');
            let showElClass = target.getAttribute('data-view');
            let hideElClass = sibling.getAttribute('data-view');
            document.querySelector('.' + showElClass).style.display = 'block';
            document.querySelector('.' + hideElClass).style.display = 'none';

            if(showElClass === 'form') {
                qrcode.stopScan(true);
                if($oauthDes) {
                    fc.removeClass($oauthDes, 'center');
                }
            } else {
                qrcode.checkToken();
                if($oauthDes) {
                    fc.addClass($oauthDes, 'center');
                }
            }
        }
    });
    if(currentTab === 'qrcode-login') {
        fc.triggerNative(tabs.querySelector(`[data-view=${currentTab}]`), 'click');
        if($oauthDes) {
            fc.addClass($oauthDes, 'center');
        }
    }
}

function initCountryPhone(showCountry) {
    if(fc.config.isMobile) {
        countryPhoneGroup = new MobilePhoneSelect({
            element: document.querySelector('.login .input-group'),
            notShowCountry: !showCountry,
            showCountry,
            default_code: showCountry ? 1 : 86
        });
    } else {
        countryPhoneGroup = new CountryPhoneGroup({
            element: document.querySelector('.login .input-group'),
            notShowCountry: !showCountry,
            showCountry,
            countrySelectOpitons: {
                showSelector: true,
                default_code: showCountry ? 1 : 86
            }
        });
    }
}

function stantardLogin() {
    const bindLogin = !!document.querySelector('.bind-login');
    const isSync = !!document.getElementById('is_sync');

    const $form = document.querySelector('form');
    const button = $form.querySelector('.btn-primary');

    $form.addEventListener('submit', function(e){
        e.preventDefault();
        let data = {
            login: countryPhoneGroup.getValue(),
            password: fc.trim(document.getElementById('password').value)
        };

        data = Encrypt.makeDataSafely(data);

        if (document.getElementById('remember_login')) {
            data.remember_login = document.getElementById('remember_login').checked;
        }

        const $protocol = document.getElementById('protocol');
        if($protocol && !$protocol.checked) {
            fc.insertNormalError($form, defineMessage({id: 'base.needAcceptProtocal', defaultMessage: '请阅读并接受服务条款和服务等级协议'}));
            return false;
        }

        if( picValidate ) {
            data.pic_captcha = fc.trim(document.querySelector('[name=pic_captcha]').value);
        }
        let url = API.LOGIN_ACTION;
        if(bindLogin) {
            url = API.BIND_LOGIN_ACTION;
        } else if(fc.config.isMobile && window.fangcloud) {
            url = API.APP_LOGIN_ACTION;
        } else if(isSync) {
            url = API.SYNC_LOGIN_ACTION;
        } else if ( document.getElementById('oauth_login') ) {
            url = API.OAUTH_LOGIN_ACTION;
        }

        let headers = {};
        if(ApiKey) {
            headers.ApiKey = ApiKey;
        }

        if (fc.config.deviceSerialNumber) {
            headers['X-Device-Serial-Number'] = fc.config.deviceSerialNumber;
        }

        const from_page = !!GetQueryString('from_page');

        if(from_page) {
            data.from_page = decodeURIComponent(GetQueryString('from_page'))
        }

        data.login_type = fc.config.login_type;
        button.setAttribute('disabled', true);
        fc.ajax({
            url,
            method: 'POST',
            data,
            headers: {...headers},
            accept_all_errors: true,
            parser: (res) => {
                fc.loginHandle(res);
                if(res.errors) {
                    button.removeAttribute('disabled');
                    res.errors.forEach((error) => {
                        if(error.field === 'pic_captcha') {
                            if(picValidate) {
                                picValidate.show();
                                picValidate.refresh();
                            } else {
                                createPicValidate();
                            }
                        }
                    });
                }
            }
        });

    });

    fc.initFormEvents($form, true);

    const $protocol = document.getElementById('protocol');
    if($protocol) {
        $protocol.addEventListener('change', (e) => clearLoginError($form, e.target));
    }

    $form.addEventListener('focus', (e) => clearLoginError($form, e.target), true);
}

function clearLoginError($form, target) {
    let input = fc.matchSelector(target, 'input');
    let button = $form.querySelector('.btn-primary');
    let error = fc.nextElementSibling(button);
    if(input && error) {
        fc.removeElement(error);
        fc.removeClass(button.parentNode, 'error');
    }
}

// ajax Setup

// placeholder 兼容处理

// 多语言选项
