import fc from './utils/common';
import * as API from './constants/API';

fc.ready(() => {
    // 二维码未过期
    if(document.querySelector('.confirm-box')) {
        document.querySelector('.btn-primary').addEventListener('click', e => {
            e.preventDefault();
            fc.ajax({
                url: API.QR_CONFIRM_LOGIN,
                data: {
                    token: document.getElementById('token').value
                },
                accept_all_errors: true,
                method: 'GET',
                parser: (res) => {
                    if(res.success) {
                        if(window.fangcloud && window.fangcloud.close){
                            window.fangcloud.close();
                        }
                    } else {
                        // TODO 二维码已失效 显示expire-box
                        fc.alert(res.errors[0].error_msg);
                    }
                }
            });
        });

        document.querySelector('.cancel').addEventListener('click', e => {
            e.preventDefault();
            if(window.fangcloud && window.fangcloud.close){
                window.fangcloud.close();
            }
        });
    }

    document.querySelector('.re-scan').addEventListener('click', e => {
        e.preventDefault();
        if(window.fangcloud && window.fangcloud.close){
            window.fangcloud.close();
        }
    });

    setTimeout(() => {
        document.querySelector('.expire-box').style.display = 'block';
        if(document.querySelector('.confirm-box')) document.querySelector('.confirm-box').style.display = 'none';
    }, 60000);

});