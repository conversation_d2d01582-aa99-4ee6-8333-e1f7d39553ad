import fc from './utils/common';
import Cookies from 'js-cookie';

fc.ready(() => {
    fc.initInputAction(document.getElementById('enterprise_key'));
    initFormAction();
});

function initFormAction() {
    const $form = document.querySelector('form');
    const $api_key = document.getElementById('api_key');
    // const $api_url = document.getElementById('api_url');
    const $enterprise_key = document.getElementById('enterprise_key');
    let api_key = $api_key && $api_key.value;

    if(!api_key){
        if(Cookies.get('api_key')){
            api_key = Cookies.get('api_key');
        }
    }

    $form.addEventListener('submit', function(e) {
        e.preventDefault();
        const target = $enterprise_key.value.trim();

        if(!target) {
            fc.insertError('enterprise_key', '必须输入企业代码或域名！');
            return false;
        }

        window.fangcloud && window.fangcloud.getProductInfo(encodeURIComponent(target), function() {
            fc.insertError('enterprise_key', '请输入正确的代码或域名！');
        });
        // fc.ajax({
        //     url: `${$api_url.value}sso/api/enterprise_info?target=${encodeURIComponent($enterprise_key.value)}`,
        //     method: 'GET',
        //     accept_all_errors: true,
        //     parser: (res) => {

        //     }
        // });
    });


}