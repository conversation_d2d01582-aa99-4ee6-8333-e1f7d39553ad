import fc from './utils/common';
import Dialog from './utils/Dialog';
import CountryPhoneGroup from './utils/CountryPhoneGroup';
import SmsValidate from './utils/SmsValidate';
import PicValidate from './utils/PicValidate';
import Notification from './utils/Notification';
import {defineMessage} from '@q/fang-intl';
import twoStepVerify from './utils/twoStepVerify';
import * as API from './constants/API';
// import './css/user_settings.scss';
import Tooltip from 'tooltip.js';
import Cookies from 'js-cookie';
import Encrypt from './utils/rsaEncrypt';
import CryptoJS from 'crypto-js';

let thirdPartyQuery = {
    'wechat': 0,
    'dingtalk': 1,
    'qihoo360': 2
};

let credentials;
let v2_picValidate;

fc.ready(() => {
    // 企业微信注册过的用户隐藏相关按钮，此为标记点
    if (document.getElementById('is_from_wechat_enterprise')) {
        const hideList = ['.two-step-verification-filed', '.password-setting-filed', '.login-credentials-filed'];
        hideList.forEach((item) => {
            document.querySelector(item).className += ' hidden';
        });
    }

    if (!document.getElementById('hide-account-info')) {
        renderAccountInfo();
        document.querySelector('.edit-password') && document.querySelector('.edit-password').addEventListener('click', editPassword);
        document.querySelector('.account-settings') && document.querySelector('.account-settings').addEventListener('click', handleEdit);
        if(document.querySelector('.third-party')) {
            thirdPartyInit();
        }
        postMessageListener();
        initTwoStepVerify();

        // 客户端绑定第三方提示
        let bindInfo = Cookies.get('bindInfo');
        if(bindInfo) {
            handleBindData(bindInfo);
            let domain_array = window.location.host.split('.');
            let domain = '.' + domain_array.slice(domain_array.length - 2).join('.');
            Cookies.remove('bindInfo', {path: '/', domain});
        }

        // 混合云未设置密码 隐藏修改密码按钮
        if(!!document.getElementById('set-password')) {
            document.querySelector('.edit-password').style.display = 'none';
        }
    }



    initDeviceManage();

    let twoStepType = Cookies.get('set_two_step_type');
    if(twoStepType) {
        let { product_name } = fc.config;
        Cookies.remove('set_two_step_type');
        if(twoStepType === 'modify') {
            fc.alert({
                main: defineMessage({id: 'settings.twoStepVerification.modifyVerifySuccessTitle', defaultMessage: '二次验证已修改'})
            });
        } else {
            fc.alert({
                main: defineMessage({id: 'settings.twoStepVerification.setVerifySuccessTitle', defaultMessage: '二次验证已开启'}),
                sub: defineMessage({id: 'settings.twoStepVerification.setVerifySuccessDes', defaultMessage: '二次验证已开启，每次登录{0}时，除帐号密码之外，还需要输入安全验证码。'}, product_name)
            });
        }
    }

    initAPIToken();

});

function handleBindData(data, render) {
    let res;
    if(data && typeof data === 'string') {
        res = JSON.parse(data);
        if(res.errors) {
            fc.alert({
                main: defineMessage({id: 'settings.thirdPartyBindFailure', defaultMessage: '绑定失败'}),
                sub: decodeURIComponent(res.errors.error_msg)
            });
        } else {
            if(render) {
                renderThirdParty();
            }
            setTimeout(() => {
                new Notification({
                    message: res.message
                });
            }, 300);
        }
    }
}

function postMessageListener() {
    window.addEventListener('message', (e) => {
        let origin = e.origin || e.originalEvent.origin;
        let { data } = e;
        handleBindData(data, true);
    });


}

function editPassword(e){
    e.preventDefault();
    let dialog = new Dialog({
        title: defineMessage({id: 'settings.editPassword', defaultMessage: '修改密码'}),
        content: `<div class="form password-edit">
            <p class="label-hint">${fc.passwordStrength()}</p>
            <div class="form-group">
                <div class="input-group">
                    <input type="password" name="old_password" class="password text form-control" placeholder="${defineMessage({id: 'settings.oldPassword', defaultMessage: '旧密码'})}" autocomplete="off">
                </div>
            </div>
            <div class="form-group">
                <div class="input-group">
                    <input type="password" name="password" class="password text form-control" placeholder="${defineMessage({id: 'settings.newPassword', defaultMessage: '新密码'})}" autocomplete="off">
                </div>
            </div>
            <div class="form-group">
                <div class="input-group">
                    <input type="password" name="password_confirmation" class="password text form-control" placeholder="${defineMessage({id: 'settings.confirmPassword', defaultMessage: '确认密码'})}" autocomplete="off">
                </div>
            </div>
        </div>`,
        confirmText: defineMessage({id: 'base.submit', defaultMessage: '提交'}),
        success: () => {
            fc.ajax({
                url: API.CHANGE_PASSWORD,
                data: Encrypt.makeDataSafely({
                    old_password: fc.trim(dialog.layer.querySelector('[name=old_password]').value),
                    password: fc.trim(dialog.layer.querySelector('[name=password]').value),
                    password_confirmation: fc.trim(dialog.layer.querySelector('[name=password_confirmation]').value)
                }),
                accept_all_errors: true,
                parser: (res) => {
                    if(res.success) {
                        // window.location.href = '/register';
                        dialog.destroy();
                        fc.alert(defineMessage({id: 'settings.editPasswordSuccess', defaultMessage: '密码修改成功，请重新登录'}), () => {
                            if(window.fangcloud || window.sync_v2) {
                                if(window.fangcloud) {
                                    window.fangcloud.userRelogin();
                                } else {
                                    window.sync_v2.userRelogin();
                                }
                            } else {
                                window.location.reload();
                            }
                        }, defineMessage({id: 'base.confirm', defaultMessage: '确认'}));
                    } else {
                        res.errors.forEach((error) => {
                            fc.insertError(error.field, error.error_msg, dialog.$('.form'));
                        });
                    }
                }
            });
        }
    }).show();

    // var validate_tip = new Tip({
    //     trigger: '[data-title]',
    //     delegateNode: this.$node,
    //     themeClass: 'tip-w250 validate-tip',
    //     width: 'auto',
    //     pos: 'bottom'
    // });
    dialog.$('.get-detail') && dialog.$('.get-detail').addEventListener('click', function() {
        if(window.fangcloud && window.fangcloud.popWebUrl) {
            window.fangcloud.popWebUrl(this.getAttribute('href'));
        }
    });

    fc.initFormEvents(dialog.layer);
}

function renderAccountInfo() {
    const credentialTypes = {
        '1': defineMessage({id: 'settings.email', defaultMessage: '邮箱：'}),
        '2': defineMessage({id: 'settings.phone', defaultMessage: '手机：'})
    };

    const emptyCredentialText = {
        '1': defineMessage({id: 'settings.emptyEmailInfo', defaultMessage: '绑定且验证后可用邮箱登录'}),
        '2': defineMessage({id: 'settings.emptyPhoneInfo', defaultMessage: '绑定后可用手机登录'})
    };

    const formatValue = (v,type) => {
        if (type == '2') {
           return fc.encryptPhone(v);
        } else if (type == '1') {
            return fc.encryptEmail(v);
        } else {
            return v;
        }
    };

    // status 0 2 -1
    let template = (credentials) => `
        <ul class="settings-form">
        ${credentials.map(({formatted_identifier, status, type, verified_at}) =>
            `<li class="form-group">
                <label for="" class="label">${credentialTypes[type]}</label>
                <div class="info-list">
                    <div class="info-item">
                        ${formatted_identifier ?
                            `<svg class="type-icon tip" aria-hidden="true"
                                data-title='${status == 2 || status == -1
                                    ? `<span class="validate-status">${defineMessage({id: 'settings.unvalidate', defaultMessage: '未验证'})}</span><span class="plain-text">${defineMessage({id: 'settings.unvalidateCredentialUsage', defaultMessage: '验证后可用于登录、找回密码'})}</span>`
                                    : `<span class="validate-status">${defineMessage({id: 'settings.validateTime', defaultMessage: '验证时间：{0}'}, fc.formatDate(verified_at * 1000, '%YY-%MM-%DD %hh:%mm'))}</span><span class="plain-text">${defineMessage({id: 'settings.validateCredentialUsage', defaultMessage: '可用于登录、找回密码'})}</span>`
                                }'
                            >
                                <use xlink:href="#icon-${status == 2 || status == -1 ? 'unverification1' : 'verification'}"></use>
                            </svg>`
                            : ''
                        }
                        ${formatted_identifier ?
                            `<span>${formatValue(formatted_identifier,type)}</span>`
                            : `<span class="unformatted">${emptyCredentialText[type]}</span>`
                        }
                        ${formatted_identifier && (status == 2 || status == -1) ?
                            `<a href="#" class="link-button action-icon edit" data-credential="${formatted_identifier}" data-role="${type}" data-action="validate" data-status="${status}">${defineMessage({id: 'settings.validate', defaultMessage: '立即验证'})}</a>`
                            : ''
                        }
                        ${formatted_identifier /*&& status == 2*/ ?
                            `<a href="#" class="link-button action-icon edit" data-credential="${formatted_identifier}" data-role="${type}" data-action="edit" data-status="${status}"><i class="iconfont icon-list-edit"></i></a>`
                            : ''
                        }
                        ${!formatted_identifier ?
                            `<a href="#" class="link-button action-icon edit" data-role="${type}" data-action="bind" data-status="${status}">${defineMessage({id: 'settings.bindImmediately', defaultMessage: '立即绑定'})}</a>`
                            : ''
                        }
                    </div>
                </div>
            </li>`
        ).join('')}
    </ul>`;

    fc.ajax({
        method: 'GET',
        url: API.GET_LOGIN,
        parser: (res) => {
            credentials = res.credentials;
            let types = [1, 2];

            credentials.forEach(({type}) => {
                let index = types.indexOf(type);
                if(index >= 0) {
                    types.splice(index, 1);
                }
            });

            if(types.length) {
                types.forEach((type) => {
                    credentials.push({
                        type,
                        formatted_identifier: '',
                        status: 2
                    });
                });
            }

                document.querySelector('.account-settings').innerHTML = template(credentials);

            [].forEach.call(document.querySelectorAll('.tip'), tip => {
                new Tooltip(tip, {
                    html: true,
                    title: tip.getAttribute('data-title')
                });
            });
        }
    });

}

function handleEdit(e, status, action) {
    e.preventDefault();
    let target = fc.matchSelector(e.target, '[data-role]');
    if(!target) {
        return false;
    }
    const role = target.getAttribute('data-role');//phone, email
    action = action ? action : target.getAttribute('data-action');// edit, validate, bind, unbind
    const credential = target.getAttribute('data-credential');
    status = status ? status : target.getAttribute('data-status');

    // 账号未验证 且是老账号迁移数据
    if(action === 'validate' && status == -1) {
        let url = role == 2 ? API.PRE_VERIFY_PHONE : API.PRE_VERIFY_EMAIL;
        fc.ajax({
            url,
            data: {credential},
            accept_all_errors: true,
            parser: (res) => {
                if(!res.success) {
                    let msg = role == 2 ? defineMessage({id: 'settings.phoneNotAvaliable', defaultMessage: '该手机号已被其它帐号占用，请修改。'})
                                        : defineMessage({id: 'settings.emailNotAvaliable', defaultMessage: '该邮箱已被其它帐号占用，请修改。'});

                    new Dialog({
                        message: {
                            main: msg
                        },
                        showTitle: false,
                        cancelText: defineMessage({id: 'base.cancelText', defaultMessage: '下次再说'}),
                        confirmText: defineMessage({id: 'settings.editNow', defaultMessage: '立即修改'}),
                        confirm: function() {
                            handleEdit(e, 2, 'edit');
                        }
                    }).show();
                } else {
                    handleEdit(e, 2);
                }
            }
        });

        // 直接在请求里面继续
        return false;
    }

    // confirmPassword(role, action, credential);
    let callback;
    // if(action === 'unbind') {
    //     callback = handleUnbind.bind(null, role);
    //     handleUnbind(type);
    // } else {
        // phone
        if(role == 2) {
            callback = hanleEditPhone.bind(null, action, credential);
        } else if(role == 1) {
            if(action === 'validate') {
                callback = bindEmail.bind(null, credential, true);
            } else if(action === 'edit' || action === 'bind') {
                if(action === 'edit' && credentials[1].status !== 0) {
                    fc.alert(defineMessage({id: 'settings.forbiddenEditEmail', defaultMessage: '暂时无法修改邮箱。请在手机号绑定成功后再试。'}));
                    return false;
                }
                callback = hanleEditEmail.bind(null, action, credential);
            }
        }
    // }
    new twoStepVerify(callback, action !== 'validate');
}

let countryPhoneGroup;
let smsValidate;
let picValidate;

function createPicValidate() {
    picValidate = new PicValidate({
        focusOnrender: false,
        picUrl: API.MODIFY_PHONE_PIC_CAPTHA
    });

    picValidate.on('destroy', function() {
        fc.removeElement(this.$el);
    });

    const $form = document.querySelector('.phone-set');
    $form.insertAdjacentElement('beforeend', picValidate.$el);
}

function hanleEditPhone(action, credential) {
    // 重置 picValidate
    picValidate = null;

    const messages = {
        edit: defineMessage({id: 'settings.editPhone', defaultMessage: '修改手机'}),
        validate: defineMessage({id: 'settings.validatePhone', defaultMessage: '验证手机'}),
        bind: defineMessage({id: 'settings.bindPhone', defaultMessage: '绑定手机'})
    };

    const toastMessages = {
        edit: defineMessage({id: 'settings.editPhoneSuccess', defaultMessage: '手机号已修改成功'}),
        validate: defineMessage({id: 'settings.validatePhoneSuccess', defaultMessage: '手机号已验证成功'}),
        bind: defineMessage({id: 'settings.bindPhoneSuccess', defaultMessage: '手机号已绑定成功'})
    };

    let content = `<div class="form phone-set ${action === 'validate' ? ' phone-validate' : ''}">
        ${action === 'validate' ? '' : `<p class="label-hint">${defineMessage({id: 'settings.currentCredential', defaultMessage: '当前绑定：{0}'}, credential)}</p>`}
        <div class="form-group phone">
            <div class="input-group">
                <input type="text" class="username text form-control" id="phone" name="phone" value="${action === 'validate' ? credential : ''}" placeholder="${defineMessage({id: 'settings.mobilePlaceholder', defaultMessage: '请输入你的手机号'})}">
            </div>
        </div>
    </div>`;

    let dialog = new Dialog({
        title: messages[action],
        content,
        needValidate: true,
        confirmText: defineMessage({id: 'base.submit', defaultMessage: '提交'}),
        success: () => {
            let data = {
                sms_captcha: smsValidate.getValue()
            };
            if(countryPhoneGroup) {
                data.phone = countryPhoneGroup.getValue();
            }
            fc.ajax({
                url: action === 'validate' ? API.VERIFY_PHONE_CREDENTIAL : API.UPDATE_PHONE,
                data,
                accept_all_errors: true,
                parser: (res) => {
                    if(res.success) {
                        // window.location.href = '/register';
                        dialog.destroy();
                        new Notification({
                            message: toastMessages[action]
                        });
                        renderAccountInfo();
                    } else {
                        res.errors.forEach((error) => {
                            fc.insertError(error.field, error.error_msg, dialog.$('.form'));
                        });
                    }
                }
            });
        }
    }).show();

    let layer = dialog.layer;

    countryPhoneGroup = new CountryPhoneGroup({
        element: layer.querySelector('.phone .input-group'),
        showCountry: true,
        countrySelectOpitons: {
            showSelector: true,
            default_code: 86
            // align_element: $('.register-form .phone .input-group')
        }
    });

    smsValidate = new SmsValidate({
        syncs: function() {
            let newDate = Math.floor(new Date().getTime() / 1000);

            let data = {
                type: 'modify_phone',
                phone: countryPhoneGroup.getValue(),
                ts: newDate,
                'signature': CryptoJS.MD5(`appId=${fc.activate_by_phone_id}&ts=${newDate}&phone=${countryPhoneGroup.getValue()}&type=modify_phone`).toString()
            };
            if (picValidate) {
                data.pic_captcha = picValidate.getValue();
            }

            if (v2_picValidate && v2_picValidate.captchaData) {
                data = Object.assign(data, v2_picValidate.captchaData);
                v2_picValidate.destroy();
            }

            return data;
        }
    });

    if(action === 'validate') {
        countryPhoneGroup.setDisabled();
        smsValidate.send();
    }

    smsValidate.on('destroy', function() {
        fc.removeElement(this.$el);
    });

    smsValidate.on('send_error', function(res){
        res.errors.forEach((error) => {
            fc.insertError(error.field, error.error_msg, dialog.$('.form'));
            if(error.field === 'pic_captcha') {
                if (picValidate) {
                    picValidate.refresh();
                } else {
                    createPicValidate();
                }
            } else if (error.field === 'sliding_pic_captcha') {
                v2_picValidate = fc.qHPassCaptcha.init('');
                return;
            }
        });
    });

    smsValidate.on('send_success', function(){
        if (picValidate) {
            picValidate.destroy();
            picValidate = null;
        }
    });

    const $form = layer.querySelector('.form');
    $form.insertAdjacentElement('beforeend' ,smsValidate.$el);
    fc.initFormEvents(dialog.layer);
}

function hanleEditEmail(action, credential) {
    const messages = {
        edit: defineMessage({id: 'settings.editEmail', defaultMessage: '修改邮箱'}),
        validate: defineMessage({id: 'settings.validateEmail', defaultMessage: '验证邮箱'}),
        bind: defineMessage({id: 'settings.bindEmail', defaultMessage: '绑定邮箱'})
    };
    let dialog = new Dialog({
        title: messages[action],
        needValidate: true,
        content: `<div class="form email-set">
            ${action === 'edit' ? `<p class="label-hint">${defineMessage({id: 'settings.currentCredential', defaultMessage: '当前绑定：{0}'}, credential)}</p>` : ''}
            <div class="form-group email">
                <div class="input-group">
                    <input type="text" class="username text form-control" id="email" name="email" value="" placeholder="${defineMessage({id: 'settings.enterNewEmail', defaultMessage: '请输入新的邮箱'})}">
                </div>
            </div>
        </div>`,
        confirmText: defineMessage({id: 'base.submit', defaultMessage: '提交'}),
        success: () => {
            let email = fc.trim(dialog.layer.querySelector('[name=email]').value);
            fc.ajax({
                url: API.UPDATE_EMAIL,
                data: {
                    email
                },
                accept_all_errors: true,
                parser: (res) => {
                    if(res.success) {
                        bindEmail(email, false);
                        dialog.destroy();
                    } else {
                        res.errors.forEach((error) => {
                            fc.insertError(error.field, error.error_msg, dialog.$('.form'));
                        });
                    }
                }
            });
        }
    }).show();

    fc.initFormEvents(dialog.layer);

}

function bindEmail(email, validate) {
    new Dialog({
        title: defineMessage({id: 'settings.bindEmail', defaultMessage: '绑定邮箱'}),
        content: `<div class="validate-email">
            <div class="content">${defineMessage({id: 'settings.emailSended', defaultMessage: '验证邮件已发送至{0}，请登录你的邮箱查看。验证后，该邮箱可以用于登录、找回密码。'}, email)}</div>
            <div class="hint">${defineMessage({id: 'settings.emailSendedTips', defaultMessage: '邮件可能会被拦截，如未在收件箱中找到，可尝试在垃圾邮件中查找'})}</div>
        </div>`,
        alert: () => {}
    }).show();

    if(validate) {
        fc.ajax({
            url: API.VERIFY_EMAIL,
            data: {email},
            parser: () => {
                renderAccountInfo();
            }
        });
    } else {
        renderAccountInfo();
    }
}

// function handleUnbind(type) {
//     const typeList = {
//         // 0: defineMessage({id: 'base.thirdParty', defaultMessage: '第三方帐号'}),
//         1: defineMessage({id: 'base.email', defaultMessage: '邮箱'}),
//         2: defineMessage({id: 'base.phone', defaultMessage: '手机'})
//     };

//     let dialog = new Dialog({
//         title: defineMessage({id: 'settings.unbind', defaultMessage: '解绑'}),
//         message: {
//             main: defineMessage({id: 'settings.unbindConfirmMain', defaultMessage: '你确定要解绑吗？'}),
//             sub: defineMessage({id: 'settings.unbindConfirmSub', defaultMessage: '解除绑定后将无法使用该{0}进行登录'}, typeList[type])
//         },
//         showTitle: false,
//         cancelText: defineMessage({id: 'base.cancelText', defaultMessage: '下次再说'}),
//         confirm: function() {
//             fc.ajax({
//                 url: API.UNBIND_ACCOUNT,
//                 data: {type},
//                 parser: (res) => {
//                     if(res.success) {
//                         dialog.destroy();
//                         renderAccountInfo();
//                     }
//                 }
//             });
//         }
//     }).show();
// }

function renderThirdParty() {
    let template = (thirdPartys) => `
        <ul class="third-party-list settings-form">
            ${thirdPartys.map(({id, icon, bind, nick, name}) =>
                id === 'dingtalk' && fc.config.login_type === 'sync' && !window.IS_WEBENGINE ? ''
                :
                `<li>
                    <label class="label">${name}</label>${bind ? `<span class="account">${nick}</span>` : ''}
                    <div class="actions">
                        ${bind ?
                            `<a href="#" class="link-button action-icon unbind" data-type="${id}" data-action="unbind">${defineMessage({id: 'settings.unbind', defaultMessage: '解绑'})}</a>`
                            : `<a ${ fc.config.login_type === 'sync' ? '' : 'target="_blank"'} href="${API.THIRD}/${id}login?${id === 'dingtalk' ? 'qrcode=true' : ''}&scene=bind&login_type=${fc.config.login_type}&redirect=/login_settings" class="link-button action-icon bind" data-action="bind">${defineMessage({id: 'settings.bindImmediately', defaultMessage: '立即绑定'})}</a>`
                        }
                    </div>
                </li>`
            ).join('')}
        </ul>
    `;

    fc.ajax({
        url: API.GET_THIRD_LOGIN,
        parser: (res) => {
            let thirdPartys = [{
                id: 'wechat',
                bind: false,
                icon: 'enterprise-wechat',
                name: defineMessage({id: 'settings.qiyeweixinLabel', defaultMessage: '企业微信：'})

            }, {
                id: 'dingtalk',
                bind: false,
                icon: 'dingtalk',
                name: defineMessage({id: 'settings.dingdingLabel', defaultMessage: '钉钉：'})
            }, {
                id: 'qihoo360',
                bind: false,
                icon: 'qihoo360',
                name: defineMessage({id: 'settings.qihoo360Label', defaultMessage: '360账号：'})
            }];
            if(res.accounts) {
                res.accounts.forEach(({type, nick}) => {
                    let index = thirdPartyQuery[type];
                    if(index >= 0) {
                        thirdPartys[index].nick = nick;
                        thirdPartys[index].bind = true;
                    }
                });
            }
            document.querySelector('.third-party').innerHTML = template(thirdPartys);
        }
    });
}

function thirdPartyInit() {
    renderThirdParty();
    let $thirdParty = document.querySelector('.third-party');
    let thirdTypes = {
        dingtalk:defineMessage({id: 'settings.dingding', defaultMessage: '钉钉'}),
        wechat: defineMessage({id: 'settings.qiyeweixin', defaultMessage: '企业微信'}),
        qihoo360: defineMessage({id: 'settings.qihoo360', defaultMessage: '360'})
    }
    $thirdParty.addEventListener('click', (e) => {
        let { target } = e;
        if(fc.hasClass(target, 'link-button') && target.getAttribute('data-action') === 'unbind') {
            e.preventDefault();
            let type = target.getAttribute('data-type');
            let account = fc.previousElementSibling(target.parentNode).innerText;

            let dialog = new Dialog({
                title: defineMessage({id: 'settings.unbind', defaultMessage: '解绑'}),
                message: {
                    main: defineMessage({id: 'settings.unbindThirdConfirmMain', defaultMessage: '你确定要解绑{0}帐号“{1}”吗？'}, thirdTypes[type], account),
                    sub: defineMessage({id: 'settings.unbindConfirmSub', defaultMessage: '解绑后，将无法使用该帐号登录'})
                },
                showTitle: false,
                cancelText: defineMessage({id: 'base.cancelText', defaultMessage: '下次再说'}),
                confirm: function() {
                    fc.ajax({
                        url: API.UNBIND_THIRD_LOGIN,
                        data: {type},
                        parser: (res) => {
                            if(res.success) {
                                dialog.destroy();
                                renderThirdParty();
                            }
                        }
                    });
                }
            }).show();
        }

    });
}

function initDeviceManage() {
    const maxSize = 5;

    const deviceTypeName = {
        'Android App': defineMessage({id: 'settings.device.fromAndroid', defaultMessage: '（Android App）'}),
        'Egeio IOS App': defineMessage({id: 'settings.device.fromIos', defaultMessage: '（iOS App）'}),
        'Windows Sync': defineMessage({id: 'settings.device.fromWindowsSync', defaultMessage: '（Windows同步端）'}),
        'Mac Sync': defineMessage({id: 'settings.device.fromMacSync', defaultMessage: '（Mac同步端）'}),
        'Windows Desktop': defineMessage({id: 'settings.device.fromWindowsDesktop', defaultMessage: '（Windows客户端）'}),
        'Mac Desktop': defineMessage({id: 'settings.device.fromMacDesktop', defaultMessage: '（Mac客户端）'})
    };

    const iconListName = {
        'Egeio IOS App': 'icon-nav-download-pc',
        'Android App': 'icon-nav-download-pc',
        'Windows Sync': 'icon-navtables',
        'Mac Sync': 'icon-computer',
        'Mac Desktop': 'icon-computer',
        'Windows Desktop': 'icon-computer'
    };

    fc.ajax({
        url: API.GET_DEVICES,
        method: 'POST',
        parser: (res) => {
            const { devices } = res;
            let template = `<div class="device-list">
                <div class="list-head">
                    <div class="list-row">
                        <div class="list-col device-name">${defineMessage({id: 'settings.device.deviceName', defaultMessage: '设备名称'})}</div>
                        <div class="list-col device-last-login">${defineMessage({id: 'settings.device.deivceLastLogin', defaultMessage: '最近访问'})}</div>
                        <div class="list-col device-last-location">${defineMessage({id: 'settings.device.deivceLastLocation', defaultMessage: '最近访问地'})}</div>
                        <div class="list-col device-operation">${defineMessage({id: 'settings.device.deivceOperation', defaultMessage: '操作'})}</div>
                    </div>
                </div>
                <div class="list-body devices-container">
                    ${devices && devices.length ?
                        devices.map(({creditable, current, device_name, device_type, updated, ip_address: {ip, location}, id}) =>
                            `<div class="device-item list-row">
                                <div class="list-col device-name">
                                    <div class="device-name-detail" title="${device_name}${deviceTypeName[device_type] || ''}">
                                        <i class="iconfont ${iconListName[device_type] || 'icon-anzhuoduanliulanqidakai'}"></i>
                                        <span class="current-name ellipsis" title="">${device_name}</span>
                                        <span class="device-from">${deviceTypeName[device_type] || ''}</span>
                                        ${creditable ? `<span class="creditable">${defineMessage({id: 'settings.device.creditable', defaultMessage: '可信'})}</span>` : ''}
                                        ${current ? `<span class="current">${defineMessage({id: 'settings.device.current', defaultMessage: '当前'})}</span>` : ''}
                                    </div>
                                </div>
                                <div class="list-col device-last-login">${fc.formatDate(updated * 1000, '%R')}</div>
                                <div class="list-col device-last-location">
                                    <span>${location}</span>
                                    <i class="iconfont icon-ip" data-title="${defineMessage({id: 'settings.device.ip', defaultMessage: 'IP地址：{0}'}, ip)}"></i>
                                </div>
                            ${!current ?
                                `<div class="list-col device-operate action-box">
                                    <svg class="type-icon" aria-hidden="true" data-role="delete" data-id="${id}" title="${defineMessage({id: 'base.delete', defaultMessage: '删除'})}">
                                        <use xlink:href="#icon-delete"></use>
                                    </svg>
                                </div>`
                                : ''
                            }
                            </div>`
                        ).join('')
                        : `<div class="empty-item"><p>${defineMessage({id: 'settings.device.noresult', defaultMessage: '暂无数据'})}</p></div>`
                    }
                </div>
                ${devices.length > maxSize ? `<a class="show-more-button action-icon" data-role="show_more">${defineMessage({id: 'settings.device.showMore', defaultMessage: '展开全部'})}</a>` : ''}
            </div>`;

            let $container = document.querySelector('.device-manage');
            document.querySelector('.device-manage').innerHTML = template;

            [].forEach.call(document.querySelectorAll('.icon-ip'), tip => {
                new Tooltip(tip, {
                    html: true,
                    title: tip.getAttribute('data-title')
                });
            });

            $container.addEventListener('click', (e) => {
                let deleteTarget = fc.matchSelector(e.target, '[data-role=delete]');
                let showMoreTarget = fc.matchSelector(e.target, '.show-more-button');

                if(deleteTarget) {
                    let is_creditable, id = deleteTarget.getAttribute('data-id') - 0;
                    let msg = is_creditable ? defineMessage({id: 'settings.device.deleteIsCreditable', defaultMessage: '在该设备上将退出登录，你需要重新登录才能继续访问，再次登录需要进行二次验证。'}) : defineMessage({id: 'settings.device.deleteNotCreditable', defaultMessage: '在该设备上将退出登录，你需要重新登录才能继续访问。'});

                    let dialog = new Dialog({
                        message: {
                            main: defineMessage({id: 'settings.device.deleteDevice', defaultMessage: '删除此设备'}),
                            sub: msg
                        },
                        showTitle: false,
                        confirmText: defineMessage({id: 'base.delete', defaultMessage: '删除'}),
                        cancelText: defineMessage({id: 'base.cancelText', defaultMessage: '下次再说'}),
                        confirm: function() {
                            fc.ajax({
                                url: API.DELETE_DEVICE  ,
                                data: {id},
                                parser: () => {
                                    dialog.destroy();
                                    fc.removeElement(deleteTarget.parentNode.parentNode);
                                }
                            });
                        }
                    }).show();
                } else if(showMoreTarget) {
                    let container = document.querySelector('.devices-container');
                    if(fc.hasClass(container, 'show-more')) {
                        fc.removeClass(container, 'show-more');
                        showMoreTarget.innerText = defineMessage({id: 'settings.device.showMore', defaultMessage: '展开全部'});
                    } else {
                        fc.addClass(container, 'show-more');
                        showMoreTarget.innerText = defineMessage({id: 'settings.device.hideMore', defaultMessage: '收起'});
                    }
                }
            });



        }
    });
}

function initTwoStepVerify() {
    const $container = document.querySelector('.two-step-verification');
    if(!$container) return false;

    const twoStepVerifyStatus = {
        data: {
            // has_validate: true,//mod.user.two_step_status,
            // validate_time: fc.formatDate( 1514256157 * 1000, '%YY-%MM-%DD'),
            // validate_type: 'sms',//mod.user.two_step_method,
        },

        init: function() {
            fc.ajax({
                url: API.GET_TWO_STEP_METHOD,
                method: 'GET',
                parser: (res) => {
                    const { method, created, identity } = res;
                    this.data.has_validate = method !== 'none';
                    this.data.validate_time = fc.formatDate(created * 1000, '%YY-%MM-%DD');
                    this.data.validate_type = method;
                    this.data.identity = identity;
                    this.reset();
                }
            });
        },

        validateTypeTemplate: function({validate_type, identity}) {
            let validateTypeList = {
                sms: `<span class="validate-type">${defineMessage({id: 'settings.twoStepVerification.typePhone', defaultMessage: '手机短信验证'})}<span class="validata-type-hint">${defineMessage({id: 'settings.twoStepVerification.typePhoneNumber', defaultMessage: '（尾号为 {0}）'}, identity)}</span></span>`,
                wechat: `<span class="validate-type">${defineMessage({id: 'settings.twoStepVerification.typeWechat', defaultMessage: '微信公众号验证'})}</span>`,
                google: `<span class="validate-type">${defineMessage({id: 'settings.twoStepVerification.typeGoogle', defaultMessage: '谷歌验证器验证'})}</span>`
            };
            return validateTypeList[validate_type];
        },

        template: function({has_validate, validate_time}) {
            return has_validate ?
                `<div class="info-item">
                    <span class="validate-success">
                        <svg class="type-icon" aria-hidden="true">
                            <use xlink:href="#icon-verification"></use>
                        </svg>
                        ${defineMessage({id: 'settings.twoStepVerification.hasOpen', defaultMessage: '二次验证已开启'})}
                    </span>
                    <span class="validata-time">${defineMessage({id: 'settings.twoStepVerification.validateTime', defaultMessage: '（设置于 {0}）'}, validate_time)}</span>
                    <a class="action-button action-icon" href="#" data-role="close">${defineMessage({id: 'settings.twoStepVerification.toClose', defaultMessage: '关闭二次验证'})}</a>
                </div>
                <div class="info-item">
                    <span class="validate-type-title">${defineMessage({id: 'settings.twoStepVerification.type', defaultMessage: '验证方式：'})}</span>
                    ${this.validateTypeTemplate(this.data)}
                    <a class="action-button action-icon" href="#" data-role="change" title="${defineMessage({id: 'base.modify', defaultMessage: '修改'})}"><i class="iconfont icon-list-edit"></i></a>
                </div>`
                : `
                    <p class="hint">${defineMessage({id: 'settings.twoStepVerification.info', defaultMessage: '二次验证为你的帐户增加一层安全保护。启用二次验证后，每次登录亿方云时，除帐号密码之外，还需要输入安全验证码（验证码将发送到你的手机上）。'})}</p>
                    <a class="action-button action-icon" href="#" data-role="set">${defineMessage({id: 'settings.twoStepVerification.toSet', defaultMessage: '开启二次验证'})}</a>
                `;
        },

        reset: function(data) {
            this.data = {...this.data, ...data};
            $container.innerHTML = this.template(this.data);
        }
    };

    twoStepVerifyStatus.init();

    $container.addEventListener('click', (e) => {
        e.preventDefault();
        let target = fc.matchSelector(e.target, '[data-role]');
        if(!target) {
            return;
        }

        let role = target.getAttribute('data-role');

        if(role === 'close') {
            if(!!(document.querySelector('[name=is_two_step_enabled]').value - 0)) {
                fc.alert({
                    main: defineMessage({id: 'settings.twoStepVerification.toClose', defaultMessage: '关闭二次验证'}),
                    sub: defineMessage({id: 'settings.twoStepVerification.disableToClose', defaultMessage: '管理员开启了“企业成员二次验证”功能，你无法单独关闭二次验证。'})
                });
            } else {
                new twoStepVerify('close_two_step', () => twoStepVerifyStatus.init());
            }
        } else if(role === 'set'){
            new twoStepVerify('set_two_step');
        }else {
            new twoStepVerify('modify_two_step');
        }
    });
}

// HOUR_8:8小时， DAY_1:1天，DAY_7:7天，DAY_30：30天，NEVER_EXPIRE：用不过期
const API_TOKEN_MAP = {
    HOUR_8: '8小时',
    DAY_1: '1天',
    DAY_7: '7天',
    DAY_30: '30天',
    NEVER_EXPIRE: '永久有效'
};

function initAPIToken() {
    const $container = document.querySelector('.api-token');
    if(!$container) return false;

    const apiToken = {
        data: {
            due_time_type: 'HOUR_8',
            token: '暂无',
            start_time: '暂无',
            end_time: '暂无'
        },

        init: function() {
            fc.ajax({
                url: API.API_TOKEN_INFO,
                method: 'POST'
            }).then(res => {
                if (res.is_enable_refresh_token) {
                    document.querySelector('#j_api_token').style.display = 'block';

                    this.reset(res.open_api_token_info);
                }
            });
        },

        template: function({ due_time_type, token, start_time, end_time }) {
            return `
                <div class="info-item">
                    <div class='info-item-label'>有效期：</div>
                    <div class="info-item-content">
                        <div class='api-setting-wp'>
                           <div class="api-select-group" id="date_setting" data-role="select">
                                <input type="hidden" name="language" value="">
                                <div class="select">${API_TOKEN_MAP[due_time_type]}</div>
                                <i class="select-caret"></i>
                                <ul id="options_date_setting" class="select-option-panel">
                                    <li data-value="HOUR_8"  data-role="options">8小时</li>
                                    <li data-value="DAY_1"  data-role="options">1天</li>
                                    <li data-value="DAY_7"  data-role="options">7天</li>
                                    <li data-value="DAY_30"  data-role="options">30天</li>
                                    <li data-value="NEVER_EXPIRE"  data-role="options">永久有效</li>
                                </ul>
                            </div>
                            
                            <a class='action-link' data-role='token'>生成token</a>
                        </div>
                        <ul class='content-wp'>
                            <li>
                                <div class='content-label'>Token：</div>
                                <div>
                                    ${token}
                                    ${token && token !== '暂无' ? '<img class="copy-icon" data-role="copy" src="https://p1.ssl.qhimg.com/t110b9a93015bd9582991b3fa22.png" />' : ''}
                                </div>
                            </li>
                            <li>
                                <div class='content-label'>创建时间：</div>
                                <div>
                                    ${start_time}
                                </div>
                            </li>
                            <li>
                                <div class='content-label'>失效时间：</div>
                                <div>
                                    ${end_time}
                                </div>
                            </li>
                        </ul>
                    </div>
                </div>
            `;
        },

        reset: function(data) {
            this.data = {...this.data, ...data};
            $container.innerHTML = this.template(this.data);
        }
    };

    apiToken.init();

    const copyAction = () => {
        const $input = document.createElement('input');
        $input.value = apiToken.data.token;
        document.body.appendChild($input);
        $input.select();
        document.execCommand('copy');
        document.body.removeChild($input);
    };

    $container.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();
        let target = fc.matchSelector(e.target, '[data-role]');
        if(!target) {
            return;
        }

        let role = target.getAttribute('data-role');

        if (role === 'select') {
            document.querySelector('#options_date_setting').style.display = 'block';
        }

        if (role === 'options') {
            document.querySelector('#options_date_setting').style.display = 'none';
            const val = e.target.getAttribute('data-value');
            apiToken.data.due_time_type = val;
            apiToken.reset();
        }

        if (role === 'token') {
            fc.ajax({
                url: API.API_TOKEN_REFRESH,
                method: 'POST',
                data: {
                    due_time_type: apiToken.data.due_time_type
                }
            }).then(res => {
                apiToken.reset(res.open_api_token_info);
                copyAction();
                new Notification({
                    message: 'token已生成，已复制到剪贴板'
                });
            });
        }

        if (role === 'copy') {
            copyAction();
            new Notification({
                message: '已复制到剪贴板'
            });
        }

    });

    document.body.addEventListener('click', (e) => {
        const el = document.querySelector('#options_date_setting');
        if(!fc.matchSelector(e.target, '[data-role]') && el) {
            el.style.display = 'none';
        }
    });
}