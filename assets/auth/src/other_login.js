import fc from './utils/common';
import {defineMessage} from '@q/fang-intl';
import * as API from './constants/API';
import Cookies from 'js-cookie';


fc.ready(() => {
    fc.initInputAction(document.getElementById('enterprise_name'));
    initFormAction();
});

function initFormAction() {
    const $form = document.querySelector('form');
    const isSync = !!document.getElementById('is_sync');
    const $api_key = document.getElementById('api_key');
    let api_key = $api_key && $api_key.value;

    const fangcloud_url = document.getElementById('fangcloud_url').value;

    if(!api_key){
        if(Cookies.get('api_key')){
            api_key = Cookies.get('api_key');
        }
    }




    $form.addEventListener('submit', function(e) {
        e.preventDefault();
        fc.ajax({
            url: `${API.DEDICATED_ENTERPRISE_LOGIN}?alias=${document.getElementById('enterprise_name').value}`,
            method: 'GET',
            accept_all_errors: true,
            parser: (res) => {
                let login_url = 'sso/login';

                if(fc.config.isMobile && window.fangcloud) {
                    login_url = 'sso/api/mobile_login';
                } else if(isSync) {
                    login_url = 'sso/api/sync_login';
                } else if ( document.getElementById('oauth_login') ) {
                    login_url = 'sso/api/oauth_login';
                }

                if(res.product_id) {
                    let href = fangcloud_url + login_url + '?product_id=' + res.product_id + '&is_dedicated_enterprise=1';
                    window.location.href = href + (api_key ? '&api_key=' + api_key : '');
                } else if (res.errors && res.errors[0].error_code === 'public_sso_enterprise_not_found') {
                    // error_tr_msg
                    fc.insertError('enterprise_name', res.errors[0].error_msg);
                }
            }
        });

    });
}