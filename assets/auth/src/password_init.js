import fc from './utils/common';
import {defineMessage} from '@q/fang-intl';
import * as API from './constants/API';
import Cookies from 'js-cookie';
import Encrypt from './utils/rsaEncrypt';


fc.ready(() => {
    fc.initInputAction(document.getElementById('password'));
    document.querySelector('.password-hint').innerHTML = fc.passwordStrength();
    initFormAction();
});

function initFormAction() {
    const $form = document.querySelector('form');

    $form.addEventListener('submit', function(e) {
        e.preventDefault();
        fc.ajax({
            url: API.PASSWORD_INIT,
            method: 'POST',
            data: Encrypt.makeDataSafely({
                password: document.getElementById('password').value
            }),
            accept_all_errors: true,
            parser: (res) => {
                fc.loginHandle(res);
            }
        });

    });
}