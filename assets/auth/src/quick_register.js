import SmsValidate from './utils/SmsValidate';
import <PERSON><PERSON><PERSON>alidate from './utils/PicValidate';
import CountryPhoneGroup from './utils/CountryPhoneGroup';
import * as API from './constants/API';
import fc from './utils/common';
import { defineMessage } from '@q/fang-intl';
import { ACCOUNT_REGISTERED, PERSONAL_ACCOUNT_REGISTERED, PHONE_ALREADY_OCCUPIED_BUT_NOT_ACTIVATED, PHONE_ALREADY_OCCUPIED_ACTIVATION_EXPIRED, FREE_PLAN_LIMIT } from './constants/code';
import Encrypt from './utils/rsaEncrypt';
import Cookies from 'js-cookie';
import CryptoJS from 'crypto-js';

let smsValidate;
let picValidate;
let countryPhoneGroup;
let v2_picValidate;

const errorMsgMap = {
    'phone': defineMessage({ id: 'auth.register.phoneError', defaultMessage: '请输入正确的手机号' }),
    'sms_captcha': defineMessage({ id: 'auth.register.sms_captchaError', defaultMessage: '请输入正确的验证码' }),
    'password': defineMessage({ id: 'auth.register.passwordError', defaultMessage: '请设置正确的密码，8~32位数字和字母的组合' }),
    'email': defineMessage({ id: 'auth.register.emailError', defaultMessage: '请输入正确的邮箱' })
};

fc.ready(() => {
    if (document.querySelector('.phone .input-group')) {
        initCountryPhone();
        document.querySelector('input[name="phone"]').focus();
    }
    document.querySelector('.get-captcha-btn').addEventListener('click', function () {
        const $input = document.getElementById('phone');
        // 为空时直接报错
        if (!fc.trim($input.value)) {
            $input.focus();
            fc.insertError('phone', errorMsgMap['phone'], document.querySelector('.normal-register'));
            return;
        }
        // 校验手机号
        requestValidatePhone({
            scene: 'register',
            successCallback: () => {
                // 发送验证码
                if (!document.querySelector('.get-captcha-group.hidden')) {
                    createSmsValidate();
                    document.querySelector('input[name="sms_captcha"]').focus();
                    // 显示密码框和用户协议
                    fc.removeClass(document.querySelector('.password-group'), 'hidden');
                    fc.addClass(document.querySelector('.register-hint'), 'hidden');
                    fc.removeClass(document.querySelector('.fangcloud-protocol'), 'hidden');
                    // 按钮改为 注册按钮
                    fc.removeClass(document.querySelector('.register-group'), 'hidden');
                    fc.addClass(document.querySelector('.get-captcha-group'), 'hidden');
                }
            }
        });
    });

    normalRegister();
    validatePhone('register');

    // 用户离开页面时做的事
    window.onbeforeunload = reportRegisterInfo;
});

function initCountryPhone() {
    countryPhoneGroup = new CountryPhoneGroup({
        element: document.querySelector('.phone .input-group'),
        showCountry: true,
        countrySelectOpitons: {
            showSelector: true,
            default_code: 86
            // align_element: $('.register-form .phone .input-group')
        }
    });

    countryPhoneGroup.on('code_change', function ({ code }) {
        const $input = document.getElementById('phone');
        const $emailGroup = document.querySelector('.email.form-group');
        if (code == 86) {
            $input.setAttribute('maxLength', 11);
            fc.addClass($emailGroup, 'hidden');
        } else {
            $input.setAttribute('maxLength', 20);
            fc.removeClass($emailGroup, 'hidden');
        }
    });
}

function createPicValidate() {
    const $form = document.querySelector('.normal-register');
    picValidate = new PicValidate({
        picUrl: API.REGISTER_PIC_CAPTHA
    });

    smsValidate && smsValidate.setDisabled(1, 'get');

    picValidate.on('destroy', function () {
        fc.removeElement(this.$el);
        picValidate = null;
    });

    picValidate.on('change', function (value) {
        if (smsValidate && smsValidate.getState() !== 'working') {
            smsValidate.setDisabled(value ? 0 : 1, 'get');
        }
    });

    $form.querySelector('.form-group.phone').after(picValidate.$el)
}

function createSmsValidate() {
    smsValidate = new SmsValidate({
        syncs: function () {
            let newDate = Math.floor(new Date().getTime() / 1000);
            let data = {
                type: 'register',
                phone: countryPhoneGroup.getValue(),
                ts: newDate,
                'register-sign': CryptoJS.MD5(`appId=${fc.app_key}&ts=${newDate}&phone=${countryPhoneGroup.getValue()}&type=register`).toString()
            };

            if (picValidate) {
                data.pic_captcha = fc.trim(picValidate.getValue());
            }

            if (v2_picValidate && v2_picValidate.captchaData) {
                data = Object.assign(data, v2_picValidate.captchaData);
                v2_picValidate.destroy();
            }

            return data;
        }
    });

    // smsValidate.setDisabled(1, 'get');
    smsValidate.on('destroy', function () {
        fc.removeElement(this.$el);
    });

    smsValidate.on('send_error', function (res) {
        res.errors.forEach((error) => {
            fc.insertError(error.field || 'sms_captcha', error.error_msg, document.querySelector('.normal-register'));
            if (error.field === 'pic_captcha') {
                if (picValidate) {
                    picValidate.refresh();
                } else {
                    createPicValidate();
                }
            } else if (error.field === 'sliding_pic_captcha') {
                v2_picValidate = fc.qHPassCaptcha.init('');
                return;
            }
        });
    });

    smsValidate.on('send_success', function(){
        if (picValidate) {
            picValidate.destroy();
            picValidate = null;
        }
    });

    const $form = document.querySelector('.normal-register');
    // if (!document.querySelector('.sms-group'))
    $form.insertBefore(smsValidate.$el, $form.querySelector('.phone').nextSibling);

    smsValidate.send();
}

function requestValidatePhone({scene, eventName, successCallback = () => {}}) {
    const $input = document.getElementById('phone');
    const phone = encodeURIComponent(countryPhoneGroup.getValue());
    // 输入为空时不判断
    // $input.focus();
    if (!fc.trim($input.value)) {
        return false;
    }
    const url = `${API.CHECK_PHONE}${scene}?phone=${phone}`;
    fc.ajax({
        url,
        method: 'GET',
        accept_all_errors: true,
        parser: (res) => {
            const { is_pic_captcha_needed } = res;
            if (res.errors) {
                if (eventName === 'change') {
                    res.errors.forEach((error) => {
                        fc.insertError(error.field, error.error_msg, document.querySelector('.normal-register'));
                    });
                }
                smsValidate && smsValidate.getState() !== 'working' && smsValidate.setDisabled(1, 'get');
            } else {
                successCallback();
                fc.clearError($input);
                smsValidate && smsValidate.getState() !== 'working' && smsValidate.setDisabled(0, 'get');
            }

            // if (is_pic_captcha_needed) {
            //     if (!picValidate) {
            //         createPicValidate();
            //     } else {
            //         picValidate.show();
            //     }
            // } else {
            //     picValidate && picValidate.destroy();
            // }
        }
    });
}

function validatePhone(scene) {
    const $input = document.getElementById('phone');
    ['change_delay', 'change'].forEach((eventName) => {
        countryPhoneGroup.on(eventName, function () {
            requestValidatePhone({scene, eventName});
        });

    });

    ['keyup', 'afterpaste'].forEach(eventName => {
        $input.addEventListener(eventName, function () {
            this.value = this.value.replace(/\D/g, '');
        });
    });
}

function normalRegister() {
    const $form = document.querySelector('.normal-register');
    document.querySelector('.register-btn').addEventListener('click', () => {

        const phone = countryPhoneGroup.getValue();
        const code = countryPhoneGroup.getCode();
        const sms_captcha = fc.trim(smsValidate.getValue());

        const password = $form.querySelector('[name=password]').value;
        const email = fc.trim(document.querySelector('[name=email]').value);
        const plan_id = $form.querySelector('[name=plan_id]').value;
        const register_position = $form.querySelector('[name=register_position]').value;
        const invite_code = $form.querySelector('[name=invite_code]').value;
        const redirect = $form.querySelector('[name=redirect]').value;
        const bind = !!GetQueryString('bind');
        const activity_code = !!GetQueryString('activity_code');
        const from_page = !!GetQueryString('from_page');
        const ticket_id = !!GetQueryString('ticket_id');
        const register_channel = !!GetQueryString('register_channel');
        const referral_id = !!Cookies.get('referral_id');

        let data = { phone, sms_captcha, plan_id, password, login_type: fc.config.login_type, is_mobile: fc.config.isMobile, register_position };

        data = Encrypt.makeDataSafely(data);

        if (picValidate) {
            data.pic_captcha = picValidate.getValue();
        }

        if(redirect) {
            data.redirect = redirect;
        }
        if(invite_code) {
            data.invite_code = invite_code;
        }
        if (code !== '86') {
            data.email = email;
        }

        let url = bind ? API.BIND_REGISTER_ACTION : API.REGISTER_ACTION;

        let hasEmpty = checkEmpty(data, $form);
        if (hasEmpty) {
            return false;
        }
        if(activity_code){
            data.activity_code = GetQueryString('activity_code')
        }
        if(ticket_id) {
            data.channel_coupon = GetQueryString('ticket_id');
        } else if (register_channel) {
            data.register_channel = GetQueryString('register_channel');
        }

        if(from_page) {
            data.from_page = decodeURIComponent(GetQueryString('from_page'))
        }

        if (referral_id) {
            data.referral_id = decodeURIComponent(Cookies.get('referral_id'))
        }

        fc.ajax({
            data,
            url,
            block: true,
            accept_all_errors: true,
            parser: (res) => {
                if (res.success) {
                    window._agl && window._agl.push(['track',['success',{t:3}]]);
                    window._baq && window._baq.track('active_register', { assets_id: 1714126423115789, plan_id: plan_id });
                    window.sguic && window.sguic('track','FORM_SUBMIT_SUCCEESS','2730649');
                    window.gdt && window.gdt('track','REGISTER',{});
                    fc.removeHomepageCookie();
                    window.onbeforeunload = null;
                    if (window.fangcloud && window.fangcloud.login) {
                        res.is_register = true;
                        window.fangcloud.login(JSON.stringify(res));
                    } else if (window.register) {
                        window.register && window.register.saveAccessToken && window.register.saveAccessToken(JSON.stringify(res));
                    } else {
                        // 官网嵌入的注册页面
                        if (/fuli=true/.test(window.location.search)) {
                            window.parent.postMessage({
                                success: true
                            }, '*');
                        } else if (/embed=true/.test(window.location.search)) {
                            window.parent.location.href = res.redirect;
                        } else {
                            window.location.href = res.redirect;
                        }
                    }
                } else {
                    res.errors.forEach(({ field, error_code, error_msg }) => {
                        if (field && ![PHONE_ALREADY_OCCUPIED_BUT_NOT_ACTIVATED, PHONE_ALREADY_OCCUPIED_ACTIVATION_EXPIRED].includes(error_code)) {
                            fc.insertError(field, error_msg, $form);
                        } else {
                            let msg = error_msg;
                            let errorOptions = {};
                            if (error_code == ACCOUNT_REGISTERED) {
                                // 手机号已注册
                                msg = defineMessage({ id: 'auth.register.phoneRegistered', defaultMessage: '该手机号已被注册，请直接<a href="{0}">登录</a>或联系客服' }, API.LOGIN);
                            } else if (error_code == PERSONAL_ACCOUNT_REGISTERED) {
                                // 手机号已注册为个人账号
                                msg = defineMessage({ id: 'auth.register.phoneRegisteredAsPersonal', defaultMessage: '你已注册个人帐号，请直接<a href="{0}">登录</a>或前往网页版、PC客户端登录后进行套餐升级' }, API.LOGIN);
                            } else if (error_code == PHONE_ALREADY_OCCUPIED_BUT_NOT_ACTIVATED) {
                                // 手机号被邀请加入企业
                                msg = defineMessage({ id: 'auth.register.phoneAlreadyAccupied', defaultMessage: '你已被邀请加入{0} <a href="{1}">立即激活</a>' }, field, `/activate_by_phone?phone=${code}-${phone}`);
                                errorOptions.className = 'black';
                            } else if (error_code == PHONE_ALREADY_OCCUPIED_ACTIVATION_EXPIRED) {
                                // 邀请过期
                                msg = defineMessage({ id: 'auth.register.phoneAlreadyAccupiedAndExpired', defaultMessage: '你在30天前被邀请加入{0}，请联系管理员重发邀请后去<a href="{1}">激活账号</a>' }, field, `/activate_by_phone?phone=${code}-${phone}`);
                                errorOptions.className = 'black';
                            } else if(error_code == FREE_PLAN_LIMIT) {
                                msg = defineMessage({id: 'auth.register.freePlanLimit', defaultMessage: '太火爆了免费版本月名额已被领完。请下个月再来。或选择其他套餐试用。'});
                            }
                            fc.insertNormalError($form, msg, errorOptions);
                        }

                    });
                }
            }
        });
    });

    fc.initFormEvents($form, true);
    listenNormalError($form);
}

function GetQueryString(name) {
    const reg = new RegExp(`(^|&)${name}=([^&]*)(&|$)`, 'i');
    const r = window.location.search.substr(1).match(reg);
    if ( r != null) {
        return r[2];
    }
    return null;
}

function checkEmpty(data, $form) {
    let hasEmpty = false;
    for (let key in data) {
        if (data[key] === '' || data[key] === null) {
            fc.insertError(key, errorMsgMap[key] || '', $form);
            hasEmpty = true;
        }
    }
    return hasEmpty;
}

function listenNormalError($form) {
    $form.addEventListener('focus', e => clearRegisterError($form, e.target), true);

    const $protocol = document.getElementById('protocol');
    if($protocol) {
        $protocol.addEventListener('change', e => clearRegisterError($form, e.target));
    }

    const $motivation = $form.querySelectorAll('[name=motivation]');
    if($motivation) {
        [].forEach.call($motivation, radio => radio.addEventListener('change', e => {
            fc.clearError(e.target);
            clearRegisterError($form, e.target);
        }));
    }
}

function clearRegisterError($form, target) {
    let input = fc.matchSelector(target, 'input');
    let button = $form.querySelector('.btn-primary');
    let error = fc.nextElementSibling(button);
    if(input && error) {
        fc.removeElement(error);
        fc.removeClass(button.parentNode, 'error');
    }
}

function reportRegisterInfo(event) {
    event.preventDefault();

    const $form = document.querySelector('.normal-register');
    const phone = countryPhoneGroup.getValue();
    const captcha = smsValidate && fc.trim(smsValidate.getValue());
    let password = $form.querySelector('[name=password]') && $form.querySelector('[name=password]').value;
    password = new Array(password.length + 1).join('*');

    let data = { phone, captcha, password};

    fc.ajax({
        url: API.REPORT_WHY_LEAVE,
        method: 'POST',
        accept_all_errors: true,
        data
    });

    event.returnValue = '';
}