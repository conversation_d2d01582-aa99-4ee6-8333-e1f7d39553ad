import SmsValidate from './utils/SmsValidate';
import <PERSON><PERSON><PERSON>alidate from './utils/PicValidate';
import CountryPhoneGroup from './utils/CountryPhoneGroup';
import MobilePhoneSelect from './utils/MobilePhoneSelect';

import {defineMessage} from '@q/fang-intl';
import * as API from './constants/API';
import {ACCOUNT_REGISTERED, PERSONAL_ACCOUNT_REGISTERED, PHONE_ALREADY_OCCUPIED_BUT_NOT_ACTIVATED, PHONE_ALREADY_OCCUPIED_ACTIVATION_EXPIRED, FREE_PLAN_LIMIT} from './constants/code';
import fc from './utils/common';
import Cookies from 'js-cookie';
import Encrypt from './utils/rsaEncrypt';
// import './css/web.scss';

import CryptoJS from 'crypto-js';

let picValidate;

function createPicValidate() {
    const $form = document.querySelector('.capt');

    picValidate = new PicValidate({
        picUrl: API.REGISTER_PIC_CAPTHA
    });


    picValidate.on('destroy', function() {
        fc.removeElement(this.$el);
        picValidate = null;
    });

    $form.insertBefore(picValidate.$el, $form.querySelector('.q'));
}

function initSubmit() {
    document.querySelector('.submit').addEventListener('click', function() {
        let code = document.querySelector('.code').value;
        let cap = document.querySelector('.capt .form-control').value;
        let data = {
            ticket_id: code,
            captcha: cap
        }

        fc.ajax({
            url: API.CHANNEL_COUPON,
            data: data,
            method: 'POST',
            parser: (res) => {
                if (res.success) {
                    fc.addClass(document.querySelector('.check_channel_coupon .success'), 'showSuccess');
                }

            }
        })
    })

    document.querySelector('.check_channel_coupon .success').addEventListener('click', function() {
        let code = document.querySelector('.code').value;
        fc.removeClass(document.querySelector('.check_channel_coupon .success'), 'showSuccess');
        window.open(`${window.location.origin}/register?plan_id=29&ticket_id=${code}`);
    })
}

fc.ready(() => {
    createPicValidate();

    initSubmit();
});
