import SmsValidate from './utils/SmsValidate';
import CountryPhoneGroup from './utils/CountryPhoneGroup';
import MobilePhoneSelect from './utils/MobilePhoneSelect';
import fc from './utils/common';
import * as API from './constants/API';
import CryptoJS from 'crypto-js';
import PicValidate from './utils/PicValidate';
let picValidate;
let v2_picValidate;


fc.ready(() => {
    init();
});

let smsValidate;

function init() {
    let countryPhoneGroup;
    if(fc.config.isMobile) {
        countryPhoneGroup = new MobilePhoneSelect({
            element: document.querySelector('.phone .input-group')
        });
    } else {
        countryPhoneGroup = new CountryPhoneGroup({
            element: document.querySelector('.phone .input-group'),
            showCountry: true,
            countrySelectOpitons: {
                showSelector: true,
                default_code: 86
                // align_element: $('.register-form .phone .input-group')
            }
        });
    }

    function createPicValidate() {
        const $form = document.querySelector('.verify-form');
        picValidate = new PicValidate({
            picUrl: API.TWO_SETP_CAPTCHA
        });

        smsValidate.setDisabled(1, 'get');

        picValidate.on('destroy', function() {
            fc.removeElement(this.$el);
            picValidate = null;
        });

        picValidate.on('change', function(value) {
            if(smsValidate.getState() !== 'working') {
                smsValidate.setDisabled(value ? 0 : 1, 'get');
            }
        });

        $form.insertBefore(picValidate.$el, $form.querySelector('.sms-item'));
    }

    smsValidate = new SmsValidate({
        api: API.SEND_TWO_STEP_CODE,
        maxLen: 6,
        fieldName: 'two_step_captcha',
        syncs: function() {
            let newDate = Math.floor(new Date().getTime() / 1000);
            let data = {
                method: 'sms',//sms wechat
                phone: countryPhoneGroup.getValue(),
                ts: newDate,
                'login-sign': CryptoJS.MD5(`appId=${fc.login_sign_app_id}&ts=${newDate}&phone=t_phone&type=two_step`).toString()
            };

            if (picValidate) {
                data.pic_captcha = picValidate.getValue();
            }

            if (v2_picValidate && v2_picValidate.captchaData) {
                data = Object.assign(data, v2_picValidate.captchaData);
                v2_picValidate.destroy();
            }

            return data;
        }
    });

    smsValidate.on('destroy', function() {
        fc.removeElement(this.$el);
    });

    smsValidate.on('send_error', (res) => {
        res.errors.forEach((error) => {
            if(!error.field) {
                error.field = 'login';
            }
            fc.insertError(error.field, error.error_msg);

            if(error.field === 'pic_captcha') {
                if (picValidate) {
                    picValidate.refresh();
                } else {
                    createPicValidate();
                }
            } else if (error.field === 'sliding_pic_captcha') {
                v2_picValidate = fc.qHPassCaptcha.init('');
                return;
            }
        });
    });

    smsValidate.on('send_success', function(){
        if (picValidate) {
            picValidate.destroy();
            picValidate = null;
        }
    });

    const $form = document.querySelector('form');
    $form.insertBefore(smsValidate.$el, $form.querySelector('.phone').nextSibling);

    document.querySelector('.form').addEventListener('submit', (ev) => {
        ev.preventDefault();
        let data = {
            phone: countryPhoneGroup.getValue(),
            code: smsValidate.getValue(),
            method: 'sms'
        };

        data.login_type = fc.config.login_type;

        fc.ajax({
            url: API.SET_TWO_STEP,
            data,
            accept_all_errors: true,
            parser: fc.loginHandle
        });
    });

    // $('form').on('submit', function (ev) {
    //     ev.preventDefault();
    //     var data = {
    //         phone: countryPhone.getValue(),
    //         code: smsCaptcha.getValue(),
    //         method: 'sms'
    //     };

    //     mobile.ajax({
    //         data: data,
    //         url: '/user_settings/set_two_step',
    //         type: 'json',
    //         autoErrorHandle: true,
    //         block: true,
    //         success: function(res) {
    //             mod.showSetSuccess({
    //                 submit: function () {
    //                     mobile.loginAction.successHandler(res);
    //                 }
    //             });
    //         },
    //         accept_errors: {
    //             'validation_errors, external_errors': function (res) {
    //                 var ev = Array.prototype.slice.call(arguments, -1)[0];
    //                 var errors = res.errors.validation_errors || res.errors.external_errors;
    //                 var filter_errors = errors.filter(function(error){
    //                     if(!error.field){
    //                         error.field = 'default';
    //                     } else if(error.field === 'identifier') {
    //                         error.field = 'phone';
    //                     }
    //                     error.msg = error.error_tr_msg;
    //                     error.options = {field: error.field};
    //                     return true;
    //                 });
    //                 if(filter_errors.length){
    //                     errorMsg.show(errors);
    //                     ev.preventDefault();
    //                 }
    //             }
    //         }
    //     })
    // });
}
