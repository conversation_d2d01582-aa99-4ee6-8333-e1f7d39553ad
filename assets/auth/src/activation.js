import fc from './utils/common';
import <PERSON><PERSON><PERSON>alidate from './utils/PicValidate';
import CountryPhoneGroup from './utils/CountryPhoneGroup';
import MobilePhoneSelect from './utils/MobilePhoneSelect';
import SmsValidate from './utils/SmsValidate';
import {defineMessage} from '@q/fang-intl';
import * as API from './constants/API';
import Encrypt from './utils/rsaEncrypt';
import CryptoJS from 'crypto-js';

let currentStep = '';
let $currentContainer;
let picValidate;
let picValidateInStep2;
let smsValidate;
let countryPhoneGroup;
const emailAccount = !!document.querySelector('.phone');
let v2_picValidate;

fc.ready(() => {
    if(document.querySelector('.fullfill-account')) {
        // 重设密码
        currentStep = 'setAccount';
        if(emailAccount){
            $currentContainer = document.querySelector('.fullfill-account');
            initCountryPhone(document.querySelector('.phone .input-group'));
            createSmsValidate();
        }
        // init password hint
        document.querySelector('.password-hint').innerHTML = fc.passwordStrength();
        document.querySelector('.get-detail') && document.querySelector('.get-detail').addEventListener('click', function() {
            if(window.fangcloud && window.fangcloud.popWebUrl) {
                window.fangcloud.popWebUrl(this.getAttribute('href'));
            }
        });
    } else {
        // 激活帐号第一步
        $currentContainer = document.querySelector('.confirm-account');
        currentStep = 'confirmPhone';
        initCountryPhone(document.querySelector('.activation .input-group'));
        createPicValidate();

    }

    bindEvents();
});

function createPicValidate() {
    picValidate = new PicValidate({
        focusOnrender: false,
        picUrl: API.PHONE_ACTIVATE_PIC_CAPTHA
    });

    picValidate.on('destroy', function() {
        fc.removeElement(this.$el);
    });

    // picValidate.on('hide', function(){
    //     // this.$el.closest('.form-control-group').find('.error-msg').remove();
    // });

    const $form = document.querySelector('.confirm-account');
    $form.insertBefore(picValidate.$el, $form.querySelector('.next-step'));
}

// 为了防止刷验证码 在第二步也加了图形验证码校验
function createPicValidateInStep2(type) {
    picValidateInStep2 = new PicValidate({
        focusOnrender: false,
        picUrl: `${API.PIC_CAPTHA}/${type}`
    });

    picValidateInStep2.on('destroy', function() {
        fc.removeElement(this.$el);
    });

    // 这边可能是激活手机号 也可能是通过链接激活的
    const $form = document.querySelector('.step-2') || document.querySelector('.fullfill-account');

    $form.insertBefore(picValidateInStep2.$el, $form.querySelector('.next-step'));
}

function initCountryPhone(element) {
    const phoneNumInQuery = document.querySelector('#query-phone-num') && document.querySelector('#query-phone-num').value;
    if(fc.config.isMobile) {
        countryPhoneGroup = new MobilePhoneSelect({
            element
        });
    } else {
        countryPhoneGroup = new CountryPhoneGroup({
            element,
            showCountry: true,
            countrySelectOpitons: {
                showSelector: true,
                default_code: phoneNumInQuery && phoneNumInQuery.split('-')[0] || 86
            }
        });
    }

    if (phoneNumInQuery) {
        document.querySelector('input[name=phone]').value = phoneNumInQuery.split('-')[1];
    }
}

const submitConfirmAccount = fc.throttle(function () {
    let phone = countryPhoneGroup.getValue();
    let pic_captcha = picValidate.getValue();
    let data = {phone, pic_captcha};
    let url = API.ACTIVATE_CONFIRM_ACCOUNT;
    fc.ajax({
        data,
        url,
        accept_all_errors: true,
        parser: (res) => {
            if(res.success) {
                changeStep(2);
                verifyPhone(phone, 'activate_by_phone');
            } else {
                res.errors.forEach((error) => {
                    if(!error.field) {
                        error.field = 'login';
                    }
                    fc.insertError(error.field, error.error_msg);

                    if(error.field === 'pic_captcha' && picValidate) {
                        picValidate.refresh();
                    }
                });
            }
        }
    });
}, 1000);

function verifyPhone(phone, type) {
    let template = `
        <span class="hint">${defineMessage({id: 'activate.enterPhoneSmsValidate', defaultMessage: '请输入手机{0}获取的短信验证码'}, phone)}</span>
    `;
    $currentContainer.querySelector('.verify-account').innerHTML = template;
    createSmsValidate(phone, type);
}

function createSmsValidate(phone, type) {
    if(smsValidate) {
        smsValidate.show();
        return;
    } else {
        smsValidate = new SmsValidate({
            syncs: function() {
                let newDate = Math.floor(new Date().getTime() / 1000);
                let data = {
                    phone: phone ? phone : countryPhoneGroup.getValue(),
                    type: type ? type : 'activate',
                    ts: newDate,
                    'signature': CryptoJS.MD5(`appId=${fc.activate_by_phone_id}&ts=${newDate}&phone=${phone ? phone : countryPhoneGroup.getValue()}&type=activate_by_phone`).toString()
                };
                if (picValidateInStep2) {
                    data.pic_captcha = picValidateInStep2.getValue();
                }

                if (v2_picValidate && v2_picValidate.captchaData) {
                    data = Object.assign(data, v2_picValidate.captchaData);
                    v2_picValidate.destroy();
                }

                return data;
            }
        });

        smsValidate.on('destroy', function() {
            fc.removeElement(this.$el);
        });

        smsValidate.on('send_error', function(res){
            res.errors.forEach((error) => {
                if(!error.field) {
                    error.field = 'login';
                }
                fc.insertError(error.field, error.error_msg, $currentContainer);
                if(error.field === 'pic_captcha') {
                    if (picValidateInStep2) {
                        picValidateInStep2.refresh();
                    } else {
                        createPicValidateInStep2(type ? type : 'activate');
                    }
                } else if (error.field === 'sliding_pic_captcha') {
                    v2_picValidate = fc.qHPassCaptcha.init('');
                    return;
                }
            });
        });

        smsValidate.on('send_success', function(){
            if (picValidateInStep2) {
                picValidateInStep2.destroy();
                picValidateInStep2 = null;
            }
        });

        $currentContainer.insertBefore(smsValidate.$el, $currentContainer.querySelector( phone ? '.verify-account' : '.phone').nextSibling);
    }

    if(phone) {
        smsValidate.send();
    }
}

const submitVerifyPhone = fc.throttle(function () {
    let sms_captcha = smsValidate.getValue();
    let data = {
        sms_captcha,
        login_type: fc.config.login_type
    };

    let url = API.VERIFY_PHONE;
    fc.ajax({
        data,
        url,
        accept_all_errors: true,
        parser: (res) => {
            if(res.success) {
                window.location.href = `${res.activation_url}?_fstate=${fc.config._fstate}`;
            } else {
                res.errors.forEach((error) => {
                    if(!error.field) {
                        error.field = 'login';
                    }
                    fc.insertError(error.field, error.error_msg);
                });
            }
        }
    });
}, 1000);

const submitValidateAccount = fc.throttle(function () {
    let name = fc.trim(document.querySelector('[name=name]').value);
    let password = fc.trim(document.querySelector('[name=password]').value);
    let code = window.location.pathname.match(/activation\/[0-9a-z]{32,40}/)[0].split('/')[1];
    let data;
    if(emailAccount) {
        let phone = countryPhoneGroup.getValue();
        let sms_captcha = smsValidate.getValue();
        data = {name, phone, sms_captcha, password, code};
    } else {
        data = {name, password, code};
    }

    data = Encrypt.makeDataSafely(data);

    data.is_mobile = fc.config.isMobile;

    data.login_type = fc.config.login_type;

    let url = API.ACTIVATE;
    const button = document.querySelector('.btn-primary');
    button.setAttribute('disabled', true);

    fc.ajax({
        data,
        url,
        accept_all_errors: true,
        parser: (data) => {
            button.setAttribute('disabled', false);
            fc.loginHandle(data);
        }
    });
}, 1000);

function changeStep(step) {
    if(step === 1) {
        currentStep = 'confirmPhone';
    } else if( step === 2) {
        currentStep = 'verifyPhone';
    }
    $currentContainer.style.display = 'none';
    $currentContainer = document.querySelector(`.step-${step}`);
    $currentContainer.style.display = 'block';
}

function bindEvents() {
    let $form = document.querySelector('.form');
    $form.addEventListener('submit', (e) => {
        e.preventDefault();
        if(currentStep === 'setAccount') {
            submitValidateAccount();
        } else if(currentStep === 'confirmPhone') {
            submitConfirmAccount();
        } else if(currentStep === 'verifyPhone') {
            submitVerifyPhone();
        }
    });

    if(document.querySelector('.go-back-confirm-account')) {
        document.querySelector('.go-back-confirm-account').addEventListener('click', (e) => {
            e.preventDefault();
            changeStep(1);
            smsValidate.destroy();
            smsValidate = null;
        });
    }

    fc.initFormEvents($form);

    // let $inputs = $form.querySelectorAll('input.text');
    // $inputs.forEach(input => fc.initInputAction(input));

    // ['keyup', 'change', 'input', 'propertychange'].forEach(eventName => {
    //     $form.addEventListener(eventName, ({ target }) => {
    //         if(fc.matchSelector(target, 'input')) {
    //             checkValidate();
    //         }
    //     });
    // });
}
// ajax Setup

// placeholder 兼容处理

// 多语言选项
