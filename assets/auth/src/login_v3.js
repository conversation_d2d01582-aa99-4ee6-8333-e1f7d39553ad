import fc, { config, app_key, signupAppId } from './utils/common_v3';
import <PERSON>c<PERSON>alidate from './utils/PicValidate';
import CountryPhoneGroup from './utils/CountryPhoneGroup';
import MobilePhoneSelect from './utils/MobilePhoneSelect';
import {defineMessage} from '@q/fang-intl';
import * as API from './constants/API';
import Tooltip from 'tooltip.js';
import Encrypt from './utils/rsaEncrypt';
import CryptoJS from 'crypto-js';
// import { QWebChannel } from './utils/qwebchannel';

let countryPhoneGroup;
let picValidate;
let ApiKey;

function GetQueryString(name) {
    const reg = new RegExp(`(^|&)${name}=([^&]*)(&|$)`, 'i');
    const r = window.location.search.substr(1).match(reg);
    if ( r != null) {
        return r[2];
    }
    return null;
}

const mode = new URLSearchParams(window.location.search).get('mode');

fc.ready(() => {
    [].forEach.call(document.querySelectorAll('.only-v2'), tip => {
        new Tooltip(tip, {
            html: true,
            placement: 'bottom',
            title: tip.getAttribute('data-title')
        });
    });

    // 获取 deviceSerialNumber 可能是异步的 所以需要放到延时里面
    setTimeout(() => {
        [].forEach.call(document.querySelectorAll('.bind-link'), link => {
            var href = link.getAttribute('href');
            if (config.deviceSerialNumber) {
                link.setAttribute('href', href + '&sn_code=' + config.deviceSerialNumber);
            }
        });
    }, 100);

    ApiKey = document.getElementById('api_key') && document.getElementById('api_key').value;

    // 中间页直接使用当前js 避免添加新的中间页跳转入口
    const $response = document.getElementById('results');
    let loginJson = $response && JSON.parse($response.value);
    if(loginJson) {
        if(window.IS_WEBENGINE){
            // require.ensure([
            //     './utils/qwebchannel'
            // ], (require) => {
                // TODO 异步加载
                const QWebChannel = require('./utils/qwebchannel').QWebChannel;
                /* global qt */
                new QWebChannel(qt.webChannelTransport, function(channel) {
                    window.fangcloud = channel.objects.sync_v2;
                    fc.loginHandle(loginJson);
                });
            // });
        } else {
            fc.loginHandle(loginJson);
        }
        return;
    }
    // init varibles
    // const scene = 'login';
    // const loginReadonly = document.getElementById('login').getAttribute('readonly');

    // bind events
    if(document.querySelector('.switch-box') && !mode) {
        initTabChange();
    }

    initCountryPhone(!!document.querySelector('.international-form'));
    validatePhone();
    stantardLogin();
    stantardPhoneLogin();

    // sync register
    if(document.querySelector('.client-register')) {
        document.querySelector('.client-register').addEventListener('click', function(ev){
            ev.preventDefault();
            var url = API.REGISTER;
            if (!!GetQueryString('is_ai_app')) {
                url = API.AI_REGISTER;
            }
            if(window.fangcloud && window.fangcloud.openInBrowser){
                window.fangcloud.openInBrowser(JSON.stringify({
                    url: window.location.origin + url
                }));
            }
        });

        document.querySelector('.forgot-pwd').addEventListener('click', function(ev){
            ev.preventDefault();
            var url = API.FORGOT;
            if(this.href.indexOf('forgot_for_international') >= 0) {
                url = API.INTERNATIONAL_FORGOT;
            }
            if(window.fangcloud && window.fangcloud.openInBrowser){
                window.fangcloud.openInBrowser(JSON.stringify({
                    url: window.location.origin + url
                }));
            }
        });
        if(document.querySelector('.qr-header-hint a')) {
            document.querySelector('.qr-header-hint a').addEventListener('click', function(ev){
                ev.preventDefault();
                let url = this.getAttribute('href');
                if(window.fangcloud && window.fangcloud.openInBrowser){
                    window.fangcloud.openInBrowser(JSON.stringify({
                        url
                    }));
                }
            });
        }
    }

    if (document.getElementById('getCodeBtn')) {
        getCode();
    }

});


function createPicValidate() {
    picValidate = new PicValidate({
        picUrl: API.LOGIN_PIC_CAPTHA
    });

    picValidate.on('destroy', function() {
        fc.removeElement(this.$el);
    });
    // picValidate.on('hide', function(){
    //     fc.checkValidate();
    // });

    // picValidate.on('show', function(){
    //     fc.checkValidate();
    // });
    const isSync = !!document.getElementById('is_sync');
    let currentTab = isSync ? 'account-login' : window.location.hash.slice(1);
    const $form = document.querySelector(currentTab === 'account-login' ? '.form.account-login' : '.form.phone-login');

    $form.insertBefore(picValidate.$el, fc.config.isMobile ? $form.querySelector('.action-group') : $form.querySelector('.remember-login'));
}


function validatePhone() {
    const $input = document.getElementById('login');
    $input.addEventListener('change', function() {
        const login = encodeURIComponent(countryPhoneGroup.getValue());
        const url = `${API.CHECK_LOGIN}?login=${login}`;
        fc.ajax({
            url,
            method: 'GET',
            parser: (res) => {
                const { is_pic_captcha_needed } = res;
                if(res.errors) {
                    // TODO
                    fc.insertError('phone',  defineMessage({id: 'validate.wrongPhone', defaultMessage: '请输入有效的手机号'}));
                }

                if( is_pic_captcha_needed) {
                    if( !picValidate ) {
                        createPicValidate();
                    } else {
                        picValidate.show();
                    }
                } else {
                    picValidate && picValidate.hide();
                }
            }
        });
    });
}

function initTabChange() {
    const tabs = document.querySelector('.switch-box');
    const $oauthDes = document.querySelector('.oauth-des');

    tabs.addEventListener('click', function(e) {
        const { target } = e;
        const { className } = target;
        const sibling = target.nextElementSibling || target.previousElementSibling;

        if(className && className.indexOf('active') < 0) {
            fc.addClass(target, 'active');
            fc.removeClass(sibling, 'active');
            let showElClass = target.getAttribute('data-view');
            let hideElClass = sibling.getAttribute('data-view');
            document.querySelector('.' + showElClass).style.display = 'block';
            document.querySelector('.' + hideElClass).style.display = 'none';

            if(showElClass === 'form') {
                if($oauthDes) {
                    fc.removeClass($oauthDes, 'center');
                }
            } else {
                if($oauthDes) {
                    fc.addClass($oauthDes, 'center');
                }
            }
        }
    });
}

function initCountryPhone(showCountry) {
    if(fc.config.isMobile) {
        countryPhoneGroup = new MobilePhoneSelect({
            element: document.querySelector('.login .input-group'),
            notShowCountry: !showCountry,
            showCountry,
            default_code: showCountry ? 1 : 86
        });
    } else {
        countryPhoneGroup = new CountryPhoneGroup({
            element: document.querySelector('.login .input-group'),
            notShowCountry: !showCountry,
            showCountry,
            countrySelectOpitons: {
                showSelector: true,
                default_code: showCountry ? 1 : 86
            }
        });
    }
}

function stantardLogin() {
    const bindLogin = !!document.querySelector('.bind-login');
    const isSync = !!document.getElementById('is_sync');

    const $form = document.querySelector('.form.account-login');
    const button = $form.querySelector('.btn-primary');

    $form.addEventListener('submit', function(e){
        e.preventDefault();
        let data = {
            login: countryPhoneGroup.getValue(),
            password: fc.trim(document.getElementById('password').value)
        };

        data = Encrypt.makeDataSafely(data);

        if (document.getElementById('remember_login')) {
            data.remember_login = document.getElementById('remember_login').checked;
        }

        const $protocol = document.getElementById('protocol');
        if($protocol && !$protocol.checked) {
            fc.insertNormalError($form, defineMessage({id: 'base.needAcceptProtocal', defaultMessage: '请阅读并接受服务条款和服务等级协议'}));
            return false;
        }

        if( picValidate ) {
            data.pic_captcha = fc.trim(document.querySelector('[name=pic_captcha]').value);
        }
        let url = API.LOGIN_ACTION;
        if(bindLogin) {
            url = API.BIND_LOGIN_ACTION;
        } else if(fc.config.isMobile && (window.fangcloud || window.wx)) {
            url = API.APP_LOGIN_ACTION;
        } else if(isSync) {
            url = API.SYNC_LOGIN_ACTION;
        } else if ( document.getElementById('oauth_login') ) {
            url = API.OAUTH_LOGIN_ACTION;
        }

        let headers = {};
        if(ApiKey) {
            headers.ApiKey = ApiKey;
        }

        // 6.6.8.300 中直接在客户端中设置，不在通过jsbridge去赋值到config中
        if (fc.config.deviceSerialNumber) {
            headers['X-Device-Serial-Number'] = fc.config.deviceSerialNumber;
        }

        const from_page = !!GetQueryString('from_page');

        if(from_page) {
            data.from_page = decodeURIComponent(GetQueryString('from_page'))
        }

        data.login_type = fc.config.login_type;
        button.setAttribute('disabled', true);
        fc.ajax({
            url,
            method: 'POST',
            data,
            headers: {...headers},
            accept_all_errors: true,
            parser: (res) => {
                fc.loginHandle(res);
                if(res.errors) {
                    button.removeAttribute('disabled');
                    res.errors.forEach((error) => {
                        if(error.field === 'pic_captcha') {
                            if(picValidate) {
                                picValidate.show();
                                picValidate.refresh();
                            } else {
                                createPicValidate();
                            }
                        }
                    });
                }
            }
        });

    });

    fc.initFormEvents($form, true);

    const $protocol = document.getElementById('protocol');
    if($protocol) {
        $protocol.addEventListener('change', (e) => clearLoginError($form, e.target));
    }

    $form.addEventListener('focus', (e) => clearLoginError($form, e.target), true);
}


function clearLoginError($form, target) {
    let input = fc.matchSelector(target, 'input');
    let button = $form.querySelector('.btn-primary');
    let error = fc.nextElementSibling(button);
    if(input && error) {
        fc.removeElement(error);
        fc.removeClass(button.parentNode, 'error');
    }
}

function getCode() {
    document.getElementById('getCodeBtn').addEventListener('click', function() {
        const $input = document.getElementById('phone');
        const phone = fc.trim($input.value);
        const url = `${API.CHECK_PHONE}login?phone=${phone}`;

        //校验手机号
        fc.clearError($input);
        if(!phone) {
            fc.insertError('phone',  defineMessage({id: 'validate.wrongPhone', defaultMessage: '请输入有效的手机号'}), document.querySelector('.form.phone-login'));
            return false;
        }
        fc.ajax({
            url,
            method: 'GET',
            accept_all_errors: true,
            parser: (res) => {
                if(res.errors) {
                    fc.insertError('phone',  defineMessage({id: 'validate.wrongPhone', defaultMessage: '请输入有效的手机号'}), document.querySelector('.form.phone-login'));
                } else {
                    fc.qHPassCaptcha.init((request_id)=>{
                        let newDate = Math.floor(new Date().getTime() / 1000);
                        fc.ajax({
                            url: API.SMS_SEND_V1,
                            method: 'POST',
                            accept_all_errors: true,
                            data: {
                                "phone": phone,
                                "type": "register",
                                "register-sign": CryptoJS.MD5(`appId=${app_key}&ts=${newDate}&phone=${phone}&type=register`).toString(),
                                "ts": newDate,
                                request_id
                            },
                            parser: (res) => {
                                if(res.errors) {
                                    fc.insertError('smsCode', '获取验证码失败', document.querySelector('.form.phone-login'));
                                } else {
                                    startCountdown(this);
                                }
                            }
                        });
                    });
                }

            }
        });

    });

}

function startCountdown(button, seconds = 60) {
    let timer = null;
    let remaining = seconds;

    button.disabled = true;
    button.classList.add('btn-disable');

    timer = setInterval(() => {
        remaining--;
        button.innerHTML = `${remaining}秒后重新获取`;

        if (remaining <= 0) {
            clearInterval(timer);
            button.disabled = false;
            button.innerHTML = '获取验证码';
            button.classList.remove('btn-disable');
        }
    }, 1000);
}

function stantardPhoneLogin() {

    const $form = document.querySelector('.form.phone-login');
    const button = $form.querySelector('.btn-primary');

    $form.addEventListener('submit', function(e){
        e.preventDefault();


        const isSync = !!document.getElementById('is_sync');
        const $input = document.getElementById('phone');
        const phone = fc.trim($input.value);

        const sms_captcha = fc.trim(document.getElementById('smsCode').value);

        if(!phone) {
            fc.insertError('phone',  defineMessage({id: 'validate.wrongPhone', defaultMessage: '请输入有效的手机号'}), document.querySelector('.form.phone-login'));
            return false;
        }

        if(!sms_captcha) {
            fc.insertError('smsCode',  '请输入验证码', document.querySelector('.form.phone-login'));
            return false;
        }


        let newDate = Math.floor(new Date().getTime() / 1000);
        let data = {
          phone: phone,
          sms_captcha: sms_captcha,
          //   "plan_id": "24",
          login_type: isSync ? "sync" : "web",
          is_mobil: false,
          register_position: "1",
          "register-sign": CryptoJS.MD5(`appId=${signupAppId}&ts=${newDate}&phone=${phone}&type=quick_login`).toString(),
          ts: newDate
        };
        if( picValidate ) {
            data.pic_captcha = fc.trim($form.querySelector('[name=pic_captcha]').value);
        }

        data = Encrypt.makeDataSafely(data);

        if (document.getElementById('remember_login')) {
            data.remember_login = document.getElementById('remember_login').checked;
        }

        const $protocol = document.getElementById('protocol');
        if($protocol && !$protocol.checked) {
            fc.insertNormalError($form, defineMessage({id: 'base.needAcceptProtocal', defaultMessage: '请阅读并接受服务条款和服务等级协议'}));
            return false;
        }

        let url = API.REGISTER_V1;

        let headers = {};
        if(ApiKey) {
            headers.ApiKey = ApiKey;
        }

        // 6.6.8.300 中直接在客户端中设置，不在通过jsbridge去赋值到config中
        if (fc.config.deviceSerialNumber) {
            headers['X-Device-Serial-Number'] = fc.config.deviceSerialNumber;
        }

        const from_page = !!GetQueryString('from_page');

        if(from_page) {
            data.from_page = decodeURIComponent(GetQueryString('from_page'))
        }

        data.login_type = fc.config.login_type;
        button.setAttribute('disabled', true);
        fc.ajax({
            url,
            method: 'POST',
            data,
            headers: {...headers},
            accept_all_errors: true,
            parser: (res) => {
                const currentUrl = new URL(window.location.href);
                const redirectUrl = currentUrl.searchParams.get('redirect');
                const API_URL = document.getElementById('api_url') && document.getElementById('api_url').value;
                res = {
                    ...res,
                    redirect: res.redirect || redirectUrl || API_URL
                };
                fc.loginHandle(res, mode);
                if(res.errors) {
                    button.removeAttribute('disabled');
                    res.errors.forEach((error) => {
                        if(error.field === 'pic_captcha') {
                            if(picValidate) {
                                picValidate.show();
                                picValidate.refresh();
                            } else {
                                createPicValidate();
                            }
                        } else if(error.field === 'phone') {
                            fc.insertError('phone',error.error_msg || defineMessage({id: 'validate.wrongPhone', defaultMessage: '请输入有效的手机号'}), document.querySelector('.form.phone-login'));
                        } else if(error.field === 'sms_captcha') {
                            fc.insertError('smsCode',  error.error_msg || '验证码错误', document.querySelector('.form.phone-login'));
                        }
                    });
                }
            }
        });

    });

    fc.initFormEvents($form, true);

    const $protocol = document.getElementById('protocol');
    if($protocol) {
        $protocol.addEventListener('change', (e) => clearLoginError($form, e.target));
    }

    $form.addEventListener('focus', (e) => clearLoginError($form, e.target), true);
}


// ajax Setup

// placeholder 兼容处理

// 多语言选项
