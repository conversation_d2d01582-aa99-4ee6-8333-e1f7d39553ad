import SmsValidate from './utils/SmsValidate';
import <PERSON><PERSON><PERSON>alidate from './utils/PicValidate';
import * as API from './constants/API';
import fc, { removeClass } from './utils/common';
import { defineMessage } from '@q/fang-intl';
import { ACCOUNT_REGISTERED, PERSONAL_ACCOUNT_REGISTERED, PHONE_ALREADY_OCCUPIED_BUT_NOT_ACTIVATED, PHONE_ALREADY_OCCUPIED_ACTIVATION_EXPIRED, FREE_PLAN_LIMIT } from './constants/code';
import Encrypt from './utils/rsaEncrypt';
import Cookies from 'js-cookie';
import CryptoJS from 'crypto-js';

let smsValidate;
let picValidate;
let countryPhoneGroup;
let linkHref = 'https://v2.fangcloud.com/desktop/introduction';
let linkDomain = '.fangcloud.com';
let v2_picValidate;

const errorMsgMap = {
    'phone': defineMessage({ id: 'auth.register.phoneError', defaultMessage: '请输入正确的手机号' }),
    'sms_captcha': defineMessage({ id: 'auth.register.sms_captchaError', defaultMessage: '请输入正确的验证码' }),
    'password': defineMessage({ id: 'auth.register.passwordError', defaultMessage: '请设置正确的密码，8~32位数字和字母的组合' }),
    'email': defineMessage({ id: 'auth.register.emailError', defaultMessage: '请输入正确的邮箱' })
};

fc.ready(() => {
    if (Cookies.get('referral_code') && Cookies.set('referral_id_invite')) {
        window.location.href = linkHref;
        return ;
    }
    normalRegister();
    createSmsValidate();
    validatePhone('register');
});

function createPicValidate() {
    const $form = document.querySelector('.normal-register');
    picValidate = new PicValidate({
        picUrl: API.REGISTER_PIC_CAPTHA_BECOME_REFERRAL
    });

    smsValidate && smsValidate.setDisabled(1, 'get');

    picValidate.on('destroy', function () {
        fc.removeElement(this.$el);
        picValidate = null;
    });

    picValidate.on('change', function (value) {
        if (smsValidate && smsValidate.getState() !== 'working') {
            smsValidate.setDisabled(value ? 0 : 1, 'get');
        }
    });

    $form.insertBefore(picValidate.$el, $form.querySelector('.phone').nextSibling);
}

function createSmsValidate() {
    smsValidate = new SmsValidate({
        syncs: function () {
            const $input = document.getElementById('phone');
            let newDate = Math.floor(new Date().getTime() / 1000);

            let data = {
                type: 'become_referral',
                phone: $input.value,
                ts: newDate,
                'referral-sign': CryptoJS.MD5(`appId=${fc.become_referral_id}&ts=${newDate}&phone=${$input.value}&type=become_referral`).toString()
            };

            if (picValidate) {
                data.pic_captcha = fc.trim(picValidate.getValue());
            }

            if (v2_picValidate && v2_picValidate.captchaData) {
                data = Object.assign(data, v2_picValidate.captchaData);
                v2_picValidate.destroy();
            }

            return data;
        }
    });

    // smsValidate.setDisabled(1, 'get');
    smsValidate.on('destroy', function () {
        fc.removeElement(this.$el);
    });

    smsValidate.on('send_error', function (res) {
        res.errors.forEach((error) => {
            fc.insertError(error.field || 'sms_captcha', error.field == 'phone' ? errorMsgMap['phone'] : error.error_msg, document.querySelector('.normal-register'));
            if (error.field === 'pic_captcha') {
                if (picValidate) {
                    picValidate.refresh();
                } else {
                    createPicValidate();
                }
            } else if (error.field === 'sliding_pic_captcha') {
                v2_picValidate = fc.qHPassCaptcha.init('');
                return;
            }
        });
    });

    smsValidate.on('send_success', function(){
        if (picValidate) {
            picValidate.destroy();
            picValidate = null;
        }
    });

    const $form = document.querySelector('.normal-register');
    // if (!document.querySelector('.sms-group'))
    $form.insertBefore(smsValidate.$el, $form.querySelector('.phone').nextSibling);
}

function listenNormalError($form) {
    $form.addEventListener('focus', e => clearRegisterError($form, e.target), true);
    $form.addEventListener('input', e => {
        const phone = document.getElementById('phone').value;
        const sms_captcha = fc.trim(smsValidate.getValue());
        if (!phone || !sms_captcha) {
            document.querySelector('.register-btn').classList.remove('can-submit');
        } else {
            document.querySelector('.register-btn').classList.add('can-submit');
        }
    }, true);
}

function clearRegisterError($form, target) {
    let input = fc.matchSelector(target, 'input');
    let button = $form.querySelector('.btn-primary');
    let error = fc.nextElementSibling(button);
    if(input && error) {
        fc.removeElement(error);
        fc.removeClass(button.parentNode, 'error');
    }
}

function validatePhone(scene) {
    const $input = document.getElementById('phone');
    ['keyup', 'afterpaste'].forEach(eventName => {
        $input.addEventListener(eventName, function () {
            this.value = this.value.replace(/\D/g, '');
        });
    });
}

function normalRegister() {
    const $form = document.querySelector('.normal-register');
    document.querySelector('.register-btn').addEventListener('click', () => {
        const phone = document.getElementById('phone').value;
        const sms_captcha = fc.trim(smsValidate.getValue());

        let data = { phone, sms_captcha };

        let url = API.BECOME_REFERRAL;

        let hasEmpty = checkEmpty(data, $form);
        if (hasEmpty) {
            return false;
        }

        fc.ajax({
            data,
            url,
            block: true,
            accept_all_errors: true,
            parser: (res) => {
                if (res.success) {
                    Cookies.set('referral_code', res.code, {domain: linkDomain});
                    Cookies.set('referral_id_invite', res.referral_id, {domain: linkDomain});
                    window.location.href = linkHref;
                } else {
                    const errorMsg = res.errors && res.errors.length && res.errors[0].error_msg || '注册/登录失败，请刷新后重试';
                    fc.insertNormalError($form, errorMsg, {});
                }
            }
        });
    });

    listenNormalError($form);
}

function checkEmpty(data, $form) {
    let hasEmpty = false;
    for (let key in data) {
        if (data[key] === '' || data[key] === null) {
            hasEmpty = true;
        }
    }
    return hasEmpty;
}