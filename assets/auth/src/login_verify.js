import fc from './utils/common';
import SmsValidate from './utils/SmsValidate';
import * as API from './constants/API';
import {defineMessage} from '@q/fang-intl';
import CryptoJS from 'crypto-js';
import Pic<PERSON>alidate from './utils/PicValidate';
let picValidate;

const method = document.querySelector('[name=validate_type]').value;
let smsValidate;
let v2_picValidate;

fc.ready(() => {
    const $form = document.querySelector('form');
    if(method !== 'google') {
        createSmsValidate();
        smsValidate.send();
    } else {
        let $code = document.createElement('div');
        fc.addClass($code, 'form-group');
        $form.insertBefore($code, $form.querySelector('.mark-credit'));
        $code.innerHTML = `
            <div class="input-group">
                <input type="text" name="two_step_captcha" data-role='captcha' maxleng=6 class="text form-control" autocomplete="off">
            </div>
        `;
    }

    $form.addEventListener('submit', (e) => {
        e.preventDefault();
        fc.ajax({
            url: API.TWO_STEP_LOGIN_ACTION,
            data: {
                login_type: fc.config.login_type,
                mark_credit: !! (document.getElementById('mark_credit') && document.getElementById('mark_credit').checked),
                code: smsValidate ? smsValidate.getValue() : document.querySelector('[data-role=captcha]').value
            },
            accept_all_errors: true,
            parser: fc.loginHandle
        });
    });

    if ($form.querySelector('input[type=checkbox]')) {
        fc.initCheckboxAction($form.querySelector('input[type=checkbox]'));
    }
    document.querySelector('.get-help').addEventListener('click', (e) => {
        e.preventDefault();
        fc.alert(`
            <p>${defineMessage({id: 'loginVerify.helperDes1', defaultMessage: '1.请确认你设置接收验证码的微信号是否关注了亿方云公众号，如未关注，请重新搜索并关注“亿方云官方服务平台”。关注后点击“重新发送验证码”完成验证即可。'})}</p>
            <p>${defineMessage({id: 'loginVerify.helperDes2', defaultMessage: '2.你也可以联系企业管理员或客服帮你关闭二次验证'})}</p>
        `);
    });
    if(document.querySelector('.login-verify-back')){
        document.querySelector('.login-verify-back').addEventListener('click', (e) => {
            e.preventDefault();
            window.history.back();
        });
    }
});

function createPicValidate() {
    const $form = document.querySelector('.verify-form');
    picValidate = new PicValidate({
        picUrl: API.TWO_SETP_CAPTCHA
    });

    smsValidate.setDisabled(1, 'get');

    picValidate.on('destroy', function() {
        fc.removeElement(this.$el);
        picValidate = null;
    });

    picValidate.on('change', function(value) {
        if(smsValidate.getState() !== 'working') {
            smsValidate.setDisabled(value ? 0 : 1, 'get');
        }
    });

    $form.insertBefore(picValidate.$el, $form.querySelector('.sms-item'));
}

function createSmsValidate() {
    const $form = document.querySelector('form');

    smsValidate = new SmsValidate({
        api: API.SEND_TWO_STEP_CODE,
        maxLen: 6,
        showVoice: method === 'sms',
        fieldName: 'two_step_captcha',
        syncs: function() {
            let newDate = Math.floor(new Date().getTime() / 1000);
            let data = {
                method,
                ts: newDate,
                'login-sign': CryptoJS.MD5(`appId=${fc.login_sign_app_id}&ts=${newDate}&phone=t_phone&type=two_step`).toString()
            };
            if (picValidate) {
                data.pic_captcha = picValidate.getValue();
            }

            if (v2_picValidate && v2_picValidate.captchaData) {
                data = Object.assign(data, v2_picValidate.captchaData);
                v2_picValidate.destroy();
            }

            return data;
        }
    });

    smsValidate.on('destroy', function() {
        fc.removeElement(this.$el);
    });

    smsValidate.on('send_error', (res) => {
        res.errors.forEach((error) => {
            if(!error.field) {
                error.field = 'login';
            }
            fc.insertError(error.field, error.error_msg);

            if(error.field === 'pic_captcha') {
                if (picValidate) {
                    picValidate.refresh();
                } else {
                    createPicValidate();
                }
            } else if (error.field === 'sliding_pic_captcha') {
                v2_picValidate = fc.qHPassCaptcha.init('');
                return;
            }
        });
    });

    smsValidate.on('send_success', function(){
        if (picValidate) {
            picValidate.destroy();
            picValidate = null;
        }
    });

    $form.insertBefore(smsValidate.$el, $form.querySelector('.mark-credit'));
}


// ajax Setup

// 多语言选项
