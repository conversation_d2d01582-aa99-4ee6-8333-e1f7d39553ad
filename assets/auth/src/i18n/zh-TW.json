{"activate.enterPhoneSmsValidate": "請輸入手機{0}獲取的短信驗證碼", "base.Iknow": "知道了", "base.confirmToContinue": "由於妳長時間未進行操作，請刷新或點擊確認後繼續使用", "base.confirm": "確定", "base.loginFail": "登入已失效，請重新登入", "base.alert.server500.title": "伺服器開了一點小差，已通知程式猿小哥處理，請稍等片刻或重新整理重試。", "base.alert.server500.content": "如有疑問，請聯絡客服", "base.alert.server502": "伺服器錯誤，請重試 (502)", "base.alert.server404": "網路出錯(404)，請檢查後重試", "base.alert.server403": "你無許可權訪問", "base.pageExpired": "當前頁面已失效，請點擊{0}重試", "base.ok": "確定", "base.passwordStrengthMid": "密碼必須為8-32位字母和數字的組合", "base.passwordStartOrEndWithSpace": "密碼首尾不能為空格", "base.passwordWithForbiddenCharacter": "密碼包含不允許的字符(<a class=\"get-detail\" href=\"http://help.fangcloud.com/posts/view/57412/\" target=\"_blank\">了解詳情</a>)", "base.time.justNow": "剛剛", "base.time.beforeSeconds": "{0}秒前", "base.time.beforeMinutes": "{0}分鐘前", "base.time.beforeHours": "{0}小時前", "base.time.yesterday": "昨天", "base.time.theDayBefore": "前天", "unit.second": "{0}秒", "unit.minute": "{0}分鐘", "unit.hour": "{0}小時", "unit.day": "{0}天", "base.hidePassword": "隱藏密碼", "base.showPassword": "顯示密碼", "login.ent.expired.sub": "若要查看雲端文件，可以在網頁端下載。", "base.openBrowser": "打開網頁端", "encryption.versionLimit": "你所在的企業開啟了檔案防洩漏，需下載特定版本客戶端方可使用", "encryption.downloadNow": "立即下載", "base.passwordWithCaptial": "密碼長度為{0}-32個字符，必須包含大寫字母、小寫字母和數字", "base.passwordWithSpecial": "密碼長度為{0}-32個字符，必須包含字母、數字和特殊字符(<a class=\"get-detail\" href=\"http://help.fangcloud.com/posts/view/57412/\" target=\"_blank\">了解詳情</a>)", "base.passwordWithCaptialAndSpecial": "密碼長度為{0}-32個字符，必須包含大寫字母、小寫字母、數字和特殊字符(<a class=\"get-detail\" href=\"http://help.fangcloud.com/posts/view/57412/\" target=\"_blank\">了解詳情</a>)", "base.passwordNormal": "密碼長度為{0}-32個字符，必須包含字母和數字", "forgot.byPhone": "通過手機號{0}找回密碼", "forgot.byEmail": "通過郵箱{0}找回密碼", "forgot.validateMode": "驗證方式：", "forgot.phoneValidate": "手機驗證", "forgot.emailValidate": "郵箱驗證", "forgot.resetSuccess": "重設成功！請使用新密碼登入", "forgot.login": "立即登入", "forgot.resetEmailSended": "重設密碼鏈接已發送至{0}<br />請登入妳的郵箱查看並按照郵件內容操作", "forgot.resetEmailSendedTips": "郵件可能會被妳的郵箱攔截，如在收件箱中未找到，可嘗試在垃圾郵件中查看。", "forgot.returnLogin": "返回到登入", "loginVerify.helperDes1": "1.請確認妳設置接收驗證碼的微信號是否關註了億方雲公眾號，如未關註，請重新搜索並關註“億方雲官方服務平臺”。關註後點擊“重新發送驗證碼”完成驗證即可。", "loginVerify.helperDes2": "2.妳也可以聯系企業管理員或客服幫妳關閉二次驗證", "validate.wrongPhone": "請輸入有效的手機號", "auth.register.phoneError": "請輸入正確的手機號", "auth.register.sms_captchaError": "請輸入正確的驗證碼", "auth.register.passwordError": "請設置正確的密碼，8~32位數字和字母的組合", "auth.register.emailError": "請輸入正確的郵箱", "auth.register.freeTrial": "免費使用，立即體驗", "auth.register.freeExperienceFor15Days": "免費體驗15天", "auth.register.leaveMessageForContact": "請留下聯系方式，我們將盡快聯系妳", "auth.register.normalTip": "若妳的企業已註冊，聯系管理員邀請即可，無需單獨註冊", "auth.register.enterpirseContactNumber": "妳也可以撥打 <b>************</b> 聯系我們", "base.needAcceptProtocal": "請閱讀並接受服務條款和服務等級協議", "base.emptyValue": "請輸入正確的信息後再試", "auth.register.phoneRegistered": "該手機號已被註冊，請直接<a href=\"{0}\">登入</a>或聯系客服", "auth.register.phoneRegisteredAsPersonal": "你已註冊個人帳號，請直接<a href=\"{0}\">登入</a>或前往網頁版、PC客戶端登入後進行套餐升級", "auth.register.phoneAlreadyAccupied": "你已被邀請加入{0} <a href=\"{1}\">立即激活</a>", "auth.register.phoneAlreadyAccupiedAndExpired": "你在30天前被邀請加入{0}，請聯繫管理員重發邀請後去<a href=\"{1}\">激活賬號</a>", "settings.confirmLoginPassword": "驗證身份", "settings.confirmLoginPasswordHint": "為保障妳的帳號安全，請輸入登入密碼進行身份驗證。", "base.inputLoginPassword": "請輸入妳的登入密碼", "base.next": "下壹步", "settings.setLoginPassword": "設置獨立登入密碼", "settings.setLoginPasswordHint1": "設置獨立登入密碼，可配合綁定的郵箱/手機登入。", "settings.pleaseSetPassword": "獨立登入密碼", "settings.twoStepVerification.selectVerifyTitle": "選擇二次驗證方式", "settings.twoStepVerification.wechat": "微信", "settings.twoStepVerification.wechatSetDes": "使用微信掃描並關註我們的微信公眾號，我們會將驗證碼通過公眾號發送給妳", "settings.twoStepVerification.sms": "手機短信", "settings.twoStepVerification.smsSetDes": "設置壹個手機號，我們會將驗證碼通過短信方式發送給妳。", "settings.twoStepVerification.google": "谷歌身份驗證器", "settings.twoStepVerification.googleSetDes": "妳可以下載身份驗證器應用來獲取驗證碼，即使手機未連接網絡也無妨。", "settings.twoStepVerification.recommend": "推薦", "settings.twoStepVerification.wechatDes1": "1. 使用微信掃描二維碼並關註我們的微信公眾號，妳將會收到驗證碼。", "settings.twoStepVerification.wechatDes2": "如已關註公眾號，可直接掃描", "settings.twoStepVerification.wechatDes3": "2. 輸入微信公眾號發給妳的6位驗證碼。", "base.twoStepVerification.setSubmit": "完成設置", "base.previous": "上壹步", "settings.twoStepVerification.setByWechatTitle": "設置微信", "settings.twoStepVerification.setBySMSTitle": "設置手機號", "settings.twoStepVerification.googleIos": "iPhone用護", "settings.twoStepVerification.googleIosDes1": "1.在iPhone上，打開App Store。", "settings.twoStepVerification.googleIosDes2": "2.搜索谷歌身份驗證器(Google Authenticator)", "settings.twoStepVerification.googleIosDes3": "3.下載並安裝應用。", "settings.twoStepVerification.googleAndroid": "Android用護", "twoStepVerification.googleAndroidDes1": "1.在手機上訪問Google Play或其它商店。", "twoStepVerification.googleAndroidDes2": "2.搜索谷歌身份驗證器(Google Authenticator)", "twoStepVerification.googleAndroidDes3": "3.下載並安裝應用。", "settings.twoStepVerification.googleHelp": "如何安裝google身份驗證器？", "settings.twoStepVerification.downloadGoogleTitle": "下載谷歌身份驗證器", "settings.twoStepVerification.googleVerifyQRcodeIosDes": "1. 打開谷歌身份驗證器，點擊+通過掃描二維碼完成設置", "settings.twoStepVerification.googleVerifyQRcodeDes": "1.打開應用，點擊菜單，選擇“設置帳戶”，再掃描條形碼即可。", "settings.twoStepVerification.googleVerifyCantQRcode": "無法掃描？", "settings.twoStepVerification.googleVerifyToManually": "手動設置", "settings.twoStepVerification.googleVerifyManuallyIosDes1": "1.打開谷歌身份驗證器，點擊+", "settings.twoStepVerification.googleVerifyManuallyIosDes2": "建議妳在“帳戶”中輸入產品和帳戶名，如“{0}：<EMAIL>”", "settings.twoStepVerification.googleVerifyManuallyIosDes3": "在“密鑰”中輸入16位密鑰：", "settings.twoStepVerification.googleVerifyManuallyAndroidDes1": "1.打開應用，點擊菜單，選擇“設置賬戶”", "settings.twoStepVerification.googleVerifyManuallyAndroidDes2": "建議妳在“賬戶”中輸入產品和帳戶名，如“{0}：<EMAIL>”", "settings.twoStepVerification.googleVerifyManuallyAndroidDes3": "在“密鑰”中輸入16位密鑰", "settings.twoStepVerification.googleVerifyToQRcode": "返回掃描設置", "settings.twoStepVerification.googleVerifyInputCaptcha": "2. 輸入谷歌驗證器的6位驗證碼", "settings.twoStepVerification.setByGoogleTitle": "設置谷歌身份驗證器", "settings.twoStepVerification.modifyVerifySuccessTitle": "二次驗證已修改", "settings.twoStepVerification.setVerifySuccessTitle": "二次驗證已開啟", "settings.twoStepVerification.setVerifySuccessDes": "二次驗證已開啟，每次登入{0}時，除帳號密碼之外，還需要輸入安全驗證碼。", "settings.twoStepVerification.closeVerifySuccess": "二次驗證已關閉", "settings.thirdPartyBindFailure": "綁定失敗", "settings.editPassword": "修改密碼", "settings.oldPassword": "舊密碼", "settings.newPassword": "新密碼", "settings.confirmPassword": "確認密碼", "settings.editPasswordSuccess": "密碼修改成功，請重新登入", "settings.email": "郵箱：", "settings.phone": "手機：", "settings.emptyEmailInfo": "綁定且驗證後可用郵箱登入", "settings.emptyPhoneInfo": "綁定後可用手機登入", "settings.unvalidate": "未驗證", "settings.unvalidateCredentialUsage": "驗證後可用於登入、找回密碼", "settings.validateTime": "驗證時間：{0}", "settings.validateCredentialUsage": "可用於登入、找回密碼", "settings.validate": "立即驗證", "settings.bindImmediately": "立即綁定", "settings.phoneNotAvaliable": "該手機號已被其它帳號占用，請修改。", "settings.emailNotAvaliable": "該郵箱已被其它帳號占用，請修改。", "base.cancelText": "下次再說", "settings.editNow": "立即修改", "settings.forbiddenEditEmail": "暫時無法修改郵箱。請在手機號綁定成功後再試。", "settings.editPhone": "修改手機", "settings.validatePhone": "驗證手機", "settings.bindPhone": "綁定手機", "settings.editPhoneSuccess": "手機號已修改成功", "settings.validatePhoneSuccess": "手機號已驗證成功", "settings.bindPhoneSuccess": "手機號已綁定成功", "settings.currentCredential": "當前綁定：{0}", "settings.mobilePlaceholder": "請輸入妳的手機號", "base.submit": "提交", "settings.editEmail": "修改郵箱", "settings.validateEmail": "驗證郵箱", "settings.bindEmail": "綁定郵箱", "settings.enterNewEmail": "請輸入新的郵箱", "settings.emailSended": "驗證郵件已發送至{0}，請登入妳的郵箱查看。驗證後，該郵箱可以用於登入、找回密碼。", "settings.emailSendedTips": "郵件可能會被攔截，如未在收件箱中找到，可嘗試在垃圾郵件中查找", "settings.unbind": "解綁", "settings.bind": "綁定", "settings.qiyeweixinLabel": "企業微信：", "settings.dingdingLabel": "釘釘：", "settings.dingding": "釘釘", "settings.qiyeweixin": "企業微信", "settings.qihoo360": "360", "settings.qihoo360Label": "360賬號:", "settings.unbindThirdConfirmMain": "妳確定要解綁{0}帳號“{1}”嗎？", "settings.unbindConfirmSub": "解綁後，將無法使用該帳號登入", "settings.device.fromAndroid": "（Android App）", "settings.device.fromIos": "（iOS App）", "settings.device.fromWindowsSync": "（Windows同步端）", "settings.device.fromMacSync": "（Mac同步端）", "settings.device.fromMacDesktop": "（Mac客護端）", "settings.device.fromWindowsDesktop": "（Windows客護端）", "settings.device.deviceName": "設備名稱", "settings.device.deivceLastLogin": "最近訪問", "settings.device.deivceLastLocation": "最近訪問地", "settings.device.deivceOperation": "操作", "settings.device.creditable": "可信", "settings.device.current": "當前", "settings.device.ip": "IP地址：{0}", "base.delete": "刪除", "settings.device.noresult": "暫無數據", "settings.device.showMore": "展開全部", "settings.device.deleteIsCreditable": "在該設備上將退出登入，妳需要重新登入才能繼續訪問，再次登入需要進行二次驗證。", "settings.device.deleteNotCreditable": "在該設備上將退出登入，妳需要重新登入才能繼續訪問。", "settings.device.deleteDevice": "刪除此設備", "settings.device.hideMore": "收起", "settings.twoStepVerification.typePhone": "手機短信驗證", "settings.twoStepVerification.typePhoneNumber": "（尾號為 {0}）", "settings.twoStepVerification.typeWechat": "微信公眾號驗證", "settings.twoStepVerification.typeGoogle": "谷歌驗證器驗證", "settings.twoStepVerification.hasOpen": "二次驗證已開啟", "settings.twoStepVerification.validateTime": "（設置於 {0}）", "settings.twoStepVerification.toClose": "關閉二次驗證", "settings.twoStepVerification.type": "驗證方式：", "base.modify": "修改", "settings.twoStepVerification.info": "二次驗證為妳的帳戶增加壹層安全保護。啟用二次驗證後，每次登入億方雲時，除帳號密碼之外，還需要輸入安全驗證碼（驗證碼將發送到妳的手機上）。", "settings.twoStepVerification.toSet": "開啟二次驗證", "settings.twoStepVerification.disableToClose": "管理員開啟了“企業成員二次驗證”功能，妳無法單獨關閉二次驗證。", "base.yfy": "億方雲", "base.countrySelectorPlaceholder": "搜索妳的國家/地區、區號", "base.searchEmpty": "無符合條件的搜索結果", "base.cancel": "取消", "base.picCaptchaPlaceholder": "請輸入圖形驗證碼", "base.smsCaptchaPlaceholder": "請輸入{0}位驗證碼", "base.getVerificationCode": "獲取驗證碼", "base.retrieveAfter": "{0}秒後重新獲取", "base.retrieve": "重新獲取驗證碼", "base.getVoice": "接收語音驗證碼", "base.voiceSMSCaptcha_send": "系統將通過免費電話給您發送語音驗證碼，60s內未收到可重新獲取", "base.voiceSMSCaptchaConfirm": "我們將以電話的方式發送語音驗證碼。請註意接聽並記下{0}位驗證碼。", "base.receiveVoiceConfirm": "接收語音驗證碼"}