{"activate.enterPhoneSmsValidate": "Enter the SMS code sent to phone number {0}", "base.Iknow": "OK", "base.confirmToContinue": "Since you have not operated for a long time, please refresh or click to confirm and continue to use", "base.confirm": "Yes", "base.loginFail": "Your session has expired. Please log in again", "base.alert.server500.title": "The server is down, and the programmer has been informed, please wait a moment or refresh to try again.", "base.alert.server500.content": "If you have any question, please contact the customer service", "base.alert.server502": "Server error (502). Please try again", "base.alert.server404": "Network error (404), please check and try again.", "base.alert.server403": "403-Forbidden:Access is denied.", "base.pageExpired": "Current session has expired, please click {0} to retry", "base.ok": "Yes", "base.passwordStrengthMid": "Your password must be a combination of 8-32 letters and figures", "base.passwordStartOrEndWithSpace": "There must be no space at the beginning and end of your password", "base.passwordWithForbiddenCharacter": "Your password contains character(s) that aren’t allowed (<a class=\"get-detail\" href=\"http://help.fangcloud.com/posts/view/57412/\" target=\"_blank\">Learn more</a>)", "base.time.justNow": "Just now", "base.time.beforeSeconds": "{0} second(s) ago", "base.time.beforeMinutes": "{0} minute(s) ago", "base.time.beforeHours": "{0} hour(s) ago", "base.time.yesterday": "Yesterday", "base.time.theDayBefore": "The day before yesterday", "unit.second": "{0} second(s)", "unit.minute": "{0} minute(s)", "unit.hour": "{0} hour(s)", "unit.day": "{0} day(s)", "base.hidePassword": "Hide the password", "base.showPassword": "Show the password", "login.ent.expired.sub": "If you want the cloud files, you can download them by logging into the website.", "base.openBrowser": "Open the website", "encryption.versionLimit": "Your company has turned on File Leakage Prevention System and needs to download a specific version of PC client to continue using", "encryption.downloadNow": "Download now", "base.passwordWithCaptial": "Your password must be a combination of {0}-32 characters, including upper-case letter(s), lower-case letter(s) and figures", "base.passwordWithSpecial": "Your password must be a combination of {0}-32 characters, including letter(s), number(s) and special character(s) (<a class=\"get-detail\" href=\"http://help.fangcloud.com/posts/view/57412/\" target=\"_blank\">Learn more</a>)", "base.passwordWithCaptialAndSpecial": "Your password must be a combination of {0}-32 characters, including upper-case letter(s), lower-case letter(s), figures and special character(s) (<a class=\"get-detail\" href=\"http://help.fangcloud.com/posts/view/57412/\" target=\"_blank\">Learn more</a>)", "base.passwordNormal": "Your password must be a combination of {0}-32 characters, including letter(s) and number(s)", "forgot.byPhone": "Reset your password via mobile phone number {0}", "forgot.byEmail": "Reset your password via email address {0}", "forgot.validateMode": "Verificaiton method:", "forgot.phoneValidate": "Via verified phone number", "forgot.emailValidate": "Via verified email", "forgot.resetSuccess": "Reset successful! Please log in with the new password", "forgot.login": "Log in now", "forgot.resetEmailSended": "The link for password reset has been sent to {0}<br />Please check your email and follow the instructions.", "forgot.resetEmailSendedTips": "The email may be blocked by your email account. If you can’t find it in your inbox, you may need to check spam.", "forgot.returnLogin": "Back to log in", "loginVerify.helperDes1": "1. Please confirm whether your WeChat account for receiving verification code has followed the FangCloud WeChat Official Account. If not, please search again and follow “亿方云官方服务平台”. Follow and then click “Resend verification code” to finish verification.", "loginVerify.helperDes2": "2. You can also contact your enterprise admin or customer service team to help disable it", "validate.wrongPhone": "Enter a valid mobile number", "auth.register.phoneError": "Please enter the correct phone number", "auth.register.sms_captchaError": "Please enter the correct verification code", "auth.register.passwordError": "Please set the correct password, a combination of 8 ~ 32 digits and letters", "auth.register.emailError": "Please enter the correct email", "auth.register.freeTrial": "Free of charge, try it now", "auth.register.freeExperienceFor15Days": "Try free for 15 days", "auth.register.leaveMessageForContact": "Please provide your contact information and we will contact you as soon as possible", "auth.register.normalTip": "If your enterprise has finished registration, please contact the administrator for invitation. Individual registration isn’t necessary", "auth.register.enterpirseContactNumber": "You may also call <b>************</b> to contact us", "base.needAcceptProtocal": "Please read and accept Terms & Conditions of Services and Service Level Agreement", "base.emptyValue": "Please enter the correct information and try again", "auth.register.phoneRegistered": "This phone number has been used for registration. Please <a href=\"{0}\"> log in </a> or contact Customer Services.", "auth.register.phoneRegisteredAsPersonal": "You have already registered as a personal account. Please <a href=\"{0}\"> login </a> or go to the website or PC client to login and update your plan.", "auth.register.phoneAlreadyAccupied": "You have been invited to join {0} <a href=\"{1}\"> activate now</a>", "auth.register.phoneAlreadyAccupiedAndExpired": "You were invited to join {0} 30 days ago, please contact the administrator to re-send the invitation to <a href=\"{1}\">activate the account</a>", "settings.confirmLoginPassword": "Identity verification", "settings.confirmLoginPasswordHint": "To ensure security of your account, please enter your login password for identity verification", "base.inputLoginPassword": "Enter your login password", "base.next": "Next", "settings.setLoginPassword": "Set an individual login password", "settings.setLoginPasswordHint1": "Set your individual login password and you may log in with the login password and the linked email address/phone number.", "settings.pleaseSetPassword": "Individual login password", "settings.twoStepVerification.selectVerifyTitle": "Choose Two-step verification method", "settings.twoStepVerification.wechat": "WeChat", "settings.twoStepVerification.wechatSetDes": "Use your WeChat app to scan and follow our WeChat Official Account, and we'll send the verification code to you via WeChat.", "settings.twoStepVerification.sms": "Text message", "settings.twoStepVerification.smsSetDes": "Set a phone number, and we'll send you the verification code via text message.", "settings.twoStepVerification.google": "Google Authenticator", "settings.twoStepVerification.googleSetDes": "You can download a Google Authenticator app to get the verification code, even if your phone is not connected to the network.", "settings.twoStepVerification.recommend": "Recommend", "settings.twoStepVerification.wechatDes1": "1. Use your WeChat app to scan and follow our WeChat Official Account, and you will receive the verification code.", "settings.twoStepVerification.wechatDes2": "If you have followed our WeChat Official Account, you can scan directly.", "settings.twoStepVerification.wechatDes3": "2. Enter the 6-digit verification code sent by the FangCloud WeChat Official Account.", "base.twoStepVerification.setSubmit": "Complete", "base.previous": "Back", "settings.twoStepVerification.setByWechatTitle": "Set WeChat", "settings.twoStepVerification.setBySMSTitle": "Set phone number", "settings.twoStepVerification.googleIos": "iPhone user", "settings.twoStepVerification.googleIosDes1": "1. Open App Store on iPhone.", "settings.twoStepVerification.googleIosDes2": "2. Search Google Authenticator", "settings.twoStepVerification.googleIosDes3": "3. Download and install the app", "settings.twoStepVerification.googleAndroid": "Android user", "twoStepVerification.googleAndroidDes1": "1. Visit Google Play or other app stores on your phone.", "twoStepVerification.googleAndroidDes2": "2. Search Google Authenticator", "twoStepVerification.googleAndroidDes3": "3. Download and install the app", "settings.twoStepVerification.googleHelp": "How to install Google Authenticator app?", "settings.twoStepVerification.downloadGoogleTitle": "Download Google Authenticator app", "settings.twoStepVerification.googleVerifyQRcodeIosDes": "1. Open Google Authenticator app, click + and scan the QR code to complete the configuration", "settings.twoStepVerification.googleVerifyQRcodeDes": "1. Open Google Authenticator app, click the menu and choose \"Setting Account\", then scan the QR code.", "settings.twoStepVerification.googleVerifyCantQRcode": "Can't scan?", "settings.twoStepVerification.googleVerifyToManually": "Set manually", "settings.twoStepVerification.googleVerifyManuallyIosDes1": "1. Open Google Authenticator app and click +", "settings.twoStepVerification.googleVerifyManuallyIosDes2": "It is recommended that you enter the product and account name in \"Account\", such as \"{0}: <EMAIL>\"", "settings.twoStepVerification.googleVerifyManuallyIosDes3": "Enter a 16-digit secret key in “Secret Key”:", "settings.twoStepVerification.googleVerifyManuallyAndroidDes1": "1. Open Google Authenticator app, click the menu and choose \"Setting Account\"", "settings.twoStepVerification.googleVerifyManuallyAndroidDes2": "It is recommended that you enter the product and account name in \"Account\", such as \"{0}: <EMAIL>\"", "settings.twoStepVerification.googleVerifyManuallyAndroidDes3": "Enter a 16-digit secret key in “Secret Key”", "settings.twoStepVerification.googleVerifyToQRcode": "Or scan a QR code instead", "settings.twoStepVerification.googleVerifyInputCaptcha": "2. Enter 6-digit code generated by Google Authenticator app", "settings.twoStepVerification.setByGoogleTitle": "Set Google Authenticator app", "settings.twoStepVerification.modifyVerifySuccessTitle": "Two-step verification has been changed", "settings.twoStepVerification.setVerifySuccessTitle": "Two-step verification is enabled", "settings.twoStepVerification.setVerifySuccessDes": "Every time you sign in {0}, you need to enter the verification code in addition to your account and password.", "settings.twoStepVerification.closeVerifySuccess": "Two-step verification has been disabled", "settings.thirdPartyBindFailure": "Linking failed", "settings.editPassword": "Change password", "settings.oldPassword": "Old password", "settings.newPassword": "New password", "settings.confirmPassword": "Confirm password", "settings.editPasswordSuccess": "Password changed successfully. Please login again.", "settings.email": "Email:", "settings.phone": "Mobile phone number:", "settings.emptyEmailInfo": "The email address can be used for login after being linked and verified", "settings.emptyPhoneInfo": "The mobile phone number can be used for login after being linked and verified", "settings.unvalidate": "Unverified", "settings.unvalidateCredentialUsage": "Can be used for login and retrieving your password after verification", "settings.validateTime": "Time of verification: {0}", "settings.validateCredentialUsage": "Can be used for login and retrieving password", "settings.validate": "Verify now", "settings.bindImmediately": "Link now", "settings.phoneNotAvaliable": "This phone number has been used by another account number. Please change it.", "settings.emailNotAvaliable": "This email address has been linked to another account. Please change it.", "base.cancelText": "Later", "settings.editNow": "Modify now", "settings.forbiddenEditEmail": "You can’t change the email address for now. Please try again after successful linking to your phone number.", "settings.editPhone": "Change mobile phone number", "settings.validatePhone": "Verify mobile phone number", "settings.bindPhone": "Link mobile phone number", "settings.editPhoneSuccess": "Your mobile phone number is successfully changed", "settings.validatePhoneSuccess": "The mobile phone number is successfully verified", "settings.bindPhoneSuccess": "The mobile phone number is successfully linked", "settings.currentCredential": "The existing linked number: {0}", "settings.mobilePlaceholder": "Enter your mobile phone number", "base.submit": "Submit", "settings.editEmail": "Change email address", "settings.validateEmail": "Verify email", "settings.bindEmail": "Link email address", "settings.enterNewEmail": "Enter a new email address", "settings.emailSended": "A verification email has been sent to {0}. Please check your inbox. This email address can be used for login and retrieving your password after verification.", "settings.emailSendedTips": "The email may be blocked by your email account. If you can’t find it in your inbox, you may need to check spam.", "settings.unbind": "Unlink", "settings.bind": "Link", "settings.qiyeweixinLabel": "Enterprise WeChat:", "settings.dingdingLabel": "DingTalk:", "settings.dingding": "DingTalk", "settings.qiyeweixin": "Enterprise WeChat", "settings.qihoo360": "360", "settings.qihoo360Label": "360 Account:", "settings.unbindThirdConfirmMain": "Are you sure that you want to unlink {0} account “{1}”?", "settings.unbindConfirmSub": "After being unlinked, the account number can’t be used for login", "settings.device.fromAndroid": "(Android App)", "settings.device.fromIos": "(iOS App)", "settings.device.fromWindowsSync": "(Sync for Windows)", "settings.device.fromMacSync": "(Sync for <PERSON>)", "settings.device.fromMacDesktop": "(FangCloud for Mac)", "settings.device.fromWindowsDesktop": "(FangCloud for Windows)", "settings.device.deviceName": "Device name", "settings.device.deivceLastLogin": "Recent access time", "settings.device.deivceLastLocation": "Recent access location", "settings.device.deivceOperation": "Operation", "settings.device.creditable": "Trust", "settings.device.current": "Current", "settings.device.ip": "IP address: {0}", "base.delete": "Delete", "settings.device.noresult": "No data for now", "settings.device.showMore": "Expand all", "settings.device.deleteIsCreditable": "Your account will be signed out of this device, and you need to login again. Two-step verification is required for next login.", "settings.device.deleteNotCreditable": "Your account will be signed out of this device, and you need to login again.", "settings.device.deleteDevice": "Delete device", "settings.device.hideMore": "<PERSON>de", "settings.twoStepVerification.typePhone": "Text message", "settings.twoStepVerification.typePhoneNumber": "(ending with {0})", "settings.twoStepVerification.typeWechat": "WeChat Official Account", "settings.twoStepVerification.typeGoogle": "Google Authenticator", "settings.twoStepVerification.hasOpen": "Two-step verification is enabled", "settings.twoStepVerification.validateTime": "(Set on {0})", "settings.twoStepVerification.toClose": "Disable Two-step verification", "settings.twoStepVerification.type": "Verificaiton method:", "base.modify": "Edit", "settings.twoStepVerification.info": "The two-step verification adds a layer of security to your account. Once two-step verification is enabled, you will need to enter a verification code in addition to the account number and password when you log into your FangCloud account (the verification code will be sent to your mobile phone).", "settings.twoStepVerification.toSet": "Click to enable", "settings.twoStepVerification.disableToClose": "The administrator has enabled \"Two-step verification of enterprise members\", and you can't disable Two-step verification by yourself.", "base.yfy": "FangCloud", "base.countrySelectorPlaceholder": "Search your country/region and area code", "base.searchEmpty": "No eligible search results", "base.cancel": "Cancel", "base.picCaptchaPlaceholder": "Enter captcha code", "base.smsCaptchaPlaceholder": "{0}-digit verification code", "base.getVerificationCode": "Get a verification code", "base.retrieveAfter": "{0}s", "base.retrieve": "Resend", "base.getVoice": "Use voice verification code", "base.voiceSMSCaptcha_send": "System will send you verification code via free call. You can retry after 60s.", "base.voiceSMSCaptchaConfirm": "We will send you a voice verification code to your registered phone number. Please answer the call and remember the {0}-digit verification code.", "base.receiveVoiceConfirm": "Send"}