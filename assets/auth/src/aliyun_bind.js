import fc from './utils/common';
import SmsValidate from './utils/SmsValidate';
import * as API from './constants/API';


fc.ready(() => {
    let ali_supplement_info = document.getElementById('ali_supplement_info') && document.getElementById('ali_supplement_info').value;
    let ali_biz_id;
    document.querySelector('form').addEventListener('submit', (e) => {
        e.preventDefault();
        let data = {
            bucket: fc.trim(document.querySelector('[name=bucket]').value),
            key: fc.trim(document.querySelector('[name=key]').value),
            secret: fc.trim(document.querySelector('[name=secret]').value),
            ali_supplement_info,
            ali_biz_id
        };

        // fc.ajax({
        //     // url: API.TWO_STEP_LOGIN_ACTION,
        //     data,
        //     accept_all_errors: true,
        //     parser: (res) => {

        //     }
        // });
    });
});

// ajax Setup

// 多语言选项
