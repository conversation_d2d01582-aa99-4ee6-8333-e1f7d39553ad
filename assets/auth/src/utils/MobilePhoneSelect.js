import Events from './Events';
import Country from './Country';
import fc from './common';

class MobilePhoneSelect extends Events {
    constructor(options) {
        super(options);

        this.options = {
            template: (accumulator, {name, code}) => `${accumulator}<option value="${code}">${name} +${code}</option>`,
            default_code: 86,
            notShowCountry: false,
            showCountry: true,
            ...options
        };

        this.render();
        this.bind();
    }

    render() {
        const { element, template, default_code, notShowCountry, showCountry } = this.options;
        this.$element = element;
        this.$container = document.createElement('div');
        fc.addClass(this.$container, 'form-group');
        this.$container.innerHTML = `<div class="input-group select-group"></div>`;
        this.$select = document.createElement('select');
        this.$container.querySelector('.select-group').appendChild(this.$select);
        let list = Country.getAll();


        this.$select.innerHTML = list.reduce(template, '');
        this.$input_phone = this.$element.querySelector('[data-role=phone],[name=phone],[name=login]');
        if(!notShowCountry && showCountry) {
            this.$element.parentNode.insertAdjacentElement('beforebegin', this.$container);
        }
        this.setValue(default_code);
    }

    bind() {
        this.$select.addEventListener('change', (e) => {
            this.trigger('change');
            this.trigger('code_change', {code: e.target.value});
        });

        this.$input_phone.addEventListener('input', () => {
            this.trigger('phone_change');
            this.trigger('change_delay');
        });

        this.$input_phone.addEventListener('change', () => {
            this.trigger('phone_change');
            this.trigger('change');
        });
    }

    getValue(options) {
        options = {
            widthout_country_code: false,
            ...options
        };
        let input = fc.trim(this.$input_phone.value);

        if(!options.widthout_country_code && this.$select){
            let country_code = this.$select.value;
            if(country_code && country_code !== '86'){
                input = `(+${country_code})${input}`;
            }
        }
        return input;
    }

    getCode() {
        return this.$select.value;
    }

    setValue(value) {
        this.$select.value = value;
    }
}

export default MobilePhoneSelect;