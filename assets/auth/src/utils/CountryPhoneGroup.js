import Events from './Events';
import CountrySelect from './CountrySelect';
import fc from './common';

class CountryPhoneGroup extends Events {
    constructor(options) {
        super(options);
        this.options = {
            input_phone_template: '<input type="text" name="phone" class="phone" data-role="phone" value="">',
            tagName: 'div',
            className: 'input-group',
            showSelectCheck: /^\d+$/,
            countrySelectOpitons: {},
            showSelectClassName: 'show-country-select',
            change_delay_time: 600,
            country_change_check: 'input', // input or change
            ...options
        };

        this.init();
    }

    init() {
        const {element, notShowCountry, showCountry } = this.options;

        this.$element = element;

        this.$input_phone = this.$element.querySelector('[data-role=phone],[name=phone],[name=login]');

        fc.bindInputChange(this.$input_phone, () => {
            this.triggerChange();
        });

        this.$input_phone.addEventListener('change', () => {
            this.trigger('phone_change');
            this.trigger('change');
        });



        this.$align_element = this.$element;

        if(!notShowCountry && (showCountry || this.is_showCountrySelect(this.getValue()))){
            this.showCountrySelect();
        }
    }

    destroy(){
        if(this.countrySelect) this.countrySelect.destroy();
    }

    createCountrySelect(){
        let countrySelect = new CountrySelect(this.options.countrySelectOpitons);
        this.$input_phone.insertAdjacentElement('beforebegin', countrySelect.$element);
        this.countrySelect = countrySelect;
        countrySelect.on('change', (data) => {
            this.triggerChange();
            this.trigger('code_change', data);
            this.trigger('change', data);
        });
    }

    triggerChange(){
        this.trigger('change_every');
        this.stopChangeDelay();
        this._change_delay_timer = setTimeout(() => {
            this.trigger('change_delay');
        }, this.options.change_delay_time);
    }

    stopChangeDelay(){
        clearTimeout(this._change_delay_timer);
    }

    showCountrySelect(){
        if(!this.countrySelect){
            this.createCountrySelect();
        }

        this.countrySelect.show();
        fc.addClass(this.$element, this.options.showSelectClassName);
    }

    hideCountrySelect(){
        if(this.countrySelect){
            this.countrySelect.hide();
            fc.removeClass(this.$element, this.options.showSelectClassName);
        }
    }

    is_showCountrySelect(str){
        if(typeof this.options.showSelectCheck === 'function'){
            if(this.options.showSelectCheck(str)){
                return true;
            }
        } else if(this.options.showSelectCheck instanceof RegExp){
            if(str && this.options.showSelectCheck.test(str)){
                return true;
            }
        } else if(str && this.options.showSelectCheck){
            return true;
        }
    }

    getValue(options){
        options = {
            widthout_country_code: false,
            ...options
        };
        let input = fc.trim(this.$input_phone.value);

        if(!options.widthout_country_code && this.countrySelect && this.countrySelect.is_show()){
            let country_code = this.getCode();
            if (country_code && country_code !== '86'){
                input = `(+${country_code})${input}`;
            }
        }
        return input;
    }

    getCode () {
        return this.countrySelect.getValue();
    }

    setValue(new_phone, options){
        this.$input_phone.value = new_phone;
        if(!options || !options.silent){
            this.triggerChange();
            this.trigger('phone_change');
            this.trigger('change');
        }
    }

    setDisabled() {
        this.$input_phone.setAttribute('disabled', 'disabled');
        fc.addClass(this.$element, 'disabled');
        if(this.countrySelect) this.countrySelect.setDisabled();
    }

}

export default CountryPhoneGroup;
