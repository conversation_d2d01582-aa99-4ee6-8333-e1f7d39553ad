import {language, defineMessage} from '@q/fang-intl';

const Country = {
    language,
    _list: {
        'zh-CN': [{'name':'中国大陆','code':'86'}],
        'en': [{'name':'China','code':'86'}]
    },
    getLanguageKey: function(language){
        if(!language) language = this.language;
        if(language !== 'zh-CN'){
            language = 'en';
        }
        return language;
    },
    getAll: function(language){
        let _country;
        language = this.getLanguageKey(language);
        _country = this._list[language];
        return _country;
    },

    getMatched: function(key, language) {
        let _country;
        language = this.getLanguageKey(language);
        _country = this._list[language].filter(({name, code}) => name.indexOf(key) >= 0 || (code + '').indexOf(key) >= 0);
        return _country;
    }
};
export default Country;