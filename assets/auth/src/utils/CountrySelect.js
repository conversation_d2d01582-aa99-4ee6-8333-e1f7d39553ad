import Events from './Events';
import fc from './common';
import {defineMessage} from '@q/fang-intl';
import Country from './Country';

class CountrySelect extends Events {
    constructor(options) {
        super(options);

        this.options = {
            tagName: 'div',
            template: `<input name="country_code" type="hidden" value="86">
                        <i class="iconfont select-arrow icon-arrow-down"></i>
                        <div class="select" data-value=""></div>
            `,
            className: 'select-group input-group-addon',
            showSelector: true,
            display: 'table-cell',
            default_code: fc.international_mobile_phone ? 1 : 86,
            ...options
        };

        this.code = this.options.default_code;

        this.render();
        this.bind();
    }

    render() {
        const { tagName, className, template, default_code } = this.options;

        this.$element = document.createElement(tagName);
        fc.addClass(this.$element, className);

        this.$contentElement = document.createElement('div');
        this.$element.appendChild(this.$contentElement);
        fc.addClass(this.$contentElement, 'select-group-content country-select');

        this.$contentElement.innerHTML = template;
        this.$select = this.$contentElement.querySelector('.select');
        this.$optionsElement = document.createElement('div');
        fc.addClass(this.$optionsElement, 'select-group-options');

        this.$optionsElement.innerHTML = `
            <div class="input-group">
                <i class="input-group-addon iconfont icon-message-search"></i>
                <input type="text" class="text form-control" placeholder="${defineMessage({id: 'base.countrySelectorPlaceholder', defaultMessage: '搜索你的国家/地区、区号'})}">
                <i class="input-group-addon clear-text iconfont icon-search-clear"></i>
            </div>
            <ul></ul>
        `;

        this.$element.appendChild(this.$optionsElement);
        // 设置宽度
        // this.$optionsElement.style.width = align_element.offsetWidth + 'px';
        // 初始化国家
        this.generateCountry();

        // TODO remove
        this.selectCountry({code: default_code});
    }

    generateCountry(key) {
        let list;
        if(key) {
            list = Country.getMatched(key);
        } else {
            list = Country.getAll();
        }
        // 生成列表
        let country;

        if( list.length > 0 ) {

            country = list.reduce((accumulator, {code, name}) => `
                ${accumulator}<li${this.code == code ? ' class="selected" ' : ' '}data-value="${code}" data-name="${name}">
                    <span class="country-name ellipsis" title="${name}">${name}</span>
                    <span class="country-code"> + ${code}</span>
                </li>
            `, '');
        } else {
            country = `<span class="empty">${defineMessage({id: 'base.searchEmpty', defaultMessage: '无符合条件的搜索结果'})}</span>`;
        }

        this.$optionsElement.querySelector('ul').innerHTML = country;

    }

    bind() {
        this.$optionsElement.addEventListener('click', (e) => this.handleClick(e));
        this.$contentElement.addEventListener('click', (e) => {
            e.stopPropagation();
            if(this.disabled) return;
            if(!this.display) {
                this.display = true;
                this.$optionsElement.style.display = 'block';
                fc.removeClass(this.$contentElement.querySelector('.select-arrow'), 'icon-arrow-down');
                fc.addClass(this.$contentElement.querySelector('.select-arrow'), 'icon-arrow-up');

                let $searchInput = this.$optionsElement.querySelector('input')
                if($searchInput.value) {
                    $searchInput.value = '';
                    fc.triggerNative($searchInput, 'keyup');
                }
            } else {
                this.handleClose();
            }
        });

        fc.bindInputChange(this.$optionsElement.querySelector('input'), (e) => {
            const value = fc.trim(e.target.value);
            if(value) {
                fc.nextElementSibling(e.target).style.display = 'table-cell';
            } else {
                fc.nextElementSibling(e.target).style.display = 'none';
            }
            this.generateCountry(value);
        });

        this.$optionsElement.querySelector('.clear-text').addEventListener('click', (e) => {
            fc.previousElementSibling(e.target).value = '';
            e.target.style.display = 'none';
            this.generateCountry();
        });

        window.addEventListener('click', this.handleClose);
    }

    handleClick(e) {
        e.stopPropagation();
        let $target = fc.matchSelector(e.target, 'li');
        if(!$target) return false;
        let code = $target.getAttribute('data-value');
        let $parent = $target.parentNode;
        if($parent.querySelector('.selected')) {
            fc.removeClass($parent.querySelector('.selected'), 'selected');
        }
        fc.addClass($target, 'selected');

        this.selectCountry({code});
    }

    handleClose = () => {
        this.$optionsElement.style.display = 'none';
        fc.removeClass(this.$contentElement.querySelector('.select-arrow'), 'icon-arrow-up');
        fc.addClass(this.$contentElement.querySelector('.select-arrow'), 'icon-arrow-down');
        this.display = false;
    }

    selectCountry({code, name}) {
        let tpl = `<span class="country-code-icon">+</span><span class="country-code">${code}</span>`;
        this.$select.innerHTML = tpl;
        this.$select.setAttribute('data-value', code);
        this.$contentElement.querySelector('[name=country_code]').value = code;
        this.trigger('change', {code});
        this.code = code;
        this.handleClose();
    }

    destroy(){
        // if(this.popup) this.popup.destroy();
    }

    showSelectedOption(selected_country){
        //  var text = this.options.selected_template.replace(/\{\{(\w+)\}\}/g, function(a, a1){
        //      if(selected_country[a1]){
        //         return selected_country[a1];
        //      }
        // });
        // this.$content_element.find('.select').attr('data-value', selected_country.code).html(text);
        // this.$content_element.find(':hidden').val(selected_country.code);
    }

    setValue(value){
        var option = this.$options_element.querySelector(`[data-value=${value}]`);
        if(option.length){
            var selected_country = {
                code: option.getAttribute('data-value'),
                name: option.getAttribute('data-name')
            };
            this.selectCountry(selected_country);
        }
    }

    getValue(){
        return this.$element.querySelector('[name=country_code]').value;
    }

    show(){
        this.$element.style.display = this.options.display;
    }

    hide(){
        this.$element.style.display = 'none';
    }

    is_show(){
        return fc.isVisible(this.$element);
    }

    setDisabled(disabled) {
        if(disabled === undefined) disabled = true;
        this.disabled = disabled;
    }



}

export default CountrySelect;