import fc from './common';

class Notification {
    constructor(options) {
        this.options = {
            duration: 1.5,
            prefixCls: 'toast',
            closable: true,
            style: {},
            ...options
        };


        const { duration, onClose } = this.options;
        if (duration) {
            this.closeTimer = setTimeout(() => {
                this.close();
            }, duration * 1000);
        }

        if(onClose) {
            this.onClose = onClose;
        }

        this.render();

    }

    clearCloseTimer() {
        if (this.closeTimer) {
            clearTimeout(this.closeTimer);
            this.closeTimer = null;
        }
    }

    onClose() {}

    close() {
        this.clearCloseTimer();
        this.onClose();
        if(this.options.closable) {
            this.$el.querySelector('[data-role=close]').removeEventListener('click', this.close);
        }
        fc.removeElement(this.$el);
    }

    render() {
        const { prefixCls, closable, message, className } = this.options;
        let cls = `${prefixCls}${className ? ' ' + className : ''}`,
        style= '',
        componentCls = `${prefixCls}-notice`;

        let content = `
            <div class="${componentCls}">
                <div class="${componentCls}-content">
                    ${message}
                    ${closable ? `<i data-role="close" class="iconfont icon-close ${componentCls}-close"></i>` : ''}
                </div>
            </div>
        `;


        this.$el = document.createElement('div');
        fc.addClass(this.$el, cls);
        this.$el.setAttribute('style', style);
        this.$el.innerHTML = content;
        document.body.appendChild(this.$el);
    }

    bindEvents() {
        const { closable } = this.options;
        if(closable) {
            this.$close = this.$el.querySelector('[data-role=close]');
            this.$close.addEventListener('click', this.close);
        }

    }

}

export default Notification;