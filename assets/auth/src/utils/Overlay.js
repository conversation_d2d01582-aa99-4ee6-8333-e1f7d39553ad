function getStyles({show}){
    const style = {
        position: 'fixed',
        width: '100%',
        height: '100%',
        top: 0,
        left: '-100%'
        //TODO add transition
    };

    if (show) {
        Object.assign(style, {
            left: 0
        });
    }

    return style;
}

class Overlay {
    constructor(options){
        this.options = options;
    }

    render() {
        const { style } = this.options;
        this.$dom = document.creatElement('div');
        const defaultStyles = getStyles(this.options);
        div.className = 'overlay';
        let computeStyle = Object.assign(defaultStyles, style);
        for(let key in computeStyle) {
            div.style[key] = computeStyle[key];
        }
    }
}

export default Overlay;