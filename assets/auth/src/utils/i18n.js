import { setLocales } from '@q/fang-intl';
import localeData from './../../../../public/auth/static/locales/data.json';

// Define user's language. Different browsers have the user locale defined
// on different fields on the `navigator` object, so we make sure to account
// for these different by checking all of them
// const language = (navigator.languages && navigator.languages[0]) ||
//                      navigator.language ||
//                      navigator.userLanguage;
export let language = document.getElementById('Language') && document.getElementById('Language').value;

language = language || 'zh-CN';
const languageType = document.getElementById('language_type') && document.getElementById('language_type').value;
// Split locales with a region code
const languageWithoutRegionCode = language.toLowerCase().split(/[_-]+/)[0];
// Try full locale, fallback to locale without region code, fallback to en
let messages = localeData[languageWithoutRegionCode] || localeData[language] || localeData['zh-CN'];

export const setLanguageType = (type) => {
    if (type !== 'enterprise') {
        messages = Object.assign(messages, localeData[`${language}-${type}`] || {});
    }
};

if (languageType) {
    setLanguageType(languageType);
}

setLocales(messages, language);
