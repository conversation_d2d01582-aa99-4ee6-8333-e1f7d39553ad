import { JSEncrypt } from 'jsencrypt';

// RSA 非对称加密 

// 单向数据传输：client向server传输用户敏感数据时的 PublicKey
// 双向数据传输，需要设计完善的key传输机制
// 1024-bit keys can only encrypt 117 bytes without some sort of padding, but a 2048-bit one can handle up to 245 bytes, and a 4096-bit key up to 501 bytes. 
// 2048-bit PublicKey
const kPublicKey = '-----BEGIN PUBLIC KEY----- \
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAvikNrllO99UrXzzbJ2sY \
Cv37fFeXXVgdTY3C/enrJtzkVWPGyzNiB38Ry0yaHnWDli5A+3H0UuK1OZUdr3Yo \
VijJTYVrtJcAu3qZa2DwpicpQasTCaPHmwPdlGv29DyS3MAlUWi7Y0ScVpHItrn/ \
ORvqWs08cY/u8RolHwgZH4kAlpyC1JaToivWkmtcqMnmwf2STyvL/5nNujALIs4+ \
xsy1Jb8sYCPLQXAOHxNsKKrYzCvsh97F2eIh5SjBHsbbSXAAmiNfgyPTWIlLjDaI \
nKKbyjmjJ/6BctY00xGjIvKRKvWOH+9Xz1wt2kZbbfh2m4r1PS6B6+n6+Trn650T \
AQIDAQAB \
-----END PUBLIC KEY-----';

const white_list = ['password', 'old_password', 'login', 'phone', 'email', 'password_confirmation'];

export default {

    makeDataSafely: function (data='', publicKey=kPublicKey) {
        if (typeof data !== 'object') {
            return data;
        }
        let box = {};
        Object.keys(data).forEach(key => {
            if (white_list.indexOf(key) > -1) {
                box[key] = data[key];
                delete data[key];
            }
        });
        if (Object.keys(box).length > 0) {
            // 新建JSEncrypt对象
            let encryptor = new JSEncrypt();
            // 设置公钥
            encryptor.setPublicKey(publicKey);
            // 加密数据
            let blackBoxData = encryptor.encrypt(JSON.stringify(box));
            // 加密出错，极有可能是太长，导致加密失败
            if (blackBoxData === false) {
                Object.keys(box).forEach(key => {
                    data[key] = box[key];
                });
            } else {
                data['secure_zone'] = blackBoxData;
            }
        }
        return data;
    },
    encryptData: function (data='', publicKey=kPublicKey) {
        if (typeof data === 'object') {
            data = JSON.stringify(data);
        }
        // 新建JSEncrypt对象
        let encryptor = new JSEncrypt();
        // 设置公钥
        encryptor.setPublicKey(publicKey);
        // 加密数据
        return encryptor.encrypt(data);
    },
        // 解密
    decryptData(encryptedData, privateKey) {
        // 新建JSEncrypt对象
        let decrypt= new JSEncrypt();
        // 设置私钥
        decrypt.setPrivateKey(privateKey);
        // 解密数据
        return decrypt.decrypt(encryptedData);
    }
};
