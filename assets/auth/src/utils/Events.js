import CustomEvent from './CustomEvent';

class Events {
    createEvent() {
        return new CustomEvent();
    }

    on(name, callback, context) {
        this._events || (this._events = {});
        var events = this._events[name] || (this._events[name] = []);
        events.push({callback: callback, context: context, ctx: context || this});
        return this;
    }

    off(name, callback, context) {
        var retain, ev, events, names, i, l, j, k;
        if (!name && !callback && !context) {
            this._events = void 0;
            return this;
        }
        if (name) {
            names = [name];
        } else {
            names = [];
            for (i in this._events){
                names.push(i);
            }
        }
        for (i = 0, l = names.length; i < l; i++) {
            name = names[i];
            if (events = this._events[name]) {
                this._events[name] = retain = [];
                if (callback || context) {
                    for (j = 0, k = events.length; j < k; j++) {
                        ev = events[j];
                        if ((callback && callback !== ev.callback && callback !== ev.callback._callback) ||
                            (context && context !== ev.context)) {
                            retain.push(ev);
                        }
                    }
                }
                if (!retain.length) delete this._events[name];
            }
        }
        return this;
    }

    trigger(name) {
        var customEvent = this.createEvent();
        if (!this._events) return customEvent;
        var args = Array.prototype.slice.call(arguments, 1);
        var events = this._events[name];
        var allEvents = this._events.all;
        var i, l, ev;
        args.push(customEvent);
        if (events) {
            for(i = 0, l = events.length; i < l; i++){
                ev = events[i];
                ev.callback.apply(ev.ctx, args);
            }
        }
        if (allEvents) {
            for(i = 0, l = allEvents.length; i < l; i++){
                ev = allEvents[i];
                ev.callback.apply(ev.ctx, args);
            }
        }
        return customEvent;
    }
}

export default Events;