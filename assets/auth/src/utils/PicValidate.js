import BaseValidate from './BaseValidate';
import {defineMessage} from '@q/fang-intl';
import fc from './common';

class PicValidate extends BaseValidate {
    constructor(options) {
        super(options);
        this.pic_captcha_url = this._get_pic_captcha_url();

        this.options = {
            template: `<div class="input-group">
                        <input type="text" name='pic_captcha' class="form-control text" maxlength="4" data-role="captcha" autocomplete="off" placeholder="${defineMessage({id:'base.picCaptchaPlaceholder', defaultMessage: '请输入图形验证码'})}">
                        <span class="input-group-addon get-captcha" data-role="get_captcha_box">
                            <img data-role="get_captcha" src="${this.pic_captcha_url}" alt="">
                        </span>
                    </div>`,
            captchaName: 'pic_captcha_code',
            focusOnrender: true,
            ...options
        };

        this.callback = options.callback || {};
        if(!this.options.has_render){
            this.render();
        } else {
            this.$el = options.element;
        }
        this.bind();

    }

    render(){
        fc.addClass(this.$el, 'form-group pic-captcha show');
        this.$el.innerHTML = this.options.template;
        this.$captcha_pic = this.$el.querySelector('[data-role=get_captcha]');
        this.$captcha_pic.addEventListener('click', () => {
            this.refresh();
        });

        // if(!('placeholder' in document.createElement('input'))){
        //     this.$el.find('input[placeholder]').placeholder();
        // }
    }

    _get_pic_captcha_url(){
        const {picUrl} = this.options;
        return `${picUrl}?t=${new Date().getTime()}`;
    }

    bind() {
        // var _self = this;
        this.$captcha = this.$el.querySelector('[data-role=captcha]');
        fc.initInputAction(this.$captcha);
        fc.bindInputChange(this.$captcha, (e) => this.trigger('change', e.target.value));

        if(this.options.focusOnrender) {
            setTimeout(() => {
                this.$captcha.focus();
                this.$captcha.select();
            }, 0);
        }
    }

    getValue() {
        if(!this.isDisabled()){
            return this.$captcha.value;
        }
    }

    refresh() {
        if(this.isDisabled()) return false;
        this.pic_captcha_url = this._get_pic_captcha_url();
        this.$captcha_pic.src = this.pic_captcha_url;
        this.$captcha.value = '';
        this.trigger('change', '');

    }

    show(){
        this.$el.style.display = 'block';
        fc.addClass(this.$el, 'show');
        this.setDisabled(0);
        this.trigger('show');
    }

    hide(){
        this.$el.style.display = 'none';
        fc.removeClass(this.$el, 'show');
        this.setDisabled();
        this.trigger('hide');
    }

    destroy(){
        this.trigger('destroy');
        this.callback = null;
    }

}

export default PicValidate;