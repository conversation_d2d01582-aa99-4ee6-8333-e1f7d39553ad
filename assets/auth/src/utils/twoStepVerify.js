import Events from './Events';
import fc from './common';
import {defineMessage} from '@q/fang-intl';
import Dialog from './Dialog';
import SmsValidate from './SmsValidate';
import CountryPhoneGroup from './CountryPhoneGroup';
import * as API from '../constants/API';
import { PASSWORD_VERIFY_STATUS_EXPIRED } from '../constants/code';
import Encrypt from './rsaEncrypt';
import CryptoJS from 'crypto-js';
import PicValidate from '../utils/PicValidate';
let picValidate;
let smsValidate;
let v2_picValidate;
// import './css/two_step_verify.scss';

// const IDENTITY_NEED_TWO_STEP_VERIFY = 300003;

class TwoStepVerify extends Events {
    constructor(jobName, successCallback) {
        super();
        const basicJobs = ['needVerify', 'checkPassword'/*, 'checkVerify'*/];

        const jobs = {
            set_two_step: [
                ...basicJobs,
                'setTwoStep'

                // 'selectType',
                // {
                //     setByWechat: 'setByWechat',
                //     setBySMS: 'setBySMS',
                //     setByGoogle: ['downloadGoogle', 'setByGoogle']
                // },
                // 'success',
                // successCallback

            ],
            modify_two_step: [
                ...basicJobs,
                'modifyTwoStep'
            ],
            'close_two_step': [
                ...basicJobs,
                'closeSuccess',
                successCallback
            ],
            'force_set_two_step': [
                'selectType',
                {
                    setByWechat: 'setByWechat',
                    setBySMS: 'setBySMS',
                    setByGoogle: ['downloadGoogle', 'setByGoogle']
                },
                'login',
                successCallback
            ]
        };

        this.step = 0;
        this.childStep = 0;
        this.childStepLength = 0;
        this.history = [];

        // if(this.force) {
        //     let container = document.querySelector('.verify-form');
        //     fc.addClass(container.parentNode, 'dialog-two-step-verify');
        // }

        if(typeof jobName === 'function') {
            // jobName 是function时， successCallback 作为判断需不需要密码验证的条件
            if(!!document.getElementById('set-password')) {
                this.tasks = ['setPassword', jobName];
            } else {
                this.tasks = [...(successCallback ? basicJobs : []), jobName];
            }
        } else if(typeof jobName === 'string') {
            this.tasks = jobs[jobName];
        }
        this.then();
    }

    then(taskName, options) {
        let currentTask = this.tasks[this.step];
        let task;
        this.childStepLength = 0;
        if(typeof currentTask === 'string') {
            this.step++;
            task = currentTask;
        } else if(typeof currentTask === 'function') {
            this.step++;
            currentTask(options);
            return;
        } else if(typeof currentTask === 'object') {
            if(Array.isArray(currentTask[taskName])) {
                this.childStepLength = currentTask[taskName].length - 1;
                task = currentTask[taskName][this.childStep];
                if(this.childStep < this.childStepLength) {
                    this.childStep++;
                } else {
                    this.step++;
                    this.childStep = 0;
                }
            } else {
                this.step++;
                task = currentTask[taskName];
            }
        }

        this[task](options);
        this.history.push(task);
    }

    jump(step, taskName, options) {
        this.step += step;
        this.then(taskName, options);
    }

    fallback() {
        if(this.childStep > 0) {
            this.childStep--;
        } else {
            this.step--;
            if(this.childStepLength) {
                this.childStep = this.childStepLength;
            }
        }

        this.history.pop();
        let task = this.history[this.history.length - 1];
        this[task]();
    }

    needVerify() {
        fc.ajax({
            url: API.VERIFY_IDENTITY,
            method: 'POST',
            accept_all_errors: true,
            parser: (res) => {
                const { errors, success } = res;
                if(errors && errors[0].error_code == PASSWORD_VERIFY_STATUS_EXPIRED) {
                    // 需要密码验证
                    this.then();
                // } else if(errors && errors[0].error_code == IDENTITY_NEED_TWO_STEP_VERIFY) {
                //     this.jump(1, null, {method: errors[0].method, identity: errors[0].identity});
                } else if(success) {
                    // 无需密码
                    this.jump(1);
                }
            }
        });
    }

    // redirctTwoStep() {
    //     window.location.href = API.SET_TWO_STEP;
    // }

    setTwoStep() {
        window.location.href = API.SET_TWO_STEP + '?type=set';
    }

    modifyTwoStep() {
        window.location.href = API.SET_TWO_STEP + '?type=modify';
    }

    checkPassword() {
        let dialog = new Dialog({
            title: defineMessage({id: 'settings.confirmLoginPassword', defaultMessage: '验证身份'}),
            className: 'dialog-two-step-verify',
            content: `<div class="form check-password">
                <p class="label-hint">${defineMessage({id: 'settings.confirmLoginPasswordHint', defaultMessage: '为保障你的帐号安全，请输入登录密码进行身份验证。'})}</p>
                <div class="form-group">
                    <div class="input-group">
                        <input type="password" name="password" class="password text form-control" data-role="password" placeholder="${defineMessage({id: 'base.inputLoginPassword', defaultMessage: '请输入你的登录密码'})}" id="password" autocomplete="off">
                    </div>
                </div>
            </div>`,
            needValidate: true,
            confirmText: defineMessage({id: 'base.next', defaultMessage: '下一步'}),
            success: () => {
                fc.ajax({
                    url: API.VERIFY_IDENTITY,
                    data: Encrypt.makeDataSafely({
                        password: fc.trim(dialog.$('[name=password]').value)
                    }),
                    accept_all_errors: true,
                    parser: (res) => {
                        if(res.success) {
                            dialog.destroy();
                            // this.jump(1);
                            this.then();
                        } else {
                            const { errors } = res;
                            // if(errors[0].error_code == IDENTITY_NEED_TWO_STEP_VERIFY) {
                            //     // 需要二次验证
                            //     this.then(null, {method: errors[0].method, identity: errors[0].identity});
                            //     dialog.destroy();
                            // } else {
                                errors.forEach((error) => {
                                    fc.insertError(error.field, error.error_msg, dialog.$('.form'));
                                });
                            // }
                        }
                    }
                });
            }
        }).show();

        fc.initInputAction(dialog.$('[data-role=password]'));

        dialog.on('change', function(){
            dialog.$('.btn-primary').disabled = false;
        });

        dialog.layer.addEventListener('keyup', function(e){
            dialog.trigger('change', e);
        });
    }

    setPassword() {
        let dialog = new Dialog({
            title: defineMessage({id: 'settings.setLoginPassword', defaultMessage: '设置独立登录密码'}),
            className: 'dialog-two-step-verify',
            content: `<div class="form set-password">
                <p class="label-hint">
                    ${defineMessage({id: 'settings.setLoginPasswordHint1', defaultMessage: '设置独立登录密码，可配合绑定的邮箱/手机登录。'})}
                    <br>
                    ${fc.passwordStrength()}
                </p>
                <div class="form-group">
                    <div class="input-group">
                        <input type="password" name="password" class="password text form-control" data-role="password" placeholder="${defineMessage({id: 'settings.pleaseSetPassword', defaultMessage: '独立登录密码'})}" id="password" autocomplete="off">
                    </div>
                </div>
            </div>`,
            needValidate: true,
            confirmText: defineMessage({id: 'base.next', defaultMessage: '下一步'}),
            success: () => {
                fc.ajax({
                    url: API.SET_PASSWORD,
                    data: Encrypt.makeDataSafely({
                        password: fc.trim(dialog.$('[name=password]').value)
                    }),
                    accept_all_errors: true,
                    parser: (res) => {
                        if(res.success) {
                            dialog.destroy();
                            this.then(null, res);
                        } else {
                            const { errors } = res;
                            errors.forEach((error) => {
                                fc.insertError(error.field, error.error_msg, dialog.$('.form'));
                            });
                        }
                    }
                });
            }
        }).show();

        fc.initInputAction(dialog.$('[data-role=password]'));

        dialog.on('change', function(){
            dialog.$('.btn-primary').disabled = false;
        });

        dialog.layer.addEventListener('keyup', function(e){
            dialog.trigger('change', e);
        });

        dialog.$('.get-detail') && dialog.$('.get-detail').addEventListener('click', function() {
            if(window.fangcloud && window.fangcloud.popWebUrl) {
                window.fangcloud.popWebUrl(this.getAttribute('href'));
            }
        });
    }

    // checkVerify(options) {
    //     // options = _.extend({
    //     //     submitText: fc.getI18n('user.setting.twoStepVerification.verifyCodeBtn')
    //     // }, options)
    //     options = {
    //         confirmText: defineMessage({id: 'settings.twoStepVerification.verifyCodeBtn', defaultMessage: '验证修改'}),
    //         title: defineMessage({id: 'settings.twoStepVerification.checkTwoStepTitle', defaultMessage: '修改二次验证'}),
    //         ...options
    //     };

    //     const { method, identifier, title, confirmText } = options;
    //     var checkVerifyDialog = new Dialog({
    //         title,
    //         className: 'dialog-two-step-verify-check-verfiy dialog-two-step-verify',
    //         content: `
    //             <div class="form">
    //                 <div class="dialog-center">
    //                     <div class="form-group captcha-group">
    //                         ${ method === 'google' ? `
    //                         <label class="label">${defineMessage({id: 'settings.twoStepVerification.verifyGoogleCode', defaultMessage: '请输入你谷歌身份验证器上随机产生的验证码'})}</label>
    //                         <div class="input-group">
    //                             <input data-role="captcha" class="text form-control" name="two_step_captcha" type="text" placeholder="" value="" autocomplete="off" maxlength="6" />
    //                         </div>
    //                         ` : ''}
    //                         ${ method === 'wechat' ? `
    //                         <label class="label">${defineMessage({id: 'settings.twoStepVerification.verifyWechatCode', defaultMessage: '请输入你亿方云公众号收到的验证码'})}</label>
    //                         ` : ''}
    //                         ${ method === 'sms' ? `
    //                         <label class="label">${defineMessage({id: 'settings.twoStepVerification.verifySMSCode', defaultMessage: '请输入你尾号{0}手机接收到的验证码'}, identifier)}</label>
    //                         ` : ''}
    //                     </div>
    //                 </div>
    //             </div>
    //         `,
    //         confirmText,
    //         success: () => {
    //             const data = {
    //                 code: checkVerifyDialog.$('[data-role=captcha]').value
    //             };
    //             if(!data.code) {
    //                 fc.insertError('test', 'please input code');
    //                 return false;
    //             }
    //             fc.ajax({
    //                 url: this.api.verify_identity,
    //                 data,
    //                 parser: () => {
    //                     this.then();
    //                     checkVerifyDialog.destroy();
    //                 }
    //             });
    //         }
    //     }).show();

    //     if(method !== 'google') {
    //         let smsValidate = new SmsValidate({
    //             api: this.api.send_two_step_code,
    //             maxLen: 6,
    //             showVoice: method === 'sms',
    //             fieldName: 'two_step_captcha',
    //             syncs: function() {
    //                 let data = {
    //                     method
    //                 };
    //                 return data;
    //             }
    //         });

    //         const $captchaGroup = checkVerifyDialog.$('.captcha-group');
    //         $captchaGroup.insertAdjacentElement('beforeend' ,smsValidate.$el);

    //         smsValidate.send();
    //     }


    //     return checkVerifyDialog;
    // }

    // closeVerify(options) {
    //     this.checkVerify({
    //         ...options,
    //         title: defineMessage({id: 'settings.twoStepVerification.toClose', defaultMessage: '关闭二次验证'}),
    //         confirmText: defineMessage({id: 'settings.twoStepVerification.verifyCodeToClose', defaultMessage: '验证关闭'})
    //     });
    // }



    getSelectType(data) {
        if(data && data.selectItems) {
            return new Promise(resolve => resolve(data.selectItems));
        } else {
            return fc.ajax({
                method: 'POST',
                url: API.GET_AVAILABLE_TWO_STEP_METHOD,
                parser: ({methods}) => {
                    let selectItems = methods.map((item, index) => {
                        return {type: item, recommend: index === 0};
                    });
                    return new Promise(resolve => resolve(selectItems));
                }
            });
        }
    }

    generateContainer({content, title, success}) {
        let container, dialog;
        // if(this.force) {
            container = document.querySelector('.verify-form');
            container.innerHTML = content;
        // } else {
        //     dialog = new Dialog({
        //         title,
        //         content,
        //         className: 'dialog-two-step-verify'
        //     }).show();
        //     container = dialog.layer;
        // }

        container.querySelector('.btn-primary').addEventListener('click', () => {
            success && success(() => {
                // dialog && dialog.destroy();
            });
        });

        // container.querySelector('.btn-default') && container.querySelector('.btn-default').addEventListener('click', () => {
        //     dialog && dialog.destroy();
        // });

        return container;
    }

    selectType() {
        let content = `
            <ul class="form select-list"></ul>
            <div class="dialog-actions">
                <button class="btn btn-primary" type="button"><span>${defineMessage({id: 'base.next', defaultMessage: '下一步'})}</span></button>
            </div>
        `;
        let container = this.generateContainer({
            title: defineMessage({id: 'settings.twoStepVerification.selectVerifyTitle', defaultMessage: '选择二次验证方式'}),
            content,
            success: (done) => {
                let type = container.querySelector('.selected').getAttribute('data-type');
                const actions = {
                    sms: 'setBySMS',
                    wechat: 'setByWechat',
                    google: 'setByGoogle'
                };
                done();
                this.then(actions[type]);
            }
        });

        container.addEventListener('click', (e) => {
            let target = fc.matchSelector(e.target, '[data-type]');
            if(!target) {
                return;
            }

            Array.prototype.filter.call(target.parentNode.children, function(child){
                if(child !== target && fc.hasClass(child, 'selected')) {
                    fc.removeClass(child, 'selected');
                }
            });

            fc.addClass(target, 'selected');
        });

        this.getSelectType().then((selectItems) => {
            container.querySelector('.select-list').outerHTML = `
                <ul class="form select-list" data-items-count="${selectItems.length}">
                    ${selectItems.map(({type, recommend}, index) => `
                        <li class="item${index === 0 ? ' selected': ''}" data-type=${type}>
                        ${type === 'wechat' ? `
                            <div class="item-title"><i class="iconfont icon-weixin"></i>${defineMessage({id: 'settings.twoStepVerification.wechat', defaultMessage: '微信'})}</div>
                            <p class="item-des">${defineMessage({id: 'settings.twoStepVerification.wechatSetDes', defaultMessage: '使用微信扫描并关注我们的微信公众号，我们会将验证码通过公众号发送给你'})}</p>
                        ` : ''}
                        ${type === 'sms' ? `
                            <div class="item-title"><i class="iconfont icon-phone"></i>${defineMessage({id: 'settings.twoStepVerification.sms', defaultMessage: '手机短信'})}</div>
                            <p class="item-des">${defineMessage({id: 'settings.twoStepVerification.smsSetDes', defaultMessage: '设置一个手机号，我们会将验证码通过短信方式发送给你。'})}</p>
                        ` : ''}
                        ${type === 'google' ? `
                            <div class="item-title"><i class="iconfont icon-google"></i>${defineMessage({id: 'settings.twoStepVerification.google', defaultMessage: '谷歌身份验证器'})}</div>
                            <p class="item-des">${defineMessage({id: 'settings.twoStepVerification.googleSetDes', defaultMessage: '你可以下载身份验证器应用来获取验证码，即使手机未连接网络也无妨。'})}</p>
                        ` : ''}
                        ${recommend ? `
                            <span class="recommend">${defineMessage({id: 'settings.twoStepVerification.recommend', defaultMessage: '推荐'})}</span>
                        ` : ''}
                        </li>
                    `).join('')}
                </ul>
            `;
        });

    }

    setByWechat() {
        let qrSrc = API.GET_WECHAT_QRCODE;
        if(fc.config._fstate) {
            qrSrc += `?_fstate=${fc.config._fstate}`;
        }
        let content = `
            <div class="form wechat-set">
                <div class="verify-tip">${defineMessage({id: 'settings.twoStepVerification.wechatDes1', defaultMessage: '1. 使用微信扫描二维码并关注我们的微信公众号，你将会收到验证码。'})}</div>
                <div class="verify-qrcode">
                    <div class="qrcode-box">
                        <img class="qrcode-image" src="${qrSrc}" width="142">
                    </div>
                    <div class="qrcode-tip">${defineMessage({id: 'settings.twoStepVerification.wechatDes2', defaultMessage: '如已关注公众号，可直接扫描'})}</div>
                </div>
                <div class="verify-tip">${defineMessage({id: 'settings.twoStepVerification.wechatDes3', defaultMessage: '2. 输入微信公众号发给你的6位验证码。'})}</div>
                <div class="form-group">
                    <div class="input-group">
                        <input class="text form-control" name="two_step_captcha" type="text" value="" autocomplete="off"  maxlength="6" />
                    </div>
                </div>
            </div>
            <div class="dialog-actions">
                <button class="btn btn-primary" type="button"><span>${defineMessage({id: 'base.twoStepVerification.setSubmit', defaultMessage: '完成设置'})}</span></button>
                <a class="backward action-box"><i class="iconfont icon-backward"></i><span>${defineMessage({id: 'base.previous', defaultMessage: '上一步'})}</span></a>
            </div>
        `;

        let container = this.generateContainer({
            title: defineMessage({id: 'settings.twoStepVerification.setByWechatTitle', defaultMessage: '设置微信'}),
            content,
            success: (done) => {
                let data = {
                    code: container.querySelector('[name=two_step_captcha]').value,
                    method: 'wechat'
                };
                this.setVerify(data, (res) => {
                    // this.trigger('success', res);
                    this.then(null, res);
                    done();
                }, function(res) {
                    res.errors.forEach((error) => {
                        fc.insertError(error.field, error.error_msg);
                    });
                });
            }
        });

        fc.initInputAction(container.querySelector('[name=two_step_captcha]'));

        container.querySelector('.dialog-actions .backward').addEventListener('click', () => {
            this.fallback();
        });

        // container.on('change', function(){
        //     container.querySelector('.btn-primary').disabled = false;
        // });

        // container.addEventListener('keyup', function(e){
        //     container.trigger('change', e);
        // });
    }

    createPicValidate() {
        const $form = document.querySelector('.sms-set');
        picValidate = new PicValidate({
            picUrl: API.TWO_SETP_CAPTCHA
        });

        smsValidate.setDisabled(1, 'get');

        picValidate.on('destroy', function() {
            fc.removeElement(this.$el);
            picValidate = null;
        });

        picValidate.on('change', function(value) {
            if(smsValidate.getState() !== 'working') {
                smsValidate.setDisabled(value ? 0 : 1, 'get');
            }
        });

        $form.insertBefore(picValidate.$el, $form.querySelector('.sms-item'));
    }

    setBySMS() {
        let content = `
            <div class="form sms-set">
                <div class="form-group country-phone">
                    <div class="input-group">
                        <input type="text" value="" name="phone" class="text form-control" data-role="phone" maxlength="11">
                    </div>
                </div>
            </div>
            <div class="dialog-actions">
                <button class="btn btn-primary" type="button"><span>${defineMessage({id: 'base.twoStepVerification.setSubmit', defaultMessage: '完成设置'})}</span></button>
                <a class="backward action-box"><i class="iconfont icon-backward"></i><span>${defineMessage({id: 'base.previous', defaultMessage: '上一步'})}</span></a>
            </div>
        `;

        let container = this.generateContainer({
            title: defineMessage({id: 'settings.twoStepVerification.setBySMSTitle', defaultMessage: '设置手机号'}),
            content,
            success: (done) => {
                let data = {
                    phone: countryPhoneGroup.getValue(),
                    code: smsValidate.getValue(),
                    method: 'sms'
                };
                this.setVerify(data, (res) => {
                    // this.trigger('success', res);
                    this.then(null, res);
                    done();
                }, function(res) {
                    res.errors.forEach((error) => {
                        fc.insertError(error.field, error.error_msg);
                    });
                });
            }
        });

        let countryPhoneGroup = new CountryPhoneGroup({
            element: container.querySelector('[data-role=phone]').parentNode,
            showCountry: true,
            countrySelectOpitons: {
                showSelector: true,
                default_code: 86
                // align_element: $('.register-form .phone .input-group')
            }
        });

        countryPhoneGroup.on('code_change', function({code}) {
            const $input = container.querySelector('[data-role=phone]');
            if(code == 86) {
                $input.setAttribute('maxLength', 11);
            } else {
                $input.setAttribute('maxLength', 20);
            }
        });

        countryPhoneGroup.on('change', function() {
            const $input = container.querySelector('[data-role=phone]');
            if(smsValidate.getState() !== 'working') {
                smsValidate.setDisabled($input.value ? 0 : 1, 'get');
            }
        });

        fc.initInputAction(container.querySelector('[data-role=phone]'));

        smsValidate = new SmsValidate({
            api: API.SEND_TWO_STEP_CODE,
            maxLen: 6,
            fieldName: 'two_step_captcha',
            syncs: function() {
                let newDate = Math.floor(new Date().getTime() / 1000);
                let data = {
                    method: 'sms',//sms wechat
                    phone: countryPhoneGroup.getValue(),
                    ts: newDate,
                    'login-sign': CryptoJS.MD5(`appId=${fc.login_sign_app_id}&ts=${newDate}&phone=t_phone&type=two_step`).toString()
                };
                if (picValidate) {
                    data.pic_captcha = picValidate.getValue();
                }

                if (v2_picValidate && v2_picValidate.captchaData) {
                    data = Object.assign(data, v2_picValidate.captchaData);
                    v2_picValidate.destroy();
                }

                return data;
            }
        });

        smsValidate.on('send_error', (res) => {
            res.errors.forEach((error) => {
                if(error.field === 'pic_captcha') {
                    if (picValidate) {
                        picValidate.refresh();
                    } else {
                        this.createPicValidate();
                    }
                } else if (error.field === 'sliding_pic_captcha') {
                    v2_picValidate = fc.qHPassCaptcha.init('');
                    return;
                }
            });
        });

        smsValidate.on('send_success', function(){
            if (picValidate) {
                picValidate.destroy();
                picValidate = null;
            }
        });

        smsValidate.setDisabled(1, 'get');

        const $form = container.querySelector('.sms-set');
        $form.insertAdjacentElement('beforeend', smsValidate.$el);

        container.querySelector('.dialog-actions .backward').addEventListener('click', () => {
            this.fallback();
        });
    }

    downloadGoogle() {
        let downloadInfo = [{
            type: 'ios',
            icon: 'icon-iphone',
            title: defineMessage({id: 'settings.twoStepVerification.googleIos', defaultMessage: 'iPhone用户'}),
            des1: defineMessage({id: 'settings.twoStepVerification.googleIosDes1', defaultMessage: '1.在iPhone上，打开App Store。'}),
            des2: defineMessage({id: 'settings.twoStepVerification.googleIosDes2', defaultMessage: '2.搜索谷歌身份验证器(Google Authenticator)'}),
            des3: defineMessage({id: 'settings.twoStepVerification.googleIosDes3', defaultMessage: '3.下载并安装应用。'})
        }, {
            type: 'android',
            icon: 'icon-android',
            title: defineMessage({id: 'settings.twoStepVerification.googleAndroid', defaultMessage: 'Android用户'}),
            des1: defineMessage({id: 'twoStepVerification.googleAndroidDes1', defaultMessage: '1.在手机上访问Google Play或其它商店。'}),
            des2: defineMessage({id: 'twoStepVerification.googleAndroidDes2', defaultMessage: '2.搜索谷歌身份验证器(Google Authenticator)'}),
            des3: defineMessage({id: 'twoStepVerification.googleAndroidDes3', defaultMessage: '3.下载并安装应用。'})

        }];

        let content = `
            <ul class="form select-list" data-items-count="2">
                ${downloadInfo.map(({icon, type, title, des1, des2, des3}, index) => `
                    <li class="item${index === 0 ? ' selected' : ''}" data-type="${type}">
                        <div class="item-title"><i class="iconfont ${icon}"></i>${title}</div>
                        <p class="item-des item-indent">${des1}</p>
                        <p class="item-des item-indent">${des2}</p>
                        <p class="item-des item-indent">${des3}</p>
                    </li>
                `).join('')}
            </ul>
            <a class="help-link" href="http://help.fangcloud.com/posts/view/215013/" target="_blank">${defineMessage({id: 'settings.twoStepVerification.googleHelp', defaultMessage: '如何安装google身份验证器？'})}</a>
            <div class="dialog-actions">
                <button class="btn btn-primary" type="button"><span>${defineMessage({id: 'base.next', defaultMessage: '下一步'})}</span></button>
                <a class="backward action-box"><i class="iconfont icon-backward"></i><span>${defineMessage({id: 'base.previous', defaultMessage: '上一步'})}</span></a>
            </div>
        `;

        let container = this.generateContainer({
            title: defineMessage({id: 'settings.twoStepVerification.downloadGoogleTitle', defaultMessage: '下载谷歌身份验证器'}),
            content,
            success: (done) => {
                this.then('setByGoogle', {
                    google_type: container.querySelector('.selected').getAttribute('data-type')
                });
                done();
            }
        });

        container.querySelector('.dialog-actions .backward').addEventListener('click', () => {
            this.fallback();
        });

        container.addEventListener('click', (e) => {
            let target = fc.matchSelector(e.target, '[data-type]');
            if(!target) {
                return;
            }

            Array.prototype.filter.call(target.parentNode.children, function(child){
                if(child !== target && fc.hasClass(child, 'selected')) {
                    fc.removeClass(child, 'selected');
                }
            });

            fc.addClass(target, 'selected');
        });
    }

    setByGoogle(options) {
        const { google_type } = options;
        let { product_name } = fc.config;

        let qrSrc = API.GET_GOOGLE_QRCODE;
        if(fc.config._fstate) {
            qrSrc += `?_fstate=${fc.config._fstate}`;
        }
        let content = `
            <div class="form google-set">
                <div class="verify-qrcode">
                    <div class="verify-tip">${google_type === 'ios' ?
                        defineMessage({id: 'settings.twoStepVerification.googleVerifyQRcodeIosDes', defaultMessage: '1. 打开谷歌身份验证器，点击+通过扫描二维码完成设置'})
                        : defineMessage({id: 'settings.twoStepVerification.googleVerifyQRcodeDes', defaultMessage: '1.打开应用，点击菜单，选择“设置帐户”，再扫描条形码即可。'})
                    }</div>
                    <div class="qrcode-box">
                        <img class="qrcode-image" src="${qrSrc}">
                    </div>
                    <div class="qrcode-tip">
                        <span>${defineMessage({id: 'settings.twoStepVerification.googleVerifyCantQRcode', defaultMessage: '无法扫描？'})}</span>
                        <a class="to-manually" href="#">${defineMessage({id: 'settings.twoStepVerification.googleVerifyToManually', defaultMessage: '手动设置'})}</a>
                    </div>

                </div>
                <div class="verify-manually hidden">
                    ${google_type === 'ios' ? `
                        <div class="verify-tip">${defineMessage({id: 'settings.twoStepVerification.googleVerifyManuallyIosDes1', defaultMessage: '1.打开谷歌身份验证器，点击+'})}</div>
                        <p class="verify-tip-des">${defineMessage({id: 'settings.twoStepVerification.googleVerifyManuallyIosDes2', defaultMessage: '建议你在“帐户”中输入产品和帐户名，如“{0}：<EMAIL>”'}, product_name)}</p>
                        <p class="verify-tip-des">${defineMessage({id: 'settings.twoStepVerification.googleVerifyManuallyIosDes3', defaultMessage: '在“密钥”中输入16位密钥：'})}</p>
                    `
                    : `
                        <div class="verify-tip">${defineMessage({id: 'settings.twoStepVerification.googleVerifyManuallyAndroidDes1', defaultMessage: '1.打开应用，点击菜单，选择“设置账户”'})}</div>
                        <p class="verify-tip-des">${defineMessage({id: 'settings.twoStepVerification.googleVerifyManuallyAndroidDes2', defaultMessage: '建议你在“账户”中输入产品和帐户名，如“{0}：<EMAIL>”'}, product_name)}</p>
                        <p class="verify-tip-des">${defineMessage({id: 'settings.twoStepVerification.googleVerifyManuallyAndroidDes3', defaultMessage: '在“密钥”中输入16位密钥'})}</p>
                    `
                    }
                    <p class="verify-secret"></p>
                    <a class="to-qrcode" href="#">${defineMessage({id: 'settings.twoStepVerification.googleVerifyToQRcode', defaultMessage: '返回扫描设置'})}</a>
                </div>

                <div class="verify-tip">${defineMessage({id: 'settings.twoStepVerification.googleVerifyInputCaptcha', defaultMessage: '2. 输入谷歌验证器的6位验证码'})}</div>
                <div class="form-group">
                    <div class="input-group">
                        <input class="text form-control" name="two_step_captcha" type="text" value="" autocomplete="off"  maxlength="6" />
                    </div>
                </div>
            </div>
            <div class="dialog-actions">
                <button class="btn btn-primary" type="button"><span>${defineMessage({id: 'base.twoStepVerification.setSubmit', defaultMessage: '完成设置'})}</span></button>
                <a class="backward action-box"><i class="iconfont icon-backward"></i><span>${defineMessage({id: 'base.previous', defaultMessage: '上一步'})}</span></a>
            </div>
        `;

        let container = this.generateContainer({
            title: defineMessage({id: 'settings.twoStepVerification.setByGoogleTitle', defaultMessage: '设置谷歌身份验证器'}),
            content,
            success: (done) => {
                let data = {
                    code: container.querySelector('[name=two_step_captcha]').value,
                    method: 'google'
                };
                this.setVerify(data, (res) => {
                    this.then(null, res);
                    done();
                }, function(res) {
                    res.errors.forEach((error) => {
                        fc.insertError(error.field, error.error_msg);
                    });
                });
            }


        });

        fc.initInputAction(container.querySelector('[name=two_step_captcha]'));

        container.querySelector('.dialog-actions .backward').addEventListener('click', () => {
            this.fallback();
        });

        container.querySelector('.to-manually').addEventListener('click', (e) => {
            e.preventDefault();
            document.querySelector('.verify-qrcode').style.display = 'none';
            document.querySelector('.verify-manually').style.display = 'block';

            fc.ajax({
                url: API.GET_GOOGLE_SECRET,
                parser: ({secret}) => {
                    document.querySelector('.verify-secret').innerText = secret;
                }
            });
        });

        container.querySelector('.to-qrcode').addEventListener('click', (e) => {
            e.preventDefault();
            document.querySelector('.verify-qrcode').style.display = 'block';
            document.querySelector('.verify-manually').style.display = 'none';
        });
    }

    setVerify(data, success, failure) {
        if(!data && !data.method && !data.code) {
            failure();
            return false;
        }

        fc.ajax({
            url: API.SET_TWO_STEP,
            data,
            accept_all_errors: true,
            parser: function(res) {
                if(res.success) {
                    success(res);
                } else if(res.errors) {
                    failure(res);
                }
            }
        });
    }

    login(res) {
        this.then(null, res);
    }

    success(data) {
        let { product_name } = fc.config;
        if(!data) {
            data = {
                set_two_step_type: 'edit'
            };
        }

        if(data.set_two_step_type === 'modify') {
            fc.alert({
                main: defineMessage({id: 'settings.twoStepVerification.modifyVerifySuccessTitle', defaultMessage: '二次验证已修改'})
            });
        } else {
            fc.alert({
                main: defineMessage({id: 'settings.twoStepVerification.setVerifySuccessTitle', defaultMessage: '二次验证已开启'}),
                sub: defineMessage({id: 'settings.twoStepVerification.setVerifySuccessDes', defaultMessage: '二次验证已开启，每次登录{0}时，除帐号密码之外，还需要输入安全验证码。'}, product_name)
            });
        }

        this.then(data);
    }

    closeSuccess() {
        fc.ajax({
            url: API.CLOSE_TWO_STEP,
            method: 'POST',
            parser: () => {
                fc.alert({
                    main: defineMessage({id: 'settings.twoStepVerification.closeVerifySuccess', defaultMessage: '二次验证已关闭'})
                });
                this.then();
            }
        });


    }

}

export default TwoStepVerify;