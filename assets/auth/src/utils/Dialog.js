import fc from './common';
import Events from './Events';
import {defineMessage} from '@q/fang-intl';

    // Container
    //     Overlay
    //     Dialog Wrapper
    //         Dialog Header
    //             title
    //             closeIcon
    //         Dialog body
    //             content
    //             action buttons
class Dialog extends Events {
    constructor(options) {
        super(options);
        this.options = {
            title: '',
            closable: true,
            modal: true,
            useLayerForClickAway: true,
            zIndex: 9999,
            offsetTop: 20,
            cancelText: defineMessage({id: 'base.cancel', defaultMessage: '取消'}),
            confirmText: defineMessage({id: 'base.confirm', defaultMessage: '确定'}),
            alertText: defineMessage({id: 'base.Iknow', defaultMessage: '知道了'}),
            minPaddingTop: 10,
            needValidate: false,
            showTitle: true,
            dialogType: 'dialog',
            onClose: () => {
                this.destroy();
            },
            ...options
        };
        if(this.options.confirm) {
            this.options.dialogType = 'confirm';
        }

        if(!this.options.title) {
            this.options.showTitle = false;
        }

        if(this.options.dialogType === 'alert') {
            const { content } = this.options;
            if(typeof content === 'object') {
                this.options.message = content;
            } else {
                this.options.message = {
                    main: content
                };
            }
        }

    }

    render() {
        const { closable, title, modal, content, success, cancelText, confirmText, alertText, alert, needValidate, showTitle, dialogType, confirm, message, className, btnType} = this.options;
        let template = `
            <div class="${dialogType}-wrap">
                <div class="${dialogType}-content">
                    ${closable ? '<div class="dialog-close"><span class="dialog-close-x"></span></div>' : ''}
                    ${ showTitle ? `<div class="${dialogType}-header">
                            <div class="dialog-title">${title}</div>
                        </div>` : ''}
                    <div class="${dialogType}-body">
                        ${confirm || dialogType === 'alert' ?
                            `<div class="message">
                                <div class="message-main">${message.main}</div>
                                <span>${message.sub ? message.sub : ''}</span>
                            </div>`
                        : content}

                        ${success ?
                            `<div class="${dialogType}-actions">
                                <button class="btn btn-default" type="button"><span>${cancelText}</span></button>
                                <button class="btn btn-primary" type="button"${needValidate ? ' disabled' : ''}><span>${confirmText}</span></button>
                            </div>`
                        : ''}
                        ${alert ?
                            `<div class="${dialogType}-actions alert-actions">
                                <button class="btn btn-${btnType ? btnType : 'default'}" type="button">${alertText}</button>
                            </div>`
                            : ''
                        }
                        ${confirm ?
                            `<div class="${dialogType}-actions">
                                <button class="btn btn-default" type="button"><span>${cancelText}</span></button>
                                <button class="btn btn-${ btnType ? btnType : 'danger'}" role="confirm" type="button"><span>${confirmText}</span></button>
                            </div>`
                            : ''
                        }
                    </div>
                </div>
            </div>
        `;

        if(!this.layer) {
            this.layer = document.createElement('div');
            this.layer.innerHTML = template;
            if(className) {
                fc.addClass(this.layer, className);
            }
            document.body.appendChild(this.layer);

            if(modal) {
                this.overlay = document.createElement('div');
                this.overlay.className= 'overlay';
                this.overlay.style.position = 'fixed';
                this.overlay.style.width = '100%';
                this.overlay.style.height = '100%';
                this.overlay.style.top = 0;
                this.overlay.style.left = 0;

                this.layer.insertAdjacentElement('afterbegin', this.overlay);

            }
        } else {
            this.layer.style.display = 'block';
        }

        !this.bind && this.bindEvent();

        this.positionDialog();

    }

    positionDialog() {
        const {
            autoScrollBodyContent,
            autoDetectWindowHeight,
            repositionOnUpdate,
            actions,
            title,
            offsetTop,
            minPaddingTop,
            dialogType
        } = this.options;


        const clientHeight = window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight;
        const container = this.layer;
        const dialogWrap = this.layer.querySelector(`.${dialogType}-wrap`);
        const dialogBody = this.layer.querySelector(`.${dialogType}-body`);
        const dialogWrapHeight = dialogWrap.offsetHeight;
        //rest style
        dialogBody.style.height = '';
        let paddingTop = ((clientHeight - dialogWrapHeight) / 2) - offsetTop;
        if (paddingTop < minPaddingTop) paddingTop = minPaddingTop;

        if (/*repositionOnUpdate || */!container.style.paddingTop) {
            container.style.paddingTop = `${paddingTop}px`;
        }

        // Force a height if the dialog is taller than clientHeight
        if (autoDetectWindowHeight || autoScrollBodyContent) {
            let maxDialogContentHeight = clientHeight - 2 * offsetTop;

            if (title) maxDialogContentHeight -= dialogBody.previousSibling.offsetHeight;

            // if (React.Children.count(actions)) {
            //     maxDialogContentHeight -= dialogBody.nextSibling.offsetHeight;
            // }

            dialogBody.style.maxHeight = `${maxDialogContentHeight}px`;
        }
    }

    unrenderLayer() {
        if(!this.layer) {
            return;
        }

        this.bind && this.unbindEvent();
        fc.removeElement(this.layer);
    }

    bindEvent() {
        const { useLayerForClickAway, zIndex, closable, success, alert, confirm, dialogType } = this.options;
        if(useLayerForClickAway) {
            this.layer.addEventListener('touchstart', this.onClickAway);
            this.layer.addEventListener('click', this.onClickAway);
            this.layer.style.position = 'fixed';
            this.layer.style.top = 0;
            this.layer.style.bottom = 0;
            this.layer.style.left = 0;
            this.layer.style.right = 0;
            this.layer.style.zIndex = zIndex;
        } else {
            setTimeout(() => {
                window.addEventListener('touchstart', this.onClickAway);
                window.addEventListener('click', this.onClickAway);
            }, 0);
        }
        if(closable) {
            this.layer.querySelector('.dialog-close').addEventListener('click', this.handleClose);
        }

        if(success && typeof success === 'function') {
            this.layer.querySelector(`.${dialogType}-actions .btn-primary`).addEventListener('click', success);
            this.layer.querySelector(`.${dialogType}-actions .btn-default`).addEventListener('click', this.handleClose);
        }

        if(alert) {
            this.layer.querySelector(`.${dialogType}-actions .btn-default`).addEventListener('click', this.handleClose);
            if(typeof alert === 'function') {
                this.layer.querySelector(`.${dialogType}-actions .btn-default`).addEventListener('click', alert);
            }
        }

        if(confirm) {
            this.layer.querySelector(`.${dialogType}-actions .btn-default`).addEventListener('click', this.handleClose);
            if(typeof confirm === 'function') {
                this.layer.querySelector(`.${dialogType}-actions [role=confirm]`).addEventListener('click', confirm);
            }
        }
        this.bind = true;
    }

    handleClose = () => {
        this.requestClose(true);
    }

    handleClickOverlay = () => {
        this.requestClose(false);
    };

    requestClose(clicked) {
        if (!clicked && this.options.modal) {
            return;
        }

        if (this.options.onClose) {
            this.options.onClose(!!clicked);
        }
    }

    unbindEvent() {
        const { useLayerForClickAway } = this.options;
        if(useLayerForClickAway) {
            this.layer.style.position = 'relative';
            this.layer.removeEventListener('touchstart', this.onClickAway);
            this.layer.removeEventListener('click', this.onClickAway);
        } else {
            window.removeEventListener('touchstart', this.onClickAway);
            window.removeEventListener('click', this.onClickAway);
        }
        this.bind = false;
    }

    onClickAway = (event) => {
        const { componentClickAway } = this.options;
        if(event.defaultPrevented) {
            return;
        }

        if(!componentClickAway) {
            return;
        }

        const el = this.layer;
        if (event.target !== el && event.target === window ||
            (document.documentElement.contains(event.target) && !fc.isDescendant(el, event.target))) {
            componentClickAway(event);
        }
    }

    getLayer(){
        return this.layer;
    }

    show() {
        this.render();
        return this;
    }

    destroy() {
        this.unrenderLayer();
    }

    $(selector) {
        return this.layer.querySelector(selector);
    }
}

export default Dialog;