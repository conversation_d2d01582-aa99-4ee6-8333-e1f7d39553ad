// axios for ie8+
// DOM 参考 http://youmightnotneedjquery.com/
// jquery 按模块加载可以参考下面
// https://stackoverflow.com/questions/35877475/trying-to-require-parts-of-jquery-with-webpack
require('es6-promise').polyfill(); //make sure require.ensure can be used
// import axios from 'axios';
import 'fetch-ie8';
// import 'babel-polyfill';
require('es5-shim');
require('es5-shim/es5-sham');
import './addEventListener';
import './i18n';
import {defineMessage, language} from '@q/fang-intl';
import Dialog from './Dialog';
import Fingerprint2 from '@fingerprintjs/fingerprintjs';
import Cookies from 'js-cookie';
import { SECURITY_EXPIRE, ENCRYPTION_VERSION_LIMITED, LOGIN_LIMIT_VERSION_LIMITED, LOGIN_ENTERPRISE_EXPIRED, ENTERPRISE_HAS_BEEN_EXPIRED_ADMIN } from '../constants/code';
import * as API from '../constants/API';
import CryptoJS from 'crypto-js';
import Notification from './Notification';

// error constants code
/* eslint-disable no-undef, no-unused-vars */
__webpack_public_path__ = document.getElementById('asset_url').value;
/* eslint-enable no-undef, no-unused-vars */

// import VConsole from 'vconsole/dist/vconsole.min.js';
// var vConsole = new VConsole();

export const config = {};

config.login_type = 'web';
config.isMobile = !!document.getElementById('is_mobile');
config.isSync = !!document.getElementById('is_sync');

config.product_name = document.getElementById('product_name').value ? document.getElementById('product_name').value : defineMessage({id: 'base.yfy', defaultMessage: '亿方云'});


// 登录限制需要设备号
if (window.fangcloud && window.fangcloud.getDeviceSerialNumber) {
    // xp 下直接返回了结果，其他情况下通过回调返回的
    config.deviceSerialNumber = window.fangcloud.getDeviceSerialNumber();
}

if(window.IS_WEBENGINE){
    // require.ensure([
    //     './qwebchannel'
    // ], (require) => {
        // TODO 异步加载
        const QWebChannel = require('./qwebchannel').QWebChannel;
        /* global qt */
        new QWebChannel(qt.webChannelTransport, function(channel) {
            window.fangcloud = channel.objects.sync_v2;
            // 登录限制需要设备号 其他版本（非XP）是通过回调获取的
            if (window.fangcloud && window.fangcloud.getDeviceSerialNumber) {
                window.fangcloud.getDeviceSerialNumber((deviceSerialNumber) => {
                    config.deviceSerialNumber = deviceSerialNumber;
                });
            }
        });
    // });
}

// 新客户端 设备登录限制相关
if (window.fangcloud && window.fangcloud.electronClient && window.fangcloud.getDeviceSerialNumber) {
    window.fangcloud.getDeviceSerialNumber((deviceSerialNumber) => {
        config.deviceSerialNumber = deviceSerialNumber;
    });
}

if(config.isSync) {
    config.login_type = 'sync';
}

if(config.isMobile ) {
    if(window.fangcloud) {
        config.login_type = 'app';
    }

    setTimeout(() => {
        if(window.fangcloud) {
            config.login_type = 'app';
        }
    }, 3000);

    // IOS 禁止缩放
    window.onload=function () {
        document.addEventListener('touchstart',function (event) {
            if(event.touches.length>1){
                event.preventDefault();
            }
        });
        var lastTouchEnd=0;
        document.addEventListener('touchend',function (event) {
            var now=(new Date()).getTime();
            if(now-lastTouchEnd<=300){
                event.preventDefault();
            }
            lastTouchEnd=now;
        },false);
    };

    // fix andriod keybord style
    document.querySelector('.wrapper').style.height = document.querySelector('.wrapper').clientHeight + 'px';
}



config.API_URL = document.getElementById('API_URL') && document.getElementById('API_URL').value;

export let hasAlertDialog;
export const alert = (msg, action, alertText) => {
    if(hasAlertDialog) return;
    hasAlertDialog = true;
    new Dialog({
        dialogType: 'alert',
        content: msg,
        alertText: alertText ? alertText : defineMessage({id: 'base.Iknow', defaultMessage: '知道了'}),
        alert: function() {
            hasAlertDialog = false;
            action && action();
        }
    }).show();
};

export const debounce = function(fn, delay) {
    let timer = null;
    return function() {
        const context = this, args = arguments;
        clearTimeout(timer);

        timer = setTimeout(function(){
            fn.apply(context, args);
        }, delay);
    };
};

export const throttle = function(fn, threshhold) {
    let last = null;
    let timer = null;
    threshhold || (threshhold = 250);

    return function() {
        const context = this, args = arguments;
        let now = +new Date();

        if (last && now < (last + threshhold)) {
            clearTimeout(timer);

            timer = setTimeout(function() {
                last = now;
                fn.apply(context, args);
            }, threshhold);
        } else {
            last = now;
            fn.apply(context, args);
        }
    };
};

export const syncBridge = (func, callback, ...args) => {
    if(window.IS_WEBENGINE){
        window.fangcloud[func](...args, callback);
    } else {
        callback(window.fangcloud[func](...args));
    }
};

config.request_token = document.querySelector('meta[name=csrf-token]').getAttribute('content');

if(document.querySelector('[name=_fstate]')) {
    config._fstate = document.querySelector('[name=_fstate]').value;
} else if(window.location.search.match(/_fstate=([0-9a-zA-Z\/]+)/)) {
    config._fstate = window.location.search.match(/_fstate=([0-9a-zA-Z\/]+)/)[1];
}

export function ready(fn) {
    if (document.readyState != 'loading'){
        fn();
    } else if (document.addEventListener) {
        document.addEventListener('DOMContentLoaded', fn);
    } else {
        document.attachEvent('onreadystatechange', function() {
            if (document.readyState != 'loading')
                fn();
        });
    }
}

export function removeClass(el, className) {
    if (el.classList) {
        let classArray = className.split(' ');
        el.classList.remove(...classArray);
    } else {
        el.className = el.className.replace(new RegExp('(^|\\b)' + className.split(' ').join('|') + '(\\b|$)', 'gi'), ' ');
    }
}

export function addClass(el, className) {
    if (el.classList) {
        let classArray = className.split(' ');
        el.classList.add(...classArray);
    } else {
        el.className += ' ' + className;
    }
}

export function hasClass(el, className) {
    if (el.classList) {
        return el.classList.contains(className);
    } else {
        return new RegExp('(^| )' + className + '( |$)', 'gi').test(el.className);
    }
}

export function triggerNative(el, eventName) {
    if (document.createEvent) {
        let event = document.createEvent('HTMLEvents');
        event.initEvent(eventName, true, false);
        el.dispatchEvent(event);
    } else {
        el.fireEvent(`on${eventName}`);
    }
}

export function nextElementSibling(el) {
    if(el.nextElementSibling) return el.nextElementSibling;
    do { el = el.nextSibling; } while ( el && el.nodeType !== 1 );
    return el;
}

export function previousElementSibling(el) {
    if(el.previousElementSibling) return el.previousElementSibling;
    do { el = el.previousSibling; } while ( el && el.nodeType !== 1 );
    return el;
}

export function setInnerText (el, string) {
    if (el.textContent !== undefined) {
        el.textContent = string;
    } else {
        el.innerText = string;
    }
}

// flag of check login when sync client login expired
let loggedInChecked = false;

function keepAlive() {
    ajax({url: API.AUTH_HI, headers: {'Device-Token': config.device_token}}).then(result => {
        if(result.logged_in){
            alert(defineMessage({id: 'base.confirmToContinue', defaultMessage: '由于你长时间未进行操作，请刷新或点击确认后继续使用'}), () => {
                if(window.fangcloud.reloadAllPage){
                    window.fangcloud.reloadAllPage();
                } else {
                    window.location.reload();
                }
            }, defineMessage({id: 'base.confirm', defaultMessage: '确认'}));
        } else {
            alert(defineMessage({id: 'base.loginFail', defaultMessage: '登录已失效，请重新登录'}), () => {
                window.fangcloud.userRelogin();
            });
        }
    });
}

let ajaxloading = false;
const globalBlocker = function(show){
    if(show === 0){
        removeElement(document.getElementById('global_blocker'));
        // $('#global_blocker').remove();
        ajaxloading = false;
    } else if(!ajaxloading){
        ajaxloading = true;
        let globalBlockerDOM = document.createElement('div');
        globalBlockerDOM.setAttribute('id', 'global_blocker');
        addClass(globalBlockerDOM, 'global-blocker');
        document.body.appendChild(globalBlockerDOM);
        // $('<div id="global_blocker" class="global-blocker"></div>').appendTo('body');
    }
};

export function ajax(options) {
    const props = {
        credentials: 'include',
        method: options.method || 'GET',
        'Content-Type': options.contentType || 'application/json'
    };

    const headers = {'X-Requested-With': 'XMLHttpRequest', 'Content-Type': options.contentType || 'application/json', 'Accept': 'application/json'};
    if (config.request_token) {
        headers['X-CSRF-TOKEN'] = config.request_token;
    }
    if (config.authtoken) {
        headers['Auth-Token'] = config.authtoken;
    }
    if(config.device_notification_id){
        headers['Device-Notification-Id'] = config.device_notification_id;
    }
    if(config.client_version){
        headers['X-User-Agent'] = config.client_version;
    }

    if(options.headers){
        Object.assign(headers, options.headers);
    }
    props.headers = headers;

    if(options.block === true){
        globalBlocker();
    }

    if(options.data){
        if(config._fstate) {
            options.data._fstate = config._fstate;
        }
        props.method = 'post';
        props.body = options.formData ? options.data : JSON.stringify(options.data);
    }

    let url = options.url.indexOf('?') > -1 ? `${options.url}&_=${+new Date()}` : `${options.url}?_=${+new Date()}`; //fix ie cache
    if(config._fstate) {
        url += `&_fstate=${config._fstate}`;
    }

    // TODO: add global block
    return fetch(url, props).then(response => {
        if(options.block === true){
            globalBlocker(0);
        }
        if(response.status === 500){
            alert({
                main: defineMessage({id: 'base.alert.server500.title', defaultMessage: '服务器开了一点小差，已通知程序猿小哥处理，请稍等片刻或刷新重试。'}),
                sub: defineMessage({id: 'base.alert.server500.content', defaultMessage: '如有疑问，请联系客服'})
            });
        } else if(response.status === 502){
            alert(defineMessage({id: 'base.alert.server502', defaultMessage: '服务器错误，请重试 (502)'}));
        } else if(response.status === 404){
            alert(defineMessage({id: 'base.alert.server404', defaultMessage: '网络出错(404)，请检查后重试'}));
        } else if (response.status === 403) {
            alert(defineMessage({id: 'base.alert.server403', defaultMessage: '你无权限访问'}));
        } else {
            return response.json();
            // TODO: remove global block
        }
    }).then(result => {
        if(result.errors) {
            const errors = result.errors;
            // if(errors.login_expired || errors.security_check_failed){
            if(errors[0].error_code == SECURITY_EXPIRE){
                if(window.fangcloud){
                    if(!loggedInChecked){
                        loggedInChecked = true;
                        if(window.fangcloud.getAuthToken) {
                            syncBridge('getAuthToken', token => {
                                config.authtoken = token;
                                keepAlive();
                            });
                        } else {
                            keepAlive();
                        }
                    }
                } else {
                    // session 失效 弹窗提示刷新页面
                    alert(defineMessage({id: 'base.pageExpired', defaultMessage: '当前页面已失效，请点击{0}重试'}, defineMessage({id: 'base.ok', defaultMessage: '确定'})),
                        function() {
                            window.location.reload(true);
                        },
                        defineMessage({id: 'base.confirm', defaultMessage: '确认'})
                    );
                    return false;
                }
            } else if(errors.force_two_step) {
                if(window.fangcloud){
                    // 客户端内不跳设置页面，直接刷新请求php页面，让后端判断去 interanl_logout, 直接退出重登录
                    window.location.reload();
                } else {
                    window.location.href = result.redirect || API.LOGIN_SETTINGS;
                }
            }
        }
        return result;
    }).then(result => {
        if(!result) {
            return false;
        }
        if(result.success || (options.accept_all_errors && result.errors) || (options.accept_errors && result.errors && Object.keys(result.errors).some(err => options.accept_errors.indexOf(err) > -1))){
            if(options.parser){
                result = options.parser(result);
            }
            return result;
        }

        // TODO 看是否需要直接跳转 不判断result 包含errors
        if(result.redirect && !result.errors) {
            window.location.href = result.redirect;
        }

        if(result.errors) {
            alert(result.errors[0].error_msg);
        }

        return result;
    }).catch(e => {
        throw e;
        //alert(e.toString());
    });
}

export function clearError(input) {
    const $parentForm = input.parentNode.parentNode;
    if(hasClass($parentForm, 'error')) {
        const $error = $parentForm.querySelector('.error');
        removeClass($parentForm, 'error');
        if ($error) {
            removeElement($error);
        }
        if($parentForm.querySelector('.hint')) {
            $parentForm.querySelector('.hint').style.display = 'block';
        }
    }
}

export function insertError(key, text, $form) {
    $form = $form || document.querySelector('form');
    if(!$form) {
        return false;
    }
    const el = $form.querySelector(`[name=${key}]`);
    if(!el) {
        return false;
    }
    const $parent = el.parentNode;
    const $parentForm = matchSelector(el, '.form-group');
    if(!hasClass($parentForm, 'error')) {
        addClass($parentForm, 'error');
        if($parentForm.parentNode.querySelector('.hint')) {
            $parentForm.parentNode.querySelector('.hint').style.display = 'none';
        }
        if (text) {
            let $error = `<span class="error"><i class="iconfont icon-error"></i>${text}</span>`;
            $parent.insertAdjacentHTML('afterend', $error);
        }
    }
}

export function trim(string) {
    return string.replace(/^\s+|\s+$/g, '');
}

export function removeElement(el) {
    el.parentNode.removeChild(el);
}

export function checkEmail(email){
    const re = /(^([a-z0-9\+_\-]+)(\.[a-z0-9\+_\-]+)*@([a-z0-9\-]+\.)+[a-z]{2,6}$)/i;
    return re.test(email);
}

export function checkPassword(password){
    // TODO password Reg
    if(password.length < 8 || password.length > 32 || !/^(?!\d+$)(?![a-zA-Z]+$)[a-zA-Z\d]+$/i.test(password)) {
        return defineMessage({id: 'base.passwordStrengthMid', defaultMessage: '密码必须为8-32位字母和数字的组合'});
    } else if(/(^\s+)|(\s+$)/g.test(password)) {
        return defineMessage({id: 'base.passwordStartOrEndWithSpace', defaultMessage: '密码首尾不能为空格'});
    } else if(!/^(?!\d+$)(?![a-zA-Z]+$)[a-zA-Z\d]+$/i.test(password)) {
        return defineMessage({id: 'base.passwordWithForbiddenCharacter', defaultMessage: '密码包含不允许的字符(<a href="http://help.fangcloud.com/posts/view/57412/" target="_blank">了解详情</a>)'});
    }
    return false;
}

export function getComputedStyle($el, key) {
    if(window.getComputedStyle) {
        return window.getComputedStyle($el)[key];
    } else if(document.documentElement.currentStyle) {
        return $el.currentStyle[key];
    }
}


export function isVisible ($le) {
    return $le.offsetWidth > 0 && $le.offsetHeight > 0;
}

export function isDescendant(parent, child) {
    let node = child.parentNode;

    while(node != null){
        if (node === parent) {
            return true;
        }
        node = node.parentNode;
    }

    return false;
}

export function formatDate(d, format, noHourMinute) {
    if(typeof(d) !== 'object') {
        d = new Date(d);
    }
    const mon = d.getMonth() + 1;
    const day = d.getDate();
    const hour = d.getHours();
    const minute = d.getMinutes();
    const second = d.getSeconds();

    format = format || '%R';

    if(format === '%R'){
        let currentTime = new Date(), todayStart = new Date();
        todayStart.setHours(0, 0, 0, 0);
        let diffSeconds = Math.floor((todayStart.getTime() - d.getTime()) / 1000);
        if(diffSeconds <= 0){
            diffSeconds = Math.floor((currentTime.getTime() - d.getTime()) / 1000);
            if(diffSeconds <= 0){
                format = defineMessage({id: 'base.time.justNow', defaultMessage: '刚刚'});
            } else if(diffSeconds < 60){
                format = defineMessage({id: 'base.time.beforeSeconds', defaultMessage: '{0}秒前'}, diffSeconds);
            } else if(diffSeconds < 60 * 60){
                format = defineMessage({id: 'base.time.beforeMinutes', defaultMessage: '{0}分钟前'}, Math.floor(diffSeconds / 60));
            } else if(diffSeconds < 60 * 60 * 24){
                format = defineMessage({id: 'base.time.beforeHours', defaultMessage: '{0}小时前'}, Math.floor(diffSeconds / 60 / 60));
            }
        } else {
            if(diffSeconds <= 60 * 60 * 24) {
                format = defineMessage({id: 'base.time.yesterday', defaultMessage: '昨天'});
            } else if(diffSeconds <= 60 * 60 * 48) {
                if (language === 'en') {
                    format = '%MM-%DD';
                } else {
                    format = defineMessage({id: 'base.time.theDayBefore', defaultMessage: '前天'});
                }
            } else {
                format = '%MM-%DD';
                if(d.getFullYear() !== todayStart.getFullYear()){
                    format = '%YY-' + format;
                }
            }

            if(!noHourMinute){
                format += ' %hh:%mm';
            }
        }
    }

    if(format === '%L'){
        let currentTime = new Date();
        const diffSeconds = Math.floor((d.getTime() - currentTime.getTime()) / 1000);
        if(diffSeconds <= 86400){
            if(diffSeconds <= 0){
                format = '';
            } else if(diffSeconds < 60){
                format = defineMessage({id: 'unit.second', defaultMessage: '{0}秒'}, diffSeconds);
            } else if(diffSeconds < 60 * 60){
                format = defineMessage({id: 'unit.minute', defaultMessage: '{0}分钟'}, Math.floor(diffSeconds / 60));
            } else if(diffSeconds < 60 * 60 * 24){
                format = defineMessage({id: 'unit.hour', defaultMessage: '{0}小时'}, Math.floor(diffSeconds / 60 / 60));
            }
        } else if (diffSeconds <= 691200) {
            format = defineMessage({id: 'unit.day', defaultMessage: '{0}天'}, Math.floor(diffSeconds / 86400));
        } else {
            format = '%YY-%MM-%DD';
        }
    }

    return format
    .replace('%YY', d.getFullYear())
    .replace('%Y', d.getFullYear() % 100)
    .replace('%MM', mon < 10 ? '0' + mon : mon)
    .replace('%M', mon)
    .replace('%DD', day < 10 ? '0' + day : day)
    .replace('%D', day)
    .replace('%hh', hour < 10 ? '0' + hour : hour)
    .replace('%h', hour)
    .replace('%mm', minute < 10 ? '0' + minute : minute)
    .replace('%m', minute)
    .replace('%ss', second < 10 ? '0' + second : second)
    .replace('%s', second);
}

function getMatcher(element) {
    if (element.matches) {
        return element.matches;
    }

    if (element.webkitMatchesSelector) {
        return element.webkitMatchesSelector;
    }

    if (element.mozMatchesSelector) {
        return element.mozMatchesSelector;
    }

    if (element.msMatchesSelector) {
        return element.msMatchesSelector;
    }

    if (element.oMatchesSelector) {
        return element.oMatchesSelector;
    }

    // if it doesn't match a native browser method
    // fall back to the gator function
    return () => {};
}

export function matchSelector(element, selector) {
    if (getMatcher(element).call(element, selector)) {
        return element;
    }

    if (element.parentNode) {
        return matchSelector(element.parentNode, selector);
    }
}

const ua = (function(){
    var ua = {},
        matched;
    ua.match = function(ua){
        ua = ua.toLowerCase();
        var uaMatch = /(chrome)[ \/]([\w.]+)/.exec(ua) ||
            /(webkit)[ \/]([\w.]+)/.exec(ua) ||
            /(opera)(?:.*version|)[ \/]([\w.]+)/.exec(ua) ||
            /(msie) ([\w.]+)/.exec(ua) ||
            ua.indexOf('Trident') < 0 && /(rv):([\w.]+)\) like gecko/.exec(ua) ||
            ua.indexOf('compatible') < 0 && /(mozilla)(?:.*? rv:([\w.]+)|)/.exec(ua) ||
            [],
            serverMatch = /(windows)[ ]([ \w.]+)/.exec(ua) || [];

        return {
            browser: uaMatch[1] || '',
            version: uaMatch[2] || '0',
            systemData: {
                system: serverMatch[1] || '',
                version: serverMatch[2] || '0'
            }
        };
    };

    matched = ua.match(navigator.userAgent);

    if (matched.browser) {
        ua[matched.browser] = true;
        ua.browser = matched.browser;
        ua.version = matched.version;
    }

    // ie 11 useragent change
    if(ua.rv){
        ua.msie = true;
        ua.browser = 'msie';
    }

    // Chrome is Webkit, but Webkit is also Safari.
    if (ua.chrome) {
        ua.webkit = true;
    } else if (ua.webkit) {
        ua.safari = true;
        ua.browser = 'safari';
    }

    ua.system = {};
    if(matched.systemData.system){
        ua.system[matched.systemData.system] = true;
        ua.system.version = matched.systemData.version;
    }

    return ua;
})();
let needRefreshKeys = {
    'e4b479eed49a89e4ffca5b7cdbab89e5': 1,
    '51e3fe6ff71ed0dd41d1ab170b6a02f9': 1
};

if(!Cookies.get('device_token') || needRefreshKeys[Cookies.get('device_token')]){
    if (window.requestIdleCallback) {
        requestIdleCallback(getFingerprint);
    } else {
        setTimeout(getFingerprint, 500);
    }

    function getFingerprint() {
        Fingerprint2.get({
            extraComponents: [{
                key: 'deviceToken',
                getData: function (done) {
                    done(window.sync_v2 && window.sync_v2.deviceToken ? window.sync_v2.deviceToken : '')
                }
            }]
        }, function (components) {
            const values = components.map(function (component) { return component.value; });
            const murmur = Fingerprint2.x64hash128(values.join(''), 31);
            let domain_array = window.location.hostname.split('.');
            let domain = domain_array.slice(1).join('.');
            Cookies.set('device_token', murmur, {domain, expires: 365});
        });
    }
}

// 多语言
let languageSwitch = document.querySelector('.language-switch');
if(languageSwitch) {
    languageSwitch.querySelector('.language-switch-dropdown').addEventListener('click', e => {
        e.preventDefault();
        let target = matchSelector(e.target, '[data-by]');
        if(target) {
            let lang = target.getAttribute('data-by');
            const {hash, origin, search, pathname} = window.location;
            let url = origin + pathname;
            if(search) {
                url += search;
            }
            window.onbeforeunload = null;
            window.location.href = changeURLArg(url, 'lang', lang) + (hash ? hash : '');
        }
    });
}

// 登录
let btnLogin = document.querySelector('.btn-login');
if(btnLogin) {
    btnLogin.addEventListener('click', () => {
        window.onbeforeunload = null;
    });
}


function changeURLArg(url, arg, arg_val){
    var pattern = arg + '=([^&]*)';
    var replaceText = arg + '=' + arg_val;
    if(url.match(pattern)){
        var tmp = '/(' + arg + '=)([^&]*)/gi';
        tmp = url.replace(eval(tmp),replaceText);
        return tmp;
    }else{
        if(url.match('[\?]')){
            return url + '&' + replaceText;
        } else {
            return url + '?' + replaceText;
        }
    }
    // return url + '\n' + arg + '\n' + arg_val;
}

function initInputAction(input) {
    const parentNode = input.parentNode;
    const inputType = input.getAttribute('type');

    // let clearText = parentNode.querySelector('.clear-text');
    let togglePassword = parentNode.querySelector('.toggle-pwd');
    let passwordHint = inputType === 'password' && nextElementSibling(parentNode);

    if(!hasClass(parentNode, 'input-group')) {
        addClass(parentNode, 'input-group');
    }

    // if(!clearText) {
    //     clearText = document.createElement('span');
    //     addClass(clearText, 'input-group-addon clear-text iconfont icon-search-clear');
    //     parentNode.appendChild(clearText);
    // }

    if(!togglePassword && inputType === 'password') {
        togglePassword = document.createElement('span');
        addClass(togglePassword, 'input-group-addon toggle-pwd iconfont icon-dark action-icon');
        parentNode.appendChild(togglePassword);
    }

    bindInputChange(input, ({target}) => {
        let display = trim(target.value) ? 'table-cell' : 'none';
        // clearText.style.display = display;
        if (togglePassword) {
            togglePassword.style.display = display;
        }

        if (passwordHint) {
            passwordHint.style.display = trim(target.value) ? 'block' : 'none';
        }

    });

    input.addEventListener('focus', activeInput);
    input.addEventListener('mouseover', activeInput);
    input.addEventListener('blur', blurInput);
    input.addEventListener('mouseout', blurInput);


    // if(clearText) {
    //     clearText.addEventListener('click', clearAction);
    // }

    if(togglePassword) {
        togglePassword.addEventListener('click', passwordAction);
    }

    function activeInput({type}) {
        addClass(parentNode, 'active');
        if(type === 'focus') {
            clearError(input);
        }
    }

    function blurInput() {
        removeClass(parentNode, 'active');
    }

    // function clearAction() {
    //     input.value = '';
    //     triggerNative(input, 'keyup');
    //     input.focus();
    // }

    function passwordAction() {
        if(hasClass(this, 'icon-dark')) {
            this.className = 'input-group-addon toggle-pwd iconfont icon-open action-icon';
            this.setAttribute('title', defineMessage({id: 'base.hidePassword', defaultMessage: '隐藏密码'}));
        } else {
            this.className = 'input-group-addon toggle-pwd iconfont icon-dark action-icon';
            this.setAttribute('title', defineMessage({id: 'base.showPassword', defaultMessage: '显示密码'}));
        }
        let newType = input.getAttribute('type') === 'text' ? 'password' : 'text';
        input.setAttribute('type', newType);
    }
}

export function loginHandle(res, mode) {
    if(res.success) {
        if(res.redirect) {
            if (mode === 'ent_reg') {
                const url = new URL(res.redirect);
                url.searchParams.set('mode', 'ent_reg');
                window.location.href = url.toString();
            } else {
                window.location.href = res.redirect;
            }
        } else if(window.fangcloud && window.fangcloud.login) {
            if(res.user) {
                res.user.login = res.user.phone || res.user.email;
            }
            window.fangcloud.login(JSON.stringify(res));
        } 
    } else if(res.errors){
        res.errors.forEach((error) => {
            // TODO 二次验证报错修复
            if(error.field) {
                if(error.field === 'login' && !document.querySelector(`[name=${error.field}]`)) {
                    error.field = 'two_step_captcha';
                }

                if(error.field === 'password') {
                    insertNormalError(document.querySelector('form'), error.error_msg);
                } else {
                    insertError(error.field, error.error_msg);
                }
            } else if(error.error_code === LOGIN_ENTERPRISE_EXPIRED || error.error_code === ENTERPRISE_HAS_BEEN_EXPIRED_ADMIN) {
                if (config.isMobile) {
                    alert({
                        main: error.error_msg,
                        sub: defineMessage({id: 'login.ent.expired.sub', defaultMessage: '若要查看云端文件，可以在网页端下载。'})
                    });
                    return;
                }

                new Dialog({
                    message: {
                        main: error.error_msg,
                        sub: defineMessage({id: 'login.ent.expired.sub', defaultMessage: '若要查看云端文件，可以在网页端下载。'})
                    },
                    showTitle: false,
                    cancelText: defineMessage({id: 'base.cancelText', defaultMessage: '下次再说'}),
                    confirmText: defineMessage({id: 'base.openBrowser', defaultMessage: '打开网页端'}),
                    btnType: 'primary',
                    confirm: function() {
                        openNewTab(location.origin);
                    }
                }).show();
            } else if(error.error_code === ENCRYPTION_VERSION_LIMITED) {
                new Dialog({
                    message: {
                        main: defineMessage({id: 'encryption.versionLimit', defaultMessage: '你所在的企业开启了文件防泄漏，需下载特定版本客户端方可使用'})
                    },
                    showTitle: false,
                    cancelText: defineMessage({id: 'base.cancelText', defaultMessage: '下次再说'}),
                    confirmText: defineMessage({id: 'encryption.downloadNow', defaultMessage: '立即下载'}),
                    btnType: 'primary',
                    confirm: function() {
                        openNewTab('https://app.fangcloud.com/sync/vv25/fangcloud/EncryptionSyncInstaller.exe');
                    }
                }).show();
            } else if(error.error_code === LOGIN_LIMIT_VERSION_LIMITED && error.download_link) {
                alert(error.error_msg, () => {
                    openNewTab(error.download_link);
                }, defineMessage({id: 'encryption.downloadNow', defaultMessage: '立即下载'}));
            } else {
                alert(error.error_msg);
            }
        });
    }
}

export const openNewTab = (link) => {
    if (window.fangcloud && (window.fangcloud.openNewTab || window.fangcloud.openInBrowser)) {
        // let openInBrowser = window.fangcloud.openNewTab || window.fangcloud.openInBrowser;
        if(window.fangcloud.openNewTab) {
            window.fangcloud.openNewTab(link);
        } else {
            window.fangcloud.openInBrowser(JSON.stringify({
                url: link
            }));
        }
    } else {
        window.open(link);
        //winOpen(link);
    }
};

export function checkValidate($form) {
    if(!$form) {
        $form = document.querySelector('form');
    }

    const $buttons = $form.querySelectorAll('.btn-primary'),
        $inputs = $form.querySelectorAll('.text');
    let $submit, disabled = false;

    // 处理表单有多步的问题
    for(let i = 0; i < $buttons.length; i++) {
        let $button = $buttons[i];
        if(isVisible($button)) {
            $submit = $button;
            break;
        }
    }

    if(!$submit) {
        return false;
    }

    for(let i = 0; i < $inputs.length; i++) {
        let $input = $inputs[i];
        let value = trim($input.value);
        if(!value && isVisible($input)) {
            disabled = true;
            break;
        }
    }

    // 注册页检查协议 没有的不需要检查， 可优化成参数
    const $protocol = document.getElementById('protocol');
    if($protocol && isVisible($protocol)) {
        let checked = $protocol.checked;
        if(!checked) {
            disabled = true;
        }
    }

    $submit.disabled = disabled;
}

export function initFormEvents($form, enableButton) {
    let $inputs = $form.querySelectorAll('input.text');
    [].forEach.call($inputs, input => initInputAction(input));

    let $radios = $form.querySelectorAll('input[type=radio]');
    [].forEach.call($radios, radio => initRadioAction(radio));

    let $checkboxs = $form.querySelectorAll('input[type=checkbox]');
    [].forEach.call($checkboxs, checkbox => initCheckboxAction(checkbox));

    if(!enableButton) {
        bindInputChange($form, ({ target }) => {
            if(matchSelector(target, 'input')) {
                checkValidate($form);
            }
        });
    }
}

export function bindInputChange($input, callback) {
    if (/\bMSIE [6789]\.0\b/.test(navigator.userAgent)) {
        let elementValue = $input.value;
        $input.addEventListener('propertychange', function(ev) {
            if(ev.propertyName !== 'value') return;
            let value = ev.srcElement.value;
            if (value === elementValue) return;
            elementValue = value;
            callback(ev);
        });

        $input.addEventListener('keyup', function(ev) {
            if (this.value !== elementValue) {
                elementValue = this.value;
                callback(ev);
            }
        });
    } else {
        $input.addEventListener('input', callback);
    }

}

export function passwordStrength() {
    let password_length_min = document.querySelector('[name=password_length_min]').value;
    let capital_letter = !!document.querySelector('[name=password_strength_require_capital_letter]').value;
    let special_symbol = !!document.querySelector('[name=password_strength_require_special_symbol]').value;
    if(capital_letter && !special_symbol) {
        return defineMessage({id: 'base.passwordWithCaptial', defaultMessage: '密码长度为{0}-32个字符，必须包含大写字母、小写字母和数字'}, password_length_min);
    } else if(!capital_letter && special_symbol) {
        return defineMessage({id: 'base.passwordWithSpecial', defaultMessage: '密码长度为{0}-32个字符，必须包含字母、数字和特殊字符(<a class="get-detail" href="http://help.fangcloud.com/posts/view/57412/" target="_blank">了解详情</a>)'}, password_length_min);
    } else if(capital_letter && special_symbol) {
        return defineMessage({id: 'base.passwordWithCaptialAndSpecial', defaultMessage: '密码长度为{0}-32个字符，必须包含大写字母、小写字母、数字和特殊字符(<a class="get-detail" href="http://help.fangcloud.com/posts/view/57412/" target="_blank">了解详情</a>)'}, password_length_min);
    } else {
        return defineMessage({id: 'base.passwordNormal', defaultMessage: '密码长度为{0}-32个字符，必须包含字母和数字'}, password_length_min);
    }
}

export function insertNormalError($form, msg, options) {
    const $submit = $form.querySelector('.btn-primary');
    const $parent = $submit.parentNode;

    let $oldError = nextElementSibling($submit);
    if($oldError && hasClass($parent, 'error')) {
        removeElement($oldError);
        removeClass($parent, 'error');
    }

    let $error = `<span class="error${options && options.className ? ' ' + options.className : ''}"><i class="iconfont icon-error"></i>${msg}</span>`;
    addClass($parent, 'error');
    $parent.insertAdjacentHTML('beforeend', $error);
}

export function initRadioAction($radio) {
    const $rootElement = matchSelector($radio, '.radiobox-group');
    const checked = $radio.checked;
    const $icon = previousElementSibling($radio);
    if (checked) {
        addClass($icon, 'checked');
    }
    $radio.addEventListener('change', function() {
        const checked = this.checked;
        if(checked) {
            let $current = $rootElement.querySelector('.checked');
            if($current) {
                removeClass($current, 'checked');
            }
            addClass($icon, 'checked');
        } else {
            removeClass($icon, 'checked');
        }
    });
}

export function initCheckboxAction($checkbox) {
    let checked = $checkbox.checked;
    const $icon = previousElementSibling($checkbox);
    if (checked) {
        addClass($icon, 'chk-checked');
    }
    $checkbox.addEventListener('change', function() {
        checked = this.checked;
        if(checked) {
            addClass($icon, 'chk-checked');
        } else {
            removeClass($icon, 'chk-checked');
        }
    });
}

export function encryptPhone(v) {
    return v.replace(v.substring(3,7), "****");
}

export function encryptEmail(v) {
    let str = v.split("@");
    let 　_s = '';
    let new_email = '';
    if (str[0].length > 4) { //@前面多于4位
        for (let i = 0; i < str[0].length - 4; i++) {
            _s+= '*';
        }
        new_email= str[0].substr(0, 2) + _s + str[0].substring(str[0].length-2) + '@' + str[1];
    }else{ //@前面小于等于于4位
        for(let i = 0;i<str[0].length - 1;i++){
            _s+='*';
        }
        new_email = str[0].substr(0,1)+ _s + '@' + str[1];
    }
    return new_email;
}

export function parseDomain (str) {
    if (!str) return '';
    if (str.indexOf('://') != -1) str = str.substr(str.indexOf('://') + 3);
    var topLevel = ['com', 'net', 'org', 'gov', 'edu', 'mil', 'biz', 'name', 'info', 'mobi', 'pro', 'travel', 'museum', 'int', 'areo', 'post', 'rec'];
    var domains = str.split('.');
    if (domains.length <= 1) return str;
    if (!isNaN(domains[domains.length - 1])) return str;
    var i = 0;
    while (i < topLevel.length && topLevel[i] != domains[domains.length - 1]) i++;
    if (i != topLevel.length) return domains[domains.length - 2] + '.' + domains[domains.length - 1];
    else {
        i = 0;
        while (i < topLevel.length && topLevel[i] != domains[domains.length - 2]) i++;
        if (i == topLevel.length) return domains[domains.length - 2] + '.' + domains[domains.length - 1];
        else return domains[domains.length - 3] + '.' + domains[domains.length - 2] + '.' + domains[domains.length - 1];
    }
};

export function removeHomepageCookie () {
    Cookies.remove('qhclick_msg',{ path: '', domain: parseDomain(window.location.origin) });
    Cookies.remove('jlclick_msg',{ path: '', domain: parseDomain(window.location.origin) });
    Cookies.remove('bdclick_msg',{ path: '', domain: parseDomain(window.location.origin) });
}

const lanuageMap = {
    'zh-TW':'tw',
    'en': 'en',
    'zh-CN': 'cn'
}

const qHPassCaptcha = {
    captchaData: {},
    init: function (onSuccess) {
        let captcha;
        let _this = this;
        document.getElementById('aliyunCaptcha-mask')?.remove();
	    document.getElementById('aliyunCaptcha-window-popup')?.remove();
        let transform = window && window.mobileTransform && typeof window.mobileTransform === 'number' ? window.mobileTransform : 1;

        initAliyunCaptcha({
            SceneId: '1evkf18n', // 场景ID。根据步骤二新建验证场景后，您可以在验证码场景列表，获取该场景的场景ID
            prefix: '1ss4h8', // 身份标。开通阿里云验证码2.0后，您可以在控制台概览页面的实例基本信息卡片区域，获取身份标
            mode: 'popup', // 验证码模式。popup表示要集成的验证码模式为弹出式。无需修改
            element: '#captcha-element', //页面上预留的渲染验证码的元素，与原代码中预留的页面元素保持一致。
            button: '#captcha-button', // 触发验证码弹窗的元素。button表示单击登录按钮后，触发captchaVerifyCallback函数。您可以根据实际使用的元素修改element的值
            captchaVerifyCallback: captchaVerifyCallback, // 业务请求(带验证码校验)回调函数，无需修改
            onBizResultCallback: onBizResultCallback, // 业务请求结果回调函数，无需修改
            getInstance: getInstance, // 绑定验证码实例函数，无需修改
            slideStyle: {
              width: 360,
              height: 40,
            }, // 滑块验证码样式，支持自定义宽度和高度，单位为px。其中，width最小值为320 px
            language: lanuageMap[language], // 验证码语言类型，支持简体中文（cn）、繁体中文（tw）、英文（en）
            region: language === 'zh-CN' ? 'cn' : 'sgp' //验证码示例所属地区，支持中国内地（cn）、新加坡（sgp）、海外(ga)
        });

        // 绑定验证码实例函数。该函数为固定写法，无需修改
        function getInstance(instance) {
            captcha = instance;
            captcha.show();
            document.getElementById('aliyunCaptcha-window-popup').style.transform = `translate(-50%,-50%) scale(${transform})`;
        }


        async function captchaVerifyCallback(captchaVerifyParam) {
            // 1.向后端发起业务请求，获取验证码验证结果和业务结果
            const _captchaVerifyParam = JSON.parse(captchaVerifyParam);
            const ts = await getTs();
            const md5_key = `sceneId=${_captchaVerifyParam.sceneId}&certifyId=${_captchaVerifyParam.certifyId}&ts=${ts}&security=dd1393a2feba4b3bb98505f1c61527ce`
            const signature = CryptoJS.MD5(md5_key).toString();
            // 1.向后端发起业务请求，获取验证码验证结果和业务结果
            const res = await ajax({
                url: API.CAPTCHA_VERIFY,
                data: {
                    captcha_verify_param: captchaVerifyParam,
                    signature: signature,
                    region: language === 'zh-CN' ? 'cn' : 'sgp',
                    ts: ts
                },
                accept_all_errors: true
            })
            const {success, data={}, code, msg} = res || {};
            if(success && data && data.verify_result){
                _this.captchaData = {
                    request_id: data.request_id
                }
                onSuccess && onSuccess(data.request_id);
                return {captchaResult: true};
            } else {
                if(code === 'verify_operation_too_frequent'){
                    new Notification({
                        message: msg
                    });
                }
                onSuccess && onSuccess('');
                return {captchaResult: false};
            }
        }

          // 业务请求验证结果回调函数
        function onBizResultCallback(bizResult) {
            // 此处拿不到captchaVerifyParam信息所以这里什么都不处理
        }
        return this;
    },

    destroy: function() {
        this.captchaData = {};
    }
}

export const app_key = '3ada220dcc804a0bb74060ca39abc862';
export const signupAppId = '49ab7a20075946a3bf0015985c4271ef';
export const login_sign_app_id = '59a0635d8d0a43c09f3ef505ef7993c0';
export const forget_password_app_id = '1bc1e7345f9f44a3a51b512a64acb58c';
export const activate_by_phone_id = '85fe58e8429c4892990c5ca7e8111499';
export const become_referral_id = '680859a041914f2684941aa92cb32b4d';

export const getTs = async () => {
    return await ajax({
        url: API.GET_TS,
    }).then(res => {
        if(res.success && res.data.server_time) {
            return res.data.server_time;
        } else {
            return Math.floor(Date.now() / 1000);
        }
    })
}

export default {
    config,
    ready,
    debounce,
    throttle,
    ajax,
    alert,
    removeClass,
    addClass,
    hasClass,
    clearError,
    insertError,
    triggerNative,
    nextElementSibling,
    previousElementSibling,
    setInnerText,
    trim,
    removeElement,
    checkEmail,
    checkPassword,
    getComputedStyle,
    isVisible,
    isDescendant,
    formatDate,
    matchSelector,
    initInputAction,
    loginHandle,
    checkValidate,
    initFormEvents,
    bindInputChange,
    passwordStrength,
    insertNormalError,
    initRadioAction,
    initCheckboxAction,
    encryptPhone,
    encryptEmail,
    parseDomain,
    removeHomepageCookie,
    app_key,
    signupAppId,
    login_sign_app_id,
    forget_password_app_id,
    activate_by_phone_id,
    become_referral_id,
    qHPassCaptcha
};
