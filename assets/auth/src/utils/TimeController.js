class TimeController {
    constructor(options) {
        this.endEvent = false;
        this.everyTime = false;
        this.waiting_time = 60;
        this.waiting_time_control = false;
        this.is_waiting_time_status = false;
        for(let key in options) {
            this[key] = options[key];
        }
        this.waiting_time_default = this.waiting_time;
    }

    init() {
        this.timedCount();
    }

    timedCount(){
        if (this.waiting_time_control) {
            this.waiting_time_control = false;
            return false;
        }
        // 如果时间截止
        if (this.waiting_time <= 1) {
            this.is_waiting_time_status = false;
            this.waiting_time = this.waiting_time_default;
            //完成后操作
            typeof this.endEvent === 'function' && this.endEvent();
        } else {
            this.waiting_time -= 1;
            this.is_waiting_time_status = true;
            this.timer = setTimeout(() => {
                this.timedCount();
            }, 1000);
            //每次操作
            typeof this.everyTime === 'function' && this.everyTime(this.waiting_time);
        }
    }

    destroy(){
        clearTimeout(this.timer);
    }
}

export default TimeController;