import BaseValidate from './BaseValidate';
import TimeController from './TimeController';
import Notification from './Notification';
import fc from './common';
import {defineMessage} from '@q/fang-intl';
import Dialog from './Dialog';
import * as API from '../constants/API';
/**
* state: init working stop
*/

class SmsValidate extends BaseValidate {
    constructor(options) {
        super(options);
        this.options = {
            api: API.SMS_SEND,
            message_type: 'txt',
            smsTime: 60,
            maxLen: 4,
            fieldName: 'sms_captcha',
            template: ({maxLen, fieldName}) => `<div class="input-group sms-group">
                <input type="text" data-role="captcha" class="text form-control" maxlength="${maxLen}" name="${fieldName}" placeholder="${defineMessage({id:'base.smsCaptchaPlaceholder', defaultMessage: '请输入{0}位验证码'}, maxLen)}" autocomplete="off">
                <span class="input-group-addon get-code" data-role="get_captcha">${defineMessage({id: 'base.getVerificationCode', defaultMessage: '获取验证码'})}</span>
            </div>`,
            captcha_btn_template_diabled: (sec) => defineMessage({id: 'base.retrieveAfter', defaultMessage: '{0}秒后重新获取'}, sec),
            captcha_btn_init_template: defineMessage({id: 'base.getVerificationCode', defaultMessage: '获取验证码'}),
            captcha_btn_template: defineMessage({id: 'base.retrieve', defaultMessage: '重新获取验证码'}),
            voice_pos: 'after',
            showVoice: 1,
            voice_template: `<a href="#" data-role="get_voice_captcha">${defineMessage({id: 'base.getVoice', defaultMessage: '接收语音验证码'})}</a>`,
            voice_success_message: defineMessage({id: 'base.voiceSMSCaptcha_send', defaultMessage: '系统将通过免费电话给您发送语音验证码，60s内未收到可重新获取'}),
            voice_confirm_message: maxLen => defineMessage({id: 'base.voiceSMSCaptchaConfirm', defaultMessage: '我们将以电话的方式发送语音验证码。请注意接听并记下{0}位验证码。'}, maxLen)
        , ...options};

        this.callback = options.callback || {};
        if(!this.options.has_render){
            this.render();
        } else {
            this.$el = options.element;
        }
        this.bind();
    }

    render(){
        fc.addClass(this.$el, 'form-group sms-item');
        this.$el.innerHTML = this.options.template(this.options);
        // this.$el.append(this.template(this.options.template, this.options));
        // if(!('placeholder' in document.createElement('input'))){
        //     this.$el.find('input[placeholder]').placeholder();
        // }
    }

    // TODO not used
    refresh() {
        if(this.state === 'stop') return false;
        this.state = 'init';
        if(!this.smsTimeController || !this.smsTimeController.is_waiting_time_status){
            this.setCaptchaBtn();
            this.removeVoice();
        }
        this.clear();
    }

    // TODO not used
    clear() {
        this.$captcha.value = '';
    }

    bind() {
        this.$get_captcha = this.$el.querySelector('[data-role=get_captcha]');
        this.$captcha = this.$el.querySelector('[data-role=captcha]');
        fc.initInputAction(this.$captcha);

        this.$get_captcha.addEventListener('click', () => {
            this.send();
        });
        const { smsTime } = this.options;

        if(smsTime){
            this.smsTimeController = new TimeController({
                waiting_time: smsTime,
                endEvent: () => {
                    this.setCaptchaBtn(0);
                    this.setVoice();
                    this.setDisabled(0, 'get');
                    this.state = 'init';
                },
                everyTime: (currentSec) => {
                    this.setCaptchaBtn(currentSec);
                    this.setDisabled(1, 'get');
                }
            });
        }

        ['keyup', 'afterpaste'].forEach(eventName => {
            this.$captcha.addEventListener(eventName, function() {
                this.value = this.value.replace(/\D/g, '');
            });
        });

    }

    send(data) {
        if(this.isDisabled('get')) return false;
        data = {
            phone: this.getPhone()
            , ...data
        };

        if(typeof this.options.syncs === 'function'){
            Object.assign(data, this.options.syncs.call(this, data, this.options));
        }

        this.state = 'working';
        this.params = data;
        data.phone && this.setPhone(data.phone);
        if(data.message_type === 'voice'){
            let dialog = new Dialog({
                message: {
                    main: this.options.voice_confirm_message(this.options.maxLen)
                },
                showTitle: false,
                btnType: 'primary',
                cancelText: defineMessage({id: 'base.cancelText', defaultMessage: '下次再说'}),
                confirmText: defineMessage({id: 'base.receiveVoiceConfirm', defaultMessage: '接收语音验证码'}),
                confirm: () => {
                    this._send(data);
                    dialog.destroy();
                }
            }).show();
        } else {
            this._send(data);
        }
    }

    _send(data) {
        this.setDisabled(1, 'get');
        this._send_captcha(data, {
            success: res => {
                let customEvent = this.trigger('send_success', res);
                if(!customEvent.isDefaultPrevented()) {
                    if(data.message_type === 'voice'){
                        new Notification({
                            message: this.options.voice_success_message
                        });
                    } else {
                        // Notification({
                        //     message: this.options.success_message
                        // })
                    }
                }
                if(this.$get_voice_captcha) this.removeVoice();
                this.smsTimeController && this.smsTimeController.init();
            },
            error: res => {
                this.setDisabled(0, 'get');
                this.trigger('send_error', res);
                this.state = 'init';
                // var errs = res.errors.validation_errors || res.errors.external_errors;
                // if(!customEvent.isDefaultPrevented() && !_self.options.disabled_auto_error && errs[0] && errs[0]['error_tr_msg']){
                //     fc.topNotify(errs[0]['error_tr_msg']);
                // }
                // var error_code = errs[0].error_code;
                // if(error_code === 'same_phone_sending_count_exceeded') {
                //     _self.state = 'stop'
                // }
            }
        });
    }

    _send_captcha(data, callback){
        let successHandle, errorHandle;
        if(typeof callback === 'function'){
            successHandle = callback;
        } else if(callback.success){
            successHandle = callback.success;
            if(typeof callback.error === 'function'){
                errorHandle = callback.error;
            }
        }

        fc.ajax({
            url: this.options.api,
            type: 'json',
            data,
            accept_all_errors: true,
            parser: (res) => {
                // let res = {success: true};
                if(res.success){
                    typeof successHandle === 'function' && successHandle(res);
                } else {
                    if(typeof errorHandle === 'function'){
                        errorHandle(res);
                    }
                    if (!this.options.accept_errors) {
                        res.errors.forEach((error) => {
                            fc.insertError(error.field || this.options.fieldName, error.error_msg);
                        });
                    }
                }
            }
        });
    }

    getPhone() {
        return this._phone;
    }

    setPhone(phone){
        this._phone = phone;
    }

    setCaptchaBtn(currentSec) {
        if(currentSec){
            this.$get_captcha.innerHTML = this.options.captcha_btn_template_diabled(currentSec);
        } else if(this.state === 'init'){
            this.$get_captcha.innerHTML = this.options.captcha_btn_init_template;
        } else {
            this.$get_captcha.innerHTML = this.options.captcha_btn_template;
        }
    }

    show(){
        this.$el.style.display = 'block';
        this.trigger('show');
    }

    hide(){
        this.$el.style.display = 'none';
        this.setDisabled();
        this.trigger('hide');
    }

    getValue(){
        return this.$captcha.value;
    }

    setValue(value){
        return this.$captcha.value = value;
    }

    destroy(){
        this.trigger('destroy');
        if(this.smsTimeController) this.smsTimeController.destroy();
    }

    setVoice(){
        if(this.$get_voice_captcha || this.state === 'init') return false;
        // 云片不支持英语播报验证码 WEB-4080
        if(typeof this.options.showVoice === 'function'){
            if(!this.options.showVoice.call(this)) return false;
        } else if(!this.options.showVoice) {
            return false;
        // } else if(!mod.checkPhoneByLocal(this.getPhone(), {check_other_phone: false})){
        //     return false;
        }

        fc.clearError(this.$el.querySelector('[data-role=captcha]'));
        this.$el_voice = document.createElement('div');
        fc.addClass(this.$el_voice, 'voice-captcha');
        this.$el.appendChild(this.$el_voice);
        this.$el_voice.innerHTML = this.options.voice_template;

        this.$get_voice_captcha = this.$el.querySelector('[data-role=get_voice_captcha]');
        this.$get_voice_captcha.addEventListener('click', ev => {
            ev.preventDefault();
            this.send({message_type: 'voice'});
        });
    }

    removeVoice(){
        if(!this.$el_voice) return;
        fc.removeElement(this.$el_voice);
        this.$get_voice_captcha = null;
        this.$el_voice = null;
    }

    disabledChange(type, disabled) {
        if(type === 'get') {
            if(disabled) {
                this.$get_captcha.setAttribute('get-disabled', 'disabled');
            } else {
                this.$get_captcha.removeAttribute('get-disabled');
            }
        }
    }

    getState() {
        return this.state;
    }
}

export default SmsValidate;