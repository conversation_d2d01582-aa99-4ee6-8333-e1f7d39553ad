import Events from './Events';
import fc from './common';

class BaseValidate extends Events {
    constructor(options) {
        super(options);
        this.options = {tagName: 'div', ...options};
        this._ensureElement();
        this.disabled_list = {};
        this.state = 'init';
    }

    _ensureElement() {
        const { attributes, tagName, className } = this.options;
        let attrs = attributes;
        let $el = document.createElement(tagName);
        for(let key in attrs) {
            $el.setAttribute(key, attrs[key]);
        }
        if(className) {
            fc.addClass($el, className);
        }
        this.$el = $el;
    }

    getMessage(msg, params) {
        if(typeof msg === 'string') {
            return msg;
        } else if(typeof msg === 'function') {
            if(!Array.isArray(params)) {
                params = [params];
            }
            return msg.apply(this, params);
        } else {
            return '';
        }
    }

    template(template, data) {
        let text;
        if(!data){
            text = template;
        } else {
            text = template.replace(/\{\{(.+?)\}\}/g, function(a, a1){
                if(typeof data[a1] === 'string' || typeof data[a1] === 'number'){
                    return data[a1];
                }
            });
        }
        return text;
    }

    disabledChange() {}

    setDisabled(disabled, type){
        if(disabled === undefined) disabled = true;
        if(this.state === 'stop' && !disabled) return false;
        if(type === undefined){
            if(disabled !== this.disabled){
                this.disabled = disabled;
                this.disabledChange(type, disabled);
            }
        } else if(type === 'reset'){
            this.disabled = disabled;
            this.disabled_list = {};
            this.disabledChange(type, disabled);
        } else {
            if(disabled !== this.disabled_list[type]){
                this.disabled_list[type] = disabled;
                this.disabledChange(type, disabled);
            }
        }
    }

    isDisabled(type){
        if(type){
            return this.disabled || this.disabled_list[type];
        } else {
            return this.disabled;
        }
    }
}

export default BaseValidate;