let prepend = '';
if(/^\/account\//i.test(window.location.pathname)) {
    prepend = '/account';
}

const api_url = document.getElementById('api_url') && document.getElementById('api_url').value;
// const api_url = '/api_v2';

export const AUTH_HI = '/auth/hi';
export const SSO_LOGOUT = prepend + '/sso/logout';
export const SECURITY_SETTINGS = prepend + '/security_settings';
export const LOGIN_SETTINGS = prepend + '/login_settings';
export const REGISTER = prepend + '/register';
export const AI_REGISTER = prepend + '/new_register?plan_id=30&register_position=2';

export const CHECK_DEVICE = prepend + '/security/check_device';
export const SMS_SEND = prepend + '/captcha/sms_send';
// captcha
export const PIC_CAPTHA = prepend + '/captcha/get_pic';
export const FORGOT_PASSWORD_PIC_CAPTHA = prepend + '/captcha/get_pic/forgot_password';
export const FORGOT_PASSWORD_BY_PHONE_PIC_CAPTHA = prepend + '/captcha/get_pic/forgot_password_by_phone';
export const PHONE_ACTIVATE_PIC_CAPTHA = prepend + '/captcha/get_pic/activate_by_phone';
export const LOGIN_PIC_CAPTHA = prepend + '/captcha/get_pic/login';
export const REGISTER_PIC_CAPTHA = prepend + '/captcha/get_pic/register';
export const MODIFY_PHONE_PIC_CAPTHA = prepend + '/captcha/get_pic/modify_phone';
export const REGISTER_PIC_CAPTHA_BECOME_REFERRAL = prepend + '/captcha/get_pic/become_referral';
export const CHECK_PHONE = prepend + '/captcha/check_phone/';
export const TWO_SETP_CAPTCHA = prepend + '/captcha/get_pic/two_step';

// activate
export const ACTIVATE_CONFIRM_ACCOUNT = prepend + '/activation/confirm_account';
export const VERIFY_PHONE = prepend + '/activation/verify_phone';
export const ACTIVATE = prepend + '/activation/activate';


// forgot
export const PASSWORD_INIT = prepend + '/password/init';
export const FORGOT = prepend + '/password/forgot';
export const INTERNATIONAL_FORGOT = prepend + '/password/forgot_for_international';
export const FORGOT_CONFIRM_ACCOUNT = prepend + '/password/confirm_account';
export const RESET_PASSWORD = prepend + '/password/reset';
export const SET_PASSWORD = prepend + '/password/set';
export const CONFIRM_FORGOT = prepend + '/password/confirm_forgot';

// LOGIN
// 登录链接特殊 需要用apiurl来拼凑
export const LOGIN = api_url + '/sso/login';
export const QR_LOGIN_IMAGE = prepend + '/qr_login/image/';
export const QR_LOGIN_CHECK_TOKEN = prepend + '/qr_login/check_token';
export const OAUTH_QR_LOGIN_CHECK_TOKEN = prepend + '/oauth/qrlogin';
export const QR_CONFIRM_LOGIN = prepend + '/qr_login/confirm_login';
export const CHECK_LOGIN = prepend + '/check_login';
export const LOGIN_ACTION = prepend + '/login';
export const BIND_LOGIN_ACTION = prepend + '/bind_login';
export const APP_LOGIN_ACTION = prepend + '/app_login';
export const SYNC_LOGIN_ACTION = prepend + '/sync_login';
export const OAUTH_LOGIN_ACTION = prepend + '/oauth/login';
export const DEDICATED_ENTERPRISE_LOGIN = prepend + '/dedicated_enterprise/get_sso_product_id';


// two step login

// register
export const PRE_REGISTER_ACTION = prepend + '/pre_register';
export const BIND_REGISTER_ACTION = prepend + '/bind_register';
export const REGISTER_ACTION = prepend + '/register';
export const REGUSTER_ACTION_MOBILE = prepend + '/api/v2/security/quick_register';
export const ENTERPRISE_REGISTER_ACTION = prepend + '/register_enterprise_edition';
export const REGISTER_SUCCESS = prepend + '/register_success';
export const ENTERPRISE_REGISTER_SUCCESS = prepend + '/enterprise_register_success';
export const PERSONAL_REGISTER = prepend + '/personal_register';
export const ALI_MARKET_REGISTER = prepend + '/ali_market_register';
// two step login settings
export const TWO_STEP_LOGIN_ACTION = prepend + '/two_step_login';
export const VERIFY_IDENTITY = prepend + '/security/verify_identity';
export const SET_TWO_STEP = prepend + '/security/set_two_step';
export const SEND_TWO_STEP_CODE = prepend + '/security/send_two_step_code';
export const CLOSE_TWO_STEP = prepend + '/security/close_two_step';
export const GET_AVAILABLE_TWO_STEP_METHOD = prepend + '/security/get_available_two_step_method';
export const GET_GOOGLE_SECRET = prepend + '/security/get_google_secret';
export const GET_GOOGLE_QRCODE = prepend + '/security/get_google_qrcode';
export const GET_WECHAT_QRCODE = prepend + '/security/get_wechat_qrcode';

// user_settings
export const CHANGE_PASSWORD = prepend + '/password/change';
export const GET_LOGIN = prepend + '/credential/get_login';
export const UPDATE_PHONE = prepend + '/credential/update_phone';
export const UPDATE_EMAIL = prepend + '/credential/update_email';
export const VERIFY_PHONE_CREDENTIAL = prepend + '/credential/verify_phone';
export const VERIFY_EMAIL = prepend + '/credential/verify_email';
export const UNBIND_ACCOUNT = prepend + '/credential/unbind';
export const GET_THIRD_LOGIN = prepend + '/security/get_thirds';
export const THIRD = prepend + '/third';
export const UNBIND_THIRD_LOGIN = prepend + '/security/unbind_third';
export const GET_DEVICES = prepend + '/security/get_devices';
export const DELETE_DEVICE = prepend + '/security/delete_device';
export const GET_TWO_STEP_METHOD = prepend + '/security/get_two_step_method';
export const PRE_VERIFY_PHONE = prepend + '/credential/pre_verify_phone';
export const PRE_VERIFY_EMAIL = prepend + '/credential/pre_verify_email';

// 注册离开打点
export const REPORT_WHY_LEAVE = prepend + '/report_why_leave';

export const CHANNEL_COUPON = prepend + '/check_channel_coupon';

// 注册推荐官
export const BECOME_REFERRAL = prepend + '/become_referral';

// 阿里验证码校验
export const CAPTCHA_VERIFY = api_url + '/hermes/captcha_verify';
// 获取服务器时间
export const GET_TS = api_url + '/hermes/ts';

export const SMS_SEND_V1 = prepend + '/quick_login/v1/captcha/sms_send';
export const REGISTER_V1 = prepend + '/quick_login/v1/register';

// API TOKEN
export const API_TOKEN_INFO = prepend + '/security/get_open_api_token_info';
export const API_TOKEN_REFRESH = prepend + '/security/refresh_open_api_token_info';

// export const API_TOKEN_INFO = 'http://127.0.0.1:4523/m1/952839-337726-default/apps/users/get_open_api_token_info';
// export const API_TOKEN_REFRESH = api_url + '/apps/users/refresh_open_api_token_info';
