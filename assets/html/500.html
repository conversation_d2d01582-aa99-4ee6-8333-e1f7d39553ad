<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>出错了</title>
    <link rel="shortcut icon" href="https://v2.fangcloud.com/favicon_500.ico" type="image/x-icon" />

    <style type="text/css">
        * { margin: 0; padding: 0; }
        body { font-family: Arial, 'Helvetica Neue', Helvetica, "Microsoft YaHei", "\5FAE\8F6F\96C5\9ED1", 'PingFang SC', \5fae\8f6f\96c5\9ed1, "WenQuanYi Micro Hei", \5b8b\4f53, sans-serif; }
        @font-face {
            font-family: "iconfont";
            src: url('//at.alicdn.com/t/font_x8a7bwval9639pb9.eot?t=1495157522455'); /* IE9*/
            src: url('//at.alicdn.com/t/font_x8a7bwval9639pb9.eot?t=1495157522455#iefix') format('embedded-opentype'), /* IE6-IE8 */
            url('//at.alicdn.com/t/font_x8a7bwval9639pb9.woff?t=1495157522455') format('woff'), /* chrome, firefox */
            url('//at.alicdn.com/t/font_x8a7bwval9639pb9.ttf?t=1495157522455') format('truetype'), /* chrome, firefox, opera, Safari, Android, iOS 4.2+*/
            url('//at.alicdn.com/t/font_x8a7bwval9639pb9.svg?t=1495157522455#iconfont') format('svg'); /* iOS 4.1- */
        }
        .iconfont {
            font-family:"iconfont" !important;
            font-size:16px;
            font-style:normal;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }
        .icon-phone1:before { content: "\e778"; }
        .icon-qq1:before { content: "\e780"; }
        .icon-email:before { content: "\e71c"; }
        .img {
            display: block;
            margin: auto;
            width: 177px;
            height: 140px;
            background: no-repeat center center;
            background-size: 100%;
            background-image:url('data:image/png;base64,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');
        }
        .content {
            position: relative;
        }
        .center {
            position: absolute;
            left: 50%;
            top: 50%;
            width: 360px;
            height: 260px;
            margin: -130px 0 0 -180px;
        }
        a {
            color: #308cef;
            text-decoration: none;
        }
        .sorry {
            text-align: center;
            color: #6a6c6d;
            width: 340px;
            margin: 20px auto 0;
        }
        .footer {
            color: #a5a9aa;
            text-align: center;
            height: 50px;
            line-height: 50px;
            display: none;
        }
        .iconfont {
            margin: 0 5px 0 15px;
        }
        html, body {
            height: 100%;
        }
        .content {
            min-height: 100%;
            margin-bottom: -50px;
        }
        .center::after {
            content: '';
            display: block;
            height: 50px;
        }
    </style>
</head>
<body>
    <div class="content">
        <div class="center">
            <div class="img"></div>
            <div class="sorry">服务器开了一点小差，程序猿小哥正在加紧处理<br />请稍后访问或<a href="javascript:location.reload();">刷新重试</a></div>
        </div>
    </div>
    <div class="footer" id="footer">联系客服：<i class="iconfont icon-phone1"></i>************<i class="iconfont icon-qq1"></i><a target="_blank" href="http://wpa.qq.com/msgrd?v=3&uin=800078322&site=qq&menu=yes" title="点击这里给我们发消息">800078322</a></div>
</body>
<script type="text/javascript">
    if (window.location.href.indexOf('https://v2.fangcloud.com') > -1) {
        document.getElementById('footer').style.display = 'block';
    }
</script>
<script type="text/javascript" src="https://staticv2.fangcloud.com/assets/desktop/dist/lib/qwebchannel.min.js"></script>
<script type="text/javascript">
    function setPageTitle() {
        window.sync_v2.setTabText('出错了');
        window.sync_v2.setTabIcon('https://v2.fangcloud.com/favicon_500.ico');
    }

    if(window.qt) {
        new QWebChannel(qt.webChannelTransport, function(channel) {
            window.sync_v2 = channel.objects.sync_v2;
            setPageTitle();
        });
    } else if (window.sync_v2) {
        setPageTitle();
    }

</script>
</html>
