.center {
    -webkit-box-align: center;
    -moz-box-align: center;
    -ms-box-align: center;
    -o-box-align: center;
    box-align: center;
    -webkit-box-pack: center;
    -moz-box-pack: center;
    -ms-box-pack: center;
    -o-box-pack: center;
    box-pack: center;
}
.center > article {
    -webkit-box-flex: 0;
    -moz-box-flex: 0;
    -ms-box-flex: 0;
    -o-box-flex: 0;
    box-flex: 0;
}
.page {
    width: 640px;
    height: 1008px !important;
}
.page1 {
    background: url(./images/1.jpg) no-repeat;
    background-size: 100%;
    padding-top: 340px;
}
.start {
    width: 440px;
    height: 226px;
    /*margin-top: 340px;*/
    margin: 0 auto;
    background: url(./images/start.gif) no-repeat 0 0;
    background-size: 100%;
}
.page2 {
    background: url(./images/2-bg.png) no-repeat;
    background-size: 100%;
}
.keyboard {
    width: 524px;
    height: 324px;
    padding-top: 97px;
    margin: 0 auto;
    background: url(./images/2-keyboard.png) no-repeat center 97px;
    background-size: 100%;
}
.form-control {
    width: 520px;
    height: 100px;
    margin: 22px auto 0;
    background-size: 100%;
}
.form-control input[type=text], .form-control select {
    width: 383px;
    height: 48px;
    margin: 36px 0 0 114px;
    font-size: 28px;
    background-color: transparent;
    background-repeat: no-repeat;
    background-size: 100%;
}
.name-input {
    background-image: url(./images/name-input.png);
}
.star-input {
    background-image: url(./images/star-select.png);
}
.job-input {
    background-image: url(./images/job-input.png);
}
form input[type=submit] {
    display: block;
    width: 283px;
    height: 95px;
    border: 0;
    background: url(./images/submit.png)  no-repeat;
    margin: 58px auto 0;
    background-size: 100%;
}
.share-button, .test-button {
    width: 209px;
    height: 96px;
    margin: 0 auto 30px;
}
.result-title {
    width: 100%;
    height: 152px;
}
.result-content {
    position: relative;
    width: 100%;
    height: 475px;
}
.result-content span {
    position: absolute;
    font-size: 26px;
    font-weight: bold;
}

.result1 {
    height: 500px;
}
.result1 span {
    left: 382px;
    top: 115px;
}

.result2 span {
    left: 53px;
    top: 45px;
}

.result3 {
    height: 600px;
}
.result3 span {
    left: 385px;
    top: 120px;
}

.result4 {
    height: 500px;
}
.result4 span {
    left: 53px;
    top: 135px;
}

.result5 {
    height: 510px;
}
.result5 span {
    left: 385px;
    top: 146px;
}

.result6 span {
    left: 35px;
    top: 55px;
}

.result7 span {
    left: 382px;
    top: 184px;
}

.result8 {
    height: 550px;
}
.result8 span {
    left: 57px;
    top: 132px;
}

.result9 span {
    left: 400px;
    top: 117px;
}

.result10 span {
    left: 43px;
    top: 92px;
}


.wechat img {
    display: block;
    width: 100%;
}
.footer {
    position: relative;
    width: 100%;
    height: 252px;
}
.footer img {
    width: 100%;
    height: 252px;
}
.footer .qrcode {
    position: absolute;
    left: 0;
    top: 0;
    width: 385px;
    height: 213px;
    padding: 44px 130px;
}