(function(){

    var base_url = $('#base_url').val();
    var asset_url = $('#asset_url').val();

    var image_dir = asset_url + 'html/wechat_career/images/';

    var share_title = '测测你10年后的职场角色';
    var share_image = 'si_1.jpg';

    var titles = [
        '晋升大学叫兽，专攻博士后',
        '成为霸道总裁，屌丝逆袭',
        '转行试睡员，美梦成真',
        '荣升少林寺方丈，德高望重',
        '当上国际导游，逛吃逛吃',
        '转行攻城狮，牛×哄哄',
        '转行摄影师，专拍美女',
        '晋升联邦警探，天天潜伏',
        '成为段子手，混迹江湖',
        '转行南极电工，工资高得没处花'
    ];

    $(function(){
        resize();

        var result = $('#result').val();
        var name = $('#y_name').val();

        if(result){
            share_title = name + '10年后将' + titles[result - 1];
            document.title = share_title;
            share_image = 'si_' + result + '.jpg';
        }


        $('.start').tap(function(ev){
            $('.page1').removeClass('active');
            $('.page2').addClass('active');
        })

        $('.test-button').tap(function(ev){
            document.title = '测测你10年后的职场角色';
            $('.page3').removeClass('active');
            $('.page1').addClass('active');
        })


        $('form').submit(function(ev){
            if($(this).find('[name=yfy_result]')[0]){
                return;
            }
            ev.preventDefault();

            var name = $.trim($('#name').val());
            var star = $('#star').val();
            // var job = $('#job').val();

            if(name === "" || star === ""){
                alert('请填写姓名！')
                return false;
            }

            var s = name + star;
            var r = string_hash(s) % 10 + 1;

            $(this).append('<input type="hidden" name="yfy_result" value="' + r + '" />');
            this.action = '/welcome/wx_career_test?yfy_name=' + name + '&yfy_result=' + r;
            $(this).submit();

        })

        $('.share-button').tap(function(ev){
            $('.wechat').addClass('show');
        })
        $('.wechat').tap(function(ev){
            $(this).removeClass('show');
        })
    })

    function string_hash(str, seed) {
        seed = !!seed ? seed : 131;
        var hash = 0;
        var i = 0;
        while (true) {
            var ch = str.charCodeAt(i++);
            if (!(ch > 0 || ch < 0)) {
                break;
            }
            hash = (seed * hash + ch) & 0x7FFFFFFF;
        }
        return hash;
    }

    function resize(){
        var scale = window.dpr||(window.navigator.userAgent.match(/iPhone|iPad|iPod/)?document.documentElement.clientWidth/window.screen.availWidth:1)

        var viewport = $('meta[name=viewport]');
        if(viewport.length === 0){
            viewport = $('head').append('<meta name="viewport">');
        }
        viewport.attr('content', 'initial-scale=' + scale + ',minimum-scale=' + scale + ',maximum-scale=' + scale + ',user-scalable=no');
        var offset = $('body').offset();
        var width = offset.width;
        var height = offset.height;
        if(offset.width < 640){
            width = 640;
            scale = scale / (width / offset.width);
            height = height * (width / offset.width);
        }
        viewport.attr('content', 'initial-scale=' + scale + ',minimum-scale=' + scale + ',maximum-scale=' + scale + ',user-scalable=no');
        $('section').css({
            width: width,
            height: height
        }).addClass('show');
    }
})();
