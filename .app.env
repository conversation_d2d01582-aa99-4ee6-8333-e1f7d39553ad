# 业务逻辑中需要生产环境和测试环境分开的放在
#       .env.app (生产环境)
#       .env.app.test (测试环境)
# 两份配置文件都会进入git仓库。
# 因此需要保密的配置放在 .env 文件中
#
# 在使用 env 变量 config 文件中也会有一个默认值。
# 建议将该默认值写到 .env.app.test 配置文件中

# -------------------------- App 相关 --------------------------
# App Name
APP_NAME="Fangcloud Account"
# base url
ACCOUNT_BASE_URL=https://account.fangcloud.com/
STATIC_URL=https://account.fangcloud.com/
# 登录之后默认跳转的页面
DEFAULT_PAGE=https://v2.fangcloud.com/apps/files/desktop
# h5登录之后默认跳转的页面
DEFAULT_H5_PAGE=https://v2.fangcloud.com/h5
# fangcloud 主站地址
FANGCLOUD_URL=https://v2.fangcloud.com/
# api url
API_URL=https://v2.fangcloud.com/
V1_URL=https://www.fangcloud.com/
FANG_HOST=fangcloud.com
# 前端资源路径
ASSET_PATH_AUTH = auth/dist/
# oia 非亿方云扫码地址路径
H5_DOWNLOAD_NOTICE=https://oia.fangcloud.com/h5_download_notice
# 百度统计开关
analytic_enable = 1

# 为了让开发测试环境能拿到
# PRODUCT_NAME_EN=Fangcloud
# PRODUCT_NAME_ZH_CN=亿方云
# PRODUCT_NAME_ZH_TW=億方雲
# PRODUCT_NAME_ZH_HK=億方雲

LOCALE=zh-CN
# -------------------------- End of App 相关 --------------------------

# auth token 过期时间
COMMON_AUTH_TOKEN_EXPIRES_AT_DURATION=21600

# 生产环境各种service id约束
COMMON_SERVICE_WEB='0'
COMMON_SERVICE_MOBILE_APP_SERVICE_IDS='1,2'
COMMON_SERVICE_IOS_APP_SERVICE_IDS='1'
COMMON_SERVICE_ANDROID_APP_SERVICE_IDS='2'
COMMON_SERVICE_SYNC_CLIENT_SERVICE_IDS='5,6,10'
COMMON_SERVICE_MAC_SYNC_CLIENT_SERVICE_IDS='6'
COMMON_SERVICE_WIN_SYNC_CLIENT_SERVICES_IDS='5'

# 二次验证可信设备持续时间
COMMON_TWO_STEP_CREDITABLE_DURATION=15552000
# 二次验证距离上一次登录的时间
COMMON_TWO_STEP_DEVICE_LAST_LOGIN_PERIOD=15552000

# session 过期时间(分钟)
SESSION_LIFETIME=1440
SESSION_DOMAIN=.fangcloud.com

#guard service
SERVICE_GUARD_SECRET_EXPIRATION=86400
# phoenix
SERVICE_PHOENIX_BASE_URI='http://127.0.0.1:9898/'

#internal auth service_id
INTERNAL_AUTH_SERVICE_ID=110

# web service id
WEB_AUTH_SERVICE_ID=1

# exception trace max lines
# log 中异常的 debug trace的最大字符数 用作分割  $exception->getTraceAsString
EXCEPTION_TRACE_CHARS=2000

# app 兼容 user 对象的最低版本号
USER_APP_VERSION=3.6.0

# sync 登陆框变大的版本
NEW_LOGIN_SYNC_VERSION=5.5.0

# sync 兼容 user 对象的最低版本号
USER_SYNC_VERSION=5.5.0

# log storage path
PATH_STORAGE=/var/log/account/

ENCRYPTION_MIN_CLIENT_VERSION_PLATFORM_WINDOWS = 6.1.00053

#content detection配置
# 是否需要按照关键词过滤
COMMON_CONTENT_DETECTION_KEYWORD_FILTER = true;

COMMON_JD_STORAGE_ID = 172
COMMON_JD_FILE_SERVER_ID = 74
